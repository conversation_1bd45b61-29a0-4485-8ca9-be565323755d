package main

import (
	"fmt"
	"math/rand"
	"sync"
	"time"

	"github.com/user/agv_nav/internal/zmq"
)

// MockDataGenerator 模拟数据生成器
type MockDataGenerator struct {
	mu      sync.RWMutex
	robotNo string

	// 模拟状态
	batteryLevel    float64
	currentPosition string
	taskId          string
	currentMachine  string
	robotState      int
	taskState       int

	// 模拟任务列表
	machineList  []string
	machineIndex int

	// 随机种子
	rand *rand.Rand
}

// NewMockDataGenerator 创建模拟数据生成器
func NewMockDataGenerator(robotNo string) *MockDataGenerator {
	generator := &MockDataGenerator{
		robotNo:         robotNo,
		batteryLevel:    85.5,
		currentPosition: zmq.PositionParkingStr, // "0" 停车点
		taskId:          "",
		currentMachine:  "",
		robotState:      zmq.RobotStateNormal,         // 1 正常
		taskState:       zmq.RobotTaskStateNotStarted, // 10 未开始
		machineList:     []string{"61L", "60R", "59L", "58R", "57L", "56R"},
		machineIndex:    0,
		rand:            rand.New(rand.NewSource(time.Now().UnixNano())),
	}

	return generator
}

// GenerateStatusReport 生成状态报告内容
func (g *MockDataGenerator) GenerateStatusReport() *zmq.StatusReportContent {
	g.mu.Lock()
	defer g.mu.Unlock()

	// 模拟电量变化 (缓慢下降)
	g.batteryLevel -= float64(g.rand.Intn(3)) * 0.1
	if g.batteryLevel < 15.0 {
		g.batteryLevel = 90.0 + float64(g.rand.Intn(10)) // 模拟充电后恢复
	}

	// 根据电量调整机器人状态
	if g.batteryLevel < 15.0 {
		g.robotState = zmq.RobotStateLowBattery // -1 电量不足
	} else if g.rand.Intn(100) < 5 { // 5% 概率模拟异常
		g.robotState = zmq.RobotStateAbnormal // -99 异常
	} else {
		g.robotState = zmq.RobotStateNormal // 1 正常
	}

	// 模拟位置变化
	g.updateMockPosition()

	// 生成备注信息
	remark := g.generateRemark()

	return &zmq.StatusReportContent{
		RobotNo:         g.robotNo,
		TaskId:          g.taskId,
		RobotTaskState:  g.taskState,
		RobotState:      g.robotState,
		BatteryPower:    g.batteryLevel,
		CurrentPosition: g.currentPosition,
		Remark:          remark,
	}
}

// updateMockPosition 更新模拟位置
func (g *MockDataGenerator) updateMockPosition() {
	switch g.taskState {
	case zmq.RobotTaskStateNotStarted: // 10 未开始
		// 随机在停车点或切换点
		if g.rand.Intn(10) < 7 {
			g.currentPosition = zmq.PositionParkingStr // "0" 停车点
		} else {
			g.currentPosition = zmq.PositionSwitchStr // "2" 权限交接点
		}

	case zmq.RobotTaskStateInProgress: // 20 进行中
		// 随机在主干道或切换点
		if g.rand.Intn(10) < 6 {
			g.currentPosition = zmq.PositionMainRoadStr // "10" 主干道
		} else {
			g.currentPosition = zmq.PositionSwitchStr // "2" 权限交接点
		}

	case zmq.RobotTaskStateCompleted: // 30 已完成
		// 通常在停车点
		g.currentPosition = zmq.PositionParkingStr // "0" 停车点
	}

	// 偶尔模拟充电点 (低概率)
	if g.batteryLevel < 20.0 && g.rand.Intn(10) < 3 {
		g.currentPosition = zmq.PositionChargingStr // "1" 充电点
	}
}

// generateRemark 生成备注信息
func (g *MockDataGenerator) generateRemark() string {
	switch {
	case g.robotState == zmq.RobotStateAbnormal:
		return "机器人故障，需要检修"
	case g.robotState == zmq.RobotStateLowBattery:
		return "电量不足，正在充电"
	case g.taskState == zmq.RobotTaskStateInProgress && g.currentMachine != "":
		return fmt.Sprintf("正在处理机器 %s", g.currentMachine)
	case g.taskState == zmq.RobotTaskStateCompleted:
		return "任务已完成，等待新任务"
	case g.currentPosition == zmq.PositionMainRoadStr:
		return "正在导航中"
	case g.currentPosition == zmq.PositionSwitchStr:
		return "在权限交接点等待"
	default:
		return "状态正常"
	}
}

// GenerateTaskId 生成任务ID
func (g *MockDataGenerator) GenerateTaskId() string {
	g.mu.Lock()
	defer g.mu.Unlock()

	if g.taskId == "" {
		timestamp := time.Now().Unix()
		g.taskId = fmt.Sprintf("TASK_%s_%d", g.robotNo, timestamp)
	}

	return g.taskId
}

// GetCurrentMachine 获取当前机器
func (g *MockDataGenerator) GetCurrentMachine() string {
	g.mu.RLock()
	defer g.mu.RUnlock()

	if g.currentMachine == "" && len(g.machineList) > 0 {
		return g.machineList[g.machineIndex]
	}

	return g.currentMachine
}

// StartNewTask 开始新任务
func (g *MockDataGenerator) StartNewTask() {
	g.mu.Lock()
	defer g.mu.Unlock()

	// 生成新任务ID
	timestamp := time.Now().Unix()
	g.taskId = fmt.Sprintf("TASK_%s_%d", g.robotNo, timestamp)

	// 设置任务状态为进行中
	g.taskState = zmq.RobotTaskStateInProgress

	// 选择当前机器
	if len(g.machineList) > 0 {
		g.currentMachine = g.machineList[g.machineIndex]
	}

	// 设置位置为主干道（开始导航）
	g.currentPosition = zmq.PositionMainRoadStr
}

// CompleteCurrentTask 完成当前任务
func (g *MockDataGenerator) CompleteCurrentTask() {
	g.mu.Lock()
	defer g.mu.Unlock()

	// 设置任务状态为已完成
	g.taskState = zmq.RobotTaskStateCompleted

	// 移动到下一个机器
	g.machineIndex = (g.machineIndex + 1) % len(g.machineList)

	// 清空当前机器
	g.currentMachine = ""

	// 设置位置为停车点
	g.currentPosition = zmq.PositionParkingStr
}

// PauseCurrentTask 暂停当前任务
func (g *MockDataGenerator) PauseCurrentTask() {
	g.mu.Lock()
	defer g.mu.Unlock()

	// 任务状态保持进行中，但位置变为切换点
	g.currentPosition = zmq.PositionSwitchStr
}

// ResumeCurrentTask 恢复当前任务
func (g *MockDataGenerator) ResumeCurrentTask() {
	g.mu.Lock()
	defer g.mu.Unlock()

	// 设置位置为主干道（恢复导航）
	g.currentPosition = zmq.PositionMainRoadStr
}

// ForceStopTask 强制停止任务
func (g *MockDataGenerator) ForceStopTask() {
	g.mu.Lock()
	defer g.mu.Unlock()

	// 设置任务状态为未开始
	g.taskState = zmq.RobotTaskStateNotStarted

	// 清空任务信息
	g.taskId = ""
	g.currentMachine = ""

	// 设置位置为停车点
	g.currentPosition = zmq.PositionParkingStr
}

// SetBatteryLevel 设置电量
func (g *MockDataGenerator) SetBatteryLevel(level float64) {
	g.mu.Lock()
	defer g.mu.Unlock()

	if level < 0 {
		level = 0
	} else if level > 100 {
		level = 100
	}

	g.batteryLevel = level
}

// SetPosition 设置位置
func (g *MockDataGenerator) SetPosition(position string) {
	g.mu.Lock()
	defer g.mu.Unlock()

	g.currentPosition = position
}

// SetRobotState 设置机器人状态
func (g *MockDataGenerator) SetRobotState(state int) {
	g.mu.Lock()
	defer g.mu.Unlock()

	g.robotState = state
}

// SetTaskState 设置任务状态
func (g *MockDataGenerator) SetTaskState(state int) {
	g.mu.Lock()
	defer g.mu.Unlock()

	g.taskState = state
}

// GetStatus 获取当前状态摘要
func (g *MockDataGenerator) GetStatus() map[string]interface{} {
	g.mu.RLock()
	defer g.mu.RUnlock()

	return map[string]interface{}{
		"robotNo":         g.robotNo,
		"batteryLevel":    g.batteryLevel,
		"currentPosition": g.currentPosition,
		"taskId":          g.taskId,
		"currentMachine":  g.currentMachine,
		"robotState":      g.robotState,
		"taskState":       g.taskState,
		"machineList":     g.machineList,
		"machineIndex":    g.machineIndex,
	}
}

// PrintStatus 打印当前状态
func (g *MockDataGenerator) PrintStatus() {
	status := g.GetStatus()

	fmt.Println("\n=== 模拟数据状态 ===")
	fmt.Printf("机器人编号: %s\n", status["robotNo"])
	fmt.Printf("电量: %.1f%%\n", status["batteryLevel"])
	fmt.Printf("当前位置: %s\n", status["currentPosition"])
	fmt.Printf("任务ID: %s\n", status["taskId"])
	fmt.Printf("当前机器: %s\n", status["currentMachine"])
	fmt.Printf("机器人状态: %d\n", status["robotState"])
	fmt.Printf("任务状态: %d\n", status["taskState"])
	fmt.Printf("机器列表: %v\n", status["machineList"])
	fmt.Printf("当前机器索引: %d\n", status["machineIndex"])
}

// SimulateRandomChange 模拟随机变化
func (g *MockDataGenerator) SimulateRandomChange() {
	g.mu.Lock()
	defer g.mu.Unlock()

	// 随机改变电量 (±5%)
	change := (g.rand.Float64() - 0.5) * 10 // -5 到 +5
	g.batteryLevel += change
	if g.batteryLevel < 0 {
		g.batteryLevel = 0
	} else if g.batteryLevel > 100 {
		g.batteryLevel = 100
	}

	// 随机改变位置 (10% 概率)
	if g.rand.Intn(10) == 0 {
		positions := []string{
			zmq.PositionParkingStr,
			zmq.PositionChargingStr,
			zmq.PositionSwitchStr,
			zmq.PositionMainRoadStr,
		}
		g.currentPosition = positions[g.rand.Intn(len(positions))]
	}

	// 随机改变任务状态 (5% 概率)
	if g.rand.Intn(20) == 0 {
		states := []int{
			zmq.RobotTaskStateNotStarted,
			zmq.RobotTaskStateInProgress,
			zmq.RobotTaskStateCompleted,
		}
		g.taskState = states[g.rand.Intn(len(states))]
	}
}

// ResetToDefault 重置为默认状态
func (g *MockDataGenerator) ResetToDefault() {
	g.mu.Lock()
	defer g.mu.Unlock()

	g.batteryLevel = 85.5
	g.currentPosition = zmq.PositionParkingStr
	g.taskId = ""
	g.currentMachine = ""
	g.robotState = zmq.RobotStateNormal
	g.taskState = zmq.RobotTaskStateNotStarted
	g.machineIndex = 0
}
