package main

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	zmqpkg "github.com/pebbe/zmq4"
	"github.com/user/agv_nav/internal/zmq"
)

// ZMQCommTester ZMQ通信测试器
type ZMQCommTester struct {
	// ZMQ配置
	robotNo       string
	schedulerAddr string
	testAddr      string

	// ZMQ连接
	requester  *zmqpkg.Socket // 发送请求的socket
	subscriber *zmqpkg.Socket // 接收调度器指令的socket

	// 模拟数据
	mockData *MockDataGenerator

	// 测试场景管理器
	scenarioManager *TestScenarioManager

	// 控制状态
	mu          sync.RWMutex
	isRunning   bool
	testResults []TestResult

	// 上下文
	ctx    context.Context
	cancel context.CancelFunc
}

// TestResult 测试结果
type TestResult struct {
	TestName     string        `json:"test_name"`
	StartTime    time.Time     `json:"start_time"`
	EndTime      time.Time     `json:"end_time"`
	Duration     time.Duration `json:"duration"`
	Success      bool          `json:"success"`
	RequestData  string        `json:"request_data"`
	ResponseData string        `json:"response_data"`
	ErrorMessage string        `json:"error_message"`
}

// NewZMQCommTester 创建ZMQ通信测试器
func NewZMQCommTester() *ZMQCommTester {
	ctx, cancel := context.WithCancel(context.Background())

	tester := &ZMQCommTester{
		robotNo:       "ROBOT_01",
		schedulerAddr: "tcp://localhost:5555", // 调度系统地址
		testAddr:      "tcp://localhost:5556", // 测试用地址
		mockData:      NewMockDataGenerator("ROBOT_01"),
		testResults:   make([]TestResult, 0),
		ctx:           ctx,
		cancel:        cancel,
	}

	// 初始化场景管理器
	tester.scenarioManager = NewTestScenarioManager(tester)

	return tester
}

// Initialize 初始化ZMQ连接
func (t *ZMQCommTester) Initialize() error {
	fmt.Println("=== 初始化ZMQ通信测试器 ===")

	// 创建请求者socket
	var err error
	t.requester, err = zmqpkg.NewSocket(zmqpkg.REQ)
	if err != nil {
		return fmt.Errorf("创建请求socket失败: %w", err)
	}

	// 创建订阅者socket
	t.subscriber, err = zmqpkg.NewSocket(zmqpkg.SUB)
	if err != nil {
		return fmt.Errorf("创建订阅socket失败: %w", err)
	}

	// 设置超时
	t.requester.SetRcvtimeo(5 * time.Second)
	t.requester.SetSndtimeo(5 * time.Second)

	fmt.Printf("请求地址: %s\n", t.schedulerAddr)
	fmt.Printf("机器人编号: %s\n", t.robotNo)
	fmt.Println("ZMQ通信测试器初始化完成")

	return nil
}

// Connect 连接到调度系统
func (t *ZMQCommTester) Connect() error {
	fmt.Println("=== 连接调度系统 ===")

	// 连接请求者
	err := t.requester.Connect(t.schedulerAddr)
	if err != nil {
		return fmt.Errorf("连接调度系统失败: %w", err)
	}

	// 设置订阅过滤（订阅所有消息）
	err = t.subscriber.SetSubscribe("")
	if err != nil {
		return fmt.Errorf("设置订阅失败: %w", err)
	}

	fmt.Println("已连接到调度系统")
	return nil
}

// Disconnect 断开连接
func (t *ZMQCommTester) Disconnect() {
	fmt.Println("=== 断开ZMQ连接 ===")

	if t.requester != nil {
		t.requester.Close()
	}
	if t.subscriber != nil {
		t.subscriber.Close()
	}

	t.cancel()
	fmt.Println("ZMQ连接已断开")
}

// TestHeartbeat 测试心跳通信 (指令0)
func (t *ZMQCommTester) TestHeartbeat() error {
	fmt.Println("\n=== 测试心跳通信 (指令0) ===")

	startTime := time.Now()
	var result TestResult
	result.TestName = "心跳通信"
	result.StartTime = startTime

	defer func() {
		result.EndTime = time.Now()
		result.Duration = result.EndTime.Sub(result.StartTime)
		t.addTestResult(result)
	}()

	// 创建心跳消息
	message := zmq.NewHeartbeatMessage(t.robotNo, true)

	// 序列化消息
	data, err := json.Marshal(message)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("序列化心跳消息失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	result.RequestData = string(data)
	fmt.Printf("发送心跳: %s\n", string(data))

	// 发送消息
	_, err = t.requester.SendBytes(data, 0)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("发送心跳失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	// 接收回复
	reply, err := t.requester.RecvBytes(0)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("接收心跳回复失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	result.ResponseData = string(reply)
	fmt.Printf("收到回复: %s\n", string(reply))

	// 解析回复
	var replyMsg zmq.Message
	err = json.Unmarshal(reply, &replyMsg)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("解析回复失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	// 验证回复
	if replyMsg.Instruction != zmq.InstructionReply {
		result.ErrorMessage = fmt.Sprintf("回复指令错误: 期望 %d, 收到 %d", zmq.InstructionReply, replyMsg.Instruction)
		return fmt.Errorf(result.ErrorMessage)
	}

	if !replyMsg.Code {
		result.ErrorMessage = "回复代码为false，表示失败"
		return fmt.Errorf(result.ErrorMessage)
	}

	result.Success = true
	fmt.Println("✅ 心跳测试成功")
	return nil
}

// TestStatusReport 测试状态上报 (指令100)
func (t *ZMQCommTester) TestStatusReport() error {
	fmt.Println("\n=== 测试状态上报 (指令100) ===")

	startTime := time.Now()
	var result TestResult
	result.TestName = "状态上报"
	result.StartTime = startTime

	defer func() {
		result.EndTime = time.Now()
		result.Duration = result.EndTime.Sub(result.StartTime)
		t.addTestResult(result)
	}()

	// 生成状态报告内容
	statusContent := t.mockData.GenerateStatusReport()

	// 创建状态上报消息
	message := &zmq.Message{
		Instruction: zmq.InstructionStatusReport,
		TimeStamp:   time.Now(),
		Code:        true,
		Message:     "机器人状态正常",
		Content:     statusContent,
	}

	// 序列化消息
	data, err := json.Marshal(message)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("序列化状态报告失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	result.RequestData = string(data)
	fmt.Printf("发送状态报告: %s\n", string(data))

	// 发送消息
	_, err = t.requester.SendBytes(data, 0)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("发送状态报告失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	// 接收回复
	reply, err := t.requester.RecvBytes(0)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("接收状态报告回复失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	result.ResponseData = string(reply)
	fmt.Printf("收到回复: %s\n", string(reply))

	// 解析回复
	var replyMsg zmq.Message
	err = json.Unmarshal(reply, &replyMsg)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("解析回复失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	// 验证回复
	if replyMsg.Instruction != zmq.InstructionReply {
		result.ErrorMessage = fmt.Sprintf("回复指令错误: 期望 %d, 收到 %d", zmq.InstructionReply, replyMsg.Instruction)
		return fmt.Errorf(result.ErrorMessage)
	}

	if !replyMsg.Code {
		result.ErrorMessage = "回复代码为false，表示失败"
		return fmt.Errorf(result.ErrorMessage)
	}

	result.Success = true
	fmt.Println("✅ 状态上报测试成功")
	return nil
}

// TestTaskStart 测试任务开始通知 (指令110)
func (t *ZMQCommTester) TestTaskStart() error {
	fmt.Println("\n=== 测试任务开始通知 (指令110) ===")

	startTime := time.Now()
	var result TestResult
	result.TestName = "任务开始通知"
	result.StartTime = startTime

	defer func() {
		result.EndTime = time.Now()
		result.Duration = result.EndTime.Sub(result.StartTime)
		t.addTestResult(result)
	}()

	// 生成任务信息
	taskId := t.mockData.GenerateTaskId()
	remark := fmt.Sprintf("开始处理机器: %s", t.mockData.GetCurrentMachine())

	// 创建任务开始消息
	message := zmq.NewTaskStartMessage(t.robotNo, taskId, remark)

	// 序列化消息
	data, err := json.Marshal(message)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("序列化任务开始消息失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	result.RequestData = string(data)
	fmt.Printf("发送任务开始通知: %s\n", string(data))

	// 发送消息
	_, err = t.requester.SendBytes(data, 0)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("发送任务开始通知失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	// 接收回复
	reply, err := t.requester.RecvBytes(0)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("接收任务开始回复失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	result.ResponseData = string(reply)
	fmt.Printf("收到回复: %s\n", string(reply))

	// 解析回复
	var replyMsg zmq.Message
	err = json.Unmarshal(reply, &replyMsg)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("解析回复失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	// 验证回复
	if replyMsg.Instruction != zmq.InstructionReply {
		result.ErrorMessage = fmt.Sprintf("回复指令错误: 期望 %d, 收到 %d", zmq.InstructionReply, replyMsg.Instruction)
		return fmt.Errorf(result.ErrorMessage)
	}

	if !replyMsg.Code {
		result.ErrorMessage = "回复代码为false，表示失败"
		return fmt.Errorf(result.ErrorMessage)
	}

	result.Success = true
	fmt.Println("✅ 任务开始通知测试成功")
	return nil
}

// TestTaskComplete 测试任务完成通知 (指令130)
func (t *ZMQCommTester) TestTaskComplete() error {
	fmt.Println("\n=== 测试任务完成通知 (指令130) ===")

	startTime := time.Now()
	var result TestResult
	result.TestName = "任务完成通知"
	result.StartTime = startTime

	defer func() {
		result.EndTime = time.Now()
		result.Duration = result.EndTime.Sub(result.StartTime)
		t.addTestResult(result)
	}()

	// 生成任务信息
	taskId := t.mockData.GenerateTaskId()
	remark := fmt.Sprintf("完成处理机器: %s", t.mockData.GetCurrentMachine())

	// 创建任务完成消息
	message := zmq.NewTaskCompleteMessage(t.robotNo, taskId, remark)

	// 序列化消息
	data, err := json.Marshal(message)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("序列化任务完成消息失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	result.RequestData = string(data)
	fmt.Printf("发送任务完成通知: %s\n", string(data))

	// 发送消息
	_, err = t.requester.SendBytes(data, 0)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("发送任务完成通知失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	// 接收回复
	reply, err := t.requester.RecvBytes(0)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("接收任务完成回复失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	result.ResponseData = string(reply)
	fmt.Printf("收到回复: %s\n", string(reply))

	// 解析回复
	var replyMsg zmq.Message
	err = json.Unmarshal(reply, &replyMsg)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("解析回复失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	// 验证回复
	if replyMsg.Instruction != zmq.InstructionReply {
		result.ErrorMessage = fmt.Sprintf("回复指令错误: 期望 %d, 收到 %d", zmq.InstructionReply, replyMsg.Instruction)
		return fmt.Errorf(result.ErrorMessage)
	}

	if !replyMsg.Code {
		result.ErrorMessage = "回复代码为false，表示失败"
		return fmt.Errorf(result.ErrorMessage)
	}

	result.Success = true
	fmt.Println("✅ 任务完成通知测试成功")
	return nil
}

// TestTaskPause 测试任务暂停通知 (指令120)
func (t *ZMQCommTester) TestTaskPause() error {
	fmt.Println("\n=== 测试任务暂停通知 (指令120) ===")

	startTime := time.Now()
	var result TestResult
	result.TestName = "任务暂停通知"
	result.StartTime = startTime

	defer func() {
		result.EndTime = time.Now()
		result.Duration = result.EndTime.Sub(result.StartTime)
		t.addTestResult(result)
	}()

	// 生成任务信息
	taskId := t.mockData.GenerateTaskId()
	remark := fmt.Sprintf("暂停处理机器: %s", t.mockData.GetCurrentMachine())

	// 创建任务暂停消息
	message := zmq.NewTaskPauseMessage(t.robotNo, taskId, remark)

	// 序列化消息
	data, err := json.Marshal(message)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("序列化任务暂停消息失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	result.RequestData = string(data)
	fmt.Printf("发送任务暂停通知: %s\n", string(data))

	// 发送消息
	_, err = t.requester.SendBytes(data, 0)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("发送任务暂停通知失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	// 接收回复
	reply, err := t.requester.RecvBytes(0)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("接收任务暂停回复失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	result.ResponseData = string(reply)
	fmt.Printf("收到回复: %s\n", string(reply))

	// 解析回复
	var replyMsg zmq.Message
	err = json.Unmarshal(reply, &replyMsg)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("解析回复失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	// 验证回复
	if replyMsg.Instruction != zmq.InstructionReply {
		result.ErrorMessage = fmt.Sprintf("回复指令错误: 期望 %d, 收到 %d", zmq.InstructionReply, replyMsg.Instruction)
		return fmt.Errorf(result.ErrorMessage)
	}

	if !replyMsg.Code {
		result.ErrorMessage = "回复代码为false，表示失败"
		return fmt.Errorf(result.ErrorMessage)
	}

	result.Success = true
	fmt.Println("✅ 任务暂停通知测试成功")
	return nil
}

// TestTaskForceStop 测试任务强制结束通知 (指令131)
func (t *ZMQCommTester) TestTaskForceStop() error {
	fmt.Println("\n=== 测试任务强制结束通知 (指令131) ===")

	startTime := time.Now()
	var result TestResult
	result.TestName = "任务强制结束通知"
	result.StartTime = startTime

	defer func() {
		result.EndTime = time.Now()
		result.Duration = result.EndTime.Sub(result.StartTime)
		t.addTestResult(result)
	}()

	// 生成任务信息
	taskId := t.mockData.GenerateTaskId()
	remark := fmt.Sprintf("强制结束处理机器: %s", t.mockData.GetCurrentMachine())

	// 创建任务强制结束消息
	message := zmq.NewTaskForceStopMessage(t.robotNo, taskId, remark)

	// 序列化消息
	data, err := json.Marshal(message)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("序列化任务强制结束消息失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	result.RequestData = string(data)
	fmt.Printf("发送任务强制结束通知: %s\n", string(data))

	// 发送消息
	_, err = t.requester.SendBytes(data, 0)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("发送任务强制结束通知失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	// 接收回复
	reply, err := t.requester.RecvBytes(0)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("接收任务强制结束回复失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	result.ResponseData = string(reply)
	fmt.Printf("收到回复: %s\n", string(reply))

	// 解析回复
	var replyMsg zmq.Message
	err = json.Unmarshal(reply, &replyMsg)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("解析回复失败: %v", err)
		return fmt.Errorf(result.ErrorMessage)
	}

	// 验证回复
	if replyMsg.Instruction != zmq.InstructionReply {
		result.ErrorMessage = fmt.Sprintf("回复指令错误: 期望 %d, 收到 %d", zmq.InstructionReply, replyMsg.Instruction)
		return fmt.Errorf(result.ErrorMessage)
	}

	if !replyMsg.Code {
		result.ErrorMessage = "回复代码为false，表示失败"
		return fmt.Errorf(result.ErrorMessage)
	}

	result.Success = true
	fmt.Println("✅ 任务强制结束通知测试成功")
	return nil
}

// addTestResult 添加测试结果
func (t *ZMQCommTester) addTestResult(result TestResult) {
	t.mu.Lock()
	defer t.mu.Unlock()
	t.testResults = append(t.testResults, result)
}

// GetTestResults 获取测试结果
func (t *ZMQCommTester) GetTestResults() []TestResult {
	t.mu.RLock()
	defer t.mu.RUnlock()

	results := make([]TestResult, len(t.testResults))
	copy(results, t.testResults)
	return results
}

// ClearTestResults 清除测试结果
func (t *ZMQCommTester) ClearTestResults() {
	t.mu.Lock()
	defer t.mu.Unlock()
	t.testResults = nil
}

// PrintTestResults 打印测试结果统计
func (t *ZMQCommTester) PrintTestResults() {
	results := t.GetTestResults()
	if len(results) == 0 {
		fmt.Println("暂无测试结果")
		return
	}

	fmt.Println("\n=== 测试结果统计 ===")
	successCount := 0
	totalDuration := time.Duration(0)

	for i, result := range results {
		status := "❌ 失败"
		if result.Success {
			status = "✅ 成功"
			successCount++
		}

		fmt.Printf("%d. %s - %s (耗时: %v)\n",
			i+1, result.TestName, status, result.Duration)

		if !result.Success && result.ErrorMessage != "" {
			fmt.Printf("   错误: %s\n", result.ErrorMessage)
		}

		totalDuration += result.Duration
	}

	fmt.Printf("\n总计: %d个测试, %d个成功, %d个失败\n",
		len(results), successCount, len(results)-successCount)
	fmt.Printf("总耗时: %v\n", totalDuration)
	fmt.Printf("成功率: %.1f%%\n", float64(successCount)/float64(len(results))*100)
}

// TestSchedulerCommandReceive 测试接收调度器指令
func (t *ZMQCommTester) TestSchedulerCommandReceive() error {
	fmt.Println("\n=== 测试接收调度器指令 ===")
	fmt.Println("此功能模拟接收调度器发送的指令 (200-203)")
	fmt.Println("请选择要模拟的指令:")
	fmt.Println("1. 指令200 - 任务下发")
	fmt.Println("2. 指令201 - 无任务 (返回停车点)")
	fmt.Println("3. 指令202 - 等待任务")
	fmt.Println("4. 指令203 - 继续任务")
	fmt.Print("请选择 (1-4): ")

	scanner := bufio.NewScanner(os.Stdin)
	if !scanner.Scan() {
		return fmt.Errorf("读取输入失败")
	}

	choice := strings.TrimSpace(scanner.Text())

	switch choice {
	case "1":
		return t.simulateTaskAssignment()
	case "2":
		return t.simulateNoTask()
	case "3":
		return t.simulateWaitTask()
	case "4":
		return t.simulateContinueTask()
	default:
		return fmt.Errorf("无效选择")
	}
}

// simulateTaskAssignment 模拟任务下发指令 (200)
func (t *ZMQCommTester) simulateTaskAssignment() error {
	fmt.Println("\n--- 模拟接收任务下发指令 (200) ---")

	// 创建任务下发消息内容
	content := map[string]interface{}{
		"LaneNos": []string{"61L", "60R"},
		"remark":  "新任务分配",
	}

	// 创建调度器消息
	message := &zmq.Message{
		Instruction: zmq.InstructionTaskAssignment, // 200
		TimeStamp:   time.Now(),
		Code:        true,
		Message:     "任务下发",
		Content:     content,
	}

	// 序列化消息
	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("序列化任务下发消息失败: %w", err)
	}

	fmt.Printf("模拟接收到调度器消息: %s\n", string(data))

	// 模拟处理逻辑
	t.mockData.StartNewTask()
	fmt.Println("✅ 任务下发处理完成，机器人开始执行新任务")

	// 发送确认回复
	return t.sendAcknowledgmentReply("任务下发已接收并开始执行")
}

// simulateNoTask 模拟无任务指令 (201)
func (t *ZMQCommTester) simulateNoTask() error {
	fmt.Println("\n--- 模拟接收无任务指令 (201) ---")

	// 创建无任务消息内容
	content := map[string]interface{}{
		"remark": "无新任务，返回停车点",
	}

	// 创建调度器消息
	message := &zmq.Message{
		Instruction: zmq.InstructionNoTask, // 201
		TimeStamp:   time.Now(),
		Code:        true,
		Message:     "无任务",
		Content:     content,
	}

	// 序列化消息
	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("序列化无任务消息失败: %w", err)
	}

	fmt.Printf("模拟接收到调度器消息: %s\n", string(data))

	// 模拟处理逻辑
	t.mockData.SetPosition(zmq.PositionMainRoadStr) // 导航到停车点过程中
	fmt.Println("✅ 无任务指令处理完成，机器人导航到停车点")

	// 模拟到达停车点
	time.Sleep(1 * time.Second)
	t.mockData.SetPosition(zmq.PositionParkingStr)
	fmt.Println("✅ 机器人已到达停车点")

	// 发送确认回复
	return t.sendAcknowledgmentReply("无任务指令已接收，正在返回停车点")
}

// simulateWaitTask 模拟等待任务指令 (202)
func (t *ZMQCommTester) simulateWaitTask() error {
	fmt.Println("\n--- 模拟接收等待任务指令 (202) ---")

	// 创建等待任务消息内容
	content := map[string]interface{}{
		"remark": "当前位置等待新任务",
	}

	// 创建调度器消息
	message := &zmq.Message{
		Instruction: zmq.InstructionWaitTask, // 202
		TimeStamp:   time.Now(),
		Code:        true,
		Message:     "等待任务",
		Content:     content,
	}

	// 序列化消息
	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("序列化等待任务消息失败: %w", err)
	}

	fmt.Printf("模拟接收到调度器消息: %s\n", string(data))

	// 模拟处理逻辑
	t.mockData.PauseCurrentTask()
	fmt.Println("✅ 等待任务指令处理完成，机器人在当前位置等待")

	// 发送确认回复
	return t.sendAcknowledgmentReply("等待任务指令已接收，在当前位置等待")
}

// simulateContinueTask 模拟继续任务指令 (203)
func (t *ZMQCommTester) simulateContinueTask() error {
	fmt.Println("\n--- 模拟接收继续任务指令 (203) ---")
	fmt.Println("继续任务指令有两种模式:")
	fmt.Println("1. 恢复之前暂停的任务")
	fmt.Println("2. 分配新任务")
	fmt.Print("请选择模式 (1-2): ")

	scanner := bufio.NewScanner(os.Stdin)
	if !scanner.Scan() {
		return fmt.Errorf("读取输入失败")
	}

	choice := strings.TrimSpace(scanner.Text())

	var content map[string]interface{}
	var remark string

	switch choice {
	case "1":
		// 恢复暂停的任务
		content = map[string]interface{}{
			"remark": "恢复之前暂停的任务",
		}
		remark = "恢复暂停任务"

	case "2":
		// 分配新任务
		content = map[string]interface{}{
			"LaneNos": []string{"59L"},
			"remark":  "继续执行新任务",
		}
		remark = "分配新任务"

	default:
		return fmt.Errorf("无效选择")
	}

	// 创建继续任务消息
	message := &zmq.Message{
		Instruction: zmq.InstructionContinueTask, // 203
		TimeStamp:   time.Now(),
		Code:        true,
		Message:     "继续任务",
		Content:     content,
	}

	// 序列化消息
	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("序列化继续任务消息失败: %w", err)
	}

	fmt.Printf("模拟接收到调度器消息: %s\n", string(data))

	// 模拟处理逻辑
	if choice == "1" {
		t.mockData.ResumeCurrentTask()
		fmt.Println("✅ 继续任务指令处理完成，机器人恢复之前的任务")
	} else {
		t.mockData.StartNewTask()
		fmt.Println("✅ 继续任务指令处理完成，机器人开始执行新任务")
	}

	// 发送确认回复
	return t.sendAcknowledgmentReply(fmt.Sprintf("继续任务指令已接收，%s", remark))
}

// sendAcknowledgmentReply 发送确认回复
func (t *ZMQCommTester) sendAcknowledgmentReply(message string) error {
	fmt.Printf("发送确认回复: %s\n", message)

	// 在真实环境中，这里会通过ZMQ发送回复
	// 由于这是模拟测试，我们只是打印日志
	fmt.Println("✅ 确认回复已发送")

	return nil
}

// TestContinuousStatusReporting 测试连续状态上报
func (t *ZMQCommTester) TestContinuousStatusReporting() {
	fmt.Println("\n=== 测试连续状态上报 ===")
	fmt.Print("请输入上报次数 (1-10): ")

	scanner := bufio.NewScanner(os.Stdin)
	if !scanner.Scan() {
		fmt.Println("读取输入失败")
		return
	}

	countStr := strings.TrimSpace(scanner.Text())
	count, err := strconv.Atoi(countStr)
	if err != nil || count < 1 || count > 10 {
		fmt.Println("无效的次数，使用默认值3次")
		count = 3
	}

	fmt.Printf("开始连续状态上报，共%d次，间隔1秒\n", count)

	for i := 1; i <= count; i++ {
		fmt.Printf("\n--- 第 %d 次状态上报 ---\n", i)

		// 模拟随机变化
		t.mockData.SimulateRandomChange()

		// 执行状态上报测试
		err := t.TestStatusReport()
		if err != nil {
			fmt.Printf("第 %d 次状态上报失败: %v\n", i, err)
		}

		// 如果不是最后一次，等待间隔
		if i < count {
			time.Sleep(1 * time.Second)
		}
	}

	fmt.Printf("\n连续状态上报测试完成，共执行 %d 次\n", count)
}

// RunInteractiveMenu 运行交互式菜单
func (t *ZMQCommTester) RunInteractiveMenu() {
	scanner := bufio.NewScanner(os.Stdin)

	for {
		fmt.Println("\n=== ZMQ通信测试器 ===")
		fmt.Println("【发送测试】")
		fmt.Println("1. 测试心跳通信 (指令0)")
		fmt.Println("2. 测试状态上报 (指令100)")
		fmt.Println("3. 测试任务开始通知 (指令110)")
		fmt.Println("4. 测试任务完成通知 (指令130)")
		fmt.Println("5. 测试任务暂停通知 (指令120)")
		fmt.Println("6. 测试任务强制结束通知 (指令131)")
		fmt.Println("【接收测试】")
		fmt.Println("7. 测试接收调度器指令 (指令200-203)")
		fmt.Println("【批量测试】")
		fmt.Println("8. 运行全部发送测试")
		fmt.Println("9. 测试连续状态上报")
		fmt.Println("【场景测试】")
		fmt.Println("10. 列出测试场景")
		fmt.Println("11. 执行指定场景")
		fmt.Println("12. 执行所有场景")
		fmt.Println("【结果和配置】")
		fmt.Println("13. 显示测试结果")
		fmt.Println("14. 清除测试结果")
		fmt.Println("15. 显示模拟数据状态")
		fmt.Println("16. 重置模拟数据")
		fmt.Println("17. 设置配置")
		fmt.Println("0. 退出")
		fmt.Print("请选择操作 (0-17): ")

		if !scanner.Scan() {
			break
		}

		choice := strings.TrimSpace(scanner.Text())

		switch choice {
		case "1":
			t.TestHeartbeat()
		case "2":
			t.TestStatusReport()
		case "3":
			t.TestTaskStart()
		case "4":
			t.TestTaskComplete()
		case "5":
			t.TestTaskPause()
		case "6":
			t.TestTaskForceStop()
		case "7":
			t.TestSchedulerCommandReceive()
		case "8":
			t.RunAllTests()
		case "9":
			t.TestContinuousStatusReporting()
		case "10":
			t.scenarioManager.ListScenarios()
		case "11":
			t.ExecuteSpecificScenario(scanner)
		case "12":
			t.scenarioManager.ExecuteAllScenarios()
		case "13":
			t.PrintTestResults()
		case "14":
			t.ClearTestResults()
			fmt.Println("测试结果已清除")
		case "15":
			t.mockData.PrintStatus()
		case "16":
			t.mockData.ResetToDefault()
			fmt.Println("模拟数据已重置为默认状态")
		case "17":
			t.ConfigureSettings(scanner)
		case "0":
			fmt.Println("退出测试器")
			return
		default:
			fmt.Println("无效选择，请重新输入")
		}
	}
}

// RunAllTests 运行所有测试
func (t *ZMQCommTester) RunAllTests() {
	fmt.Println("\n=== 运行全部测试 ===")

	tests := []func() error{
		t.TestHeartbeat,
		t.TestStatusReport,
		t.TestTaskStart,
		t.TestTaskComplete,
		t.TestTaskPause,
		t.TestTaskForceStop,
	}

	for _, test := range tests {
		test()
		time.Sleep(100 * time.Millisecond) // 短暂间隔
	}

	fmt.Println("\n=== 全部测试完成 ===")
	t.PrintTestResults()
}

// ExecuteSpecificScenario 执行指定场景
func (t *ZMQCommTester) ExecuteSpecificScenario(scanner *bufio.Scanner) {
	fmt.Println("\n=== 选择要执行的场景 ===")
	scenarios := t.scenarioManager.GetScenarios()

	for i, scenario := range scenarios {
		fmt.Printf("%d. %s - %s\n", i+1, scenario.Name, scenario.Description)
	}

	fmt.Print("请选择场景编号 (1-" + fmt.Sprintf("%d", len(scenarios)) + "): ")

	if !scanner.Scan() {
		fmt.Println("读取输入失败")
		return
	}

	choiceStr := strings.TrimSpace(scanner.Text())
	choice, err := strconv.Atoi(choiceStr)
	if err != nil || choice < 1 || choice > len(scenarios) {
		fmt.Println("无效的选择")
		return
	}

	selectedScenario := scenarios[choice-1]
	fmt.Printf("正在执行场景: %s\n", selectedScenario.Name)

	err = t.scenarioManager.ExecuteScenario(selectedScenario.ID)
	if err != nil {
		fmt.Printf("场景执行失败: %v\n", err)
	}
}

// ConfigureSettings 配置设置
func (t *ZMQCommTester) ConfigureSettings(scanner *bufio.Scanner) {
	fmt.Println("\n=== 配置设置 ===")
	fmt.Printf("当前机器人编号: %s\n", t.robotNo)
	fmt.Printf("当前调度器地址: %s\n", t.schedulerAddr)

	fmt.Print("输入新的机器人编号 (回车保持不变): ")
	if scanner.Scan() {
		input := strings.TrimSpace(scanner.Text())
		if input != "" {
			t.robotNo = input
			t.mockData.robotNo = input
			fmt.Printf("机器人编号已更新为: %s\n", t.robotNo)
		}
	}

	fmt.Print("输入新的调度器地址 (回车保持不变): ")
	if scanner.Scan() {
		input := strings.TrimSpace(scanner.Text())
		if input != "" {
			t.schedulerAddr = input
			fmt.Printf("调度器地址已更新为: %s\n", t.schedulerAddr)
		}
	}
}

// main 主函数
func main() {
	fmt.Println("启动ZMQ通信测试器...")

	tester := NewZMQCommTester()

	// 初始化
	err := tester.Initialize()
	if err != nil {
		fmt.Printf("初始化失败: %v\n", err)
		return
	}
	defer tester.Disconnect()

	// 连接
	err = tester.Connect()
	if err != nil {
		fmt.Printf("连接失败: %v\n", err)
		fmt.Println("提示: 请确保调度系统正在运行并监听指定地址")
		fmt.Println("你可以使用简单的ZMQ服务器来测试，或修改连接地址")
	}

	// 运行交互式菜单
	tester.RunInteractiveMenu()
}
