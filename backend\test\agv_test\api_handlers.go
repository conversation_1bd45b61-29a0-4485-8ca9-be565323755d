package agv_test

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"sync"
	"time"

	"github.com/gorilla/mux"
	"github.com/gorilla/websocket"
	"github.com/user/agv_nav/pkg/agv"
)

// AGVTestAPIHandler AGV测试API处理器
type AGVTestAPIHandler struct {
	mu              sync.RWMutex
	testController  *AGVTestController
	navigator       *AGVNavigator
	monitor         *AGVMonitor
	workflowManager *WorkflowManager

	// WebSocket连接管理
	wsUpgrader      websocket.Upgrader
	wsConnections   map[*websocket.Conn]bool
	wsConnectionsMu sync.RWMutex

	// 消息广播
	broadcast chan []byte

	// 状态
	isInitialized bool
}

// APIResponse API响应结构
type APIResponse struct {
	Success   bool        `json:"success"`
	Message   string      `json:"message,omitempty"`
	Data      interface{} `json:"data,omitempty"`
	Error     string      `json:"error,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
}

// WebSocketMessage WebSocket消息结构
type WebSocketMessage struct {
	Type      string      `json:"type"`
	Data      interface{} `json:"data,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
}

// NewAGVTestAPIHandler 创建AGV测试API处理器
func NewAGVTestAPIHandler(agvController *agv.Controller) (*AGVTestAPIHandler, error) {
	// 创建测试控制器（复用主程序的AGV控制器）
	testController, err := NewAGVTestController(agvController)
	if err != nil {
		return nil, fmt.Errorf("创建AGV测试控制器失败: %v", err)
	}

	// 创建导航器
	navigator := NewAGVNavigator(testController)

	// 创建监控器
	monitor := NewAGVMonitor(testController)

	// 创建工作流程管理器
	workflowManager := NewWorkflowManager(testController, navigator, monitor)

	// 创建API处理器
	handler := &AGVTestAPIHandler{
		testController:  testController,
		navigator:       navigator,
		monitor:         monitor,
		workflowManager: workflowManager,
		wsUpgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // 允许所有来源（生产环境需要限制）
			},
		},
		wsConnections: make(map[*websocket.Conn]bool),
		broadcast:     make(chan []byte, 256),
		isInitialized: false,
	}

	// 设置回调
	handler.setupCallbacks()

	// 启动WebSocket广播协程
	go handler.handleWebSocketBroadcast()

	handler.isInitialized = true
	log.Printf("AGV测试API处理器初始化完成")

	return handler, nil
}

// RegisterRoutes 注册路由
func (h *AGVTestAPIHandler) RegisterRoutes(r *mux.Router) {
	// 创建子路由
	agvTestRouter := r.PathPrefix("/agv-test").Subrouter()

	// 连接管理
	agvTestRouter.HandleFunc("/connect", h.handleConnect).Methods("POST", "OPTIONS")
	agvTestRouter.HandleFunc("/disconnect", h.handleDisconnect).Methods("POST", "OPTIONS")
	agvTestRouter.HandleFunc("/status", h.handleGetStatus).Methods("GET", "OPTIONS")

	// 配置管理
	agvTestRouter.HandleFunc("/config", h.handleGetConfig).Methods("GET", "OPTIONS")
	agvTestRouter.HandleFunc("/config", h.handleUpdateConfig).Methods("PUT", "OPTIONS")

	// 导航控制
	agvTestRouter.HandleFunc("/navigate/point", h.handleNavigateToPoint).Methods("POST", "OPTIONS")
	agvTestRouter.HandleFunc("/navigate/machine", h.handleNavigateToMachine).Methods("POST", "OPTIONS")
	agvTestRouter.HandleFunc("/navigate/query-point", h.handleQueryNavigationPoint).Methods("POST", "OPTIONS")

	// 模式控制
	agvTestRouter.HandleFunc("/mode/switch", h.handleSwitchMode).Methods("POST", "OPTIONS")
	agvTestRouter.HandleFunc("/mode/laser-init", h.handleLaserInit).Methods("POST", "OPTIONS")

	// 监控管理
	agvTestRouter.HandleFunc("/monitor/start", h.handleStartMonitoring).Methods("POST", "OPTIONS")
	agvTestRouter.HandleFunc("/monitor/stop", h.handleStopMonitoring).Methods("POST", "OPTIONS")
	agvTestRouter.HandleFunc("/monitor/history", h.handleGetMonitorHistory).Methods("GET", "OPTIONS")
	agvTestRouter.HandleFunc("/monitor/events", h.handleGetConnectionEvents).Methods("GET", "OPTIONS")
	agvTestRouter.HandleFunc("/monitor/stats", h.handleGetMonitorStats).Methods("GET", "OPTIONS")
	agvTestRouter.HandleFunc("/monitor/clear", h.handleClearMonitorHistory).Methods("POST", "OPTIONS")

	// 工作流程管理
	agvTestRouter.HandleFunc("/workflow/templates", h.handleGetWorkflowTemplates).Methods("GET", "OPTIONS")
	agvTestRouter.HandleFunc("/workflow/start", h.handleStartWorkflow).Methods("POST", "OPTIONS")
	agvTestRouter.HandleFunc("/workflow/stop", h.handleStopWorkflow).Methods("POST", "OPTIONS")
	agvTestRouter.HandleFunc("/workflow/current", h.handleGetCurrentWorkflow).Methods("GET", "OPTIONS")
	agvTestRouter.HandleFunc("/workflow/history", h.handleGetWorkflowHistory).Methods("GET", "OPTIONS")

	// 测试结果管理
	agvTestRouter.HandleFunc("/results", h.handleGetTestResults).Methods("GET", "OPTIONS")
	agvTestRouter.HandleFunc("/results/clear", h.handleClearTestResults).Methods("POST", "OPTIONS")

	// WebSocket连接
	agvTestRouter.HandleFunc("/ws", h.handleWebSocket)

	log.Printf("AGV测试API路由注册完成")
}

// setupCallbacks 设置回调函数
func (h *AGVTestAPIHandler) setupCallbacks() {
	// 监控状态更新回调
	h.monitor.SetStatusUpdateCallback(func(status *StatusUpdate) {
		h.broadcastWebSocketMessage("agv_status_update", status)
	})

	// 监控连接变化回调
	h.monitor.SetConnectionChangeCallback(func(event *ConnectionEvent) {
		h.broadcastWebSocketMessage("agv_connection_change", event)
	})

	// 工作流程更新回调
	h.workflowManager.SetWorkflowUpdateCallback(func(execution *WorkflowExecution) {
		h.broadcastWebSocketMessage("workflow_update", execution)
	})

	// 工作流程步骤更新回调
	h.workflowManager.SetStepUpdateCallback(func(step *WorkflowStepExecution) {
		h.broadcastWebSocketMessage("workflow_step_update", step)
	})
}

// 连接管理处理器
func (h *AGVTestAPIHandler) handleConnect(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	err := h.testController.Connect()
	if err != nil {
		h.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	h.respondSuccess(w, "AGV连接成功", nil)
}

func (h *AGVTestAPIHandler) handleDisconnect(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	h.testController.Disconnect()
	h.respondSuccess(w, "AGV连接已断开", nil)
}

func (h *AGVTestAPIHandler) handleGetStatus(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	status := h.testController.GetStatus()
	connected := h.testController.IsConnected()

	data := map[string]interface{}{
		"connected": connected,
		"status":    status,
	}

	h.respondSuccess(w, "状态查询成功", data)
}

// 配置管理处理器
func (h *AGVTestAPIHandler) handleGetConfig(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	config := h.testController.GetConfig()
	h.respondSuccess(w, "配置获取成功", config)
}

func (h *AGVTestAPIHandler) handleUpdateConfig(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	var config AGVTestConfig
	if err := json.NewDecoder(r.Body).Decode(&config); err != nil {
		h.respondError(w, "请求数据格式错误", http.StatusBadRequest)
		return
	}

	err := h.testController.UpdateConfig(&config)
	if err != nil {
		h.respondError(w, err.Error(), http.StatusBadRequest)
		return
	}

	h.respondSuccess(w, "配置更新成功", nil)
}

// 导航控制处理器
func (h *AGVTestAPIHandler) handleNavigateToPoint(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	var req struct {
		PointID int `json:"point_id"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.respondError(w, "请求数据格式错误", http.StatusBadRequest)
		return
	}

	result, err := h.navigator.NavigateToPoint(req.PointID)
	if err != nil {
		h.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	h.respondSuccess(w, "导航命令执行完成", result)
}

func (h *AGVTestAPIHandler) handleNavigateToMachine(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	var req struct {
		Machine string `json:"machine"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.respondError(w, "请求数据格式错误", http.StatusBadRequest)
		return
	}

	result, err := h.navigator.NavigateToMachine(req.Machine)
	if err != nil {
		h.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	h.respondSuccess(w, "机器导航执行完成", result)
}

func (h *AGVTestAPIHandler) handleQueryNavigationPoint(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	var req struct {
		Machine string `json:"machine"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.respondError(w, "请求数据格式错误", http.StatusBadRequest)
		return
	}

	navPoint, err := h.testController.QueryNavigationPoint(req.Machine)
	if err != nil {
		h.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	data := map[string]interface{}{
		"machine":          req.Machine,
		"navigation_point": navPoint,
	}

	h.respondSuccess(w, "导航点查询成功", data)
}

// 模式控制处理器
func (h *AGVTestAPIHandler) handleSwitchMode(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	var req struct {
		IsAuto bool `json:"is_auto"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.respondError(w, "请求数据格式错误", http.StatusBadRequest)
		return
	}

	result, err := h.navigator.SwitchWorkMode(req.IsAuto)
	if err != nil {
		h.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	h.respondSuccess(w, "模式切换完成", result)
}

func (h *AGVTestAPIHandler) handleLaserInit(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	var req struct {
		X     float64 `json:"x"`
		Y     float64 `json:"y"`
		Angle float64 `json:"angle"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.respondError(w, "请求数据格式错误", http.StatusBadRequest)
		return
	}

	result, err := h.navigator.LaserNavInit(req.X, req.Y, req.Angle)
	if err != nil {
		h.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	h.respondSuccess(w, "激光导航初始化完成", result)
}

// 监控管理处理器
func (h *AGVTestAPIHandler) handleStartMonitoring(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	var req struct {
		IntervalMs int `json:"interval_ms,omitempty"`
	}

	json.NewDecoder(r.Body).Decode(&req)

	if req.IntervalMs > 0 {
		h.monitor.SetMonitorInterval(time.Duration(req.IntervalMs) * time.Millisecond)
	}

	err := h.monitor.StartMonitoring()
	if err != nil {
		h.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	h.respondSuccess(w, "监控已启动", nil)
}

func (h *AGVTestAPIHandler) handleStopMonitoring(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	h.monitor.StopMonitoring()
	h.respondSuccess(w, "监控已停止", nil)
}

func (h *AGVTestAPIHandler) handleGetMonitorHistory(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	limitStr := r.URL.Query().Get("limit")
	limit := 50 // 默认50条
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	history := h.monitor.GetStatusHistory(limit)
	h.respondSuccess(w, "监控历史获取成功", history)
}

func (h *AGVTestAPIHandler) handleGetConnectionEvents(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	limitStr := r.URL.Query().Get("limit")
	limit := 20 // 默认20条
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	events := h.monitor.GetConnectionEvents(limit)
	h.respondSuccess(w, "连接事件获取成功", events)
}

func (h *AGVTestAPIHandler) handleGetMonitorStats(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	stats := h.monitor.GetMonitorStats()
	h.respondSuccess(w, "监控统计获取成功", stats)
}

func (h *AGVTestAPIHandler) handleClearMonitorHistory(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	h.monitor.ClearHistory()
	h.respondSuccess(w, "监控历史已清空", nil)
}

// 工作流程管理处理器
func (h *AGVTestAPIHandler) handleGetWorkflowTemplates(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	templates := h.workflowManager.GetPredefinedWorkflows()
	h.respondSuccess(w, "工作流程模板获取成功", templates)
}

func (h *AGVTestAPIHandler) handleStartWorkflow(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	var req struct {
		TemplateName string                 `json:"template_name"`
		Config       map[string]interface{} `json:"config,omitempty"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.respondError(w, "请求数据格式错误", http.StatusBadRequest)
		return
	}

	// 查找模板
	templates := h.workflowManager.GetPredefinedWorkflows()
	var selectedTemplate *WorkflowTemplate
	for _, template := range templates {
		if template.Name == req.TemplateName {
			selectedTemplate = &template
			break
		}
	}

	if selectedTemplate == nil {
		h.respondError(w, "未找到指定的工作流程模板", http.StatusBadRequest)
		return
	}

	workflowID, err := h.workflowManager.StartWorkflow(*selectedTemplate, req.Config)
	if err != nil {
		h.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	data := map[string]interface{}{
		"workflow_id": workflowID,
		"template":    selectedTemplate.Name,
	}

	h.respondSuccess(w, "工作流程已启动", data)
}

func (h *AGVTestAPIHandler) handleStopWorkflow(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	err := h.workflowManager.StopWorkflow()
	if err != nil {
		h.respondError(w, err.Error(), http.StatusBadRequest)
		return
	}

	h.respondSuccess(w, "工作流程已停止", nil)
}

func (h *AGVTestAPIHandler) handleGetCurrentWorkflow(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	workflow := h.workflowManager.GetCurrentWorkflow()
	h.respondSuccess(w, "当前工作流程获取成功", workflow)
}

func (h *AGVTestAPIHandler) handleGetWorkflowHistory(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	limitStr := r.URL.Query().Get("limit")
	limit := 10 // 默认10条
	if limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 {
			limit = l
		}
	}

	history := h.workflowManager.GetWorkflowHistory(limit)
	h.respondSuccess(w, "工作流程历史获取成功", history)
}

// 测试结果管理处理器
func (h *AGVTestAPIHandler) handleGetTestResults(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	results := h.testController.GetTestResults()
	h.respondSuccess(w, "测试结果获取成功", results)
}

func (h *AGVTestAPIHandler) handleClearTestResults(w http.ResponseWriter, r *http.Request) {
	h.enableCORS(w)
	if r.Method == "OPTIONS" {
		return
	}

	h.testController.ClearTestResults()
	h.respondSuccess(w, "测试结果已清空", nil)
}

// WebSocket处理器
func (h *AGVTestAPIHandler) handleWebSocket(w http.ResponseWriter, r *http.Request) {
	conn, err := h.wsUpgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket升级失败: %v", err)
		return
	}

	defer func() {
		h.wsConnectionsMu.Lock()
		delete(h.wsConnections, conn)
		h.wsConnectionsMu.Unlock()
		conn.Close()
	}()

	h.wsConnectionsMu.Lock()
	h.wsConnections[conn] = true
	h.wsConnectionsMu.Unlock()

	log.Printf("新的WebSocket连接已建立，当前连接数: %d", len(h.wsConnections))

	// 发送欢迎消息
	welcomeMsg := WebSocketMessage{
		Type:      "welcome",
		Data:      map[string]interface{}{"message": "AGV测试WebSocket连接已建立"},
		Timestamp: time.Now(),
	}
	h.sendWebSocketMessage(conn, welcomeMsg)

	// 监听连接
	for {
		_, _, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket连接错误: %v", err)
			}
			break
		}
	}
}

// WebSocket广播处理
func (h *AGVTestAPIHandler) handleWebSocketBroadcast() {
	for {
		select {
		case message := <-h.broadcast:
			h.wsConnectionsMu.RLock()
			for conn := range h.wsConnections {
				select {
				case <-time.After(1 * time.Second):
					// 写入超时，关闭连接
					h.wsConnectionsMu.RUnlock()
					h.wsConnectionsMu.Lock()
					delete(h.wsConnections, conn)
					h.wsConnectionsMu.Unlock()
					conn.Close()
					h.wsConnectionsMu.RLock()
				default:
					err := conn.WriteMessage(websocket.TextMessage, message)
					if err != nil {
						h.wsConnectionsMu.RUnlock()
						h.wsConnectionsMu.Lock()
						delete(h.wsConnections, conn)
						h.wsConnectionsMu.Unlock()
						conn.Close()
						h.wsConnectionsMu.RLock()
					}
				}
			}
			h.wsConnectionsMu.RUnlock()
		}
	}
}

// 辅助方法
func (h *AGVTestAPIHandler) broadcastWebSocketMessage(msgType string, data interface{}) {
	message := WebSocketMessage{
		Type:      msgType,
		Data:      data,
		Timestamp: time.Now(),
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		log.Printf("WebSocket消息序列化失败: %v", err)
		return
	}

	select {
	case h.broadcast <- messageBytes:
	default:
		// 广播通道满了，丢弃消息
	}
}

func (h *AGVTestAPIHandler) sendWebSocketMessage(conn *websocket.Conn, message WebSocketMessage) {
	messageBytes, err := json.Marshal(message)
	if err != nil {
		log.Printf("WebSocket消息序列化失败: %v", err)
		return
	}

	err = conn.WriteMessage(websocket.TextMessage, messageBytes)
	if err != nil {
		log.Printf("WebSocket消息发送失败: %v", err)
	}
}

func (h *AGVTestAPIHandler) respondSuccess(w http.ResponseWriter, message string, data interface{}) {
	response := APIResponse{
		Success:   true,
		Message:   message,
		Data:      data,
		Timestamp: time.Now(),
	}
	h.respondJSON(w, response)
}

func (h *AGVTestAPIHandler) respondError(w http.ResponseWriter, message string, statusCode int) {
	response := APIResponse{
		Success:   false,
		Error:     message,
		Timestamp: time.Now(),
	}
	w.WriteHeader(statusCode)
	h.respondJSON(w, response)
}

func (h *AGVTestAPIHandler) respondJSON(w http.ResponseWriter, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	h.enableCORS(w)

	if err := json.NewEncoder(w).Encode(data); err != nil {
		log.Printf("响应编码失败: %v", err)
	}
}

func (h *AGVTestAPIHandler) enableCORS(w http.ResponseWriter) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
}
