package zmq

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"
)

// ExampleMessage 示例消息结构
type ExampleMessage struct {
	ID        string    `json:"id"`
	Command   string    `json:"command"`
	Data      string    `json:"data"`
	Timestamp time.Time `json:"timestamp"`
}

// ExampleResponse 示例响应结构
type ExampleResponse struct {
	ID        string    `json:"id"`
	Status    string    `json:"status"`
	Result    string    `json:"result"`
	Timestamp time.Time `json:"timestamp"`
}

// RunResponderExample 运行响应者示例
func RunResponderExample() {
	// 创建配置
	config := DefaultConfig()
	config.ResponderEndpoint = "tcp://*:5556"

	// 创建响应者
	responder, err := NewResponder(config)
	if err != nil {
		log.Fatalf("Create responder failed: %v", err)
	}
	defer responder.Close()

	// 设置请求处理函数
	responder.SetHandler(func(request []byte) ([]byte, error) {
		// 解析请求
		var msg ExampleMessage
		if err := json.Unmarshal(request, &msg); err != nil {
			return nil, fmt.Errorf("parse request failed: %w", err)
		}

		log.Printf("Received request: ID=%s, Command=%s, Data=%s", msg.ID, msg.Command, msg.Data)

		// 处理请求
		var result string
		switch msg.Command {
		case "echo":
			result = msg.Data
		case "reverse":
			runes := []rune(msg.Data)
			for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
				runes[i], runes[j] = runes[j], runes[i]
			}
			result = string(runes)
		case "upper":
			result = string([]byte(msg.Data))
		default:
			return nil, fmt.Errorf("unknown command: %s", msg.Command)
		}

		// 创建响应
		resp := ExampleResponse{
			ID:        msg.ID,
			Status:    "success",
			Result:    result,
			Timestamp: time.Now(),
		}

		// 序列化响应
		return json.Marshal(resp)
	})

	// 启动响应者
	ctx := context.Background()
	log.Println("Responder started, waiting for requests...")
	if err := responder.Start(ctx); err != nil {
		log.Fatalf("Start responder failed: %v", err)
	}
}

// RunRequesterExample 运行请求者示例
func RunRequesterExample() {
	// 创建配置
	config := DefaultConfig()
	config.RequesterEndpoint = "tcp://localhost:5556"

	// 创建请求者
	requester, err := NewRequester(config)
	if err != nil {
		log.Fatalf("Create requester failed: %v", err)
	}
	defer requester.Close()

	// 发送多个请求
	commands := []struct {
		cmd  string
		data string
	}{
		{"echo", "Hello ZeroMQ!"},
		{"reverse", "Hello World"},
		{"upper", "convert to upper"},
	}

	for i, cmd := range commands {
		// 创建请求
		msg := ExampleMessage{
			ID:        fmt.Sprintf("req-%d", i+1),
			Command:   cmd.cmd,
			Data:      cmd.data,
			Timestamp: time.Now(),
		}

		// 序列化请求
		reqData, err := json.Marshal(msg)
		if err != nil {
			log.Printf("Marshal request failed: %v", err)
			continue
		}

		// 发送请求并等待响应
		log.Printf("Sending request: %s", string(reqData))
		respData, err := requester.Request(reqData)
		if err != nil {
			log.Printf("Request failed: %v", err)
			continue
		}

		// 解析响应
		var resp ExampleResponse
		if err := json.Unmarshal(respData, &resp); err != nil {
			log.Printf("Parse response failed: %v", err)
			continue
		}

		log.Printf("Received response: ID=%s, Status=%s, Result=%s", resp.ID, resp.Status, resp.Result)

		// 稍微延迟
		time.Sleep(100 * time.Millisecond)
	}
}

// SimpleRequestResponseExample 简单的请求响应示例
func SimpleRequestResponseExample() {
	// 服务端代码（在一个goroutine中运行）
	go func() {
		config := &Config{
			ResponderEndpoint: "tcp://*:5557",
			RecvTimeout:       100 * time.Millisecond,
			SendTimeout:       1 * time.Second,
		}

		responder, err := NewResponder(config)
		if err != nil {
			log.Printf("Create responder failed: %v", err)
			return
		}
		defer responder.Close()

		// 简单的echo处理器
		responder.SetHandler(func(request []byte) ([]byte, error) {
			log.Printf("Server received: %s", string(request))
			return []byte("Echo: " + string(request)), nil
		})

		ctx := context.Background()
		responder.Start(ctx)
	}()

	// 等待服务端启动
	time.Sleep(100 * time.Millisecond)

	// 客户端代码
	config := &Config{
		RequesterEndpoint: "tcp://localhost:5557",
		ConnectTimeout:    5 * time.Second,
		SendTimeout:       1 * time.Second,
		RecvTimeout:       1 * time.Second,
	}

	requester, err := NewRequester(config)
	if err != nil {
		log.Fatalf("Create requester failed: %v", err)
	}
	defer requester.Close()

	// 发送请求
	response, err := requester.RequestString("Hello Server!")
	if err != nil {
		log.Fatalf("Request failed: %v", err)
	}

	log.Printf("Client received: %s", response)
}

// AdvancedRequestResponseExample 高级请求响应示例（带超时和错误处理）
func AdvancedRequestResponseExample() {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动服务端
	go func() {
		config := DefaultConfig()
		config.ResponderEndpoint = "tcp://*:5558"

		responder, err := NewResponder(config)
		if err != nil {
			log.Printf("Create responder failed: %v", err)
			return
		}
		defer responder.Close()

		responder.SetHandler(func(request []byte) ([]byte, error) {
			cmd := string(request)

			switch cmd {
			case "fast":
				return []byte("Fast response"), nil
			case "slow":
				time.Sleep(2 * time.Second)
				return []byte("Slow response"), nil
			case "error":
				return nil, fmt.Errorf("simulated error")
			default:
				return []byte("Unknown command"), nil
			}
		})

		responder.Start(ctx)
	}()

	time.Sleep(100 * time.Millisecond)

	// 客户端代码
	config := DefaultConfig()
	config.RequesterEndpoint = "tcp://localhost:5558"

	requester, err := NewRequester(config)
	if err != nil {
		log.Fatalf("Create requester failed: %v", err)
	}
	defer requester.Close()

	// 测试快速响应
	log.Println("Testing fast response...")
	resp, err := requester.RequestString("fast")
	if err != nil {
		log.Printf("Fast request failed: %v", err)
	} else {
		log.Printf("Fast response: %s", resp)
	}

	// 测试超时
	log.Println("Testing timeout...")
	respBytes, err := requester.RequestWithTimeout([]byte("slow"), 500*time.Millisecond)
	if err != nil {
		log.Printf("Slow request timeout (expected): %v", err)
	} else {
		log.Printf("Slow response: %s", string(respBytes))
	}

	// 测试错误处理
	log.Println("Testing error handling...")
	resp, err = requester.RequestString("error")
	if err != nil {
		log.Printf("Error request failed: %v", err)
	} else {
		log.Printf("Error response: %s", resp)
	}
}
