# 巷道导航逻辑重构记录

**日期**: 2024-12-19  
**作者**: Claude  
**版本**: v1.0  

## 背景和原因

### 问题描述
当前系统将每个细纱机作为独立任务处理，没有考虑巷道结构：
- 61L和60R组成一个巷道，应该连续处理
- 处理完L侧后需要在巷道内调头处理R侧
- 中央通道位置（如61R）需要特殊处理

### 解决方案
引入任务组概念，将巷道双侧作为一个整体处理，同时支持特殊位置的灵活配置。

## 修改清单

### 1. 新增文件

#### 1.1 `backend/internal/task/lane_config.go`
**目的**: 管理巷道配置和配对规则
```go
// 主要结构和功能：
- SpecialLaneConfig: 特殊巷道配置
- LoadSpecialConfigs(): 加载特殊配置
- findLanePair(): 自动推断巷道配对关系
- parseMachine(): 解析细纱机编号
```

#### 1.2 `backend/internal/task/task_preprocessor.go`
**目的**: 任务预处理，生成任务组
```go
// 主要结构和功能：
- TaskGroup: 任务组结构
- PreprocessTasks(): 将细纱机列表转换为任务组
```

### 2. 修改文件

#### 2.1 `backend/internal/task/task_service.go`
**修改内容**:
1. `workRoutine()` - 使用任务组替代直接遍历
2. 新增 `processTaskGroup()` - 处理任务组
3. 新增 `processLane()` - 处理巷道任务
4. 新增 `processSingleSide()` - 处理单侧任务

#### 2.2 `backend/data/task_config.json`
**修改内容**:
- 添加 `special_lanes` 配置节
- 添加 `lane_rules` 配置节

#### 2.3 `backend/internal/task/plc_data_helper.go`
**修改内容**:
- 新增 `NotifyPLCTurnAround()` - 通知PLC调头（M607）
- 新增 `WaitForTurnComplete()` - 等待调头完成（M507）
- 新增 `NotifyPLCExitLane()` - 通知PLC退出巷道（M608）
- 新增 `WaitForLaneExit()` - 等待巷道退出完成（M508）

#### 2.4 `backend/internal/task/config.go`
**修改内容**:
- 添加巷道相关PLC地址配置字段
- 添加巷道相关超时配置字段
- 更新默认配置值

## 详细修改说明

### 巷道配对规则
- 默认规则：nL 与 (n-1)R 组成巷道
- 特殊情况通过配置文件指定
- 支持动态扩展新的配对规则

### 任务处理流程变化
**原流程**:
```
61R → 61L → 60R → 60L (每个独立处理)
```

**新流程**:
```
组1: 61R (单侧)
组2: 61L + 60R (巷道)
组3: 60L + 59R (巷道)
```

### 配置示例
```json
{
  "special_lanes": [
    {
      "machine": "61R",
      "type": "central_aisle",
      "strategy": "single_only"
    }
  ]
}
```

## 测试要点
1. 验证巷道任务连续处理
2. 验证中央通道单侧处理
3. 验证特殊配置生效
4. 验证任务中断恢复

## 潜在影响
1. 任务执行顺序改变
2. PLC通信增加调头相关信号
3. 任务状态显示需要适配

## 回滚方案
```bash
# 如果出现问题，回滚到上一个提交
git reset --hard 3c9a40ca

# 或者回滚到修改前
git reset --hard HEAD~1
```

## 后续优化建议
1. 支持更多巷道类型配置
2. 添加巷道导航可视化
3. 优化调头时间估算