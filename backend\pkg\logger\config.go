package logger

import (
	"encoding/json"
	"os"
	"path/filepath"
)

// Config 日志配置
type Config struct {
	Level            LogLevel `json:"level"`            // 日志级别
	EnableConsole    bool     `json:"enableConsole"`    // 是否输出到控制台
	EnableFile       bool     `json:"enableFile"`       // 是否输出到文件
	EnableStructured bool     `json:"enableStructured"` // 是否使用结构化格式
	FilePath         string   `json:"filePath"`         // 日志文件路径
	MaxSize          int64    `json:"maxSize"`          // 单个日志文件最大大小(MB)
	MaxBackups       int      `json:"maxBackups"`       // 保留的备份文件数量
	MaxAge           int      `json:"maxAge"`           // 日志文件保留天数
}

// GetDefaultConfig 获取默认配置
func GetDefaultConfig() *Config {
	return &Config{
		Level:            INFO,
		EnableConsole:    true,
		EnableFile:       true,
		EnableStructured: false,
		FilePath:         "logs/agv_nav.log",
		MaxSize:          10, // 10MB
		MaxBackups:       5,  // 保留5个备份文件
		MaxAge:           30, // 保留30天
	}
}

// LoadConfig 从文件加载配置
func LoadConfig(configPath string) (*Config, error) {
	// 如果配置文件不存在，返回默认配置
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return GetDefaultConfig(), nil
	}

	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, err
	}

	config := &Config{}
	err = json.Unmarshal(data, config)
	if err != nil {
		return nil, err
	}

	// 验证配置
	config.validate()

	return config, nil
}

// SaveConfig 保存配置到文件
func (c *Config) SaveConfig(configPath string) error {
	// 确保目录存在
	dir := filepath.Dir(configPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return err
	}

	data, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile(configPath, data, 0644)
}

// validate 验证并修复配置
func (c *Config) validate() {
	// 确保日志级别在有效范围内
	if c.Level < DEBUG || c.Level > FATAL {
		c.Level = INFO
	}

	// 确保至少有一个输出目标
	if !c.EnableConsole && !c.EnableFile {
		c.EnableConsole = true
	}

	// 确保文件路径有效
	if c.EnableFile && c.FilePath == "" {
		c.FilePath = "logs/agv_nav.log"
	}

	// 确保文件大小限制合理
	if c.MaxSize <= 0 {
		c.MaxSize = 10
	}

	// 确保备份数量合理
	if c.MaxBackups < 0 {
		c.MaxBackups = 5
	}

	// 确保保留天数合理
	if c.MaxAge < 0 {
		c.MaxAge = 30
	}
}

// LogLevelFromString 从字符串解析日志级别
func LogLevelFromString(levelStr string) LogLevel {
	switch levelStr {
	case "DEBUG", "debug":
		return DEBUG
	case "INFO", "info":
		return INFO
	case "WARN", "warn", "WARNING", "warning":
		return WARN
	case "ERROR", "error":
		return ERROR
	case "FATAL", "fatal":
		return FATAL
	default:
		return INFO
	}
}

// GetModuleConfig 获取特定模块的配置
func GetModuleConfig(module string) *Config {
	// 尝试加载模块特定配置
	configPath := filepath.Join("config", "logger", module+".json")
	config, err := LoadConfig(configPath)
	if err != nil {
		// 如果模块配置不存在，使用默认配置
		config = GetDefaultConfig()

		// 根据模块调整默认配置
		switch module {
		case "task":
			// 任务模块可能需要更详细的日志
			config.Level = DEBUG
			config.FilePath = "logs/task.log"
		case "plc":
			// PLC模块日志
			config.FilePath = "logs/plc.log"
		case "agv":
			// AGV控制模块日志
			config.FilePath = "logs/agv.log"
		case "api":
			// API模块日志
			config.FilePath = "logs/api.log"
			config.EnableStructured = true // API适合结构化日志
		}
	}

	return config
}
