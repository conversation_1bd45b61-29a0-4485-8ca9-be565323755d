package zmq

import (
	"encoding/json"
	"fmt"
	"os"
)

// SystemConfig 系统配置（用于ZMQ通信）
type SystemConfig struct {
	RobotNo     string `json:"robot_no"`    // 机器人编号
	Description string `json:"description"` // 系统描述
}

// TaskConfig 任务配置文件的部分结构（只包含我们需要的部分）
type TaskConfig struct {
	System SystemConfig `json:"system"`
	// 其他配置项不需要在这里定义
}

// LoadSystemConfig 从task_config.json加载系统配置
func LoadSystemConfig(configPath string) (*SystemConfig, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("read config file failed: %w", err)
	}

	var config TaskConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("parse config failed: %w", err)
	}

	// 验证配置
	if config.System.RobotNo == "" {
		return nil, fmt.Errorf("robot_no cannot be empty")
	}

	return &config.System, nil
}

// DefaultSystemConfig 默认系统配置（当配置文件不存在时使用）
func DefaultSystemConfig() *SystemConfig {
	return &SystemConfig{
		RobotNo:     "AGV001",
		Description: "AGV Navigation Control System",
	}
}
