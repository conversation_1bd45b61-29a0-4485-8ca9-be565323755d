package api

import (
	"encoding/json"
	"log"
	"net/http"

	"github.com/user/agv_nav/internal/task"
)

// WatchTaskStartRequest 开始看车任务请求
type WatchTaskStartRequest struct {
	Robots []string `json:"robots"` // 前端传过来的细纱机范围列表
}

// StartWatchTaskHandler 开始看车任务处理器
func (s *Server) StartWatchTaskHandler(w http.ResponseWriter, r *http.Request) {
	// 处理CORS预检请求
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	// 解析前端发送的请求
	var req WatchTaskStartRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		s.respondError(w, "请求数据格式错误", http.StatusBadRequest)
		return
	}

	// 检查数据是否正常
	if len(req.Robots) == 0 {
		s.respondError(w, "请选择要看车的细纱机范围", http.StatusBadRequest)
		return
	}

	// 检查设备连接状态
	agvConnected := s.agvController.IsConnected()
	plcConnected := s.plcController.IsConnected()

	if !agvConnected && !plcConnected {
		s.respondError(w, "AGV和PLC均未连接，请先连接设备", http.StatusBadRequest)
		return
	} else if !agvConnected {
		s.respondError(w, "AGV未连接，请先连接AGV设备", http.StatusBadRequest)
		return
	} else if !plcConnected {
		s.respondError(w, "PLC未连接，请先连接PLC设备", http.StatusBadRequest)
		return
	}

	// 打印收到的信息
	log.Printf("收到看车请求，细纱机范围: %v", req.Robots)

	// 调用业务逻辑，启动队列协程任务
	err := task.StartTask(req.Robots)
	if err != nil {
		log.Printf("看车任务启动失败: %v", err)
		s.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 返回成功响应
	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"message": "看车任务已启动",
		"data": map[string]interface{}{
			"total": len(req.Robots),
			"queue": req.Robots,
		},
	})
}

// StopWatchTaskHandler 停止看车任务处理器
func (s *Server) StopWatchTaskHandler(w http.ResponseWriter, r *http.Request) {
	// 处理CORS预检请求
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	log.Printf("收到停止看车请求")

	// 调用业务逻辑停止任务
	err := task.StopWatchTask()
	if err != nil {
		log.Printf("停止看车任务失败: %v", err)
		s.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 重置流程监控状态，清空所有流程信息
	ResetFlowStatus()
	log.Printf("流程监控状态已重置，用户可开始新任务")

	// 返回成功响应
	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"message": "看车任务停止成功，任务状态已清空",
	})
}

// PauseWatchTaskHandler 暂停看车任务处理器
func (s *Server) PauseWatchTaskHandler(w http.ResponseWriter, r *http.Request) {
	// 处理CORS预检请求
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	log.Printf("收到暂停看车请求")

	err := task.PauseTask()
	if err != nil {
		log.Printf("暂停看车任务失败: %v", err)
		s.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"message": "看车任务已暂停",
	})
}

// ResumeWatchTaskHandler 恢复看车任务处理器
func (s *Server) ResumeWatchTaskHandler(w http.ResponseWriter, r *http.Request) {
	// 处理CORS预检请求
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	log.Printf("收到恢复看车请求")

	err := task.ResumeTask()
	if err != nil {
		log.Printf("恢复看车任务失败: %v", err)
		s.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"message": "看车任务已恢复",
	})
}

// GetWatchTaskStatusHandler 获取看车任务状态处理器
func (s *Server) GetWatchTaskStatusHandler(w http.ResponseWriter, r *http.Request) {
	// 处理CORS预检请求
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	// 获取任务状态
	status := task.GetTaskStatus()

	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"message": "状态查询成功",
		"data":    status,
	})
}
