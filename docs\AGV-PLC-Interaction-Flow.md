# AGV与PLC交互流程详细文档

## 目录
1. [系统概述](#系统概述)
2. [PLC地址映射表](#plc地址映射表)
3. [任务分组机制](#任务分组机制)
4. [详细交互流程](#详细交互流程)
5. [单侧任务专用流程](#单侧任务专用流程)
6. [巷道任务专用流程](#巷道任务专用流程)
7. [错误处理机制](#错误处理机制)
8. [通信协议详解](#通信协议详解)

## 系统概述

### 系统架构
- **前端**: React + TypeScript + Electron桌面应用
- **后端**: Go语言REST API + WebSocket服务
- **AGV通信**: UDP协议（***************:17804）
- **PLC通信**: Modbus TCP协议（端口502）

### 控制模式
- **巷道外**: AGV激光导航自主控制
- **巷道内**: PLC通过码带读码器控制

### 工作流程概览
```
前端点击 → API请求 → 任务初始化 → 任务预处理分组 → 按组处理 → 完成返回
```

## PLC地址映射表

### 1. 控制权管理地址

| 地址 | 类型 | 方向 | 描述 | 值说明 |
|------|------|------|------|--------|
| M601 | 线圈 | 写入 | AGV请求PLC控制权 | 1=请求控制 |
| M501 | 线圈 | 读取 | PLC控制权确认 | 1=控制权已转移 |
| M602 | 线圈 | 写入 | 开始任务信号 | 1=开始执行任务 |
| M607 | 线圈 | 写入 | 控制权交接请求 | 1=请求切换回激光控制 |

### 2. 数据通信地址

| 地址 | 类型 | 方向 | 描述 | 值说明 |
|------|------|------|------|--------|
| M509 | 线圈 | 读取 | PLC准备接收数据 | 1=准备就绪 |
| M604 | 线圈 | 写入 | 方向数据 | 0=左侧, 1=右侧 |
| D602-D603 | 寄存器 | 写入 | 距离数据（浮点） | BADC字节序，2个寄存器 |
| D600 | 寄存器 | 写入 | 滚筒方向 | 1=左滚筒, 2=右滚筒 |
| M603 | 线圈 | 写入 | 数据写入完成 | 1=写入完成 |

### 3. 数据验证地址

| 地址 | 类型 | 方向 | 描述 | 值说明 |
|------|------|------|------|--------|
| M505 | 线圈 | 读取 | 读回方向值 | 0=左侧, 1=右侧 |
| D501 | 寄存器 | 读取 | 读回滚筒值 | 1=左滚筒, 2=右滚筒 |
| D502-D503 | 寄存器 | 读取 | 读回距离值（浮点） | BADC字节序 |
| M605 | 线圈 | 写入 | 数据验证完成 | 1=验证完成 |

### 4. 状态监控地址

| 地址 | 类型 | 方向 | 描述 | 值说明 |
|------|------|------|------|--------|
| D500 | 寄存器 | 读取 | PLC工作状态 | 0=工作中, 1=成功, 2=失败 |
| M502 | 线圈 | 读取 | 机器人运行条件 | 1=允许运行 |

### 5. 任务完成控制地址

| 地址 | 类型 | 方向 | 描述 | 值说明 |
|------|------|------|------|--------|
| M606 | 线圈 | 写入 | 机台完成信号 | 1=机台处理完成 |
| M610 | 线圈 | 写入 | 回到码带原点位置 | 1=需要回到原点 |
| M608 | 线圈 | 写入 | 调头命令 | 1=执行调头 |

### 6. 巷道控制地址

| 地址 | 类型 | 方向 | 描述 | 值说明 |
|------|------|------|------|--------|
| M608 | 线圈 | 写入 | 调头命令 | 1=执行调头 |
| M511 | 线圈 | 读取 | 调头完成信号 | 1=调头完成 |
| M609 | 线圈 | 写入 | 出巷道命令 | 1=出巷道 |
| M508 | 线圈 | 读取 | 出巷道完成信号 | 1=已出巷道 |

### 7. 原点控制地址

| 地址 | 类型 | 方向 | 描述 | 值说明 |
|------|------|------|------|--------|
| M611 | 线圈 | 写入 | 原点方向 | 0=方向0, 1=方向1 |
| D604-D605 | 寄存器 | 写入 | 原点码值（浮点） | BADC字节序 |
| M510 | 线圈 | 读取 | 原点到达信号 | 1=已到达原点 |

## 任务分组机制

### 预处理阶段

任务在执行前会进行预处理，将用户选择的机台分组：

1. **任务分组逻辑**（文件位置：`task_preprocessor.go`）
   ```go
   - 单侧任务：IsSingleOnly(machine) 或配对不完整
   - 巷道任务：FindLanePair() 找到完整配对
   ```

2. **任务组类型**
   ```go
   单侧任务: Type = "single"   // 中央过道位置（如61R）
   巷道任务: Type = "lane"     // 成对机台（如61L-60R）
   ```

3. **处理策略**
   - 单侧任务：processSingleSide() → 直接完成，不调头
   - 巷道任务：processLane() → L侧→调头→R侧

## 详细交互流程

### 第一步：前端触发（MonitorPanel.tsx）

1. **用户点击"开始看车"按钮**
   - 文件位置: `frontend/src/components/MonitorPanel.tsx:handleStartWatch()`
   
2. **前置条件检查**
   ```typescript
   - AGV连接状态: isAGVConnected === true
   - PLC连接状态: isPLCConnected === true
   - 选中机台数量: selectedRobots.length > 0
   - 当前无运行任务: !currentTask || currentTask.status !== "running"
   ```

3. **发送API请求**
   ```typescript
   POST /api/task/start-watch
   Body: {
     "robots": ["61R", "60L", "59R", ...]  // 选中的机台号数组
   }
   ```

### 第二步：后端API处理（task_handlers.go）

1. **接收请求处理**
   - 文件位置: `backend/pkg/api/task_handlers.go:StartWatchTaskHandler()`
   
2. **验证步骤**
   ```go
   - 检查robots数组非空
   - 验证AGV连接: agvConnected == true
   - 验证PLC连接: plcConnected == true
   ```

3. **调用任务服务**
   ```go
   task.StartTask(req.Robots)
   ```

### 第三步：任务服务初始化（task_service.go）

1. **检查运行条件**
   - 文件位置: `backend/internal/task/task_service.go:StartTask()`
   - PLC操作: 读取M502，确认值为1

2. **初始化任务状态**
   ```go
   - 设置状态为"running"
   - 记录开始时间
   - 初始化进度计数器
   ```

3. **启动工作协程**
   ```go
   go workRoutine()
   ```

### 第四步：任务预处理分组（task_service.go）

1. **任务预处理**
   - 文件位置: `backend/internal/task/task_service.go:workRoutine()`
   
2. **调用预处理函数**
   ```go
   taskGroups, err := PreprocessTasks(tm.queue)
   ```

3. **生成任务组**
   ```go
   示例结果:
   taskGroups = []TaskGroup{
     {Type: "single", Machines: ["61R"]},
     {Type: "lane", Machines: ["61L", "60R"], LaneInfo: {...}},
   }
   ```

### 第五步：按任务组处理

**处理每个任务组**
```go
for groupIndex, group := range taskGroups {
    err := tm.processTaskGroup(group)
    // 根据group.Type调用不同的处理方法
}
```

## 单侧任务专用流程

### 适用场景
- 中央过道位置（如61R）
- 配置为IsSingleOnly的机台
- 巷道配对不完整的机台

### 处理流程（processSingleSide）

1. **AGV导航到目标位置**
   - 查询导航点: `SELECT navigation_id FROM machine_navigation WHERE machine_number = ?`
   - 发送导航命令: UDP命令码0x16
   - 等待到达确认

2. **切换PLC控制权**
   - 写入M601 = 1（请求控制）
   - 读取M501确认控制权转移

3. **通知PLC开始任务**
   - 写入M602 = 1（开始信号）

4. **执行看车工作**
   ```go
   executeWatchWork(machine, true)  // callMachineComplete = true
   ```

5. **锭位处理循环**（详见锭位处理章节）

6. **单侧任务完成信号**
   ```go
   调用NotifyMachineComplete():
   - M606 = true（机台完成）
   - M610 = true（回到原点）
   - M608 = false（不调头）
   ```

7. **设置原点信息**
   - 写入M611（原点方向）
   - 写入D604（原点码值）

8. **等待AGV到达原点**
   - 循环读取M510，等待值为1

9. **控制权交接**
   - 写入M607 = 1（请求切换回激光控制）
   - 等待M501 = false（控制权交接完成）

10. **任务完成**
    - AGV控制权返回程序
    - 准备处理下一个任务

## 巷道任务专用流程

### 适用场景
- 成对的巷道机台（如61L-60R）
- 需要在巷道内调头的场景

### 处理流程（processLane）

#### 第一阶段：导航到巷道入口

1. **导航到L侧入口**
   - 查询L侧机台导航点
   - AGV导航到位

2. **切换PLC控制，进入巷道**
   - 写入M601 = 1
   增加 M604 写码值
   - 等待M501 = 1
   - 写入M602 = 1

#### 第二阶段：处理L侧

3. **执行L侧看车工作**
   ```go
   executeWatchWork(leftMachine, false)  // callMachineComplete = false
   ```

4. **L侧完成信号**
   ```go
   调用NotifyLaneLSideComplete():
   - M606 = true（机台完成）
   - M608 = true（调头信号）
   - M610 = false（不回到原点）
   ```

#### 第三阶段：巷道内调头

5. **等待调头完成**
   - 循环读取M511，等待值为1
   - 调头完成后自动重置M608 = false

#### 第四阶段：处理R侧

6. **执行R侧看车工作**
   ```go
   executeWatchWork(rightMachine, false)  // callMachineComplete = false
   ```

#### 第五阶段：R侧完成流程

7. **R侧完成5步流程**
   ```go
   调用executeRSideCompleteFlow():
   ```

   **步骤1：发送R侧完成信号**
   ```go
   - M606 = true（机台完成）
   - M608 = false（调头信号关闭）
   - M610 = false（不回到原点）
   ```

   **步骤2：写入码带信息**
   ```go
   - M611 = direction（码带方向）
   - D604 = codeValue（码带位置）
   ```

   **步骤3：等待AGV到达原点**
   ```go
   - 循环读取M510，等待值为1
   ```

   **步骤4：切换回激光控制**
   ```go
   - M607 = 1（请求切换）
   - 等待M501 = false（切换完成）
   ```

   **步骤5：巷道任务完成**
   ```go
   - AGV控制权返回程序
   - 准备处理下一个任务
   ```

## 锭位处理循环（executeWatchWork）

### 函数签名
```go
executeWatchWork(machine string, callMachineComplete bool)
```

### 参数说明
- `machine`: 机台号（如"61L"）
- `callMachineComplete`: 
  - `true`: 单侧任务，处理完成后调用NotifyMachineComplete
  - `false`: 巷道任务，不调用NotifyMachineComplete

### 处理流程

1. **获取锭位列表**
   - HTTP请求: `POST http://47.92.194.122:8081/mes/86099703/abnormalData`
   - 请求体: `{"customerNo": "86099703", "machineId": "***********61"}`
   - 说明: machineId = "***********" + 机器号（如61）
   - 侧面信息（L/R）用于响应数据筛选，不在请求体中

2. **锭位排序**
   - 数据库查询: `SELECT spindle_direction FROM machine_config WHERE machine_number = ?`
   - 根据方向排序（正向/反向）

3. **处理每个锭位**（5步协议）

   **步骤1: 等待PLC就绪**
   ```go
   循环读取M509，直到值为1
   超时时间: 30秒
   ```

   **步骤2: 写入锭位数据**
   ```go
   - 查询距离码: SELECT distance_code FROM spindle_distance WHERE spindle_number = ?
   - 写入方向: M604 = direction (0=左, 1=右)
   - 写入距离: D602-D603 = distance (float, BADC)
   - 写入滚筒: D600 = roller (1=左, 2=右)
   ```

   **步骤3: 发送完成信号M603**
   ```go
   写入M603 = 1  // 通知PLC数据写入完成
   ```

   **步骤4: 验证数据**
   ```go
   - 读取M505，验证方向匹配
   - 读取D501，验证滚筒匹配
   - 读取D502-D503，验证距离匹配
   - 发送验证完成: M605 = 1
   ```

   **步骤5: 等待处理完成**
   ```go
   循环读取D500
   值为1: 成功，标记锭位已处理
   值为2: 失败，记录错误
   超时时间: 60秒
   ```

4. **完成处理**
   - 如果callMachineComplete为true：调用NotifyMachineComplete()
   - 如果callMachineComplete为false：直接返回

## 错误处理机制

### 1. 超时处理

所有等待操作都设置超时：
- AGV导航: 300秒
- PLC响应: 120秒
- 锭位处理: 300秒
- 调头操作: 60秒
- 原点到达: 300秒

### 2. 重试机制

关键操作失败时的重试：
- 单机处理: 最多3次
- 递增延迟: 5秒 × 尝试次数
- 严重错误不重试

### 3. 错误恢复

发生错误时的恢复流程：
1. 记录错误日志
2. 尝试恢复到安全状态
3. 释放PLC控制权
4. 通知前端错误信息

## 通信协议详解

### 1. AGV UDP协议

**消息头结构**（28字节）：
```
0-3:   同步字（0x5A5A5A5A）
4-7:   序列号
8-11:  消息长度
12-13: 命令码
14-15: 保留
16-19: 时间戳
20-27: 认证码前8字节
```

**主要命令码**：
- 0x03: 写变量
- 0x16: 导航命令
- 0xAF: 查询状态
- 0xB1: 订阅状态

### 2. PLC Modbus协议

**功能码**：
- 0x01: 读线圈
- 0x05: 写单个线圈
- 0x03: 读保持寄存器
- 0x10: 写多个寄存器

**数据格式**：
- 线圈: 布尔值（0/1）
- 寄存器: 16位整数
- 浮点数: 32位，BADC字节序

### 3. WebSocket消息格式

**任务状态更新**：
```json
{
  "type": "TASK_UPDATE",
  "data": {
    "id": "task_123456",
    "status": "running",
    "progress": 5,
    "totalCount": 10,
    "currentRobot": "61L",
    "currentStep": "处理锭位",
    "remainingRobots": ["60R", "59L"],
    "current_step": "处理任务组 lane_61L_60R",
    "step_progress": 75
  }
}
```

**设备状态更新**：
```json
{
  "type": "STATUS_UPDATE",
  "data": {
    "agv": {
      "connected": true,
      "position": {"x": 100.5, "y": 200.3},
      "battery": 85.5,
      "workMode": "自动模式"
    },
    "plc": {
      "connected": true,
      "controlMode": "plc"
    }
  }
}
```

## 关键差异总结

### 单侧任务 vs 巷道任务

| 特征 | 单侧任务 | 巷道任务 |
|------|----------|----------|
| 调头操作 | 无 | 有（M608→M511） |
| 机台完成信号 | M606=1, M610=1, M608=0 | L侧：M606=1, M608=1, M610=0<br>R侧：M606=1, M608=0, M610=0 |
| 原点信息设置 | 有（M611, D604） | 仅R侧有 |
| 控制权交接 | 有（M607→M501=0） | 仅R侧有 |
| callMachineComplete | true | false |

### 关键代码位置

- 前端启动按钮: `frontend/src/components/MonitorPanel.tsx`
- API处理器: `backend/pkg/api/task_handlers.go`
- 任务服务: `backend/internal/task/task_service.go`
- 任务预处理: `backend/internal/task/task_preprocessor.go`
- AGV控制器: `backend/pkg/agv/agv_controller.go`
- PLC数据辅助: `backend/internal/task/plc_data_helper.go`
- 配置管理: `backend/internal/task/config.go`
- WebSocket服务: `backend/pkg/api/websocket.go`