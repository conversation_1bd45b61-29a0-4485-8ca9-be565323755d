package agv

import (
	"encoding/binary"
	"fmt"
	"math"
	"net"
	"time"

	"github.com/user/agv_nav/pkg/logger"
	"github.com/user/agv_nav/pkg/protocol"
)

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// Controller 表示AGV控制器
type Controller struct {
	conn      *net.UDPConn
	authCode  []byte
	seqNum    uint16
	status    *Status
	navStatus *NavigationStatus // 新增：导航状态
}

// NewController 创建新的AGV控制器
func NewController() *Controller {
	return &Controller{}
}

// Connect 连接到AGV控制器
func (c *Controller) Connect(authHex, ip, port string) error {
	agvLogger := logger.GetModuleLogger("agv")
	agvLogger.Info("开始连接AGV控制器", "ip", ip, "port", port)

	// 解析授权码
	authCode, err := protocol.HexToBytes(authHex)
	if err != nil {
		agvLogger.Error("AGV授权码解析失败", "authHex", authHex, "error", err)
		return fmt.Errorf("授权码错误: %v", err)
	}

	// 解析地址
	addrStr := fmt.Sprintf("%s:%s", ip, port)
	udpAddr, err := net.ResolveUDPAddr("udp", addrStr)
	if err != nil {
		agvLogger.Error("AGV地址解析失败", "address", addrStr, "error", err)
		return fmt.Errorf("地址解析错误: %v", err)
	}

	// 建立连接
	conn, err := net.DialUDP("udp", nil, udpAddr)
	if err != nil {
		agvLogger.Error("AGV UDP连接失败", "address", addrStr, "error", err)
		return fmt.Errorf("连接失败: %v", err)
	}

	c.conn = conn
	c.authCode = authCode
	c.seqNum = 0
	c.status = &Status{}

	agvLogger.Info("AGV UDP socket创建成功，正在验证设备连接", "address", addrStr, "authCodeLength", len(authCode))

	// 验证连接 - 发送测试命令确认设备在线
	agvLogger.Info("开始验证AGV设备连接")

	// 尝试查询状态来验证连接
	maxRetries := 3
	var lastErr error

	for i := 0; i < maxRetries; i++ {
		agvLogger.Info("AGV连接验证尝试", "attempt", i+1, "maxRetries", maxRetries)

		err = c.QueryStatus()
		if err == nil {
			// 验证成功
			agvLogger.Info("AGV连接验证成功", "address", addrStr, "attempt", i+1)
			return nil
		}

		lastErr = err
		agvLogger.Warn("AGV连接验证失败，准备重试", "attempt", i+1, "error", err)

		// 短暂等待后重试（除了最后一次）
		if i < maxRetries-1 {
			time.Sleep(time.Millisecond * 500)
		}
	}

	// 验证失败，断开连接
	agvLogger.Error("AGV连接验证失败，断开连接", "address", addrStr, "finalError", lastErr)
	c.conn.Close()
	c.conn = nil

	return fmt.Errorf("AGV连接验证失败：设备无响应，请检查设备是否在线。错误详情: %v", lastErr)
}

// Disconnect 断开与AGV的连接
func (c *Controller) Disconnect() {
	agvLogger := logger.GetModuleLogger("agv")
	if c.conn != nil {
		remoteAddr := c.conn.RemoteAddr().String()
		c.conn.Close()
		c.conn = nil
		agvLogger.Info("AGV连接已断开", "remoteAddress", remoteAddr)
	} else {
		agvLogger.Warn("尝试断开AGV连接，但连接不存在")
	}
}

// IsConnected 检查是否已连接
func (c *Controller) IsConnected() bool {
	return c.conn != nil
}

// clearReceiveBuffer 清空接收缓冲区
func (c *Controller) clearReceiveBuffer() {
	if c.conn == nil {
		return
	}

	// 设置很短的超时时间
	c.conn.SetReadDeadline(time.Now().Add(10 * time.Millisecond))
	buf := make([]byte, 1024)
	for {
		_, err := c.conn.Read(buf)
		if err != nil {
			// 超时或其他错误，缓冲区已清空
			break
		}
	}
}

// SendNavToPointID 发送导航命令 (0x16)
func (c *Controller) SendNavToPointID(pointID uint16) error {
	agvLogger := logger.GetModuleLogger("agv")
	if c.conn == nil {
		agvLogger.Error("发送导航命令失败：AGV未连接", "pointID", pointID)
		return fmt.Errorf("未连接到AGV")
	}

	agvLogger.Info("发送AGV导航命令", "pointID", pointID, "sequenceNum", c.seqNum+1)
	c.seqNum++

	dataLen := uint16(432)
	data := make([]byte, dataLen)

	data[0] = 0x00 // 操作 = 0x00 (开始导航)
	data[1] = 0x00 // 导航方式 = 0x00 (导航到路径点)
	data[2] = 0x00 // 是否指定路径 = 0x00 (不指定)
	data[3] = 0x00 // 是否启用交通管理 = 0x00 (不启用)

	idStr := fmt.Sprintf("%d", pointID)
	if len(idStr) > 8 {
		return fmt.Errorf("path ID string too long: %s", idStr)
	}
	copy(data[4:4+len(idStr)], []byte(idStr))

	header, err := protocol.BuildHeader(c.authCode, c.seqNum, protocol.CmdNavigation, dataLen)
	if err != nil {
		return fmt.Errorf("buildHeader error: %w", err)
	}

	packet := append(header, data...)
	_, err = c.conn.Write(packet)
	if err != nil {
		agvLogger.Error("AGV导航命令发送失败", "pointID", pointID, "error", err)
		return err
	}
	agvLogger.Info("AGV导航命令发送成功", "pointID", pointID, "packetSize", len(packet))
	return nil
}

// Subscribe 订阅状态信息 (0xB1)
func (c *Controller) Subscribe() error {
	agvLogger := logger.GetModuleLogger("agv")
	if c.conn == nil {
		agvLogger.Error("AGV订阅失败：未连接")
		return fmt.Errorf("未连接到AGV")
	}

	agvLogger.Info("开始AGV状态订阅", "sequenceNum", c.seqNum+1)
	c.seqNum++

	// 构建订阅数据
	// 数据结构：8个Info结构体(16字节*8) + UUID(64字节) = 192字节
	dataLen := uint16(192)
	data := make([]byte, dataLen)

	// 订阅0xAF命令（查询机器人状态）
	binary.LittleEndian.PutUint16(data[0:2], 0xAF)  // 命令码
	binary.LittleEndian.PutUint16(data[2:4], 200)   // 上报间隔200ms
	binary.LittleEndian.PutUint32(data[4:8], 60000) // 持续时间60秒
	data[8] = 0                                     // 变化上报：0-关闭

	// 订阅0xB0命令（查询载货状态）- 可选
	binary.LittleEndian.PutUint16(data[16:18], 0xB0)  // 命令码
	binary.LittleEndian.PutUint16(data[18:20], 1000)  // 上报间隔1000ms
	binary.LittleEndian.PutUint32(data[20:24], 60000) // 持续时间60秒
	data[24] = 0                                      // 变化上报：0-关闭

	// 生成UUID (从偏移128开始，64字节)
	uuid := fmt.Sprintf("agv-client-%d", time.Now().Unix())
	copy(data[128:], []byte(uuid))

	header, err := protocol.BuildHeader(c.authCode, c.seqNum, protocol.CmdSubscribe, dataLen)
	if err != nil {
		return fmt.Errorf("buildHeader error: %w", err)
	}

	packet := append(header, data...)

	fmt.Printf("Debug - 发送订阅命令: 序列号=%d, UUID=%s\n", c.seqNum, uuid)

	_, err = c.conn.Write(packet)
	if err != nil {
		return err
	}

	// 读取响应
	response := make([]byte, 1024)
	c.conn.SetReadDeadline(time.Now().Add(5 * time.Second))
	n, err := c.conn.Read(response)
	if err != nil {
		return fmt.Errorf("read response error: %w", err)
	}

	// 检查响应
	if n >= 28 {
		execCode := response[22]
		if execCode != 0x00 {
			agvLogger.Error("AGV订阅失败", "execCode", fmt.Sprintf("0x%02X", execCode))
			return fmt.Errorf("subscribe failed, exec code: 0x%02X", execCode)
		}

		// 检查订阅结果
		if n >= 28+68 {
			errCode := response[28+64]
			if errCode != 0x00 {
				agvLogger.Error("AGV订阅失败", "errorCode", fmt.Sprintf("0x%02X", errCode))
				return fmt.Errorf("subscribe failed, error code: 0x%02X", errCode)
			}
		}
	}

	agvLogger.Info("AGV状态订阅成功", "uuid", uuid)
	return nil
}

// QueryStatus 查询AGV状态 (0xAF)
func (c *Controller) QueryStatus() error {
	agvLogger := logger.GetModuleLogger("agv")
	if c.conn == nil {
		agvLogger.Error("AGV状态查询失败：未连接")
		return fmt.Errorf("未连接到AGV")
	}

	// 清空可能存在的旧响应
	c.clearReceiveBuffer()

	c.seqNum++
	expectedSeqNum := c.seqNum // 保存期望的序列号
	agvLogger.Debug("发送AGV状态查询", "sequenceNum", expectedSeqNum)

	// 0xAF命令无数据区
	header, err := protocol.BuildHeader(c.authCode, c.seqNum, protocol.CmdQueryStatus, 0)
	if err != nil {
		return fmt.Errorf("buildHeader error: %w", err)
	}

	fmt.Printf("Debug - 发送查询状态命令: 序列号=%d, 长度=%d字节\n", c.seqNum, len(header))

	_, err = c.conn.Write(header)
	if err != nil {
		agvLogger.Error("AGV状态查询命令发送失败", "error", err)
		return fmt.Errorf("发送查询命令失败: %w", err)
	}

	// 可能需要多次读取，直到收到正确的响应
	for retries := 0; retries < 3; retries++ {
		response := make([]byte, 1024)
		c.conn.SetReadDeadline(time.Now().Add(5 * time.Second))
		n, err := c.conn.Read(response)
		if err != nil {
			if retries == 2 {
				return fmt.Errorf("读取响应失败: %w", err)
			}
			continue
		}

		fmt.Printf("Debug - 接收到响应: 长度=%d字节\n", n)

		if n < 28 {
			continue
		}

		// 验证响应是否匹配
		respSeqNum := binary.LittleEndian.Uint16(response[18:20])
		respCmd := response[21]

		fmt.Printf("Debug - 响应验证: 序列号=%d(期望%d), 命令码=0x%02X(期望0xAF)\n",
			respSeqNum, expectedSeqNum, respCmd)

		if respSeqNum == expectedSeqNum && respCmd == protocol.CmdQueryStatus {
			// 正确的响应，解析它
			agvLogger.Debug("收到AGV状态响应", "responseSize", n)
			return c.parseStatusResponse(response[:n])
		}

		// 不是我们要的响应，继续读取
		fmt.Printf("Debug - 忽略不匹配的响应，继续读取...\n")
	}

	agvLogger.Error("AGV状态查询超时：3次尝试内未获得正确响应")
	return fmt.Errorf("未能在3次尝试内获得正确的响应")
}

// GetStatus 获取当前AGV状态
func (c *Controller) GetStatus() *Status {
	return c.status
}

// SwitchWorkMode 切换工作模式
func (c *Controller) SwitchWorkMode(isAuto bool) error {
	agvLogger := logger.GetModuleLogger("agv")
	if c.conn == nil {
		agvLogger.Error("AGV工作模式切换失败：未连接")
		return fmt.Errorf("未连接到AGV")
	}

	var modeValue uint32
	if isAuto {
		modeValue = 1 // 自动模式
	} else {
		modeValue = 0 // 手动模式
	}

	modeStr := map[bool]string{true: "自动", false: "手动"}[isAuto]
	agvLogger.Info("切换AGV工作模式", "targetMode", modeStr, "modeValue", modeValue)

	// 使用0x11命令直接切换模式
	err := c.SwitchWorkModeDirect(modeValue)
	if err != nil {
		agvLogger.Error("AGV工作模式切换失败", "targetMode", modeStr, "error", err)
		return err
	}
	agvLogger.Info("AGV工作模式切换成功", "newMode", modeStr)
	return nil
}

// WriteVariable 写变量值 (0x03)
func (c *Controller) WriteVariable(varName string, value uint32) error {
	agvLogger := logger.GetModuleLogger("agv")
	if c.conn == nil {
		agvLogger.Error("AGV变量写入失败：未连接", "variable", varName, "value", value)
		return fmt.Errorf("未连接到AGV")
	}

	agvLogger.Info("写入AGV变量", "variable", varName, "value", value, "sequenceNum", c.seqNum+1)
	c.seqNum++

	// 构建数据区
	// 数据结构：变量数量(1) + 预留(3) + 变量结构体
	// 变量结构体：变量名(16) + 成员数量(4) + 变量成员(8)
	dataLen := uint16(32) // 1+3+16+4+8 = 32字节

	data := make([]byte, dataLen)

	// 变量数量
	data[0] = 0x01 // 1个变量
	// data[1:4] 预留，已初始化为0

	// 变量名（16字节，ASCII编码）
	varNameBytes := []byte(varName)
	if len(varNameBytes) > 16 {
		varNameBytes = varNameBytes[:16]
	}
	copy(data[4:20], varNameBytes)

	// 变量成员数量
	binary.LittleEndian.PutUint32(data[20:24], 1)

	// 变量成员
	binary.LittleEndian.PutUint16(data[24:26], 0)     // 成员偏移 = 0
	binary.LittleEndian.PutUint16(data[26:28], 4)     // 成员长度 = 4
	binary.LittleEndian.PutUint32(data[28:32], value) // 成员值

	header, err := protocol.BuildHeader(c.authCode, c.seqNum, protocol.CmdWriteVariable, dataLen)
	if err != nil {
		return fmt.Errorf("buildHeader error: %w", err)
	}

	packet := append(header, data...)

	// 打印调试信息
	fmt.Printf("Debug - Writing variable %s = %d\n", varName, value)
	fmt.Printf("Debug - Packet length: %d bytes\n", len(packet))

	_, err = c.conn.Write(packet)
	if err != nil {
		return err
	}

	// 读取响应
	response := make([]byte, 1024)
	c.conn.SetReadDeadline(time.Now().Add(5 * time.Second))
	n, err := c.conn.Read(response)
	if err != nil {
		return fmt.Errorf("read response error: %w", err)
	}

	// 检查响应的执行码
	if n >= 28 {
		execCode := response[22]
		if execCode != 0x00 {
			agvLogger.Error("AGV变量写入失败", "variable", varName, "value", value, "execCode", fmt.Sprintf("0x%02X", execCode))
			return fmt.Errorf("write variable failed, exec code: 0x%02X", execCode)
		}
	}

	agvLogger.Info("AGV变量写入成功", "variable", varName, "value", value)
	return nil
}

// ManualPosition 机器人定位 (0x14) - 手动模式下设置机器人位置
func (c *Controller) ManualPosition(x, y, angle float64) error {
	agvLogger := logger.GetModuleLogger("agv")
	if c.conn == nil {
		agvLogger.Error("AGV手动定位失败：未连接", "x", x, "y", y, "angle", angle)
		return fmt.Errorf("未连接到AGV")
	}

	agvLogger.Info("设置AGV手动定位", "x", x, "y", y, "angle", angle, "sequenceNum", c.seqNum+1)
	c.seqNum++

	dataLen := uint16(24) // 3个FLOAT64，每个8字节
	data := make([]byte, dataLen)

	// 写入x坐标
	binary.LittleEndian.PutUint64(data[0:8], math.Float64bits(x))
	// 写入y坐标
	binary.LittleEndian.PutUint64(data[8:16], math.Float64bits(y))
	// 写入朝向角度（弧度）
	binary.LittleEndian.PutUint64(data[16:24], math.Float64bits(angle))

	header, err := protocol.BuildHeader(c.authCode, c.seqNum, protocol.CmdManualPosition, dataLen)
	if err != nil {
		return fmt.Errorf("buildHeader error: %w", err)
	}

	packet := append(header, data...)
	_, err = c.conn.Write(packet)
	if err != nil {
		agvLogger.Error("AGV手动定位命令发送失败", "x", x, "y", y, "angle", angle, "error", err)
		return err
	}
	agvLogger.Info("AGV手动定位命令发送成功", "x", x, "y", y, "angle", angle)
	return nil
}

// ConfirmReposition 确认重定位数据 (0x1F) - 手动定位后需要确认
func (c *Controller) ConfirmReposition() error {
	agvLogger := logger.GetModuleLogger("agv")
	if c.conn == nil {
		agvLogger.Error("AGV重定位确认失败：未连接")
		return fmt.Errorf("未连接到AGV")
	}

	agvLogger.Info("发送AGV重定位确认", "sequenceNum", c.seqNum+1)
	c.seqNum++

	// 0x1F命令无数据区
	header, err := protocol.BuildHeader(c.authCode, c.seqNum, protocol.CmdConfirmReposition, 0)
	if err != nil {
		return fmt.Errorf("buildHeader error: %w", err)
	}

	_, err = c.conn.Write(header)
	if err != nil {
		return err
	}

	// 读取响应
	response := make([]byte, 1024)
	c.conn.SetReadDeadline(time.Now().Add(5 * time.Second))
	n, err := c.conn.Read(response)
	if err != nil {
		return fmt.Errorf("read response error: %w", err)
	}

	// 检查响应的执行码
	if n >= 28 {
		execCode := response[22]
		if execCode != 0x00 {
			agvLogger.Error("AGV重定位确认失败", "execCode", fmt.Sprintf("0x%02X", execCode))
			return fmt.Errorf("confirm reposition failed, exec code: 0x%02X", execCode)
		}
	}

	agvLogger.Info("AGV重定位确认成功")
	return nil
}

// 解析AGV状态响应
func (c *Controller) parseStatusResponse(data []byte) error {
	agvLogger := logger.GetModuleLogger("agv")

	// 打印原始响应数据的前32字节(如果有)
	hexHeader := ""
	for i := 0; i < min(32, len(data)); i++ {
		hexHeader += fmt.Sprintf("%02X ", data[i])
	}
	fmt.Printf("Debug - 响应头部: %s\n", hexHeader)

	if len(data) < 28 {
		return fmt.Errorf("response too short: %d bytes", len(data))
	}

	// 检查执行码
	execCode := data[22]
	if execCode != 0x00 {
		return fmt.Errorf("command execution failed, exec code: 0x%02X", execCode)
	}

	// 获取数据区长度
	dataLen := binary.LittleEndian.Uint16(data[24:26])
	fmt.Printf("Debug - 数据区长度: %d字节\n", dataLen)

	if len(data) < 28+int(dataLen) {
		return fmt.Errorf("incomplete response data: expected %d bytes, got %d bytes", 28+int(dataLen), len(data))
	}

	payload := data[28:]
	if len(payload) < 4 {
		return fmt.Errorf("payload too short for header: %d bytes", len(payload))
	}

	// 解析响应头部信息（前4个字节）
	abnormalSize := payload[0] // 异常事件状态信息长度
	actionSize := payload[1]   // 动作状态长度
	infoSize := payload[2]     // 信息数量
	// reserved := payload[3]   // 预留

	fmt.Printf("Debug - 响应信息: 异常事件=%d, 动作状态=%d, 信息数量=%d\n",
		abnormalSize, actionSize, infoSize)

	// 数据起始位置从第4字节开始
	dataStart := 4

	if c.status == nil {
		c.status = &Status{}
	}

	// 解析位置状态信息 (LocationStatusInfo - 32字节)
	if len(payload) < dataStart+32 {
		return fmt.Errorf("payload too short for LocationStatusInfo: %d bytes", len(payload))
	}

	locOffset := dataStart
	c.status.PosX = math.Float32frombits(binary.LittleEndian.Uint32(payload[locOffset : locOffset+4]))
	c.status.PosY = math.Float32frombits(binary.LittleEndian.Uint32(payload[locOffset+4 : locOffset+8]))
	c.status.Angle = math.Float32frombits(binary.LittleEndian.Uint32(payload[locOffset+8 : locOffset+12]))
	c.status.LastPointID = binary.LittleEndian.Uint32(payload[locOffset+12 : locOffset+16])
	c.status.LastPathID = binary.LittleEndian.Uint32(payload[locOffset+16 : locOffset+20])
	c.status.PointSequenceID = binary.LittleEndian.Uint32(payload[locOffset+20 : locOffset+24])
	c.status.Confidence = payload[locOffset+24]

	// 解析运行状态信息 (RunningStatusInfo - 20字节)
	runOffset := 36 // 0x24的十进制值
	if len(payload) >= runOffset+20 {
		c.status.VelX = math.Float32frombits(binary.LittleEndian.Uint32(payload[runOffset : runOffset+4]))
		c.status.VelY = math.Float32frombits(binary.LittleEndian.Uint32(payload[runOffset+4 : runOffset+8]))
		c.status.AngVel = math.Float32frombits(binary.LittleEndian.Uint32(payload[runOffset+8 : runOffset+12]))
		c.status.WorkMode = payload[runOffset+12]
		c.status.AGVState = payload[runOffset+13]
		c.status.CapabilitySetStatus = payload[runOffset+14]
	}

	// 解析任务状态信息 (TaskStatusInfo - 变长)
	taskOffset := 56 // 0x38的十进制值
	var pointSize, pathSize uint8
	if len(payload) >= taskOffset+12 {
		c.status.OrderID = binary.LittleEndian.Uint32(payload[taskOffset : taskOffset+4])
		c.status.TaskKey = binary.LittleEndian.Uint32(payload[taskOffset+4 : taskOffset+8])
		pointSize = payload[taskOffset+8]
		pathSize = payload[taskOffset+9]
	}

	// 计算电池状态的偏移位置
	batteryOffset := taskOffset + 12 + int(pointSize)*8 + int(pathSize)*8

	// 解析电池状态信息 (BatteryStatusInfo - 20字节)
	if len(payload) >= batteryOffset+20 {
		c.status.BatteryPercent = math.Float32frombits(binary.LittleEndian.Uint32(payload[batteryOffset : batteryOffset+4]))
		c.status.BatteryVoltage = math.Float32frombits(binary.LittleEndian.Uint32(payload[batteryOffset+4 : batteryOffset+8]))
		c.status.BatteryCurrent = math.Float32frombits(binary.LittleEndian.Uint32(payload[batteryOffset+8 : batteryOffset+12]))
		c.status.ChargingStatus = payload[batteryOffset+12]
	}

	// 计算异常事件和动作状态的偏移位置
	abnormalOffset := batteryOffset + 20
	actionOffset := abnormalOffset + int(abnormalSize)*12 // 每个异常事件12字节

	// 解析异常事件状态信息（如果有）
	if abnormalSize > 0 && len(payload) >= abnormalOffset+12 {
		// 这里只解析第一个异常事件，实际可能有多个
		c.status.EventCode = binary.LittleEndian.Uint16(payload[abnormalOffset : abnormalOffset+2])
		c.status.EventLevel = binary.LittleEndian.Uint16(payload[abnormalOffset+2 : abnormalOffset+4])
		// 后面8字节是预留
	}

	// 解析动作状态信息（如果有）
	if actionSize > 0 && len(payload) >= actionOffset+12 {
		// 根据文档，ActionInfo结构：
		// - 动作ID (4字节)
		// - 动作状态 (1字节)
		// - 预留 (7字节)
		// 这里可以添加动作状态的解析
		actionID := binary.LittleEndian.Uint32(payload[actionOffset : actionOffset+4])
		actionStatus := payload[actionOffset+4]

		// 打印动作状态用于调试
		if actionID != 0 {
			actionStatusText := map[uint8]string{
				0x00: "等待", 0x01: "初始化中", 0x02: "执行中",
				0x03: "完成", 0x04: "失败", 0x05: "取消", 0x06: "暂停",
			}[actionStatus]
			fmt.Printf("Debug - Action: ID=%d, Status=%s\n", actionID, actionStatusText)
		}
	}

	// 打印调试信息
	fmt.Printf("Debug - Header: abnormalSize=%d, actionSize=%d\n", abnormalSize, actionSize)
	fmt.Printf("Debug - Raw position data: X=%f, Y=%f, Angle=%f rad (%.2f°)\n",
		c.status.PosX, c.status.PosY, c.status.Angle, c.status.Angle*180/math.Pi)
	fmt.Printf("Debug - Velocity: VelX=%f, VelY=%f, AngVel=%f\n",
		c.status.VelX, c.status.VelY, c.status.AngVel)
	fmt.Printf("Debug - Battery: %.1f%%, Voltage=%.2fV, Current=%.2fA\n",
		c.status.BatteryPercent*100, c.status.BatteryVoltage, c.status.BatteryCurrent)

	// 记录状态更新日志
	agvLogger.Info("AGV状态更新",
		"position", fmt.Sprintf("(%.3f, %.3f)", c.status.PosX, c.status.PosY),
		"angle", fmt.Sprintf("%.2f°", c.status.Angle*180/math.Pi),
		"workMode", c.status.WorkModeString(),
		"agvState", c.status.AGVStateString(),
		"batteryPercent", fmt.Sprintf("%.1f%%", c.status.BatteryPercent*100),
		"confidence", fmt.Sprintf("%d%%", c.status.Confidence))

	// 通知UI更新
	c.notifyStatusUpdated()
	return nil
}

// 通知状态更新 (已移除UI更新逻辑，状态通过WebSocket API广播)
func (c *Controller) notifyStatusUpdated() {
	// 状态更新现在通过API Server的WebSocket自动广播给前端
	// 这个方法保留以维持接口兼容性，但不再执行UI更新
}

// InitializeLaserNavigation 激光导航初始化流程
func (c *Controller) InitializeLaserNavigation(x, y, angle float64) error {
	agvLogger := logger.GetModuleLogger("agv")
	if c.conn == nil {
		agvLogger.Error("AGV激光导航初始化失败：未连接", "x", x, "y", y, "angle", angle)
		return fmt.Errorf("未连接到AGV")
	}

	agvLogger.Info("开始AGV激光导航初始化流程", "targetX", x, "targetY", y, "targetAngle", angle)

	// 步骤1: 切换到手动模式
	agvLogger.Info("激光导航步骤1: 切换到手动模式")
	err := c.SwitchWorkModeDirect(0) // 使用0x11命令
	if err != nil {
		agvLogger.Error("激光导航步骤1失败", "error", err)
		return fmt.Errorf("切换手动模式失败: %w", err)
	}
	time.Sleep(1 * time.Second)

	// 验证是否切换成功
	err = c.QueryStatus()
	if err != nil {
		return fmt.Errorf("查询状态失败: %w", err)
	}
	if c.status.WorkMode != 0x01 { // 手动模式
		agvLogger.Error("激光导航步骤1失败：工作模式切换未成功", "currentMode", c.status.WorkModeString())
		return fmt.Errorf("切换手动模式失败，当前模式: %s", c.status.WorkModeString())
	}

	// 等待AGV停止移动
	fmt.Println("等待AGV停止...")
	for i := 0; i < 10; i++ {
		err = c.QueryStatus()
		if err == nil && c.status != nil {
			// 检查速度是否为0
			if math.Abs(float64(c.status.VelX)) < 0.001 &&
				math.Abs(float64(c.status.VelY)) < 0.001 &&
				math.Abs(float64(c.status.AngVel)) < 0.001 {
				fmt.Println("AGV已停止")
				break
			}
		}
		time.Sleep(500 * time.Millisecond)
	}

	// 步骤2: 执行手动定位（使用AGV当前实际位置）
	agvLogger.Info("激光导航步骤2: 设置手动定位", "x", x, "y", y, "angle", angle)
	err = c.ManualPosition(x, y, angle)
	if err != nil {
		agvLogger.Error("激光导航步骤2失败", "error", err)
		return fmt.Errorf("手动定位失败: %w", err)
	}
	time.Sleep(2 * time.Second) // 给AGV更多时间完成定位计算

	// 步骤3: 查询状态，等待定位完成且AGV空闲
	fmt.Println("步骤3: 等待定位完成...")

	// 尝试使用0x17命令获取更详细的定位状态
	if runStatus, err := c.QueryRunStatus(); err == nil {
		// 如果支持0x17命令，使用它的定位状态
		if runStatus.PositioningStatus == 2 { // 假设2表示定位完成
			fmt.Println("定位已完成（通过0x17确认）")
		}
	} else {
		// 如果0x17命令不可用，继续使用原来的方法
		fmt.Printf("0x17命令不可用: %v，使用0xAF命令\n", err)
	}

	positionStable := false
	var lastX, lastY, lastAngle float32
	stableCount := 0

	for i := 0; i < 20; i++ { // 增加尝试次数
		err = c.QueryStatus()
		if err != nil {
			fmt.Printf("查询状态失败: %v\n", err)
			time.Sleep(1 * time.Second)
			continue
		}

		// 检查AGV状态是否为空闲
		if c.status.AGVState != 0x00 {
			fmt.Printf("等待AGV空闲，当前状态: %s\n", c.status.AGVStateString())
			time.Sleep(1 * time.Second)
			continue
		}

		// 检查位置是否稳定
		if i > 0 {
			deltaX := math.Abs(float64(c.status.PosX - lastX))
			deltaY := math.Abs(float64(c.status.PosY - lastY))
			deltaAngle := math.Abs(float64(c.status.Angle - lastAngle))

			if deltaX < 3 && deltaY < 3 && deltaAngle < 5 {
				stableCount++
				if stableCount >= 3 { // 连续3次位置稳定
					positionStable = true
					break
				}
			} else {
				stableCount = 0
			}
		}

		lastX = c.status.PosX
		lastY = c.status.PosY
		lastAngle = c.status.Angle

		time.Sleep(1 * time.Second)
	}

	if !positionStable {
		return fmt.Errorf("定位未能稳定")
	}

	// 检查是否有异常事件
	if c.status.EventCode != 0 {
		fmt.Printf("警告：存在异常事件 0x%04X，等级: %d\n", c.status.EventCode, c.status.EventLevel)
		// 暂时忽略等级2的异常，只有等级3及以上才阻止流程
		if c.status.EventLevel >= 0x03 { // 严重错误级别
			return fmt.Errorf("存在严重错误级别异常事件: 0x%04X", c.status.EventCode)
		}
		fmt.Printf("异常等级%d可忽略，继续执行流程\n", c.status.EventLevel)
	}

	agvLogger.Info("激光导航定位稳定", "confidence", c.status.Confidence, "posX", c.status.PosX, "posY", c.status.PosY)

	// 步骤4: 确认重定位
	agvLogger.Info("激光导航步骤4: 确认重定位")
	err = c.ConfirmReposition()
	if err != nil {
		agvLogger.Error("激光导航步骤4失败", "error", err)
		return fmt.Errorf("确认重定位失败: %w", err)
	}
	time.Sleep(1 * time.Second)

	// 步骤5: 切换到自动模式
	agvLogger.Info("激光导航步骤5: 切换到自动模式")
	err = c.SwitchWorkModeDirect(1) // 使用0x11命令
	if err != nil {
		agvLogger.Error("激光导航步骤5失败", "error", err)
		return fmt.Errorf("切换自动模式失败: %w", err)
	}

	// 验证切换成功
	time.Sleep(1 * time.Second)
	err = c.QueryStatus()
	if err == nil && c.status.WorkMode != 0x03 { // 自动模式
		agvLogger.Error("激光导航步骤5失败：自动模式切换未成功", "currentMode", c.status.WorkModeString())
		return fmt.Errorf("切换自动模式失败，当前模式: %s", c.status.WorkModeString())
	}

	agvLogger.Info("AGV激光导航初始化完成", "finalMode", c.status.WorkModeString())
	return nil
}

// 在其他命令函数附近添加这个新函数
// QueryRunStatus 查询机器人运行状态 (0x17) - 激光导航专用
func (c *Controller) QueryRunStatus() (*RunStatus, error) {
	if c.conn == nil {
		return nil, fmt.Errorf("未连接到AGV")
	}

	c.seqNum++

	header, err := protocol.BuildHeader(c.authCode, c.seqNum, protocol.CmdQueryRunStatus, 0)
	if err != nil {
		return nil, fmt.Errorf("buildHeader error: %w", err)
	}

	_, err = c.conn.Write(header)
	if err != nil {
		return nil, err
	}

	// 读取响应
	response := make([]byte, 1024)
	c.conn.SetReadDeadline(time.Now().Add(5 * time.Second))
	n, err := c.conn.Read(response)
	if err != nil {
		return nil, fmt.Errorf("read response error: %w", err)
	}

	// 检查响应
	if n < 28 {
		return nil, fmt.Errorf("response too short: %d bytes", n)
	}

	// 验证响应头部
	respSeqNum := binary.LittleEndian.Uint16(response[18:20])
	respCmd := response[21]
	execCode := response[22]

	// 打印响应头部调试信息
	fmt.Printf("Debug - 0x17响应头部: 序列号=%d, 命令码=0x%02X, 执行码=0x%02X\n",
		respSeqNum, respCmd, execCode)

	// 验证是否是我们期望的响应
	if respCmd != protocol.CmdQueryRunStatus {
		return nil, fmt.Errorf("unexpected command code: 0x%02X", respCmd)
	}

	if execCode != 0x00 {
		return nil, fmt.Errorf("query run status failed, exec code: 0x%02X", execCode)
	}

	// 获取数据区长度
	dataLen := binary.LittleEndian.Uint16(response[24:26])
	fmt.Printf("Debug - 0x17数据区长度: %d字节\n", dataLen)

	if len(response) < 28+int(dataLen) {
		return nil, fmt.Errorf("incomplete response data")
	}

	// 打印原始响应数据用于调试
	if dataLen > 0 {
		fmt.Printf("Debug - 0x17原始响应数据: ")
		for i := 0; i < int(dataLen) && i < 32; i++ {
			fmt.Printf("%02X ", response[28+i])
		}
		if dataLen > 32 {
			fmt.Printf("... (共%d字节)", dataLen)
		}
		fmt.Println()
	}

	// 解析运行状态数据
	runStatus := &RunStatus{}

	if dataLen >= 1 {
		payload := response[28:]
		runStatus.PositioningStatus = payload[0]

		// 如果有更多数据，保存到Reserved字段供后续分析
		if dataLen > 1 {
			runStatus.Reserved = make([]byte, dataLen-1)
			copy(runStatus.Reserved, payload[1:dataLen])
		}

		// 打印解析后的信息
		posStatusText := map[uint8]string{
			0: "未定位",
			1: "定位中",
			2: "定位完成",
		}
		statusText, ok := posStatusText[runStatus.PositioningStatus]
		if !ok {
			statusText = fmt.Sprintf("未知状态(%d)", runStatus.PositioningStatus)
		}
		fmt.Printf("Debug - 0x17解析结果: 定位状态=%s\n", statusText)

		// 如果有额外数据，打印供分析
		if len(runStatus.Reserved) > 0 {
			fmt.Printf("Debug - 0x17额外数据(未解析): ")
			for i, b := range runStatus.Reserved {
				if i < 16 { // 只打印前16字节
					fmt.Printf("%02X ", b)
				}
			}
			if len(runStatus.Reserved) > 16 {
				fmt.Printf("... (共%d字节)", len(runStatus.Reserved))
			}
			fmt.Println()
		}
	}

	return runStatus, nil
}

// SwitchWorkModeDirect 使用0x11命令直接切换工作模式
func (c *Controller) SwitchWorkModeDirect(mode uint32) error {
	if c.conn == nil {
		return fmt.Errorf("未连接到AGV")
	}

	c.seqNum++

	// 构建数据区 - 4字节的uint32
	dataLen := uint16(4)
	data := make([]byte, dataLen)
	binary.LittleEndian.PutUint32(data, mode)

	header, err := protocol.BuildHeader(c.authCode, c.seqNum, protocol.CmdSwitchMode, dataLen)
	if err != nil {
		return fmt.Errorf("buildHeader error: %w", err)
	}

	packet := append(header, data...)

	fmt.Printf("Debug - Switching to mode %d using 0x11 command\n", mode)

	_, err = c.conn.Write(packet)
	if err != nil {
		return err
	}

	// 读取响应
	response := make([]byte, 1024)
	c.conn.SetReadDeadline(time.Now().Add(5 * time.Second))
	n, err := c.conn.Read(response)
	if err != nil {
		return fmt.Errorf("read response error: %w", err)
	}

	// 检查响应的执行码
	if n >= 28 {
		execCode := response[22]
		if execCode != 0x00 {
			return fmt.Errorf("switch mode failed, exec code: 0x%02X", execCode)
		}
	}

	return nil
}

// QueryNavigationStatus 查询AGV导航状态 (0x1D)
func (c *Controller) QueryNavigationStatus() error {
	agvLogger := logger.GetModuleLogger("agv")
	if c.conn == nil {
		agvLogger.Error("AGV导航状态查询失败：未连接")
		return fmt.Errorf("未连接到AGV")
	}

	// 清空可能存在的旧响应
	c.clearReceiveBuffer()

	c.seqNum++
	expectedSeqNum := c.seqNum // 保存期望的序列号
	agvLogger.Debug("发送AGV导航状态查询", "sequenceNum", expectedSeqNum)

	// 0x1D命令无数据区
	header, err := protocol.BuildHeader(c.authCode, c.seqNum, protocol.CmdQueryNavStatus, 0)
	if err != nil {
		return fmt.Errorf("buildHeader error: %w", err)
	}

	fmt.Printf("Debug - 发送查询导航状态命令: 序列号=%d, 长度=%d字节\n", c.seqNum, len(header))

	_, err = c.conn.Write(header)
	if err != nil {
		agvLogger.Error("AGV导航状态查询命令发送失败", "error", err)
		return fmt.Errorf("发送查询命令失败: %w", err)
	}

	// 可能需要多次读取，直到收到正确的响应
	for retries := 0; retries < 3; retries++ {
		response := make([]byte, 1024)
		c.conn.SetReadDeadline(time.Now().Add(5 * time.Second))
		n, err := c.conn.Read(response)
		if err != nil {
			if retries == 2 {
				return fmt.Errorf("读取响应失败: %w", err)
			}
			continue
		}

		agvLogger.Info("Debug - 接收到导航状态响应: 长度=%d字节\n", n)

		if n < 28 {
			continue
		}

		// 验证响应是否匹配
		respSeqNum := binary.LittleEndian.Uint16(response[18:20])
		respCmd := response[21]

		agvLogger.Info("Debug - 导航状态响应验证: 序列号=%d(期望%d), 命令码=0x%02X(期望0x1D)\n",
			respSeqNum, expectedSeqNum, respCmd)

		if respSeqNum == expectedSeqNum && respCmd == protocol.CmdQueryNavStatus {
			// 正确的响应，解析它
			agvLogger.Debug("收到AGV导航状态响应", "responseSize", n)
			return c.parseNavigationStatusResponse(response[:n])
		}

		// 不是我们要的响应，继续读取
		agvLogger.Info("Debug - 忽略不匹配的导航状态响应，继续读取...\n")
	}

	agvLogger.Error("AGV导航状态查询超时：3次尝试内未获得正确响应")
	return fmt.Errorf("未能在3次尝试内获得正确的响应")
}

// parseNavigationStatusResponse 解析导航状态响应
func (c *Controller) parseNavigationStatusResponse(data []byte) error {
	agvLogger := logger.GetModuleLogger("agv")

	// 打印原始响应数据的前32字节(如果有)
	hexHeader := ""
	for i := 0; i < min(32, len(data)); i++ {
		hexHeader += fmt.Sprintf("%02X ", data[i])
	}
	agvLogger.Info("Debug - 导航状态响应头部: %s\n", hexHeader)

	if len(data) < 28 {
		agvLogger.Error("response too short: %d bytes", len(data))
		return fmt.Errorf("response too short: %d bytes", len(data))
	}

	// 检查执行码
	execCode := data[22]
	if execCode != 0x00 {
		agvLogger.Error("command execution failed, exec code: 0x%02X", execCode)
		return fmt.Errorf("command execution failed, exec code: 0x%02X", execCode)
	}

	// 获取数据区长度
	dataLen := binary.LittleEndian.Uint16(data[24:26])
	agvLogger.Info("Debug - 导航状态数据区长度: %d字节\n", dataLen)

	if len(data) < 28+int(dataLen) {
		return fmt.Errorf("incomplete response data: expected %d bytes, got %d bytes", 28+int(dataLen), len(data))
	}

	// 根据PDF文档，导航状态响应的数据区应该是512字节
	// 偏移0x00: 状态(1字节) + 偏移0x01: 保留(3字节) + 偏移0x04: 目标点ID(2字节) + 偏移0x06: 保留(2字节)
	// + 偏移0x08: 已经过路径点(126*2字节) + 偏移0x104: 未经过路径点(126*2字节) = 512字节
	expectedDataLen := 512
	if int(dataLen) < expectedDataLen {
		agvLogger.Warn("导航状态数据长度不足，可能是部分数据", "expected", expectedDataLen, "actual", dataLen)
	}

	payload := data[28:]
	if len(payload) < 8 {
		agvLogger.Error("payload too short for navigation status", "len", len(payload))
		return fmt.Errorf("payload too short for navigation status: %d bytes", len(payload))
	}

	if c.navStatus == nil {
		c.navStatus = &NavigationStatus{}
	}

	// 解析导航状态数据
	// 偏移0x00: 状态 (1字节)
	c.navStatus.Status = payload[0]

	// 偏移0x01-0x03: 保留字节，跳过

	// 偏移0x04: 目标点ID (2字节，小端)
	if len(payload) >= 6 {
		c.navStatus.TargetPointID = binary.LittleEndian.Uint16(payload[4:6])
	}

	// 偏移0x06-0x07: 保留字节，跳过

	// 偏移0x08: 已经过的路径点ID (126个uint16，小端)
	passedPointsOffset := 8
	if len(payload) >= passedPointsOffset+126*2 {
		c.navStatus.PassedPathPoints = make([]uint16, 126)
		for i := 0; i < 126; i++ {
			offset := passedPointsOffset + i*2
			c.navStatus.PassedPathPoints[i] = binary.LittleEndian.Uint16(payload[offset : offset+2])
		}
	}

	// 偏移0x104: 未经过的路径点ID (126个uint16，小端)
	remainingPointsOffset := 8 + 126*2 // 0x104 = 260
	if len(payload) >= remainingPointsOffset+126*2 {
		c.navStatus.RemainingPathPoints = make([]uint16, 126)
		for i := 0; i < 126; i++ {
			offset := remainingPointsOffset + i*2
			c.navStatus.RemainingPathPoints[i] = binary.LittleEndian.Uint16(payload[offset : offset+2])
		}
	}

	// 打印调试信息

	agvLogger.Info("Debug - 导航状态解析结果:\n",
		"状态", c.navStatus.GetStatusString(),
		"目标点ID", c.navStatus.TargetPointID,
		"已经过路径点数量", len(c.navStatus.GetValidPassedPoints()),
		"未经过路径点数量", len(c.navStatus.GetValidRemainingPoints()),
		"是否在导航中", c.navStatus.IsNavigating())

	// 记录状态更新日志
	agvLogger.Info("AGV导航状态更新",
		"navStatus", c.navStatus.GetStatusString(),
		"targetPointID", c.navStatus.TargetPointID,
		"passedPoints", len(c.navStatus.GetValidPassedPoints()),
		"remainingPoints", len(c.navStatus.GetValidRemainingPoints()),
		"isNavigating", c.navStatus.IsNavigating())

	return nil
}

// GetNavigationStatus 获取当前AGV导航状态
func (c *Controller) GetNavigationStatus() *NavigationStatus {
	return c.navStatus
}
