"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path_1 = require("path");
const child_process_1 = require("child_process");
const fs_1 = require("fs");
// 定义全局变量
let mainWindow = null;
let backendProcess = null;
let backendStarted = false;
const isDev = process.env.NODE_ENV === 'development';
const BACKEND_PORT = '8080';
// 获取用户数据目录
function getUserDataPath() {
    return electron_1.app.getPath('userData');
}
// 获取后端可执行文件路径
function getBackendPath() {
    if (isDev) {
        // 开发模式：使用相对路径
        return process.platform === 'win32' ?
            (0, path_1.join)(process.cwd(), '../backend/agv_nav.exe') :
            (0, path_1.join)(process.cwd(), '../backend/agv_nav');
    }
    else {
        // 生产模式：使用资源路径
        const basePath = (0, path_1.join)(process.resourcesPath, 'agv-backend');
        return process.platform === 'win32' ? `${basePath}.exe` : basePath;
    }
}
// 获取后端工作目录
function getBackendWorkingDirectory() {
    if (isDev) {
        return (0, path_1.join)(process.cwd(), '../backend');
    }
    else {
        // 生产模式：使用用户数据目录
        const userDataPath = getUserDataPath();
        const backendDataPath = (0, path_1.join)(userDataPath, 'backend-data');
        // 确保目录存在
        try {
            if (!(0, fs_1.existsSync)(backendDataPath)) {
                (0, fs_1.mkdirSync)(backendDataPath, { recursive: true });
            }
            // 复制配置文件到用户数据目录
            copyBackendResourcesIfNeeded(backendDataPath);
        }
        catch (error) {
            console.error('创建后端数据目录失败:', error);
        }
        return backendDataPath;
    }
}
// 复制后端资源到用户数据目录
function copyBackendResourcesIfNeeded(targetPath) {
    const fs = require('fs');
    const path = require('path');
    try {
        // 复制配置文件
        const configSrc = (0, path_1.join)(process.resourcesPath, 'backend', 'config');
        const configDest = (0, path_1.join)(targetPath, 'config');
        if ((0, fs_1.existsSync)(configSrc) && !(0, fs_1.existsSync)(configDest)) {
            console.log('复制配置文件到用户数据目录...');
            fs.cpSync(configSrc, configDest, { recursive: true });
        }
        // 复制数据文件
        const dataSrc = (0, path_1.join)(process.resourcesPath, 'backend', 'data');
        const dataDest = (0, path_1.join)(targetPath, 'data');
        if ((0, fs_1.existsSync)(dataSrc) && !(0, fs_1.existsSync)(dataDest)) {
            console.log('复制数据文件到用户数据目录...');
            fs.cpSync(dataSrc, dataDest, { recursive: true });
        }
        // 创建logs目录
        const logsPath = (0, path_1.join)(targetPath, 'logs');
        if (!(0, fs_1.existsSync)(logsPath)) {
            (0, fs_1.mkdirSync)(logsPath, { recursive: true });
        }
    }
    catch (error) {
        console.error('复制后端资源失败:', error);
    }
}
// 检查后端文件是否存在
function checkBackendFile() {
    const backendPath = getBackendPath();
    const exists = (0, fs_1.existsSync)(backendPath);
    if (!exists) {
        console.error(`❌ 后端文件不存在: ${backendPath}`);
        electron_1.dialog.showErrorBox('错误', `找不到后端服务文件：${backendPath}`);
    }
    return exists;
}
// 启动Go后端服务
function startBackend() {
    return new Promise((resolve) => {
        if (backendStarted) {
            console.log('后端服务已经在运行');
            resolve(true);
            return;
        }
        if (isDev) {
            // 开发模式：假设后端已经在运行
            console.log('🔧 开发模式：跳过后端启动，假设后端已在端口8080运行');
            backendStarted = true;
            resolve(true);
            return;
        }
        if (!checkBackendFile()) {
            resolve(false);
            return;
        }
        try {
            const backendPath = getBackendPath();
            console.log('🚀 启动后端服务:', backendPath);
            // 设置工作目录为用户数据目录
            const cwd = getBackendWorkingDirectory();
            console.log('🔧 后端工作目录:', cwd);
            backendProcess = (0, child_process_1.spawn)(backendPath, ['--port', BACKEND_PORT], {
                stdio: ['pipe', 'pipe', 'pipe'],
                cwd: cwd,
                env: { ...process.env }
            });
            // 设置超时机制
            const startupTimeout = setTimeout(() => {
                console.error('❌ 后端启动超时');
                if (backendProcess) {
                    backendProcess.kill();
                    backendProcess = null;
                }
                resolve(false);
            }, 10000); // 10秒超时
            backendProcess.stdout?.on('data', (data) => {
                const output = data.toString();
                console.log('📊 后端输出:', output);
                // 检查是否启动成功
                if (output.includes('server started') || output.includes('listening on')) {
                    console.log('✅ 后端服务启动成功');
                    backendStarted = true;
                    clearTimeout(startupTimeout);
                    resolve(true);
                }
            });
            backendProcess.stderr?.on('data', (data) => {
                const error = data.toString();
                console.error('⚠️ 后端错误:', error);
                // 某些错误信息可能不是致命的
                if (error.includes('FATAL') || error.includes('panic')) {
                    clearTimeout(startupTimeout);
                    resolve(false);
                }
            });
            backendProcess.on('exit', (code, signal) => {
                console.log(`🔚 后端进程退出，代码: ${code}, 信号: ${signal}`);
                backendProcess = null;
                backendStarted = false;
                // 如果非正常退出，尝试重启
                if (code !== 0 && code !== null) {
                    console.log('🔄 检测到后端异常退出，尝试重启...');
                    setTimeout(() => {
                        startBackend();
                    }, 3000);
                }
                clearTimeout(startupTimeout);
                resolve(false);
            });
            backendProcess.on('error', (error) => {
                console.error('❌ 启动后端失败:', error);
                backendProcess = null;
                backendStarted = false;
                clearTimeout(startupTimeout);
                resolve(false);
            });
            // 如果3秒后还没有收到启动成功信息，假设启动成功
            setTimeout(() => {
                if (backendProcess && !backendStarted) {
                    console.log('⏰ 后端启动超时，假设启动成功');
                    backendStarted = true;
                    clearTimeout(startupTimeout);
                    resolve(true);
                }
            }, 3000);
        }
        catch (error) {
            console.error('❌ 启动后端服务失败:', error);
            resolve(false);
        }
    });
}
// 停止后端服务
function stopBackend() {
    return new Promise((resolve) => {
        if (!backendProcess) {
            resolve();
            return;
        }
        console.log('🛑 停止后端服务...');
        // 优雅关闭
        backendProcess.kill('SIGTERM');
        // 如果5秒后还没有关闭，强制关闭
        const forceKillTimeout = setTimeout(() => {
            if (backendProcess) {
                console.log('🔨 强制关闭后端进程');
                backendProcess.kill('SIGKILL');
            }
        }, 5000);
        backendProcess.on('exit', () => {
            clearTimeout(forceKillTimeout);
            backendProcess = null;
            backendStarted = false;
            console.log('✅ 后端服务已停止');
            resolve();
        });
    });
}
// 创建主窗口
async function createMainWindow() {
    // 根据环境确定图标路径
    const iconPath = isDev
        ? (0, path_1.join)(__dirname, '../assets/icon.ico')
        : (0, path_1.join)(process.resourcesPath, 'assets/icon.ico');
    mainWindow = new electron_1.BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 1000,
        minHeight: 600,
        icon: iconPath,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            webSecurity: true,
        },
        titleBarStyle: 'default',
        show: true, // 立即显示窗口
        title: 'AGV Controller'
    });
    // 加载应用内容
    if (isDev) {
        await loadDevContent();
    }
    else {
        await loadProdContent();
    }
    // 处理窗口关闭
    mainWindow.on('closed', () => {
        mainWindow = null;
    });
    // 处理外部链接
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        electron_1.shell.openExternal(url);
        return { action: 'deny' };
    });
}
// 加载开发模式内容
async function loadDevContent() {
    const tryPorts = [5173, 5174, 5175, 5176, 5177];
    let loaded = false;
    const tryLoadURL = async (port) => {
        return new Promise((resolve) => {
            const url = `http://localhost:${port}`;
            console.log(`🔍 尝试连接到: ${url}`);
            const timeoutId = setTimeout(() => {
                console.log(`⏰ 连接超时: ${url}`);
                resolve(false);
            }, 3000); // 减少超时时间
            mainWindow?.loadURL(url).then(() => {
                clearTimeout(timeoutId);
                console.log(`✅ 成功连接到Vite开发服务器: ${url}`);
                resolve(true);
            }).catch((error) => {
                clearTimeout(timeoutId);
                console.log(`❌ 连接失败: ${url} - ${error.message}`);
                resolve(false);
            });
        });
    };
    // 等待Vite服务器启动
    console.log('⏳ 等待Vite开发服务器启动...');
    await new Promise(resolve => setTimeout(resolve, 2000)); // 减少等待时间
    for (const port of tryPorts) {
        if (await tryLoadURL(port)) {
            loaded = true;
            break;
        }
    }
    if (!loaded) {
        console.error('❌ 无法连接到Vite开发服务器');
        mainWindow?.loadURL('data:text/html,<h1>无法连接到开发服务器</h1><p>请确保前端开发服务器正在运行</p>');
    }
    // 开发模式打开开发者工具
    mainWindow?.webContents.openDevTools();
}
// 加载生产模式内容
async function loadProdContent() {
    const htmlPath = (0, path_1.join)(__dirname, '../dist/index.html');
    if (!(0, fs_1.existsSync)(htmlPath)) {
        console.error('❌ 找不到打包的HTML文件:', htmlPath);
        electron_1.dialog.showErrorBox('错误', `找不到应用文件：${htmlPath}`);
        return;
    }
    try {
        await mainWindow?.loadFile(htmlPath);
        console.log('✅ 生产模式内容加载成功');
    }
    catch (error) {
        console.error('❌ 加载生产模式内容失败:', error);
        electron_1.dialog.showErrorBox('错误', '加载应用内容失败');
    }
}
// 创建应用菜单
function createMenu() {
    const template = [
        {
            label: '文件',
            submenu: [
                {
                    label: '重启后端服务',
                    click: async () => {
                        await stopBackend();
                        await startBackend();
                    }
                },
                { type: 'separator' },
                {
                    label: '退出',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        electron_1.app.quit();
                    }
                }
            ]
        },
        {
            label: '编辑',
            submenu: [
                { role: 'undo', label: '撤销' },
                { role: 'redo', label: '重做' },
                { type: 'separator' },
                { role: 'cut', label: '剪切' },
                { role: 'copy', label: '复制' },
                { role: 'paste', label: '粘贴' }
            ]
        },
        {
            label: '视图',
            submenu: [
                { role: 'reload', label: '重新加载' },
                { role: 'forceReload', label: '强制重新加载' },
                { role: 'toggleDevTools', label: '开发者工具' },
                { type: 'separator' },
                { role: 'resetZoom', label: '重置缩放' },
                { role: 'zoomIn', label: '放大' },
                { role: 'zoomOut', label: '缩小' },
                { type: 'separator' },
                { role: 'togglefullscreen', label: '全屏' }
            ]
        },
        {
            label: '窗口',
            submenu: [
                { role: 'minimize', label: '最小化' },
                { role: 'close', label: '关闭' }
            ]
        },
        {
            label: '帮助',
            submenu: [
                {
                    label: '关于AGV Controller',
                    click: () => {
                        electron_1.dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: '关于',
                            message: 'AGV Controller',
                            detail: 'AGV自动导航控制系统\n版本: 1.0.0\n\n© 2024 AGV Navigation System'
                        });
                    }
                }
            ]
        }
    ];
    const menu = electron_1.Menu.buildFromTemplate(template);
    electron_1.Menu.setApplicationMenu(menu);
}
// 应用事件处理
electron_1.app.whenReady().then(async () => {
    console.log('🚀 Electron应用已准备就绪');
    // 启动后端服务
    const backendSuccess = await startBackend();
    if (!backendSuccess && !isDev) {
        const result = await electron_1.dialog.showMessageBox({
            type: 'error',
            title: '错误',
            message: '后端服务启动失败',
            detail: '无法启动AGV控制服务，应用可能无法正常工作。',
            buttons: ['继续', '退出'],
            defaultId: 1
        });
        if (result.response === 1) {
            electron_1.app.quit();
            return;
        }
    }
    // 创建主窗口
    await createMainWindow();
    createMenu();
    // macOS：当所有窗口关闭时不退出应用
    electron_1.app.on('activate', async () => {
        if (electron_1.BrowserWindow.getAllWindows().length === 0) {
            await createMainWindow();
        }
    });
});
// 所有窗口关闭时的行为
electron_1.app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        electron_1.app.quit();
    }
});
// 应用退出前的清理
electron_1.app.on('before-quit', async () => {
    console.log('🧹 应用即将退出，清理资源...');
    await stopBackend();
});
// 处理应用异常
process.on('uncaughtException', (error) => {
    console.error('💥 未捕获的异常:', error);
    electron_1.dialog.showErrorBox('严重错误', `应用发生未捕获的异常：${error.message}`);
});
process.on('unhandledRejection', (reason, promise) => {
    console.error('🚫 未处理的Promise拒绝:', reason, promise);
});
