package config

// ConnectionConfig 存储AGV连接配置
type ConnectionConfig struct {
	AuthCode string
	IP       string
	Port     string
}

// PLCConnectionConfig 存储PLC连接配置
type PLCConnectionConfig struct {
	IP   string
	Port string
}

// CacheConfig 存储缓存配置
type CacheConfig struct {
	Enable               bool     `json:"enable"`                 // 是否启用缓存
	DefaultInterval      int      `json:"default_interval"`       // 默认刷新间隔（分钟）
	DefaultMachines      []string `json:"default_machines"`       // 默认缓存机器列表
	ExpiryMinutes        int      `json:"expiry_minutes"`         // 缓存过期时间（分钟）
	MaxRetries           int      `json:"max_retries"`            // API重试次数
	RetryIntervalSeconds int      `json:"retry_interval_seconds"` // API重试间隔（秒）
	DataStoragePath      string   `json:"data_storage_path"`      // 缓存数据存储路径
	EnableFileStorage    bool     `json:"enable_file_storage"`    // 是否启用文件存储
	AutoStartOnBoot      bool     `json:"auto_start_on_boot"`     // 程序启动时自动开始缓存
}

// MySQLConfig 存储MySQL连接配置
type MySQLConfig struct {
	Host     string `json:"host"`
	Port     string `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	Database string `json:"database"`
}

// NewDefaultConfig 创建默认配置
func NewDefaultConfig() *ConnectionConfig {
	return &ConnectionConfig{
		AuthCode: "8e8e48298e28ba40b18096ed0acfee74",
		IP:       "***************",
		Port:     "17804",
	}
}

// NewDefaultPLCConfig 创建默认PLC配置
func NewDefaultPLCConfig() *PLCConnectionConfig {
	return &PLCConnectionConfig{
		IP:   "**************", // PLC IP地址
		Port: "502",            // Modbus TCP标准端口
	}
}

// NewDefaultCacheConfig 创建默认缓存配置
func NewDefaultCacheConfig() *CacheConfig {
	return &CacheConfig{
		Enable:               false,
		DefaultInterval:      5,
		DefaultMachines:      []string{"61", "60", "59", "58", "57"},
		ExpiryMinutes:        30,
		MaxRetries:           3,
		RetryIntervalSeconds: 5,
		DataStoragePath:      "backend/data/cache_data.json",
		EnableFileStorage:    true,
		AutoStartOnBoot:      false,
	}
}

// NewDefaultMySQLConfig 创建默认MySQL配置
func NewDefaultMySQLConfig() *MySQLConfig {
	return &MySQLConfig{
		Host:     "localhost",
		Port:     "3306",
		Username: "root",
		Password: "",
		Database: "agv_system",
	}
}
