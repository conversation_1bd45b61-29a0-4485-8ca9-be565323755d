package agv_test

import (
	"context"
	"encoding/json"
	"log"
	"sync"
	"time"

	"github.com/user/agv_nav/pkg/agv"
)

// AGVMonitor AGV监控器
type AGVMonitor struct {
	mu              sync.RWMutex
	testController  *AGVTestController
	isMonitoring    bool
	monitorInterval time.Duration
	ctx             context.Context
	cancel          context.CancelFunc

	// 状态相关
	lastStatus     *agv.Status
	statusHistory  []StatusSnapshot
	maxHistorySize int

	// 连接监控
	connectionStatus bool
	lastConnected    time.Time
	connectionEvents []ConnectionEvent

	// 变化检测
	changeDetector *StatusChangeDetector

	// WebSocket推送 (回调函数)
	onStatusUpdate     func(status *StatusUpdate)
	onConnectionChange func(event *ConnectionEvent)
}

// StatusSnapshot 状态快照
type StatusSnapshot struct {
	Timestamp time.Time   `json:"timestamp"`
	Status    *agv.Status `json:"status"`
	Connected bool        `json:"connected"`
}

// ConnectionEvent 连接事件
type ConnectionEvent struct {
	Timestamp time.Time `json:"timestamp"`
	Connected bool      `json:"connected"`
	Message   string    `json:"message"`
	Duration  int64     `json:"duration_ms,omitempty"` // 连接持续时间
}

// StatusUpdate 状态更新
type StatusUpdate struct {
	Timestamp time.Time   `json:"timestamp"`
	Status    *agv.Status `json:"status"`
	Connected bool        `json:"connected"`
	Changes   []string    `json:"changes,omitempty"`
}

// StatusChangeDetector 状态变化检测器
type StatusChangeDetector struct {
	positionThreshold float32 // 位置变化阈值
	angleThreshold    float32 // 角度变化阈值
}

// NewAGVMonitor 创建AGV监控器实例
func NewAGVMonitor(controller *AGVTestController) *AGVMonitor {
	changeDetector := &StatusChangeDetector{
		positionThreshold: 0.1, // 10cm
		angleThreshold:    0.1, // 0.1弧度
	}

	monitor := &AGVMonitor{
		testController:   controller,
		isMonitoring:     false,
		monitorInterval:  1 * time.Second, // 默认1秒间隔
		statusHistory:    make([]StatusSnapshot, 0),
		maxHistorySize:   1000, // 保留最近1000条记录
		connectionEvents: make([]ConnectionEvent, 0),
		changeDetector:   changeDetector,
	}

	return monitor
}

// StartMonitoring 开始监控
func (m *AGVMonitor) StartMonitoring() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.isMonitoring {
		return nil // 已经在监控中
	}

	log.Printf("开始AGV状态监控，间隔: %v", m.monitorInterval)

	// 创建context
	m.ctx, m.cancel = context.WithCancel(context.Background())
	m.isMonitoring = true

	// 启动监控协程
	go m.monitorLoop()

	return nil
}

// StopMonitoring 停止监控
func (m *AGVMonitor) StopMonitoring() {
	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.isMonitoring {
		return
	}

	log.Printf("停止AGV状态监控")

	if m.cancel != nil {
		m.cancel()
	}

	m.isMonitoring = false
}

// IsMonitoring 检查是否正在监控
func (m *AGVMonitor) IsMonitoring() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.isMonitoring
}

// SetMonitorInterval 设置监控间隔
func (m *AGVMonitor) SetMonitorInterval(interval time.Duration) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if interval < 100*time.Millisecond {
		interval = 100 * time.Millisecond // 最小100ms
	}

	m.monitorInterval = interval
	log.Printf("AGV监控间隔已设置为: %v", interval)
}

// SetStatusUpdateCallback 设置状态更新回调
func (m *AGVMonitor) SetStatusUpdateCallback(callback func(status *StatusUpdate)) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.onStatusUpdate = callback
}

// SetConnectionChangeCallback 设置连接变化回调
func (m *AGVMonitor) SetConnectionChangeCallback(callback func(event *ConnectionEvent)) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.onConnectionChange = callback
}

// GetCurrentStatus 获取当前状态
func (m *AGVMonitor) GetCurrentStatus() *StatusSnapshot {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.lastStatus == nil {
		return nil
	}

	return &StatusSnapshot{
		Timestamp: time.Now(),
		Status:    m.lastStatus,
		Connected: m.connectionStatus,
	}
}

// GetStatusHistory 获取状态历史
func (m *AGVMonitor) GetStatusHistory(limit int) []StatusSnapshot {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if limit <= 0 || limit > len(m.statusHistory) {
		limit = len(m.statusHistory)
	}

	// 返回最新的limit条记录
	start := len(m.statusHistory) - limit
	if start < 0 {
		start = 0
	}

	result := make([]StatusSnapshot, limit)
	copy(result, m.statusHistory[start:])
	return result
}

// GetConnectionEvents 获取连接事件
func (m *AGVMonitor) GetConnectionEvents(limit int) []ConnectionEvent {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if limit <= 0 || limit > len(m.connectionEvents) {
		limit = len(m.connectionEvents)
	}

	// 返回最新的limit条记录
	start := len(m.connectionEvents) - limit
	if start < 0 {
		start = 0
	}

	result := make([]ConnectionEvent, limit)
	copy(result, m.connectionEvents[start:])
	return result
}

// ClearHistory 清空历史记录
func (m *AGVMonitor) ClearHistory() {
	m.mu.Lock()
	defer m.mu.Unlock()

	m.statusHistory = make([]StatusSnapshot, 0)
	m.connectionEvents = make([]ConnectionEvent, 0)
	log.Printf("AGV监控历史记录已清空")
}

// monitorLoop 监控循环
func (m *AGVMonitor) monitorLoop() {
	ticker := time.NewTicker(m.monitorInterval)
	defer ticker.Stop()

	for {
		select {
		case <-m.ctx.Done():
			log.Printf("AGV监控循环已停止")
			return

		case <-ticker.C:
			m.performMonitorCheck()

			// 动态调整ticker间隔
			m.mu.RLock()
			currentInterval := m.monitorInterval
			m.mu.RUnlock()

			if ticker.C != time.NewTicker(currentInterval).C {
				ticker.Stop()
				ticker = time.NewTicker(currentInterval)
			}
		}
	}
}

// performMonitorCheck 执行监控检查
func (m *AGVMonitor) performMonitorCheck() {
	currentTime := time.Now()

	// 检查连接状态
	connected := m.testController.IsConnected()
	m.checkConnectionChange(connected, currentTime)

	if !connected {
		// 如果未连接，记录空状态
		m.recordStatusSnapshot(nil, false, currentTime)
		return
	}

	// 查询AGV状态
	var status *agv.Status
	err := m.testController.agvController.QueryStatus()
	if err != nil {
		log.Printf("查询AGV状态失败: %v", err)
		// 查询失败，但连接可能还在，记录错误状态
		m.recordStatusSnapshot(nil, true, currentTime)
		return
	}

	status = m.testController.agvController.GetStatus()
	m.recordStatusSnapshot(status, true, currentTime)
}

// checkConnectionChange 检查连接状态变化
func (m *AGVMonitor) checkConnectionChange(connected bool, timestamp time.Time) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.connectionStatus != connected {
		// 连接状态发生变化
		var duration int64
		var message string

		if connected {
			message = "AGV连接已建立"
			m.lastConnected = timestamp
		} else {
			message = "AGV连接已断开"
			if !m.lastConnected.IsZero() {
				duration = timestamp.Sub(m.lastConnected).Milliseconds()
			}
		}

		event := ConnectionEvent{
			Timestamp: timestamp,
			Connected: connected,
			Message:   message,
			Duration:  duration,
		}

		// 添加到事件列表
		m.connectionEvents = append(m.connectionEvents, event)

		// 限制事件数量
		if len(m.connectionEvents) > 100 {
			m.connectionEvents = m.connectionEvents[1:]
		}

		m.connectionStatus = connected

		log.Printf("AGV连接状态变化: %s", message)

		// 触发回调
		if m.onConnectionChange != nil {
			go m.onConnectionChange(&event)
		}
	}
}

// recordStatusSnapshot 记录状态快照
func (m *AGVMonitor) recordStatusSnapshot(status *agv.Status, connected bool, timestamp time.Time) {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 检测状态变化
	var changes []string
	if status != nil && m.lastStatus != nil {
		changes = m.changeDetector.DetectChanges(m.lastStatus, status)
	}

	// 创建快照
	snapshot := StatusSnapshot{
		Timestamp: timestamp,
		Status:    status,
		Connected: connected,
	}

	// 添加到历史记录
	m.statusHistory = append(m.statusHistory, snapshot)

	// 限制历史记录大小
	if len(m.statusHistory) > m.maxHistorySize {
		m.statusHistory = m.statusHistory[1:]
	}

	// 更新最后状态
	m.lastStatus = status

	// 触发状态更新回调
	if m.onStatusUpdate != nil {
		update := &StatusUpdate{
			Timestamp: timestamp,
			Status:    status,
			Connected: connected,
			Changes:   changes,
		}
		go m.onStatusUpdate(update)
	}
}

// DetectChanges 检测状态变化
func (d *StatusChangeDetector) DetectChanges(oldStatus, newStatus *agv.Status) []string {
	var changes []string

	if oldStatus == nil || newStatus == nil {
		return changes
	}

	// 检查位置变化
	deltaX := abs(newStatus.PosX - oldStatus.PosX)
	deltaY := abs(newStatus.PosY - oldStatus.PosY)
	if deltaX > d.positionThreshold || deltaY > d.positionThreshold {
		changes = append(changes, "位置变化")
	}

	// 检查角度变化
	deltaAngle := abs(newStatus.Angle - oldStatus.Angle)
	if deltaAngle > d.angleThreshold {
		changes = append(changes, "角度变化")
	}

	// 检查工作模式变化
	if newStatus.WorkMode != oldStatus.WorkMode {
		changes = append(changes, "工作模式变化")
	}

	// 检查AGV状态变化
	if newStatus.AGVState != oldStatus.AGVState {
		changes = append(changes, "AGV状态变化")
	}

	// 检查任务变化
	if newStatus.OrderID != oldStatus.OrderID {
		changes = append(changes, "任务ID变化")
	}

	// 检查导航点变化
	if newStatus.LastPointID != oldStatus.LastPointID {
		changes = append(changes, "导航点变化")
	}

	// 检查电池状态变化（5%以上变化）
	batteryDelta := abs(newStatus.BatteryPercent - oldStatus.BatteryPercent)
	if batteryDelta > 5.0 {
		changes = append(changes, "电池状态变化")
	}

	// 检查事件代码变化
	if newStatus.EventCode != oldStatus.EventCode {
		changes = append(changes, "异常事件变化")
	}

	return changes
}

// GetMonitorStats 获取监控统计信息
func (m *AGVMonitor) GetMonitorStats() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	stats := map[string]interface{}{
		"is_monitoring":       m.isMonitoring,
		"monitor_interval_ms": m.monitorInterval.Milliseconds(),
		"history_count":       len(m.statusHistory),
		"max_history_size":    m.maxHistorySize,
		"connection_events":   len(m.connectionEvents),
		"current_connected":   m.connectionStatus,
	}

	if !m.lastConnected.IsZero() {
		stats["last_connected"] = m.lastConnected
		if m.connectionStatus {
			stats["connection_duration_ms"] = time.Since(m.lastConnected).Milliseconds()
		}
	}

	return stats
}

// CreateWebSocketMessage 创建WebSocket消息
func (m *AGVMonitor) CreateWebSocketMessage(msgType string, data interface{}) ([]byte, error) {
	message := map[string]interface{}{
		"type":      msgType,
		"timestamp": time.Now(),
		"data":      data,
	}

	return json.Marshal(message)
}

// abs 辅助函数：浮点数绝对值
func abs(x float32) float32 {
	if x < 0 {
		return -x
	}
	return x
}
