package database

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"path/filepath"

	_ "github.com/mattn/go-sqlite3"
)

// NavigationInfo 导航信息结构体
type NavigationInfo struct {
	ID           int    `json:"id"`
	SideNumber   string `json:"side_number"`
	NavigationID int    `json:"navigation_id"`
}

// NavigationDB 导航数据库操作
type NavigationDB struct {
	db *sql.DB
}

// NewNavigationDB 创建新的导航数据库实例
func NewNavigationDB() (*NavigationDB, error) {
	// 智能检测数据库文件路径
	dbPath := getDBPath()

	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return nil, fmt.Errorf("打开数据库失败: %v", err)
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("连接数据库失败: %v", err)
	}

	return &NavigationDB{db: db}, nil
}

// getDBPath 智能获取数据库文件路径
func getDBPath() string {
	// 可能的数据库路径（按优先级排序）
	possiblePaths := []string{
		"data/agv_data.db",                        // 当前目录下的data文件夹
		"backend/data/agv_data.db",                // 项目根目录下的backend/data
		"./data/agv_data.db",                      // 显式当前目录
		"../data/agv_data.db",                     // 上级目录的data文件夹
		filepath.Join(".", "data", "agv_data.db"), // 使用filepath.Join构建
		// 添加用户数据目录支持（用于打包后的程序）
		filepath.Join("backend-data", "data", "agv_data.db"),       // 用户数据目录
		filepath.Join("..", "backend-data", "data", "agv_data.db"), // 上级用户数据目录
	}

	// 逐个检查路径是否存在
	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			log.Printf("找到数据库文件: %s", path)
			return path
		}
	}

	// 如果都不存在，返回默认路径（会尝试创建）
	defaultPath := "data/agv_data.db"

	// 确保目录存在
	dir := filepath.Dir(defaultPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		log.Printf("创建数据库目录失败: %v", err)
		// 如果创建失败，尝试在当前目录创建
		if err := os.MkdirAll(".", 0755); err == nil {
			log.Printf("使用当前目录作为数据库目录")
			defaultPath = "agv_data.db"
		}
	}

	log.Printf("使用默认数据库路径: %s", defaultPath)
	return defaultPath
}

// Close 关闭数据库连接
func (n *NavigationDB) Close() error {
	if n.db != nil {
		return n.db.Close()
	}
	return nil
}

// GetNavigationByRobotID 根据机器人ID获取导航点信息
func (n *NavigationDB) GetNavigationByRobotID(machineID string) (*NavigationInfo, error) {
	query := `SELECT id, SideNumber, NavigationID FROM navigation_info WHERE SideNumber = ?`

	row := n.db.QueryRow(query, machineID)

	var info NavigationInfo
	err := row.Scan(&info.ID, &info.SideNumber, &info.NavigationID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("未找到机器人 %s 的导航信息", machineID)
		}
		return nil, fmt.Errorf("查询数据库失败: %v", err)
	}

	return &info, nil
}

// GetNavigationByMachineIDs 根据机器人ID列表批量获取导航点信息
func (n *NavigationDB) GetNavigationByMachineIDs(machineIDs []string) ([]NavigationInfo, error) {
	if len(machineIDs) == 0 {
		return nil, fmt.Errorf("机器人ID列表不能为空")
	}

	// 构建IN查询语句
	query := `SELECT id, SideNumber, NavigationID FROM navigation_info WHERE SideNumber IN (`
	params := make([]interface{}, len(machineIDs))
	for i, id := range machineIDs {
		if i > 0 {
			query += ","
		}
		query += "?"
		params[i] = id
	}
	query += ")"

	rows, err := n.db.Query(query, params...)
	if err != nil {
		return nil, fmt.Errorf("查询数据库失败: %v", err)
	}
	defer rows.Close()

	var results []NavigationInfo
	for rows.Next() {
		var info NavigationInfo
		err := rows.Scan(&info.ID, &info.SideNumber, &info.NavigationID)
		if err != nil {
			return nil, fmt.Errorf("扫描数据失败: %v", err)
		}
		results = append(results, info)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历结果失败: %v", err)
	}

	return results, nil
}

// GetAllNavigationInfo 获取所有导航信息
func (n *NavigationDB) GetAllNavigationInfo() ([]NavigationInfo, error) {
	query := `SELECT id, SideNumber, NavigationID FROM navigation_info ORDER BY id`

	rows, err := n.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询数据库失败: %v", err)
	}
	defer rows.Close()

	var results []NavigationInfo
	for rows.Next() {
		var info NavigationInfo
		err := rows.Scan(&info.ID, &info.SideNumber, &info.NavigationID)
		if err != nil {
			return nil, fmt.Errorf("扫描数据失败: %v", err)
		}
		results = append(results, info)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历结果失败: %v", err)
	}

	return results, nil
}

// GetDirectionByMachine 根据机器ID获取方向信息
func (n *NavigationDB) GetDirectionByMachine(machineID string) (int, error) {
	query := `SELECT Direction FROM navigation_info WHERE SideNumber = ?`

	row := n.db.QueryRow(query, machineID)

	var direction int
	err := row.Scan(&direction)
	if err != nil {
		if err == sql.ErrNoRows {
			return -1, fmt.Errorf("未找到机器 %s 的方向信息", machineID)
		}
		return -1, fmt.Errorf("查询方向信息失败: %v", err)
	}

	return direction, nil
}

// GetOrderByMachine 根据机器ID获取排序信息
func (n *NavigationDB) GetOrderByMachine(machineID string) (int, error) {
	query := `SELECT "Order" FROM navigation_info WHERE SideNumber = ?`

	row := n.db.QueryRow(query, machineID)

	var order int
	err := row.Scan(&order)
	if err != nil {
		if err == sql.ErrNoRows {
			return -1, fmt.Errorf("未找到机器 %s 的排序信息", machineID)
		}
		return -1, fmt.Errorf("查询排序信息失败: %v", err)
	}

	return order, nil
}

// GetSpindleDistance 根据锭号key获取码值
func (n *NavigationDB) GetSpindleDistance(spindleKey string) (float32, error) {
	query := `SELECT Value FROM SpindleDistance WHERE Spindle = ?`

	row := n.db.QueryRow(query, spindleKey)

	var value float32
	err := row.Scan(&value)
	if err != nil {
		if err == sql.ErrNoRows {
			return 0, fmt.Errorf("未找到锭号 %s 的码值", spindleKey)
		}
		return 0, fmt.Errorf("查询锭号码值失败: %v", err)
	}

	return value, nil
}

// GetOriginCodeValueByMachine 根据机器ID获取原点码值
func (n *NavigationDB) GetOriginCodeValueByMachine(machineID string) (float32, error) {
	query := `SELECT CodeValue FROM navigation_info WHERE SideNumber = ?`

	row := n.db.QueryRow(query, machineID)

	var codeValue float32
	err := row.Scan(&codeValue)
	if err != nil {
		if err == sql.ErrNoRows {
			return 0, fmt.Errorf("未找到机器 %s 的原点码值", machineID)
		}
		return 0, fmt.Errorf("查询原点码值失败: %v", err)
	}

	return codeValue, nil
}

// GetNavigationPosition 根据导航点ID获取坐标信息
func (n *NavigationDB) GetNavigationPosition(navigationID int) (float32, float32, float32, error) {
	query := `SELECT X, Y, Angle FROM navigation_info WHERE NavigationID = ?`

	row := n.db.QueryRow(query, navigationID)

	var x, y, angle sql.NullFloat64
	err := row.Scan(&x, &y, &angle)
	if err != nil {
		if err == sql.ErrNoRows {
			return 0, 0, 0, fmt.Errorf("未找到导航点 %d 的坐标信息", navigationID)
		}
		return 0, 0, 0, fmt.Errorf("查询坐标信息失败: %v", err)
	}

	// 检查是否有完整的坐标信息
	if !x.Valid || !y.Valid || !angle.Valid {
		return 0, 0, 0, fmt.Errorf("导航点 %d 的坐标信息不完整", navigationID)
	}

	return float32(x.Float64), float32(y.Float64), float32(angle.Float64), nil
}
