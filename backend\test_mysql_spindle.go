package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
	"github.com/user/agv_nav/internal/task"
)

func main() {
	fmt.Println("=== MySQL断头数据源测试 ===")

	// 直接测试MySQL连接
	fmt.Println("\n--- 测试MySQL连接 ---")

	// 从配置文件读取MySQL配置
	mysqlConfig := task.MySQLSpindleConfig{
		Host:     "*************",
		Port:     "3306",
		Username: "root",
		Password: "remote",
		Database: "schedule",
	}

	fmt.Printf("MySQL配置: %+v\n", mysqlConfig)

	// 构建连接字符串
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		mysqlConfig.Username,
		mysqlConfig.Password,
		mysqlConfig.Host,
		mysqlConfig.Port,
		mysqlConfig.Database)

	fmt.Printf("连接字符串: %s\n", dsn)

	// 尝试连接
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("打开MySQL连接失败: %v", err)
	}
	defer db.Close()

	// 测试连接
	err = db.Ping()
	if err != nil {
		log.Fatalf("MySQL连接测试失败: %v", err)
	}

	fmt.Println("MySQL连接成功！")

	// 查看数据库中的表
	fmt.Println("\n--- 查看数据库表 ---")
	rows, err := db.Query("SHOW TABLES")
	if err != nil {
		log.Printf("查询表列表失败: %v", err)
	} else {
		defer rows.Close()
		fmt.Println("数据库中的表:")
		for rows.Next() {
			var tableName string
			if err := rows.Scan(&tableName); err != nil {
				log.Printf("扫描表名失败: %v", err)
				continue
			}
			fmt.Printf("  - %s\n", tableName)
		}
	}

	// 测试查询断头数据表（如果存在）
	fmt.Println("\n--- 测试断头数据表 ---")
	testTables := []string{"data61_disconnection", "data60_disconnection", "data59_disconnection"}

	for _, tableName := range testTables {
		fmt.Printf("检查表: %s\n", tableName)

		// 检查表是否存在
		var count int
		err := db.QueryRow("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = ? AND table_name = ?",
			mysqlConfig.Database, tableName).Scan(&count)
		if err != nil {
			fmt.Printf("  查询表信息失败: %v\n", err)
			continue
		}

		if count == 0 {
			fmt.Printf("  表不存在\n")
			continue
		}

		fmt.Printf("  表存在，查询数据...\n")

		// 查询表结构
		rows, err := db.Query(fmt.Sprintf("DESCRIBE %s", tableName))
		if err != nil {
			fmt.Printf("  查询表结构失败: %v\n", err)
			continue
		}

		fmt.Printf("  表结构:\n")
		for rows.Next() {
			var field, fieldType, null, key, defaultVal, extra sql.NullString
			if err := rows.Scan(&field, &fieldType, &null, &key, &defaultVal, &extra); err != nil {
				fmt.Printf("    扫描字段失败: %v\n", err)
				continue
			}
			fmt.Printf("    %s %s\n", field.String, fieldType.String)
		}
		rows.Close()

		// 查询数据数量
		var dataCount int
		err = db.QueryRow(fmt.Sprintf("SELECT COUNT(*) FROM %s", tableName)).Scan(&dataCount)
		if err != nil {
			fmt.Printf("  查询数据数量失败: %v\n", err)
		} else {
			fmt.Printf("  数据数量: %d\n", dataCount)
		}
	}

	fmt.Println("\n=== 测试完成 ===")
}
