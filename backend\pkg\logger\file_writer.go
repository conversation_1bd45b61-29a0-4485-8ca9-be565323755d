package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"
)

// FileWriter 文件写入器，支持日志轮转
type FileWriter struct {
	mu          sync.Mutex
	file        *os.File
	filePath    string
	maxSize     int64 // 最大文件大小(字节)
	maxBackups  int   // 最大备份文件数
	currentSize int64
	closed      bool
}

// NewFileWriter 创建新的文件写入器
func NewFileWriter(filePath string, maxSizeMB int64, maxBackups int) (*FileWriter, error) {
	// 智能处理日志目录权限问题
	finalPath, err := getLogFilePathWithFallback(filePath)
	if err != nil {
		return nil, fmt.Errorf("无法确定可写的日志文件路径: %v", err)
	}

	fw := &FileWriter{
		filePath:   finalPath,
		maxSize:    maxSizeMB * 1024 * 1024, // 转换为字节
		maxBackups: maxBackups,
	}

	// 打开文件
	if err := fw.openFile(); err != nil {
		return nil, err
	}

	return fw, nil
}

// getLogFilePathWithFallback 智能获取可写的日志文件路径
func getLogFilePathWithFallback(originalPath string) (string, error) {
	// 尝试的日志目录优先级列表
	fallbackDirs := []string{
		filepath.Dir(originalPath), // 原始路径的目录
		"logs",                     // 当前目录下的logs
		"temp/logs",                // 临时目录下的logs
		os.TempDir(),               // 系统临时目录
		".",                        // 当前目录（最后的备选）
	}

	filename := filepath.Base(originalPath)

	for i, dir := range fallbackDirs {
		// 测试目录是否可写
		testPath := filepath.Join(dir, filename)

		// 尝试创建目录
		if err := os.MkdirAll(dir, 0755); err != nil {
			if i < len(fallbackDirs)-1 {
				// 如果不是最后一个选项，继续尝试下一个
				continue
			}
			// 最后一个选项也失败了
			return "", fmt.Errorf("所有目录都无法创建: %v", err)
		}

		// 测试文件是否可写
		if err := testFileWritable(testPath); err != nil {
			if i < len(fallbackDirs)-1 {
				continue
			}
			return "", fmt.Errorf("所有位置都无法写入文件: %v", err)
		}

		// 成功找到可写位置
		if i > 0 {
			// 使用了备用位置，记录日志
			fmt.Fprintf(os.Stderr, "使用备用日志目录: %s (原路径 %s 不可写)\n", dir, filepath.Dir(originalPath))
		}

		return testPath, nil
	}

	return "", fmt.Errorf("没有找到可写的日志目录")
}

// testFileWritable 测试文件路径是否可写
func testFileWritable(filePath string) error {
	// 尝试创建或打开文件进行写入测试
	file, err := os.OpenFile(filePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return err
	}

	// 测试写入
	_, writeErr := file.WriteString("")
	file.Close()

	// 如果是测试文件且为空，删除它
	if writeErr == nil {
		if info, statErr := os.Stat(filePath); statErr == nil && info.Size() == 0 {
			os.Remove(filePath)
		}
	}

	return writeErr
}

// openFile 打开日志文件
func (fw *FileWriter) openFile() error {
	// 获取现有文件大小
	if info, err := os.Stat(fw.filePath); err == nil {
		fw.currentSize = info.Size()
	}

	// 打开文件（如果不存在则创建）
	file, err := os.OpenFile(fw.filePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return fmt.Errorf("打开日志文件失败: %v", err)
	}

	fw.file = file
	return nil
}

// Write 实现io.Writer接口
func (fw *FileWriter) Write(data []byte) (int, error) {
	fw.mu.Lock()
	defer fw.mu.Unlock()

	if fw.closed {
		return 0, fmt.Errorf("文件写入器已关闭")
	}

	// 检查是否需要轮转
	if fw.needsRotation(len(data)) {
		if err := fw.rotate(); err != nil {
			return 0, fmt.Errorf("日志轮转失败: %v", err)
		}
	}

	// 写入数据
	n, err := fw.file.Write(data)
	if err != nil {
		return n, err
	}

	fw.currentSize += int64(n)
	return n, nil
}

// needsRotation 检查是否需要轮转
func (fw *FileWriter) needsRotation(dataSize int) bool {
	return fw.currentSize+int64(dataSize) > fw.maxSize
}

// rotate 执行日志轮转
func (fw *FileWriter) rotate() error {
	// 关闭当前文件
	if err := fw.file.Close(); err != nil {
		return err
	}

	// 移动当前文件到备份位置
	if err := fw.moveToBackup(); err != nil {
		return err
	}

	// 清理旧的备份文件
	fw.cleanOldBackups()

	// 重新打开文件
	fw.currentSize = 0
	return fw.openFile()
}

// moveToBackup 将当前文件移动到备份位置
func (fw *FileWriter) moveToBackup() error {
	// 生成备份文件名：原文件名.1, 原文件名.2, ...
	backupPath := fw.filePath + ".1"

	// 如果备份文件已存在，需要先移动现有备份
	fw.shiftBackups()

	// 移动当前文件到备份位置
	return os.Rename(fw.filePath, backupPath)
}

// shiftBackups 移动现有备份文件
func (fw *FileWriter) shiftBackups() {
	// 从最大序号开始，依次向后移动备份文件
	for i := fw.maxBackups; i >= 1; i-- {
		oldPath := fw.filePath + "." + strconv.Itoa(i)
		newPath := fw.filePath + "." + strconv.Itoa(i+1)

		if _, err := os.Stat(oldPath); err == nil {
			if i == fw.maxBackups {
				// 删除最老的备份文件
				os.Remove(oldPath)
			} else {
				// 移动到下一个序号
				os.Rename(oldPath, newPath)
			}
		}
	}
}

// cleanOldBackups 清理超出数量限制的备份文件
func (fw *FileWriter) cleanOldBackups() {
	if fw.maxBackups <= 0 {
		return
	}

	// 获取所有备份文件
	dir := filepath.Dir(fw.filePath)
	baseName := filepath.Base(fw.filePath)

	files, err := os.ReadDir(dir)
	if err != nil {
		return
	}

	var backups []string
	for _, file := range files {
		if file.IsDir() {
			continue
		}

		name := file.Name()
		if strings.HasPrefix(name, baseName+".") {
			// 检查是否是数字后缀
			suffix := strings.TrimPrefix(name, baseName+".")
			if _, err := strconv.Atoi(suffix); err == nil {
				backups = append(backups, filepath.Join(dir, name))
			}
		}
	}

	// 如果备份文件数量超过限制，删除最老的
	if len(backups) > fw.maxBackups {
		// 按文件名排序（数字后缀排序）
		sort.Slice(backups, func(i, j int) bool {
			iSuffix := extractNumber(backups[i])
			jSuffix := extractNumber(backups[j])
			return iSuffix > jSuffix // 降序排列，大数字在前
		})

		// 删除超出限制的文件
		for i := fw.maxBackups; i < len(backups); i++ {
			os.Remove(backups[i])
		}
	}
}

// extractNumber 从文件名中提取数字后缀
func extractNumber(filename string) int {
	parts := strings.Split(filename, ".")
	if len(parts) < 2 {
		return 0
	}

	suffix := parts[len(parts)-1]
	num, err := strconv.Atoi(suffix)
	if err != nil {
		return 0
	}

	return num
}

// Close 关闭文件写入器
func (fw *FileWriter) Close() error {
	fw.mu.Lock()
	defer fw.mu.Unlock()

	if fw.closed {
		return nil
	}

	fw.closed = true
	if fw.file != nil {
		return fw.file.Close()
	}

	return nil
}

// Sync 强制同步数据到磁盘
func (fw *FileWriter) Sync() error {
	fw.mu.Lock()
	defer fw.mu.Unlock()

	if fw.closed || fw.file == nil {
		return nil
	}

	return fw.file.Sync()
}

// Size 获取当前文件大小
func (fw *FileWriter) Size() int64 {
	fw.mu.Lock()
	defer fw.mu.Unlock()
	return fw.currentSize
}

// RotateLogger 定时轮转器
type RotateLogger struct {
	fw       *FileWriter
	ticker   *time.Ticker
	stopChan chan struct{}
}

// NewRotateLogger 创建定时轮转的日志记录器
func NewRotateLogger(filePath string, maxSizeMB int64, maxBackups int, rotateInterval time.Duration) (*RotateLogger, error) {
	fw, err := NewFileWriter(filePath, maxSizeMB, maxBackups)
	if err != nil {
		return nil, err
	}

	rl := &RotateLogger{
		fw:       fw,
		ticker:   time.NewTicker(rotateInterval),
		stopChan: make(chan struct{}),
	}

	// 启动定时轮转
	go rl.rotateRoutine()

	return rl, nil
}

// rotateRoutine 定时轮转协程
func (rl *RotateLogger) rotateRoutine() {
	for {
		select {
		case <-rl.ticker.C:
			// 强制轮转（比如每天轮转一次）
			rl.fw.mu.Lock()
			if !rl.fw.closed {
				rl.fw.rotate()
			}
			rl.fw.mu.Unlock()
		case <-rl.stopChan:
			return
		}
	}
}

// Write 实现io.Writer接口
func (rl *RotateLogger) Write(data []byte) (int, error) {
	return rl.fw.Write(data)
}

// Close 关闭轮转日志记录器
func (rl *RotateLogger) Close() error {
	close(rl.stopChan)
	rl.ticker.Stop()
	return rl.fw.Close()
}
