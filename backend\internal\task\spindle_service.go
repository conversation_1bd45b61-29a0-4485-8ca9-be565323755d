package task

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"

	//"net"
	"net/http"
	//"net/url"
	"regexp"
	"time"

	"github.com/user/agv_nav/internal/cache"
	"github.com/user/agv_nav/internal/database"
)

// MES API 相关结构体
type MESRequest struct {
	CustomerNo string `json:"customerNo"`
	MachineID  string `json:"machineId"`
}

type MESBreakageDTO struct {
	MachineID string `json:"machineId"`
	Side      int    `json:"side"`
	SpindleNo int    `json:"spindleNo"`
	StartTime int64  `json:"startTime"`
	EndTime   *int64 `json:"endTime"`
}

type MESResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		BreakageDTOList []MESBreakageDTO `json:"mes86099703BreakageDTOList"`
	} `json:"data"`
}

// SpindleService 锭号相关服务
type SpindleService struct {
	mesAPIURL   string
	customerNo  string
	httpTimeout time.Duration
	dbService   *database.NavigationDB // 添加数据库服务
}

// NewSpindleService 创建锭号服务实例
func NewSpindleService() *SpindleService {
	// 初始化数据库连接
	dbService, err := database.NewNavigationDB()
	if err != nil {
		log.Printf("警告：初始化数据库连接失败: %v", err)
		dbService = nil
	}

	return &SpindleService{
		mesAPIURL:   "http://47.92.194.122:8081/mes/86099703/abnormalData",
		customerNo:  "86099703",
		httpTimeout: 1200 * time.Second, // 增加超时时间到120秒
		dbService:   dbService,
	}
}

// GetSpindleList 获取指定细纱机的锭号列表（3次重试，失败后使用缓存）
func (s *SpindleService) GetSpindleList(machine string) ([]int, error) {
	log.Printf("开始向细纱机 %s 请求锭号列表", machine)

	// 重试配置 - 改为3次
	maxRetries := 5
	retryInterval := 5 * time.Second
	startTime := time.Now()

	var lastErr error
	for attempt := 1; attempt <= maxRetries; attempt++ {
		attemptStartTime := time.Now()

		// 尝试获取锭号列表
		spindleList, err := s.getSpindleListOnce(machine)

		if err == nil {
			// 成功获取数据
			if attempt > 1 {
				totalDuration := time.Since(startTime)
				log.Printf("✓ MES请求成功恢复！第%d次尝试成功，总耗时: %v", attempt, totalDuration)
			}
			return spindleList, nil
		}

		lastErr = err
		attemptDuration := time.Since(attemptStartTime)

		// 记录失败信息
		if attempt < maxRetries {
			log.Printf("⚠️  MES请求失败（第%d/%d次）耗时%v: %v",
				attempt, maxRetries, attemptDuration, err)
			log.Printf("   → 等待 %v 后进行第%d次重试...", retryInterval, attempt+1)
			time.Sleep(retryInterval)
		}
	}

	// 3次重试失败，尝试使用缓存
	totalDuration := time.Since(startTime)
	log.Printf("✗ MES请求失败，已重试%d次，总耗时: %v，最后错误: %v",
		maxRetries, totalDuration, lastErr)
	log.Printf("尝试使用缓存数据...")

	// 解析机器ID
	machineNum, side, err := s.parseMachineID(machine)
	if err != nil {
		return nil, fmt.Errorf("解析机器ID失败: %v", err)
	}

	// 先尝试有效缓存
	if cachedData, exists := cache.GetCachedSpindleData(machineNum); exists {
		log.Printf("✓ 使用有效缓存数据: 机器%s", machineNum)
		// 转换类型
		taskData := s.convertCacheToTaskData(cachedData)
		filteredSpindles := s.filterSpindlesBySide(taskData, side)
		return filteredSpindles, nil
	}

	// 再尝试过期缓存
	if expiredData, exists := cache.GetExpiredSpindleData(machineNum); exists {
		log.Printf("✓ 使用过期缓存数据: 机器%s", machineNum)
		// 转换类型
		taskData := s.convertCacheToTaskData(expiredData)
		filteredSpindles := s.filterSpindlesBySide(taskData, side)
		return filteredSpindles, nil
	}

	// 完全没有数据
	return nil, fmt.Errorf("MES请求失败且无缓存数据可用，已重试%d次: %v", maxRetries, lastErr)
}

// getSpindleListOnce 执行一次获取锭号列表的操作
func (s *SpindleService) getSpindleListOnce(machine string) ([]int, error) {
	// 步骤1: 解析machine参数，提取数字和侧面信息
	machineNum, side, err := s.parseMachineID(machine)
	if err != nil {
		return nil, fmt.Errorf("解析机器ID失败: %v", err)
	}

	// 步骤2: 构造完整的机器ID
	fullMachineID := fmt.Sprintf("86099703040%s", machineNum)
	log.Printf("机器 %s → 完整ID: %s, 侧面: %s", machine, fullMachineID, side)

	// 步骤3: 构造请求体
	reqBody := MESRequest{
		CustomerNo: s.customerNo,
		MachineID:  fullMachineID,
	}

	// 步骤4: 调用MES API
	spindleData, err := s.callMESAPI(reqBody)
	if err != nil {
		return nil, fmt.Errorf("调用MES API失败: %v", err)
	}

	// 步骤5: 根据侧面筛选锭号
	filteredSpindles := s.filterSpindlesBySide(spindleData, side)

	log.Printf("细纱机 %s 筛选后获得 %d 个锭号: %v", machine, len(filteredSpindles), filteredSpindles)
	return filteredSpindles, nil
}

// parseMachineID 解析machine参数，提取数字和侧面信息
func (s *SpindleService) parseMachineID(machine string) (string, string, error) {
	// 使用正则表达式提取数字和字母
	re := regexp.MustCompile(`^(\d+)([RL])$`)
	matches := re.FindStringSubmatch(machine)

	if len(matches) != 3 {
		return "", "", fmt.Errorf("无效的机器ID格式: %s，期望格式如 '61R' 或 '61L'", machine)
	}

	machineNum := matches[1] // 数字部分，如 "61"
	side := matches[2]       // 侧面标识，"R" 或 "L"

	return machineNum, side, nil
}

// callMESAPI 调用MES API获取断头数据
func (s *SpindleService) callMESAPI(reqBody MESRequest) ([]MESBreakageDTO, error) {
	// 序列化请求体
	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", s.mesAPIURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "AGV-Navigation-System/1.0")

	// 发送请求
	client := &http.Client{
		Timeout: s.httpTimeout,
	}

	log.Printf("→ 发送MES API请求: %s", s.mesAPIURL)
	log.Printf("  请求体: %s", string(jsonData))

	requestStartTime := time.Now()
	resp, err := client.Do(req)
	requestDuration := time.Since(requestStartTime)

	if err != nil {
		log.Printf("✗ HTTP请求失败（耗时%v）: %v", requestDuration, err)
		return nil, fmt.Errorf("发送HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %v", err)
	}

	log.Printf("← 收到MES API响应，状态码: %d，耗时: %v", resp.StatusCode, requestDuration)

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("MES API返回错误状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析JSON响应
	var mesResp MESResponse
	err = json.Unmarshal(body, &mesResp)
	if err != nil {
		return nil, fmt.Errorf("解析JSON响应失败: %v, 响应体: %s", err, string(body))
	}

	// 检查业务状态码
	if mesResp.Code != 0 {
		return nil, fmt.Errorf("MES API返回业务错误: code=%d, msg=%s", mesResp.Code, mesResp.Msg)
	}

	log.Printf("MES API调用成功，获得 %d 条断头数据", len(mesResp.Data.BreakageDTOList))
	return mesResp.Data.BreakageDTOList, nil
}

// filterSpindlesBySide 根据侧面筛选锭号
func (s *SpindleService) filterSpindlesBySide(spindleData []MESBreakageDTO, side string) []int {
	var filteredSpindles []int

	for _, item := range spindleData {
		spindleNo := item.SpindleNo

		// 根据侧面筛选锭号范围
		switch side {
		case "R":
			// 右侧：0-600（包含600）
			if spindleNo >= 0 && spindleNo <= 600 {
				filteredSpindles = append(filteredSpindles, spindleNo)
			}
		case "L":
			// 左侧：601-1200
			if spindleNo >= 601 && spindleNo <= 1200 {
				filteredSpindles = append(filteredSpindles, spindleNo)
			}
		}
	}

	log.Printf("侧面 %s 筛选结果: 原始 %d 条 → 筛选后 %d 条", side, len(spindleData), len(filteredSpindles))
	return filteredSpindles
}

// SortSpindlesByOrder 根据数据库中的Order字段排序锭号
func (s *SpindleService) SortSpindlesByOrder(spindleList []int, machine string) []int {
	log.Printf("为机器 %s 排序锭号: %v", machine, spindleList)

	// 检查数据库连接
	if s.dbService == nil {
		log.Printf("警告：数据库未连接，使用默认升序排序")
		return s.defaultSort(spindleList, true)
	}

	// 查询数据库获取排序信息
	order, err := s.dbService.GetOrderByMachine(machine)
	if err != nil {
		log.Printf("警告：查询机器 %s 排序信息失败: %v，使用默认升序排序", machine, err)
		return s.defaultSort(spindleList, true)
	}

	log.Printf("机器 %s 的排序值: %d (1=升序, 0=降序)", machine, order)

	// 根据排序值排序
	isAscending := order == 1
	return s.defaultSort(spindleList, isAscending)
}

// defaultSort 执行实际的排序操作
func (s *SpindleService) defaultSort(spindleList []int, isAscending bool) []int {
	// 复制数组，避免修改原数组
	sortedSpindles := make([]int, len(spindleList))
	copy(sortedSpindles, spindleList)

	// 冒泡排序
	for i := 0; i < len(sortedSpindles)-1; i++ {
		for j := 0; j < len(sortedSpindles)-1-i; j++ {
			var shouldSwap bool
			if isAscending {
				shouldSwap = sortedSpindles[j] > sortedSpindles[j+1]
			} else {
				shouldSwap = sortedSpindles[j] < sortedSpindles[j+1]
			}

			if shouldSwap {
				sortedSpindles[j], sortedSpindles[j+1] = sortedSpindles[j+1], sortedSpindles[j]
			}
		}
	}

	sortDirection := "升序"
	if !isAscending {
		sortDirection = "降序"
	}
	log.Printf("排序完成 (%s): %v", sortDirection, sortedSpindles)
	return sortedSpindles
}

// convertCacheToTaskData 将cache.MESBreakageDTO转换为task.MESBreakageDTO
func (s *SpindleService) convertCacheToTaskData(cachedData []cache.MESBreakageDTO) []MESBreakageDTO {
	taskData := make([]MESBreakageDTO, len(cachedData))
	for i, item := range cachedData {
		taskData[i] = MESBreakageDTO{
			MachineID: item.MachineID,
			Side:      item.Side,
			SpindleNo: item.SpindleNo,
			StartTime: item.StartTime,
			EndTime:   item.EndTime,
		}
	}
	return taskData
}
