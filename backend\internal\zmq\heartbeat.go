package zmq

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	zmqpkg "github.com/user/agv_nav/pkg/zmq"
)

// HeartbeatManager 心跳管理器
type HeartbeatManager struct {
	requester     zmqpkg.Requester // ZMQ 请求者（发送心跳）
	config        *SystemConfig    // 系统配置
	currentState  bool             // 当前状态值
	lastHeartbeat time.Time        // 最后心跳时间
	mu            sync.RWMutex     // 并发保护
	stopCh        chan struct{}    // 停止信号
	logger        Logger           // 日志接口

	// 超时配置
	replyTimeout     time.Duration // 等待回复的超时时间
	scheduleTimeout  time.Duration // 等待调度系统心跳的超时时间
	stateToggleDelay time.Duration // 状态反转延迟时间
	
	// 反转心跳等待机制（用于MessageRouter路由模式）
	waitingForReversedHeartbeat bool           // 是否正在等待反转心跳
	reversedHeartbeatCh         chan []byte    // 反转心跳数据通道
	reversedHeartbeatErrorCh    chan error     // 反转心跳错误通道
}

// Logger 日志接口
type Logger interface {
	Debug(msg string, fields ...interface{})
	Info(msg string, fields ...interface{})
	Warn(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
}

// defaultLogger 默认日志实现
type defaultLogger struct{}

func (l *defaultLogger) Debug(msg string, fields ...interface{}) {
	log.Printf("[DEBUG] Heartbeat: %s %v", msg, fields)
}

func (l *defaultLogger) Info(msg string, fields ...interface{}) {
	log.Printf("[INFO] Heartbeat: %s %v", msg, fields)
}

func (l *defaultLogger) Warn(msg string, fields ...interface{}) {
	log.Printf("[WARN] Heartbeat: %s %v", msg, fields)
}

func (l *defaultLogger) Error(msg string, fields ...interface{}) {
	log.Printf("[ERROR] Heartbeat: %s %v", msg, fields)
}

// NewHeartbeatManager 创建心跳管理器
func NewHeartbeatManager(requester zmqpkg.Requester, config *SystemConfig) *HeartbeatManager {
	return &HeartbeatManager{
		requester:        requester,
		config:           config,
		currentState:     true, // 初始状态为 true
		stopCh:           make(chan struct{}),
		logger:           &defaultLogger{},
		replyTimeout:     5 * time.Second, // 等待回复超时
		scheduleTimeout:  2 * time.Minute, // 等待调度系统心跳超时
		stateToggleDelay: 1 * time.Minute, // 状态反转延迟
	}
}

// NewHeartbeatManagerWithLogger 创建带自定义日志的心跳管理器
func NewHeartbeatManagerWithLogger(requester zmqpkg.Requester, config *SystemConfig, logger Logger) *HeartbeatManager {
	manager := NewHeartbeatManager(requester, config)
	manager.logger = logger
	return manager
}

// Start 启动心跳管理
func (h *HeartbeatManager) Start(ctx context.Context) error {
	h.logger.Info("Starting heartbeat manager", "robotNo", h.config.RobotNo)

	// 注意：不再在这里设置响应者处理函数，由外部调用者负责启动响应者
	// 这样可以支持消息路由器统一处理所有消息

	// 发送初始心跳
	if err := h.sendInitialHeartbeat(); err != nil {
		return fmt.Errorf("send initial heartbeat failed: %w", err)
	}

	// 开始心跳循环
	h.startHeartbeatLoop(ctx)

	return nil
}

// Stop 停止心跳管理
func (h *HeartbeatManager) Stop() {
	h.logger.Info("Stopping heartbeat manager")
	close(h.stopCh)

	if h.requester != nil {
		h.requester.Close()
	}
	// 注意：responder现在由ZMQ Manager统一管理，不需要在这里关闭
}

// sendInitialHeartbeat 发送初始心跳 - 使用完整的5步流程
func (h *HeartbeatManager) sendInitialHeartbeat() error {
	h.mu.RLock()
	currentState := h.currentState
	h.mu.RUnlock()

	h.logger.Info("Sending initial heartbeat", "state", currentState)

	// 使用完整的5步心跳流程，发送反转状态
	return h.sendHeartbeat(!currentState)
}

// startHeartbeatLoop 开始心跳循环 - 按照心跳测试代码的1分钟间隔逻辑
func (h *HeartbeatManager) startHeartbeatLoop(ctx context.Context) {
	h.logger.Info("Starting heartbeat loop with 1-minute interval")

	go func() {
		ticker := time.NewTicker(h.stateToggleDelay) // 使用1分钟间隔
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				h.logger.Info("Heartbeat loop stopped by context")
				return
			case <-h.stopCh:
				h.logger.Info("Heartbeat loop stopped by stop channel")
				return
			case <-ticker.C:
				// 发送定期心跳
				h.mu.RLock()
				currentState := h.currentState
				h.mu.RUnlock()

				h.logger.Info("Sending scheduled heartbeat", "currentState", currentState)

				// 发送心跳（会触发完整的5步流程）
				if err := h.sendHeartbeat(!currentState); err != nil {
					h.logger.Error("Failed to send scheduled heartbeat", "error", err)
				}
			}
		}
	}()
}

// handleSchedulerHeartbeat 处理调度系统发来的心跳
func (h *HeartbeatManager) handleSchedulerHeartbeat(request []byte) ([]byte, error) {
	h.logger.Debug("Received scheduler heartbeat", "size", len(request))

	// 解析心跳消息
	var heartbeatMsg Message
	if err := json.Unmarshal(request, &heartbeatMsg); err != nil {
		h.logger.Error("Failed to unmarshal scheduler heartbeat", "error", err)
		return nil, fmt.Errorf("unmarshal heartbeat failed: %w", err)
	}

	// 验证是心跳消息
	if heartbeatMsg.Instruction != 0 {
		h.logger.Error("Invalid instruction in scheduler heartbeat", "instruction", heartbeatMsg.Instruction)
		return nil, fmt.Errorf("expected instruction 0, got %d", heartbeatMsg.Instruction)
	}

	// 解析心跳内容
	contentMap, ok := heartbeatMsg.Content.(map[string]interface{})
	if !ok {
		h.logger.Error("Invalid content format in scheduler heartbeat")
		return nil, fmt.Errorf("invalid content format")
	}

	state, ok := contentMap["state"].(bool)
	if !ok {
		h.logger.Error("Invalid state in scheduler heartbeat content")
		return nil, fmt.Errorf("invalid state format")
	}

	h.logger.Info("📥 第3步：接收到调度系统消息", "state", state, "data", string(request))

	// 更新最后心跳时间
	h.mu.Lock()
	h.lastHeartbeat = time.Now()
	
	// 检查是否有等待反转心跳的sendHeartbeat流程
	if h.waitingForReversedHeartbeat && h.reversedHeartbeatCh != nil {
		h.logger.Info("📨 检测到等待反转心跳的流程，发送数据到等待通道")
		// 发送原始请求数据到等待通道
		select {
		case h.reversedHeartbeatCh <- request:
			h.logger.Info("✅ 反转心跳数据已发送到等待通道")
		default:
			h.logger.Warn("⚠️ 反转心跳通道已满，跳过发送")
		}
	}
	h.mu.Unlock()

	// 立即回复确认
	reply := NewMessage(1, true, nil) // Instruction=1 (回复), Code=true
	replyData, err := json.Marshal(reply)
	if err != nil {
		h.logger.Error("Failed to marshal reply", "error", err)
		return nil, fmt.Errorf("marshal reply failed: %w", err)
	}

	h.logger.Info("📤 第4步：发送确认回复", "reply", string(replyData))

	return replyData, nil
}

// sendHeartbeat 发送心跳 - 按照心跳测试代码的完整5步流程实现
func (h *HeartbeatManager) sendHeartbeat(state bool) error {
	startTime := time.Now()

	h.logger.Info("📤 第1步：发送心跳消息", "state", state)

	// 第1步：创建心跳消息
	msg := NewHeartbeatMessage(h.config.RobotNo, state)

	// 序列化消息
	data, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("marshal heartbeat message failed: %w", err)
	}

	// 记录详细的心跳发送信息
	h.logger.Info("📤 Heartbeat Manager sending heartbeat message",
		"robotNo", h.config.RobotNo,
		"state", state,
		"messageSize", len(data),
		"messageContent", string(data))

	// 第1-2步：发送心跳并等待确认回复
	replyData, err := h.requester.RequestWithTimeout(data, h.replyTimeout)
	if err != nil {
		h.logger.Error("❌ Heartbeat Manager send heartbeat failed",
			"robotNo", h.config.RobotNo,
			"state", state,
			"error", err,
			"messageContent", string(data))
		return fmt.Errorf("send heartbeat failed: %w", err)
	}

	// 记录接收的确认回复
	h.logger.Info("📥 Heartbeat Manager received confirmation reply",
		"robotNo", h.config.RobotNo,
		"replySize", len(replyData),
		"replyContent", string(replyData))

	// 第2步：解析调度系统的确认回复
	var reply Message
	if err := json.Unmarshal(replyData, &reply); err != nil {
		return fmt.Errorf("unmarshal reply failed: %w", err)
	}

	// 验证确认回复：instruction=1, code=true
	if reply.Instruction != 1 || !reply.Code {
		return fmt.Errorf("invalid confirmation reply: instruction=%d, code=%v", reply.Instruction, reply.Code)
	}

	h.logger.Info("✅ 第2步：确认回复验证成功", "instruction", reply.Instruction, "code", reply.Code)

	// 第3步：等待调度系统发送反转心跳
	h.logger.Info("⏳ 第3步：等待调度系统发送反转心跳...")

	reversedHeartbeatData, err := h.waitForReversedHeartbeat(h.scheduleTimeout)
	if err != nil {
		return fmt.Errorf("wait for reversed heartbeat failed: %w", err)
	}

	h.logger.Info("📥 第3步：接收反转心跳", "reversedHeartbeat", string(reversedHeartbeatData))

	// 解析反转心跳
	var reversedHeartbeat Message
	if err := json.Unmarshal(reversedHeartbeatData, &reversedHeartbeat); err != nil {
		return fmt.Errorf("unmarshal reversed heartbeat failed: %w", err)
	}

	// 验证是心跳消息
	if reversedHeartbeat.Instruction != 0 {
		return fmt.Errorf("invalid reversed heartbeat instruction: expected 0, got %d", reversedHeartbeat.Instruction)
	}

	// 解析反转后的状态
	contentMap, ok := reversedHeartbeat.Content.(map[string]interface{})
	if !ok {
		return fmt.Errorf("invalid content format in reversed heartbeat")
	}

	reversedState, ok := contentMap["state"].(bool)
	if !ok {
		return fmt.Errorf("invalid state in reversed heartbeat content")
	}

	h.logger.Info("✅ 第3步：反转心跳验证成功", "reversedState", reversedState)

	// 第4步：回复确认给调度系统（通过Responder handler自动处理）
	h.logger.Info("✅ 第4步：确认回复已通过Responder handler发送")

	// 第5步：更新状态
	h.mu.Lock()
	oldState := h.currentState
	h.currentState = reversedState // 使用调度系统发送的反转状态
	h.lastHeartbeat = time.Now()
	h.mu.Unlock()

	h.logger.Info("🔄 第5步：状态更新完成", "oldState", oldState, "newState", h.currentState)
	h.logger.Info("Heartbeat cycle completed successfully", "duration", time.Since(startTime))

	return nil
}

// waitForReversedHeartbeat 等待调度系统发送反转心跳
// 现在心跳通过MessageRouter路由，不需要直接设置handler
func (h *HeartbeatManager) waitForReversedHeartbeat(timeout time.Duration) ([]byte, error) {
	h.logger.Info("⏳ 等待调度系统发送反转心跳", "timeout", timeout)
	h.logger.Info("💡 使用MessageRouter路由模式等待反转心跳")

	// 创建接收通道
	h.mu.Lock()
	h.reversedHeartbeatCh = make(chan []byte, 1)
	h.reversedHeartbeatErrorCh = make(chan error, 1)
	h.waitingForReversedHeartbeat = true
	h.mu.Unlock()

	// 清理函数
	defer func() {
		h.mu.Lock()
		h.waitingForReversedHeartbeat = false
		h.reversedHeartbeatCh = nil
		h.reversedHeartbeatErrorCh = nil
		h.mu.Unlock()
	}()

	// 等待结果或超时
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	select {
	case data := <-h.reversedHeartbeatCh:
		h.logger.Info("📥 收到反转心跳数据", "size", len(data))
		return data, nil
	case err := <-h.reversedHeartbeatErrorCh:
		h.logger.Error("❌ 反转心跳处理错误", "error", err)
		return nil, err
	case <-ctx.Done():
		h.logger.Error("⏰ 等待反转心跳超时", "timeout", timeout)
		return nil, fmt.Errorf("wait for reversed heartbeat timeout after %v", timeout)
	}
}

// GetCurrentState 获取当前状态
func (h *HeartbeatManager) GetCurrentState() bool {
	h.mu.RLock()
	defer h.mu.RUnlock()
	return h.currentState
}

// GetLastHeartbeat 获取最后心跳时间
func (h *HeartbeatManager) GetLastHeartbeat() time.Time {
	h.mu.RLock()
	defer h.mu.RUnlock()
	return h.lastHeartbeat
}

// HandleMessage 实现MessageHandler接口 - 处理调度系统的心跳消息
func (h *HeartbeatManager) HandleMessage(request []byte) ([]byte, error) {
	return h.handleSchedulerHeartbeat(request)
}

// CanHandle 实现MessageHandler接口 - 检查是否能处理指定指令
func (h *HeartbeatManager) CanHandle(instruction int) bool {
	return instruction == InstructionHeartbeat // 只处理心跳消息 (instruction = 0)
}

// GetName 实现MessageHandler接口 - 返回处理器名称
func (h *HeartbeatManager) GetName() string {
	return "HeartbeatManager"
}
