# AGV导航任务配置说明

## 文件说明

本文档说明 `task_config.json` 配置文件中各个参数的含义、建议范围和注意事项。

## 配置结构

### 1. 基本配置

```json
{
  "maxRetries": 3,                    // 失败重试次数
  "retryDelayBase": "5s"              // 重试基础延迟时间
}
```

| 参数 | 含义 | 建议范围 | 推荐值 | 说明 |
|------|------|----------|--------|------|
| `maxRetries` | 任务失败时的最大重试次数 | 1-5 | 3 | 过小：容易因偶然错误失败<br>过大：可能掩盖系统问题 |
| `retryDelayBase` | 重试之间的基础延迟时间 | 3s-10s | 5s | 过小：重试过快可能加重系统负担<br>过大：恢复时间过长 |

### 2. PLC通讯地址配置

```json
{
  "plcAddresses": {
    "controlRequestAddress": 601,       // M601: 控制权请求信号
    "controlConfirmAddress": 501,       // M501: 控制权确认信号
    "taskStartAddress": 602,            // M602: 任务开始信号
    "readyAddress": 509,                // M509: PLC准备接收数据信号
    "directionAddress": 604,            // M604: 方向信号
    "distanceAddress": 602,             // D602: 距离数据
    "rollerAddress": 600,               // D600: 滚筒数据
    "completeAddress": 603,             // M603: 数据写入完成信号
    "verifyDirectionAddress": 505,      // M505: 方向验证信号
    "verifyRollerAddress": 501,         // D501: 滚筒验证数据
    "verifyDistanceAddress": 502,       // D502: 距离验证数据
    "verificationCompleteAddress": 605, // M605: 验证完成信号
    "workStatusAddress": 500,           // D500: 工作状态 (0=工作中, 1=成功, 2=失败)
    "robotStartSignalAddress": 612,     // M612: 机器人启动信号
    "machineCompleteAddress": 606,      // M606: 机台完成信号
    "returnToOriginAddress": 610,       // M610: 返回原点信号
    "originDirectionAddress": 611,      // M611: 原点方向信号
    "originCodeValueAddress": 604,      // D604: 原点码值
    "originArrivalAddress": 510,        // M510: AGV到达原点信号
    "controlHandoverAddress": 607,      // M607: 控制权交接信号
    "turnAroundAddress": 608,           // M608: 调头请求信号
    "turnCompleteAddress": 511,         // M511: 调头完成信号
    "exitLaneAddress": 609,             // M609: 退出巷道信号
    "laneExitCompleteAddress": 508      // M508: 巷道退出完成信号
  }
}
```

**重要提醒：** 这些地址是硬件相关的，与PLC程序配置对应，**不要随意修改**！

#### 地址类型说明
- **M地址**: 线圈地址，用于布尔值（true/false）
- **D地址**: 数据寄存器地址，用于数值数据

#### 信号流程
1. **控制权获取**: M601 → M501
2. **任务启动**: M602 → M509
3. **数据传输**: M604/D602/D600 → M603
4. **数据验证**: M505/D501/D502 → M605
5. **工作状态**: D500 (0=工作中, 1=成功, 2=失败)
6. **调头流程**: M608 → M511
7. **巷道退出**: M609 → M508

### 3. 超时配置

这是最重要的配置部分，直接影响系统稳定性和响应速度。

#### 3.1 AGV导航相关

```json
{
  "navigationTimeout": "20m0s",         // AGV导航超时
  "navigationCheckInterval": "2s"       // 导航状态检查间隔
}
```

| 参数 | 含义 | 建议范围 | 推荐值 | 影响 |
|------|------|----------|--------|------|
| `navigationTimeout` | AGV从一个点导航到另一个点的最大等待时间 | 10m-30m | 20m | **过小**：可能导致正常导航被中断<br>**过大**：异常情况下等待时间过长 |
| `navigationCheckInterval` | 检查AGV导航状态的频率 | 1s-5s | 2s | **过小**：占用过多CPU资源<br>**过大**：状态更新不及时 |

#### 3.2 PLC准备相关

```json
{
  "plcReadyTimeout": "2m0s",            // PLC准备超时
  "plcReadyCheckInterval": "500ms"      // PLC准备检查间隔
}
```

| 参数 | 含义 | 建议范围 | 推荐值 | 影响 |
|------|------|----------|--------|------|
| `plcReadyTimeout` | 等待PLC准备接收数据的最大时间 | 1m-5m | 2m | **过小**：PLC来不及准备<br>**过大**：异常时等待时间过长 |
| `plcReadyCheckInterval` | 检查PLC是否准备好的频率 | 200ms-1s | 500ms | **过小**：网络负载过重<br>**过大**：响应不够及时 |

#### 3.3 PLC工作相关

```json
{
  "plcWorkTimeout": "5m0s",             // PLC工作超时
  "plcWorkCheckInterval": "1s"          // PLC工作检查间隔
}
```

| 参数 | 含义 | 建议范围 | 推荐值 | 影响 |
|------|------|----------|--------|------|
| `plcWorkTimeout` | PLC处理锭位工作的最大时间 | 3m-10m | 5m | **过小**：正常工作可能被中断<br>**过大**：异常锭位处理时间过长 |
| `plcWorkCheckInterval` | 检查PLC工作完成状态的频率 | 500ms-3s | 1s | **过小**：频繁查询影响PLC性能<br>**过大**：工作完成通知不及时 |

#### 3.4 调头相关 ⚠️ **重要配置**

```json
{
  "turnAroundTimeout": "1m0s",          // 调头超时
  "turnAroundCheckInterval": "500ms"    // 调头检查间隔
}
```

| 参数 | 含义 | 建议范围 | 推荐值 | 影响 |
|------|------|----------|--------|------|
| `turnAroundTimeout` | 等待AGV调头完成的最大时间 | 30s-5m | 1m | **过小**：正常调头可能被中断<br>**过大**：卡住时等待时间过长 |
| `turnAroundCheckInterval` | 检查调头完成信号(M511)的频率 | 200ms-2s | 500ms | **过小**：频繁查询M511信号<br>**过大**：调头完成通知不及时 |

**注意事项：**
- 调头是巷道任务的关键步骤，超时时间要合理设置
- 检查间隔不宜过长，确保及时响应
- 如果经常出现调头超时，可适当增加 `turnAroundTimeout`

#### 3.5 巷道退出相关

```json
{
  "laneExitTimeout": "30s",             // 巷道退出超时
  "laneExitCheckInterval": "500ms"      // 巷道退出检查间隔
}
```

| 参数 | 含义 | 建议范围 | 推荐值 | 影响 |
|------|------|----------|--------|------|
| `laneExitTimeout` | AGV退出巷道的最大时间 | 15s-60s | 30s | **过小**：正常退出可能被中断<br>**过大**：卡住时等待时间过长 |
| `laneExitCheckInterval` | 检查巷道退出完成信号(M508)的频率 | 200ms-1s | 500ms | **过小**：频繁查询M508信号<br>**过大**：退出完成通知不及时 |

#### 3.6 原点到达相关

```json
{
  "originArrivalTimeout": "5m0s",       // 原点到达超时
  "originArrivalCheckInterval": "500ms" // 原点到达检查间隔
}
```

| 参数 | 含义 | 建议范围 | 推荐值 | 影响 |
|------|------|----------|--------|------|
| `originArrivalTimeout` | AGV到达原点位置的最大时间 | 3m-10m | 5m | **过小**：正常到达可能被中断<br>**过大**：异常时等待时间过长 |
| `originArrivalCheckInterval` | 检查AGV到达原点信号(M510)的频率 | 200ms-2s | 500ms | **过小**：频繁查询M510信号<br>**过大**：到达通知不及时 |

### 4. 日志配置

```json
{
  "logging": {
    "level": "INFO",                    // 日志级别
    "enableConsole": true,              // 是否输出到控制台
    "enableFile": true,                 // 是否输出到文件
    "enableStructured": false,          // 是否使用结构化格式
    "filePath": "logs/agv_nav.log",     // 日志文件路径
    "maxSize": 10,                      // 单个日志文件最大大小(MB)
    "maxBackups": 5,                    // 保留的备份文件数量
    "maxAge": 30                        // 日志文件保留天数
  }
}
```

#### 日志级别说明
- **DEBUG**: 详细调试信息，包含所有日志
- **INFO**: 一般信息，包含重要的运行状态
- **WARN**: 警告信息，可能的问题
- **ERROR**: 错误信息，程序仍能继续运行
- **FATAL**: 致命错误，程序可能终止

#### 日志文件管理
- 当日志文件达到 `maxSize` 时自动轮转
- 最多保留 `maxBackups` 个备份文件
- 超过 `maxAge` 天的日志会被自动删除

### 5. 巷道配置

```json
{
  "lane_configuration": {
    "special_lanes": [
      {
        "machine": "61R",
        "type": "central_aisle",
        "strategy": "single_only",
        "reason": "中央通道位置，只处理单侧"
      },
      {
        "machine": "62L",
        "type": "skip",
        "strategy": "skip",
        "reason": "位置原因放弃处理"
      }
    ],
    "lane_rules": {
      "default_pairing": "nL_with_n-1R",  // 默认配对规则
      "entry_side": "L"                   // 默认入口侧
    }
  }
}
```

#### 特殊巷道配置
- **single_only**: 只处理单侧，不执行双侧巷道任务
- **skip**: 完全跳过，不处理该机台

## 常见问题与解决方案

### 1. 超时时间过长的问题

**症状**: 系统响应慢，异常时等待时间过长

**可能原因**:
- 检查间隔设置过大（如50000ms）
- 超时时间设置过长（如3000s）

**解决方案**:
- 检查间隔建议设置为200ms-2s
- 超时时间根据实际情况合理设置

### 2. 频繁超时的问题

**症状**: 经常出现超时错误，任务失败

**可能原因**:
- 超时时间设置过小
- 硬件响应慢

**解决方案**:
- 适当增加超时时间
- 检查硬件连接状态

### 3. 调头经常失败

**症状**: M511信号等待超时

**可能原因**:
- `turnAroundTimeout` 设置过小
- AGV调头机制有问题

**解决方案**:
- 检查AGV调头功能
- 适当增加调头超时时间

## 配置变更建议

1. **生产环境**: 使用推荐值，确保稳定性
2. **测试环境**: 可以适当缩短超时时间以快速发现问题
3. **调试阶段**: 将日志级别设置为DEBUG，获取详细信息

## 配置变更后的操作

1. 修改配置文件后需要重启后端程序
2. 检查日志确认配置加载成功
3. 进行基本功能测试验证配置正确性

---

**重要提醒**: 修改配置前请备份原始配置文件，确保可以快速恢复！ 