package agv

import "fmt"

// Status 表示AGV状态结构体
type Status struct {
	// 位置信息
	PosX            float32 `json:"posX"`
	PosY            float32 `json:"posY"`
	Angle           float32 `json:"angle"`
	LastPointID     uint32  `json:"lastPointID"`
	LastPathID      uint32  `json:"lastPathID"`
	PointSequenceID uint32  `json:"pointSequenceID"`
	Confidence      uint8   `json:"confidence"`

	// 运行状态
	VelX                float32 `json:"velX"`
	VelY                float32 `json:"velY"`
	AngVel              float32 `json:"angVel"`
	WorkMode            uint8   `json:"workMode"`
	AGVState            uint8   `json:"agvState"`
	CapabilitySetStatus uint8   `json:"capabilitySetStatus"`

	// 任务状态
	OrderID uint32 `json:"orderID"`
	TaskKey uint32 `json:"taskKey"`

	// 电池状态
	BatteryPercent float32 `json:"batteryPercent"`
	BatteryVoltage float32 `json:"batteryVoltage"`
	BatteryCurrent float32 `json:"batteryCurrent"`
	ChargingStatus uint8   `json:"chargingStatus"`

	// 异常事件
	EventCode  uint16 `json:"eventCode"`
	EventLevel uint16 `json:"eventLevel"`
}

// RunStatus 机器人运行状态（0x17命令返回）
type RunStatus struct {
	PositioningStatus uint8  `json:"positioningStatus"` // 定位状态：0-未定位，1-定位中，2-定位完成
	NavigationStatus  uint8  `json:"navigationStatus"`  // 导航状态：可能包含是否在导航中等信息（待确认）
	RunMode           uint8  `json:"runMode"`           // 运行模式：可能与工作模式类似（待确认）
	Reserved          []byte `json:"reserved"`          // 预留字段，保存其他未解析的数据
}

// WorkModeString 返回工作模式的字符串表示
func (s *Status) WorkModeString() string {
	workModes := map[uint8]string{
		0x00: "待机", 0x01: "手动", 0x02: "半自动",
		0x03: "自动", 0x04: "示教", 0x05: "服务", 0x06: "维修",
	}
	if mode, ok := workModes[s.WorkMode]; ok {
		return mode
	}
	return "未知"
}

// AGVStateString 返回AGV状态的字符串表示
func (s *Status) AGVStateString() string {
	agvStates := map[uint8]string{
		0x00: "空闲", 0x01: "运行", 0x02: "暂停",
		0x03: "未初始化", 0x04: "人工确认", 0x06: "导航失败",
	}
	if state, ok := agvStates[s.AGVState]; ok {
		return state
	}
	return "未知"
}

// ChargingStatusString 返回充电状态的字符串表示
func (s *Status) ChargingStatusString() string {
	chargingStates := map[uint8]string{
		0x00: "放电", 0x01: "充电",
	}
	if state, ok := chargingStates[s.ChargingStatus]; ok {
		return state
	}
	return "未知"
}

// NavigationStatus 导航状态信息
type NavigationStatus struct {
	// 导航状态
	Status uint8 `json:"status"`

	// 目标点ID
	TargetPointID uint16 `json:"target_point_id"`

	// 已经经过的路径点ID列表 (最多126个)
	PassedPathPoints []uint16 `json:"passed_path_points"`

	// 未经过的路径点ID列表 (最多126个)
	RemainingPathPoints []uint16 `json:"remaining_path_points"`
}

// GetStatusString 获取状态字符串描述
func (ns *NavigationStatus) GetStatusString() string {
	statusMap := map[uint8]string{
		0: "无到导航点任务",
		1: "等待",
		2: "正在前往导航点",
		3: "暂停",
		4: "完成",
		5: "失败",
		6: "退出",
		7: "等待开/关门操作",
	}

	if status, ok := statusMap[ns.Status]; ok {
		return status
	}
	return fmt.Sprintf("未知状态(%d)", ns.Status)
}

// IsNavigating 是否正在导航
func (ns *NavigationStatus) IsNavigating() bool {
	return ns.Status == 2 // 正在前往导航点
}

// IsCompleted 是否已完成
func (ns *NavigationStatus) IsCompleted() bool {
	return ns.Status == 4 // 完成
}

// IsFailed 是否失败
func (ns *NavigationStatus) IsFailed() bool {
	return ns.Status == 5 // 失败
}

// GetValidPassedPoints 获取有效的已经过路径点（过滤掉0值）
func (ns *NavigationStatus) GetValidPassedPoints() []uint16 {
	var validPoints []uint16
	for _, point := range ns.PassedPathPoints {
		if point != 0 {
			validPoints = append(validPoints, point)
		}
	}
	return validPoints
}

// GetValidRemainingPoints 获取有效的未经过路径点（过滤掉0值）
func (ns *NavigationStatus) GetValidRemainingPoints() []uint16 {
	var validPoints []uint16
	for _, point := range ns.RemainingPathPoints {
		if point != 0 {
			validPoints = append(validPoints, point)
		}
	}
	return validPoints
}
