package api

import (
	"encoding/binary"
	"encoding/json"
	"net/http"
	"time"

	"github.com/user/agv_nav/pkg/logger"
)

// PLC连接请求
type PLCConnectRequest struct {
	IP   string `json:"ip"`
	Port string `json:"port"`
}

// PLC读取请求
type PLCReadRequest struct {
	Type     string `json:"type"` // "coils", "discrete", "holding", "input"
	Address  uint16 `json:"address"`
	Quantity uint16 `json:"quantity"`
}

// PLC写入请求
type PLCWriteRequest struct {
	Type    string      `json:"type"` // "coil", "register"
	Address uint16      `json:"address"`
	Value   interface{} `json:"value"` // bool for coil, uint16 for register
}

// handlePLCConnect 处理PLC连接
func (s *Server) handlePLCConnect(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	apiLogger := logger.GetModuleLogger("api")
	startTime := time.Now()
	clientIP := r.RemoteAddr
	apiLogger.Info("PLC连接API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	var req PLCConnectRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		apiLogger.Error("PLC连接请求数据解析失败", "clientIP", clientIP, "error", err)
		s.respondError(w, "请求数据格式错误", http.StatusBadRequest)
		return
	}

	apiLogger.Info("处理PLC连接请求", "ip", req.IP, "port", req.Port, "clientIP", clientIP)
	err := s.plcController.Connect(req.IP, req.Port)
	if err != nil {
		apiLogger.Error("PLC连接失败", "ip", req.IP, "port", req.Port, "clientIP", clientIP, "error", err, "duration", time.Since(startTime))
		s.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	apiLogger.Info("PLC连接成功", "ip", req.IP, "port", req.Port, "clientIP", clientIP, "duration", time.Since(startTime))
	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"message": "PLC连接成功",
	})
}

// handlePLCDisconnect 处理PLC断开连接
func (s *Server) handlePLCDisconnect(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	apiLogger := logger.GetModuleLogger("api")
	clientIP := r.RemoteAddr
	apiLogger.Info("PLC断开连接API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	err := s.plcController.Disconnect()
	if err != nil {
		apiLogger.Error("PLC断开连接失败", "clientIP", clientIP, "error", err)
		s.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	apiLogger.Info("PLC断开连接成功", "clientIP", clientIP)
	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"message": "PLC已断开连接",
	})
}

// handlePLCStatus 获取PLC状态
func (s *Server) handlePLCStatus(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	connected := s.plcController.IsConnected()

	s.respondJSON(w, map[string]interface{}{
		"connected": connected,
	})
}

// handlePLCRead 处理PLC读取
func (s *Server) handlePLCRead(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	var req PLCReadRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		s.respondError(w, "请求数据格式错误", http.StatusBadRequest)
		return
	}

	var data []byte
	var err error

	switch req.Type {
	case "coils":
		data, err = s.plcController.ReadCoils(req.Address, req.Quantity)
	case "discrete":
		data, err = s.plcController.ReadDiscreteInputs(req.Address, req.Quantity)
	case "holding":
		data, err = s.plcController.ReadHoldingRegisters(req.Address, req.Quantity)
	case "input":
		data, err = s.plcController.ReadInputRegisters(req.Address, req.Quantity)
	default:
		s.respondError(w, "不支持的读取类型", http.StatusBadRequest)
		return
	}

	if err != nil {
		s.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 解析数据根据类型
	result := s.parsePLCData(req.Type, data)

	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"type":    req.Type,
		"address": req.Address,
		"data":    result,
	})
}

// handlePLCWrite 处理PLC写入
func (s *Server) handlePLCWrite(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	var req PLCWriteRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		s.respondError(w, "请求数据格式错误", http.StatusBadRequest)
		return
	}

	var err error

	switch req.Type {
	case "coil":
		// 转换值为bool
		value, ok := req.Value.(bool)
		if !ok {
			s.respondError(w, "线圈值必须是布尔类型", http.StatusBadRequest)
			return
		}
		_, err = s.plcController.WriteSingleCoil(req.Address, value)
	case "register":
		// 转换值为uint16
		var value uint16
		switch v := req.Value.(type) {
		case float64:
			value = uint16(v)
		case int:
			value = uint16(v)
		case uint16:
			value = v
		default:
			s.respondError(w, "寄存器值必须是数字类型", http.StatusBadRequest)
			return
		}
		_, err = s.plcController.WriteSingleRegister(req.Address, value)
	default:
		s.respondError(w, "不支持的写入类型", http.StatusBadRequest)
		return
	}

	if err != nil {
		s.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"message": "写入成功",
		"type":    req.Type,
		"address": req.Address,
		"value":   req.Value,
	})
}

// parsePLCData 解析PLC数据
func (s *Server) parsePLCData(dataType string, data []byte) interface{} {
	switch dataType {
	case "coils", "discrete":
		// 位数据，每个字节包含8个位
		bits := make([]bool, len(data)*8)
		for i, b := range data {
			for j := 0; j < 8; j++ {
				bits[i*8+j] = (b & (1 << j)) != 0
			}
		}
		return bits
	case "holding", "input":
		// 寄存器数据，每2个字节一个16位寄存器
		registers := make([]uint16, len(data)/2)
		for i := 0; i < len(registers); i++ {
			registers[i] = binary.BigEndian.Uint16(data[i*2 : i*2+2])
		}
		return registers
	}
	return nil
}
