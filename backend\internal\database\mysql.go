package database

import (
	"database/sql"
	"fmt"

	_ "github.com/go-sql-driver/mysql"
)

// MySQLDB MySQL数据库操作
type MySQLDB struct {
	db *sql.DB
}

// NewMySQLDB 创建新的MySQL数据库实例
func NewMySQLDB(host, port, username, password, database string) (*MySQLDB, error) {
	// 构建MySQL连接字符串
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		username, password, host, port, database)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("打开MySQL连接失败: %v", err)
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("连接MySQL失败: %v", err)
	}

	return &MySQLDB{db: db}, nil
}

// Close 关闭数据库连接
func (m *MySQLDB) Close() error {
	if m.db != nil {
		return m.db.Close()
	}
	return nil
}

// GetDB 获取原始数据库连接对象
func (m *MySQLDB) GetDB() *sql.DB {
	return m.db
}
