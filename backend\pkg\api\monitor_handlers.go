package api

import (
	"encoding/json"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/user/agv_nav/internal/task"
)

// FlowStatus 流程状态结构
type FlowStatus struct {
	// 当前任务信息
	CurrentTask struct {
		TaskID              string `json:"taskId"`
		GroupID             string `json:"groupId"`
		Type                string `json:"type"` // "single" | "lane"
		Machine             string `json:"machine"`
		Side                string `json:"side,omitempty"` // "L" | "R"
		TotalMachines       int    `json:"totalMachines"`
		CurrentMachineIndex int    `json:"currentMachineIndex"`
	} `json:"currentTask"`

	// 当前执行步骤
	CurrentStep struct {
		Phase      string    `json:"phase"`
		Step       string    `json:"step"`
		StepNumber int       `json:"stepNumber"`
		TotalSteps int       `json:"totalSteps"`
		StartTime  time.Time `json:"startTime"`
		Estimated  bool      `json:"estimated"`
	} `json:"currentStep"`

	// PLC通信状态
	PLCCommunication struct {
		LastAction  string      `json:"lastAction"` // "read" | "write" | "none"
		Address     string      `json:"address"`
		AddressType string      `json:"addressType"` // "M" | "D"
		Value       interface{} `json:"value"`
		Description string      `json:"description"`
		Timestamp   time.Time   `json:"timestamp"`
		Success     bool        `json:"success"`
		Error       string      `json:"error,omitempty"`
	} `json:"plcCommunication"`

	// 导航状态
	Navigation struct {
		ControlMode     string `json:"controlMode"` // "laser" | "plc"
		CurrentPosition *struct {
			X     float64 `json:"x"`
			Y     float64 `json:"y"`
			Angle float64 `json:"angle"`
		} `json:"currentPosition,omitempty"`
		TargetPoint *struct {
			Name  string  `json:"name"`
			X     float64 `json:"x"`
			Y     float64 `json:"y"`
			Angle float64 `json:"angle"`
		} `json:"targetPoint,omitempty"`
		NavigationStatus string `json:"navigationStatus"` // "idle" | "moving" | "arrived" | "failed"
	} `json:"navigation"`

	// 锭号处理状态
	SpindleProcessing struct {
		CurrentSpindle     *int  `json:"currentSpindle,omitempty"`
		TotalSpindles      int   `json:"totalSpindles"`
		ProcessedSpindles  int   `json:"processedSpindles"`
		SpindleList        []int `json:"spindleList"`
		CurrentSpindleData *struct {
			Direction int     `json:"direction"`
			Distance  float64 `json:"distance"`
			Roller    int     `json:"roller"`
		} `json:"currentSpindleData,omitempty"`
		VerificationStatus  string `json:"verificationStatus"` // "pending" | "verifying" | "success" | "failed"
		VerificationAttempt int    `json:"verificationAttempt"`
		MaxAttempts         int    `json:"maxAttempts"`
	} `json:"spindleProcessing"`

	// 任务进度
	Progress struct {
		Phase                  string  `json:"phase"`                            // "navigation" | "plc_handover" | "spindle_processing" | "completion" | "error"
		OverallProgress        float64 `json:"overallProgress"`                  // 0-100
		CurrentPhaseProgress   float64 `json:"currentPhaseProgress"`             // 0-100
		EstimatedTimeRemaining *int    `json:"estimatedTimeRemaining,omitempty"` // 秒
	} `json:"progress"`

	// 错误信息
	Errors []struct {
		Timestamp time.Time   `json:"timestamp"`
		Type      string      `json:"type"` // "warning" | "error" | "critical"
		Message   string      `json:"message"`
		Context   interface{} `json:"context,omitempty"`
	} `json:"errors"`
}

// 全局流程状态，供任务模块更新
var globalFlowStatus = &FlowStatus{
	Progress: struct {
		Phase                  string  `json:"phase"`
		OverallProgress        float64 `json:"overallProgress"`
		CurrentPhaseProgress   float64 `json:"currentPhaseProgress"`
		EstimatedTimeRemaining *int    `json:"estimatedTimeRemaining,omitempty"`
	}{
		Phase:                "navigation",
		OverallProgress:      0,
		CurrentPhaseProgress: 0,
	},
	PLCCommunication: struct {
		LastAction  string      `json:"lastAction"`
		Address     string      `json:"address"`
		AddressType string      `json:"addressType"`
		Value       interface{} `json:"value"`
		Description string      `json:"description"`
		Timestamp   time.Time   `json:"timestamp"`
		Success     bool        `json:"success"`
		Error       string      `json:"error,omitempty"`
	}{
		LastAction: "none",
	},
	Navigation: struct {
		ControlMode     string `json:"controlMode"`
		CurrentPosition *struct {
			X     float64 `json:"x"`
			Y     float64 `json:"y"`
			Angle float64 `json:"angle"`
		} `json:"currentPosition,omitempty"`
		TargetPoint *struct {
			Name  string  `json:"name"`
			X     float64 `json:"x"`
			Y     float64 `json:"y"`
			Angle float64 `json:"angle"`
		} `json:"targetPoint,omitempty"`
		NavigationStatus string `json:"navigationStatus"`
	}{
		ControlMode:      "laser",
		NavigationStatus: "idle",
	},
}

// handleGetFlowStatus 获取流程状态
func (s *Server) handleGetFlowStatus(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		return
	}

	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"data":    globalFlowStatus,
	})
}

// BroadcastFlowStatus 广播流程状态给所有连接的客户端
func (s *Server) BroadcastFlowStatus() {
	// 使用简单流程状态而不是复杂状态
	simpleStatus := task.GetSimpleFlowStatus()
	statusData, err := json.Marshal(map[string]interface{}{
		"type": "flow_status",
		"data": simpleStatus,
	})
	if err != nil {
		log.Printf("编码流程状态数据失败: %v", err)
		return
	}

	s.clientsMutex.RLock()
	// 复制客户端列表和锁引用，避免在持有读锁时进行写操作
	clientList := make([]*websocket.Conn, 0, len(s.clients))
	lockList := make([]*sync.Mutex, 0, len(s.clients))
	for client := range s.clients {
		if lock, exists := s.clientLocks[client]; exists {
			clientList = append(clientList, client)
			lockList = append(lockList, lock)
		}
	}
	s.clientsMutex.RUnlock()

	// 向每个客户端发送消息，使用各自的锁保护写入
	for i, client := range clientList {
		lock := lockList[i]
		go func(c *websocket.Conn, l *sync.Mutex) {
			l.Lock()
			defer l.Unlock()

			err := c.WriteMessage(websocket.TextMessage, statusData)
			if err != nil {
				log.Printf("发送流程状态WebSocket消息失败: %v", err)
				// 标记连接需要清理
				s.clientsMutex.Lock()
				if _, exists := s.clients[c]; exists {
					delete(s.clients, c)
					delete(s.clientLocks, c)
					c.Close()
				}
				s.clientsMutex.Unlock()
			}
		}(client, lock)
	}
}

// 供任务模块调用的流程状态更新函数

// UpdateCurrentTask 更新当前任务信息
func UpdateCurrentTask(taskID, groupID, taskType, machine, side string, totalMachines, currentIndex int) {
	globalFlowStatus.CurrentTask.TaskID = taskID
	globalFlowStatus.CurrentTask.GroupID = groupID
	globalFlowStatus.CurrentTask.Type = taskType
	globalFlowStatus.CurrentTask.Machine = machine
	globalFlowStatus.CurrentTask.Side = side
	globalFlowStatus.CurrentTask.TotalMachines = totalMachines
	globalFlowStatus.CurrentTask.CurrentMachineIndex = currentIndex

	// 触发广播
	if globalServer != nil {
		globalServer.BroadcastFlowStatus()
	}
}

// UpdateCurrentStep 更新当前执行步骤
func UpdateCurrentStep(phase, step string, stepNumber, totalSteps int, estimated bool) {
	globalFlowStatus.CurrentStep.Phase = phase
	globalFlowStatus.CurrentStep.Step = step
	globalFlowStatus.CurrentStep.StepNumber = stepNumber
	globalFlowStatus.CurrentStep.TotalSteps = totalSteps
	globalFlowStatus.CurrentStep.StartTime = time.Now()
	globalFlowStatus.CurrentStep.Estimated = estimated

	// 更新进度阶段
	globalFlowStatus.Progress.Phase = phase
	globalFlowStatus.Progress.CurrentPhaseProgress = float64(stepNumber) / float64(totalSteps) * 100

	// 触发广播
	if globalServer != nil {
		globalServer.BroadcastFlowStatus()
	}
}

// UpdatePLCCommunication 更新PLC通信状态
func UpdatePLCCommunication(action, address, addressType string, value interface{}, success bool, errorMsg string) {
	globalFlowStatus.PLCCommunication.LastAction = action
	globalFlowStatus.PLCCommunication.Address = address
	globalFlowStatus.PLCCommunication.AddressType = addressType
	globalFlowStatus.PLCCommunication.Value = value
	globalFlowStatus.PLCCommunication.Success = success
	globalFlowStatus.PLCCommunication.Error = errorMsg
	globalFlowStatus.PLCCommunication.Timestamp = time.Now()

	// 设置地址描述
	globalFlowStatus.PLCCommunication.Description = getPLCAddressDescription(address, addressType)

	// 触发广播
	if globalServer != nil {
		globalServer.BroadcastFlowStatus()
	}
}

// UpdateNavigation 更新导航状态
func UpdateNavigation(controlMode, navigationStatus string, currentPos, targetPos map[string]interface{}) {
	globalFlowStatus.Navigation.ControlMode = controlMode
	globalFlowStatus.Navigation.NavigationStatus = navigationStatus

	// 更新当前位置
	if currentPos != nil {
		if globalFlowStatus.Navigation.CurrentPosition == nil {
			globalFlowStatus.Navigation.CurrentPosition = &struct {
				X     float64 `json:"x"`
				Y     float64 `json:"y"`
				Angle float64 `json:"angle"`
			}{}
		}
		if x, ok := currentPos["x"].(float64); ok {
			globalFlowStatus.Navigation.CurrentPosition.X = x
		}
		if y, ok := currentPos["y"].(float64); ok {
			globalFlowStatus.Navigation.CurrentPosition.Y = y
		}
		if angle, ok := currentPos["angle"].(float64); ok {
			globalFlowStatus.Navigation.CurrentPosition.Angle = angle
		}
	}

	// 更新目标位置
	if targetPos != nil {
		if globalFlowStatus.Navigation.TargetPoint == nil {
			globalFlowStatus.Navigation.TargetPoint = &struct {
				Name  string  `json:"name"`
				X     float64 `json:"x"`
				Y     float64 `json:"y"`
				Angle float64 `json:"angle"`
			}{}
		}
		if name, ok := targetPos["name"].(string); ok {
			globalFlowStatus.Navigation.TargetPoint.Name = name
		}
		if x, ok := targetPos["x"].(float64); ok {
			globalFlowStatus.Navigation.TargetPoint.X = x
		}
		if y, ok := targetPos["y"].(float64); ok {
			globalFlowStatus.Navigation.TargetPoint.Y = y
		}
		if angle, ok := targetPos["angle"].(float64); ok {
			globalFlowStatus.Navigation.TargetPoint.Angle = angle
		}
	}

	// 触发广播
	if globalServer != nil {
		globalServer.BroadcastFlowStatus()
	}
}

// UpdateSpindleProcessing 更新锭号处理状态
func UpdateSpindleProcessing(currentSpindle *int, totalSpindles, processedSpindles int, spindleList []int,
	currentData map[string]interface{}, verificationStatus string, verificationAttempt, maxAttempts int) {

	globalFlowStatus.SpindleProcessing.CurrentSpindle = currentSpindle
	globalFlowStatus.SpindleProcessing.TotalSpindles = totalSpindles
	globalFlowStatus.SpindleProcessing.ProcessedSpindles = processedSpindles
	globalFlowStatus.SpindleProcessing.SpindleList = spindleList
	globalFlowStatus.SpindleProcessing.VerificationStatus = verificationStatus
	globalFlowStatus.SpindleProcessing.VerificationAttempt = verificationAttempt
	globalFlowStatus.SpindleProcessing.MaxAttempts = maxAttempts

	// 更新当前锭号数据
	if currentData != nil {
		if globalFlowStatus.SpindleProcessing.CurrentSpindleData == nil {
			globalFlowStatus.SpindleProcessing.CurrentSpindleData = &struct {
				Direction int     `json:"direction"`
				Distance  float64 `json:"distance"`
				Roller    int     `json:"roller"`
			}{}
		}
		if direction, ok := currentData["direction"].(int); ok {
			globalFlowStatus.SpindleProcessing.CurrentSpindleData.Direction = direction
		}
		if distance, ok := currentData["distance"].(float64); ok {
			globalFlowStatus.SpindleProcessing.CurrentSpindleData.Distance = distance
		}
		if roller, ok := currentData["roller"].(int); ok {
			globalFlowStatus.SpindleProcessing.CurrentSpindleData.Roller = roller
		}
	}

	// 更新总体进度
	if totalSpindles > 0 {
		globalFlowStatus.Progress.OverallProgress = float64(processedSpindles) / float64(totalSpindles) * 100
	}

	// 触发广播
	if globalServer != nil {
		globalServer.BroadcastFlowStatus()
	}
}

// AddFlowError 添加流程错误
func AddFlowError(errorType, message string, context interface{}) {
	error := struct {
		Timestamp time.Time   `json:"timestamp"`
		Type      string      `json:"type"`
		Message   string      `json:"message"`
		Context   interface{} `json:"context,omitempty"`
	}{
		Timestamp: time.Now(),
		Type:      errorType,
		Message:   message,
		Context:   context,
	}

	// 添加到错误列表，最多保留20个错误
	globalFlowStatus.Errors = append(globalFlowStatus.Errors, error)
	if len(globalFlowStatus.Errors) > 20 {
		globalFlowStatus.Errors = globalFlowStatus.Errors[1:]
	}

	// 触发广播
	if globalServer != nil {
		globalServer.BroadcastFlowStatus()
	}
}

// ClearFlowErrors 清空流程错误
func ClearFlowErrors() {
	globalFlowStatus.Errors = []struct {
		Timestamp time.Time   `json:"timestamp"`
		Type      string      `json:"type"`
		Message   string      `json:"message"`
		Context   interface{} `json:"context,omitempty"`
	}{}

	// 触发广播
	if globalServer != nil {
		globalServer.BroadcastFlowStatus()
	}
}

// ResetFlowStatus 重置流程状态（用于任务停止时清空状态）
func ResetFlowStatus() {
	// 重置当前任务信息
	globalFlowStatus.CurrentTask.TaskID = ""
	globalFlowStatus.CurrentTask.GroupID = ""
	globalFlowStatus.CurrentTask.Type = ""
	globalFlowStatus.CurrentTask.Machine = ""
	globalFlowStatus.CurrentTask.Side = ""
	globalFlowStatus.CurrentTask.TotalMachines = 0
	globalFlowStatus.CurrentTask.CurrentMachineIndex = 0

	// 重置当前步骤
	globalFlowStatus.CurrentStep.Phase = ""
	globalFlowStatus.CurrentStep.Step = ""
	globalFlowStatus.CurrentStep.StepNumber = 0
	globalFlowStatus.CurrentStep.TotalSteps = 0
	globalFlowStatus.CurrentStep.StartTime = time.Time{}
	globalFlowStatus.CurrentStep.Estimated = false

	// 重置PLC通信状态
	globalFlowStatus.PLCCommunication.LastAction = "none"
	globalFlowStatus.PLCCommunication.Address = ""
	globalFlowStatus.PLCCommunication.AddressType = ""
	globalFlowStatus.PLCCommunication.Value = nil
	globalFlowStatus.PLCCommunication.Description = ""
	globalFlowStatus.PLCCommunication.Success = false
	globalFlowStatus.PLCCommunication.Error = ""
	globalFlowStatus.PLCCommunication.Timestamp = time.Time{}

	// 重置导航状态
	globalFlowStatus.Navigation.ControlMode = "laser"
	globalFlowStatus.Navigation.CurrentPosition = nil
	globalFlowStatus.Navigation.TargetPoint = nil
	globalFlowStatus.Navigation.NavigationStatus = "idle"

	// 重置锭号处理状态
	globalFlowStatus.SpindleProcessing.CurrentSpindle = nil
	globalFlowStatus.SpindleProcessing.TotalSpindles = 0
	globalFlowStatus.SpindleProcessing.ProcessedSpindles = 0
	globalFlowStatus.SpindleProcessing.SpindleList = []int{}
	globalFlowStatus.SpindleProcessing.CurrentSpindleData = nil
	globalFlowStatus.SpindleProcessing.VerificationStatus = "pending"
	globalFlowStatus.SpindleProcessing.VerificationAttempt = 0
	globalFlowStatus.SpindleProcessing.MaxAttempts = 5

	// 重置进度
	globalFlowStatus.Progress.Phase = "navigation"
	globalFlowStatus.Progress.OverallProgress = 0
	globalFlowStatus.Progress.CurrentPhaseProgress = 0
	globalFlowStatus.Progress.EstimatedTimeRemaining = nil

	// 清空错误列表
	globalFlowStatus.Errors = []struct {
		Timestamp time.Time   `json:"timestamp"`
		Type      string      `json:"type"`
		Message   string      `json:"message"`
		Context   interface{} `json:"context,omitempty"`
	}{}

	log.Printf("流程状态已重置")

	// 触发广播
	if globalServer != nil {
		globalServer.BroadcastFlowStatus()
	}
}

// getPLCAddressDescription 获取PLC地址描述
func getPLCAddressDescription(address, addressType string) string {
	descriptions := map[string]string{
		// M地址（线圈）
		"M501": "控制权确认信号",
		"M502": "机器人运行条件",
		"M505": "方向验证信号",
		"M509": "PLC准备信号",
		"M510": "AGV到达原点信号",
		"M511": "调头完成信号",
		"M601": "控制权请求信号",
		"M602": "任务开始信号",
		"M603": "数据写入完成信号",
		"M604": "码带方向信号",
		"M605": "验证完成信号",
		"M606": "机台完成信号",
		"M607": "切换回激光控制",
		"M608": "调头信号",
		"M610": "回到原点信号",
		"M611": "原点方向信号",
		"M612": "强制取得控制权",

		// D地址（寄存器）
		"D500": "PLC工作状态 (0=工作中, 1=成功, 2=失败)",
		"D501": "皮辊验证 (1=左, 2=右)",
		"D502": "码值验证(低位)",
		"D503": "码值验证(高位)",
		"D600": "皮辊方向 (1=左, 2=右)",
		"D602": "码值(低位)",
		"D603": "码值(高位)",
		"D604": "原点码值",
	}

	if desc, ok := descriptions[address]; ok {
		return desc
	}
	return addressType + "地址 " + address
}
