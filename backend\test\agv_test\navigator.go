package agv_test

import (
	"fmt"
	"log"
	"math"
	"time"

	"github.com/user/agv_nav/pkg/agv"
)

// AGVNavigator AGV导航器
type AGVNavigator struct {
	testController *AGVTestController
}

// NavigationResult 导航结果
type NavigationResult struct {
	Success     bool                   `json:"success"`
	Message     string                 `json:"message"`
	StartTime   time.Time              `json:"start_time"`
	EndTime     time.Time              `json:"end_time"`
	Duration    int64                  `json:"duration_ms"`
	TargetPoint int                    `json:"target_point"`
	Data        map[string]interface{} `json:"data,omitempty"`
}

// WorkflowResult 工作流程结果
type WorkflowResult struct {
	Success   bool                   `json:"success"`
	Message   string                 `json:"message"`
	StartTime time.Time              `json:"start_time"`
	EndTime   time.Time              `json:"end_time"`
	Duration  int64                  `json:"duration_ms"`
	Steps     []WorkflowStep         `json:"steps"`
	Data      map[string]interface{} `json:"data,omitempty"`
}

// WorkflowStep 工作流程步骤
type WorkflowStep struct {
	ID          int                    `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Success     bool                   `json:"success"`
	Message     string                 `json:"message"`
	StartTime   time.Time              `json:"start_time"`
	EndTime     time.Time              `json:"end_time"`
	Duration    int64                  `json:"duration_ms"`
	Data        map[string]interface{} `json:"data,omitempty"`
}

// NewAGVNavigator 创建AGV导航器实例
func NewAGVNavigator(controller *AGVTestController) *AGVNavigator {
	return &AGVNavigator{
		testController: controller,
	}
}

// NavigateToPoint 导航到指定导航点（复用主程序的完整导航流程）
func (n *AGVNavigator) NavigateToPoint(pointID int) (*NavigationResult, error) {
	startTime := time.Now()
	result := &NavigationResult{
		TargetPoint: pointID,
		StartTime:   startTime,
		Data:        make(map[string]interface{}),
	}

	// 检查AGV连接状态
	if !n.testController.IsConnected() {
		result.Success = false
		result.Message = "AGV未连接，无法执行导航"
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime).Milliseconds()
		return result, fmt.Errorf(result.Message)
	}

	agvController := n.testController.agvController

	// 检查AGV是否在自动模式
	status := agvController.GetStatus()
	if status != nil && status.WorkMode != 0x03 { // 0x03 = 自动模式
		log.Printf("AGV当前工作模式: %s，尝试切换到自动模式", status.WorkModeString())

		// 自动切换到自动模式
		err := agvController.SwitchWorkMode(true)
		if err != nil {
			result.Success = false
			result.Message = fmt.Sprintf("切换AGV到自动模式失败: %v", err)
			result.EndTime = time.Now()
			result.Duration = time.Since(startTime).Milliseconds()
			return result, fmt.Errorf(result.Message)
		}

		// 等待模式切换完成
		time.Sleep(2 * time.Second)

		// 验证模式切换
		err = agvController.QueryStatus()
		if err != nil {
			result.Success = false
			result.Message = fmt.Sprintf("查询AGV状态失败: %v", err)
			result.EndTime = time.Now()
			result.Duration = time.Since(startTime).Milliseconds()
			return result, fmt.Errorf(result.Message)
		}

		status = agvController.GetStatus()
		if status == nil || status.WorkMode != 0x03 {
			result.Success = false
			result.Message = fmt.Sprintf("AGV模式切换失败，当前模式: %s", status.WorkModeString())
			result.EndTime = time.Now()
			result.Duration = time.Since(startTime).Milliseconds()
			return result, fmt.Errorf(result.Message)
		}
	}

	// 发送导航命令 - 复用已有的SendNavToPointID方法
	err := agvController.SendNavToPointID(uint16(pointID))
	if err != nil {
		result.Success = false
		result.Message = fmt.Sprintf("发送导航命令失败: %v", err)
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime).Milliseconds()
		return result, fmt.Errorf(result.Message)
	}

	log.Printf("导航命令已发送，目标点: %d", pointID)

	// 等待导航完成
	err = n.waitForNavigationComplete(pointID)
	if err != nil {
		result.Success = false
		result.Message = fmt.Sprintf("导航执行失败: %v", err)
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime).Milliseconds()
		return result, fmt.Errorf(result.Message)
	}

	// 导航成功
	result.Success = true
	result.Message = fmt.Sprintf("AGV已成功到达导航点: %d", pointID)
	result.EndTime = time.Now()
	result.Duration = time.Since(startTime).Milliseconds()

	// 添加最终状态数据
	finalStatus := agvController.GetStatus()
	if finalStatus != nil {
		result.Data["final_position"] = map[string]interface{}{
			"x":     finalStatus.PosX,
			"y":     finalStatus.PosY,
			"angle": finalStatus.Angle,
		}
		result.Data["last_point_id"] = finalStatus.LastPointID
		result.Data["agv_state"] = finalStatus.AGVStateString()
	}

	log.Printf("AGV已成功到达导航点: %d", pointID)
	return result, nil
}

// NavigateToMachine 导航到指定机器位置（复用主程序数据库查询）
func (n *AGVNavigator) NavigateToMachine(machine string) (*NavigationResult, error) {
	startTime := time.Now()
	result := &NavigationResult{
		StartTime: startTime,
		Data:      make(map[string]interface{}),
	}

	// 步骤1: 查询导航点（复用主程序数据库查询）
	log.Printf("查询机器 %s 的导航点", machine)
	navPoint, err := n.testController.QueryNavigationPoint(machine)
	if err != nil {
		result.Success = false
		result.Message = fmt.Sprintf("查询导航点失败: %v", err)
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime).Milliseconds()
		return result, fmt.Errorf(result.Message)
	}

	log.Printf("机器 %s 的导航点: %d", machine, navPoint)
	result.TargetPoint = navPoint
	result.Data["machine"] = machine
	result.Data["navigation_point"] = navPoint

	// 步骤2: 执行导航
	navResult, err := n.NavigateToPoint(navPoint)
	if err != nil {
		result.Success = false
		result.Message = fmt.Sprintf("导航到机器 %s 失败: %v", machine, err)
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime).Milliseconds()
		return result, err
	}

	// 合并导航结果
	result.Success = navResult.Success
	result.Message = fmt.Sprintf("AGV已成功到达机器 %s 的位置 (导航点 %d)", machine, navPoint)
	result.EndTime = time.Now()
	result.Duration = time.Since(startTime).Milliseconds()

	// 合并数据
	for k, v := range navResult.Data {
		result.Data[k] = v
	}

	return result, nil
}

// SwitchWorkMode 切换AGV工作模式
func (n *AGVNavigator) SwitchWorkMode(isAuto bool) (*TestResult, error) {
	startTime := time.Now()
	testID := fmt.Sprintf("switch_mode_%d", startTime.Unix())
	modeStr := "手动"
	if isAuto {
		modeStr = "自动"
	}

	result := TestResult{
		ID:          testID,
		TestType:    "work_mode_switch",
		Description: fmt.Sprintf("切换AGV工作模式到%s模式", modeStr),
		StartTime:   startTime,
		Data:        make(map[string]interface{}),
	}

	if !n.testController.IsConnected() {
		result.Success = false
		result.Message = "AGV未连接，无法切换工作模式"
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime).Milliseconds()
		return &result, fmt.Errorf(result.Message)
	}

	// 执行模式切换
	err := n.testController.agvController.SwitchWorkMode(isAuto)
	if err != nil {
		result.Success = false
		result.Message = fmt.Sprintf("切换到%s模式失败: %v", modeStr, err)
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime).Milliseconds()
		return &result, err
	}

	// 验证模式切换结果
	time.Sleep(1 * time.Second)
	err = n.testController.agvController.QueryStatus()
	if err != nil {
		result.Success = false
		result.Message = fmt.Sprintf("验证模式切换失败: %v", err)
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime).Milliseconds()
		return &result, err
	}

	status := n.testController.agvController.GetStatus()
	if status != nil {
		expectedMode := uint8(0x01) // 手动模式
		if isAuto {
			expectedMode = 0x03 // 自动模式
		}

		if status.WorkMode == expectedMode {
			result.Success = true
			result.Message = fmt.Sprintf("AGV工作模式已成功切换到%s模式", modeStr)
		} else {
			result.Success = false
			result.Message = fmt.Sprintf("AGV工作模式切换失败，期望%s模式，实际%s", modeStr, status.WorkModeString())
		}

		result.Data["target_mode"] = modeStr
		result.Data["current_mode"] = status.WorkModeString()
		result.Data["work_mode_code"] = status.WorkMode
	}

	result.EndTime = time.Now()
	result.Duration = time.Since(startTime).Milliseconds()

	// 添加到测试结果
	n.testController.addTestResult(result)

	return &result, nil
}

// QueryStatus 查询AGV状态
func (n *AGVNavigator) QueryStatus() (*agv.Status, error) {
	if !n.testController.IsConnected() {
		return nil, fmt.Errorf("AGV未连接")
	}

	err := n.testController.agvController.QueryStatus()
	if err != nil {
		return nil, fmt.Errorf("查询AGV状态失败: %v", err)
	}

	return n.testController.agvController.GetStatus(), nil
}

// LaserNavInit 激光导航初始化（复用主程序完整流程）
func (n *AGVNavigator) LaserNavInit(x, y, angle float64) (*TestResult, error) {
	startTime := time.Now()
	testID := fmt.Sprintf("laser_init_%d", startTime.Unix())

	result := TestResult{
		ID:          testID,
		TestType:    "laser_navigation_init",
		Description: fmt.Sprintf("激光导航初始化 (%.2f, %.2f, %.2f)", x, y, angle),
		StartTime:   startTime,
		Data:        make(map[string]interface{}),
	}

	if !n.testController.IsConnected() {
		result.Success = false
		result.Message = "AGV未连接，无法执行激光导航初始化"
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime).Milliseconds()
		return &result, fmt.Errorf(result.Message)
	}

	// 执行激光导航初始化（复用主程序的InitializeLaserNavigation方法）
	err := n.testController.agvController.InitializeLaserNavigation(x, y, angle)
	if err != nil {
		result.Success = false
		result.Message = fmt.Sprintf("激光导航初始化失败: %v", err)
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime).Milliseconds()
		return &result, err
	}

	// 查询最终状态
	finalStatus := n.testController.agvController.GetStatus()
	if finalStatus != nil {
		result.Data["target_position"] = map[string]interface{}{
			"x":     x,
			"y":     y,
			"angle": angle,
		}
		result.Data["final_position"] = map[string]interface{}{
			"x":     finalStatus.PosX,
			"y":     finalStatus.PosY,
			"angle": finalStatus.Angle,
		}
		result.Data["work_mode"] = finalStatus.WorkModeString()
		result.Data["agv_state"] = finalStatus.AGVStateString()
	}

	result.Success = true
	result.Message = fmt.Sprintf("激光导航初始化成功 (%.2f, %.2f, %.2f)", x, y, angle)
	result.EndTime = time.Now()
	result.Duration = time.Since(startTime).Milliseconds()

	// 添加到测试结果
	n.testController.addTestResult(result)

	return &result, nil
}

// TestLaneNavigation 测试完整巷道导航工作流程
func (n *AGVNavigator) TestLaneNavigation(leftMachine, rightMachine string) (*WorkflowResult, error) {
	startTime := time.Now()
	result := &WorkflowResult{
		StartTime: startTime,
		Data:      make(map[string]interface{}),
		Steps:     make([]WorkflowStep, 0),
	}

	result.Data["left_machine"] = leftMachine
	result.Data["right_machine"] = rightMachine

	// 步骤1: 导航到巷道入口（L侧）
	step1 := n.executeWorkflowStep(1, "导航到巷道入口", fmt.Sprintf("导航到左侧机器 %s", leftMachine), func() error {
		_, err := n.NavigateToMachine(leftMachine)
		return err
	})
	result.Steps = append(result.Steps, step1)
	if !step1.Success {
		result.Success = false
		result.Message = "巷道导航测试失败：无法到达巷道入口"
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime).Milliseconds()
		return result, fmt.Errorf(result.Message)
	}

	// 步骤2: 模拟巷道内处理（仅状态查询，不实际操作PLC）
	step2 := n.executeWorkflowStep(2, "模拟L侧处理", "查询AGV状态，模拟左侧处理", func() error {
		status, err := n.QueryStatus()
		if err != nil {
			return err
		}
		log.Printf("L侧处理模拟完成，AGV位置: (%.2f, %.2f)", status.PosX, status.PosY)
		return nil
	})
	result.Steps = append(result.Steps, step2)

	// 步骤3: 模拟调头过程（仅记录，实际巷道中由PLC控制）
	step3 := n.executeWorkflowStep(3, "模拟调头过程", "记录调头阶段", func() error {
		log.Printf("模拟巷道内调头过程...")
		time.Sleep(2 * time.Second) // 模拟调头时间
		return nil
	})
	result.Steps = append(result.Steps, step3)

	// 步骤4: 导航到右侧机器位置
	step4 := n.executeWorkflowStep(4, "导航到右侧位置", fmt.Sprintf("导航到右侧机器 %s", rightMachine), func() error {
		_, err := n.NavigateToMachine(rightMachine)
		return err
	})
	result.Steps = append(result.Steps, step4)
	if !step4.Success {
		result.Success = false
		result.Message = "巷道导航测试失败：无法到达右侧位置"
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime).Milliseconds()
		return result, fmt.Errorf(result.Message)
	}

	// 步骤5: 模拟R侧处理
	step5 := n.executeWorkflowStep(5, "模拟R侧处理", "查询AGV状态，模拟右侧处理", func() error {
		status, err := n.QueryStatus()
		if err != nil {
			return err
		}
		log.Printf("R侧处理模拟完成，AGV位置: (%.2f, %.2f)", status.PosX, status.PosY)
		return nil
	})
	result.Steps = append(result.Steps, step5)

	// 工作流程完成
	result.Success = true
	result.Message = fmt.Sprintf("巷道导航测试完成：%s <-> %s", leftMachine, rightMachine)
	result.EndTime = time.Now()
	result.Duration = time.Since(startTime).Milliseconds()

	return result, nil
}

// executeWorkflowStep 执行工作流程步骤
func (n *AGVNavigator) executeWorkflowStep(id int, name, description string, fn func() error) WorkflowStep {
	startTime := time.Now()
	step := WorkflowStep{
		ID:          id,
		Name:        name,
		Description: description,
		StartTime:   startTime,
		Data:        make(map[string]interface{}),
	}

	err := fn()
	if err != nil {
		step.Success = false
		step.Message = err.Error()
	} else {
		step.Success = true
		step.Message = "步骤执行成功"
	}

	step.EndTime = time.Now()
	step.Duration = time.Since(startTime).Milliseconds()

	log.Printf("工作流程步骤 %d [%s] 完成: %s", id, name, step.Message)
	return step
}

// waitForNavigationComplete 等待导航完成（复用主程序逻辑）
func (n *AGVNavigator) waitForNavigationComplete(targetPoint int) error {
	maxWaitTime := 300 * time.Second // 5分钟超时
	checkInterval := 2 * time.Second // 2秒检查间隔

	timeout := time.After(maxWaitTime)
	ticker := time.NewTicker(checkInterval)
	defer ticker.Stop()

	agvController := n.testController.agvController

	for {
		select {
		case <-timeout:
			return fmt.Errorf("导航超时，超过%v未完成", maxWaitTime)

		case <-ticker.C:
			// 查询AGV当前状态
			err := agvController.QueryStatus()
			if err != nil {
				log.Printf("查询AGV状态失败: %v", err)
				continue
			}

			status := agvController.GetStatus()
			if status == nil {
				log.Printf("获取AGV状态失败")
				continue
			}

			log.Printf("AGV状态 - 位置:(%.2f,%.2f) 状态:%s 最后点ID:%d",
				status.PosX, status.PosY, status.AGVStateString(), status.LastPointID)

			// 检查是否到达目标点
			if uint32(targetPoint) == status.LastPointID {
				// 检查AGV是否已停止（速度接近0）
				if n.abs(status.VelX) < 0.1 && n.abs(status.VelY) < 0.1 && n.abs(status.AngVel) < 0.1 {
					// 检查AGV状态是否为空闲
					if status.AGVState == 0x00 { // 0x00 = 空闲
						log.Printf("AGV已到达目标点%d并停止", targetPoint)
						return nil
					}
				}
			}

			// 检查是否有导航失败的状态
			if status.AGVState == 0x06 { // 0x06 = 导航失败
				return fmt.Errorf("AGV报告导航失败状态")
			}

			// 检查是否有严重异常
			if status.EventLevel >= 0x03 { // 严重错误级别
				return fmt.Errorf("AGV出现严重异常: 代码0x%04X", status.EventCode)
			}
		}
	}
}

// abs 辅助函数：浮点数绝对值
func (n *AGVNavigator) abs(x float32) float32 {
	return float32(math.Abs(float64(x)))
}
