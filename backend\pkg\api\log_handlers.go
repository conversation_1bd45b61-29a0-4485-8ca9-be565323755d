package api

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/user/agv_nav/pkg/logger"
)

// LogHandlers 日志API处理器
type LogHandlers struct {
	queryService *logger.LogQueryService
}

// NewLogHandlers 创建日志处理器
func NewLogHandlers() *LogHandlers {
	return &LogHandlers{
		queryService: logger.GetLogQueryService(),
	}
}

// QueryLogs 查询日志
func (h *LogHandlers) QueryLogs(w http.ResponseWriter, r *http.Request) {
	// 添加CORS支持
	h.enableCORS(w)

	// 处理OPTIONS预检请求
	if r.Method == http.MethodOptions {
		w.WriteHeader(http.StatusOK)
		return
	}

	// 解析查询参数
	req, err := h.parseQueryRequest(r)
	if err != nil {
		http.Error(w, fmt.Sprintf("参数解析失败: %v", err), http.StatusBadRequest)
		return
	}

	// 执行查询
	response, err := h.queryService.QueryLogs(req)
	if err != nil {
		http.Error(w, fmt.Sprintf("查询日志失败: %v", err), http.StatusInternalServerError)
		return
	}

	// 返回JSON响应
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"data":    response,
	})
}

// GetLogStats 获取日志统计信息
func (h *LogHandlers) GetLogStats(w http.ResponseWriter, r *http.Request) {
	// 添加CORS支持
	h.enableCORS(w)

	// 处理OPTIONS预检请求
	if r.Method == http.MethodOptions {
		w.WriteHeader(http.StatusOK)
		return
	}

	stats, err := h.queryService.GetLogStats()
	if err != nil {
		http.Error(w, fmt.Sprintf("获取统计信息失败: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"data":    stats,
	})
}

// ExportLogs 导出日志
func (h *LogHandlers) ExportLogs(w http.ResponseWriter, r *http.Request) {
	// 添加CORS支持
	h.enableCORS(w)

	// 处理OPTIONS预检请求
	if r.Method == http.MethodOptions {
		w.WriteHeader(http.StatusOK)
		return
	}

	// 解析查询参数
	req, err := h.parseQueryRequest(r)
	if err != nil {
		http.Error(w, fmt.Sprintf("参数解析失败: %v", err), http.StatusBadRequest)
		return
	}

	// 获取导出格式
	format := r.URL.Query().Get("format")
	if format == "" {
		format = "json"
	}

	// 设置较大的限制用于导出
	req.Limit = 10000
	if req.Limit > 50000 {
		req.Limit = 50000 // 最大导出5万条
	}

	// 查询日志
	response, err := h.queryService.QueryLogs(req)
	if err != nil {
		http.Error(w, fmt.Sprintf("查询日志失败: %v", err), http.StatusInternalServerError)
		return
	}

	// 根据格式导出
	switch strings.ToLower(format) {
	case "json":
		h.exportJSON(w, response.Logs)
	case "csv":
		h.exportCSV(w, response.Logs)
	case "txt":
		h.exportTXT(w, response.Logs)
	default:
		http.Error(w, "不支持的导出格式", http.StatusBadRequest)
	}
}

// ClearLogCache 清除日志缓存
func (h *LogHandlers) ClearLogCache(w http.ResponseWriter, r *http.Request) {
	// 添加CORS支持
	h.enableCORS(w)

	// 处理OPTIONS预检请求
	if r.Method == http.MethodOptions {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != http.MethodPost {
		http.Error(w, "仅支持POST方法", http.StatusMethodNotAllowed)
		return
	}

	h.queryService.ClearCache()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"message": "缓存已清除",
	})
}

// parseQueryRequest 解析查询请求参数
func (h *LogHandlers) parseQueryRequest(r *http.Request) (*logger.LogQueryRequest, error) {
	query := r.URL.Query()

	req := &logger.LogQueryRequest{
		Module:  query.Get("module"),
		Level:   query.Get("level"),
		Search:  query.Get("search"),
		Reverse: true, // 默认倒序显示最新日志
	}

	// 解析时间范围
	if startTimeStr := query.Get("startTime"); startTimeStr != "" {
		if startTime, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			req.StartTime = startTime
		} else if startTime, err := time.Parse("2006-01-02 15:04:05", startTimeStr); err == nil {
			req.StartTime = startTime
		}
	}

	if endTimeStr := query.Get("endTime"); endTimeStr != "" {
		if endTime, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			req.EndTime = endTime
		} else if endTime, err := time.Parse("2006-01-02 15:04:05", endTimeStr); err == nil {
			req.EndTime = endTime
		}
	}

	// 解析分页参数
	if limitStr := query.Get("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 {
			req.Limit = limit
		} else {
			req.Limit = 100 // 默认限制
		}
	} else {
		req.Limit = 100
	}

	if offsetStr := query.Get("offset"); offsetStr != "" {
		if offset, err := strconv.Atoi(offsetStr); err == nil && offset >= 0 {
			req.Offset = offset
		}
	}

	// 解析排序
	if reverseStr := query.Get("reverse"); reverseStr != "" {
		req.Reverse = reverseStr == "true"
	}

	return req, nil
}

// exportJSON 导出JSON格式
func (h *LogHandlers) exportJSON(w http.ResponseWriter, logs []logger.LogEntry) {
	filename := fmt.Sprintf("agv_logs_%s.json", time.Now().Format("20060102_150405"))
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))

	json.NewEncoder(w).Encode(map[string]interface{}{
		"exportTime": time.Now(),
		"count":      len(logs),
		"logs":       logs,
	})
}

// exportCSV 导出CSV格式
func (h *LogHandlers) exportCSV(w http.ResponseWriter, logs []logger.LogEntry) {
	filename := fmt.Sprintf("agv_logs_%s.csv", time.Now().Format("20060102_150405"))
	w.Header().Set("Content-Type", "text/csv")
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))

	writer := csv.NewWriter(w)
	defer writer.Flush()

	// 写入标题行
	headers := []string{"时间戳", "级别", "模块", "调用位置", "消息"}
	writer.Write(headers)

	// 写入数据行
	for _, log := range logs {
		record := []string{
			log.Timestamp.Format("2006-01-02 15:04:05.000"),
			log.Level,
			log.Module,
			log.Caller,
			log.Message,
		}
		writer.Write(record)
	}
}

// exportTXT 导出文本格式
func (h *LogHandlers) exportTXT(w http.ResponseWriter, logs []logger.LogEntry) {
	filename := fmt.Sprintf("agv_logs_%s.txt", time.Now().Format("20060102_150405"))
	w.Header().Set("Content-Type", "text/plain; charset=utf-8")
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))

	// 写入导出信息
	fmt.Fprintf(w, "AGV系统日志导出\n")
	fmt.Fprintf(w, "导出时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Fprintf(w, "日志条数: %d\n", len(logs))
	fmt.Fprintf(w, "%s\n\n", strings.Repeat("=", 80))

	// 写入日志条目
	for _, log := range logs {
		if log.Raw != "" {
			// 如果有原始行，使用原始格式
			fmt.Fprintf(w, "%s\n", log.Raw)
		} else {
			// 否则格式化输出
			fmt.Fprintf(w, "[%s] [%s] [%s] %s - %s\n",
				log.Timestamp.Format("2006-01-02 15:04:05.000"),
				log.Level,
				log.Module,
				log.Caller,
				log.Message)
		}
	}
}

// GetLogModules 获取可用的日志模块列表
func (h *LogHandlers) GetLogModules(w http.ResponseWriter, r *http.Request) {
	// 添加CORS支持
	h.enableCORS(w)

	// 处理OPTIONS预检请求
	if r.Method == http.MethodOptions {
		w.WriteHeader(http.StatusOK)
		return
	}

	stats, err := h.queryService.GetLogStats()
	if err != nil {
		http.Error(w, fmt.Sprintf("获取模块列表失败: %v", err), http.StatusInternalServerError)
		return
	}

	modules := make([]string, 0, len(stats.LogsByModule))
	for module := range stats.LogsByModule {
		modules = append(modules, module)
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"data":    modules,
	})
}

// GetLogLevels 获取可用的日志级别列表
func (h *LogHandlers) GetLogLevels(w http.ResponseWriter, r *http.Request) {
	// 添加CORS支持
	h.enableCORS(w)

	// 处理OPTIONS预检请求
	if r.Method == http.MethodOptions {
		w.WriteHeader(http.StatusOK)
		return
	}

	levels := []string{"DEBUG", "INFO", "WARN", "ERROR", "FATAL"}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"data":    levels,
	})
}

// enableCORS 添加CORS支持
func (h *LogHandlers) enableCORS(w http.ResponseWriter) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
}

// AddLogHandlers 为服务器添加日志处理方法
func (s *Server) AddLogHandlers() *LogHandlers {
	return NewLogHandlers()
}
