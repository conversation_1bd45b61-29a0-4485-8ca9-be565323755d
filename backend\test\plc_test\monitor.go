package plc_test

import (
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/user/agv_nav/pkg/plc"
)

// PLCMonitor PLC实时监控器
type PLCMonitor struct {
	plcController *plc.Controller
	isActive      bool
	currentValues map[string]interface{}
	subscribers   map[string]func(PLCChangeData)
	mutex         sync.RWMutex
	stopChan      chan bool
}

// PLCChangeData PLC地址变化数据
type PLCChangeData struct {
	Address     string      `json:"address"`     // "M601", "D602"
	OldValue    interface{} `json:"oldValue"`    // 旧值
	NewValue    interface{} `json:"newValue"`    // 新值
	Timestamp   string      `json:"timestamp"`   // 变化时间
	Description string      `json:"description"` // 地址描述
	Message     string      `json:"message"`     // "地址M601从false变为true"
}

// MonitoredAddress 监控地址定义
type MonitoredAddress struct {
	Name        string `json:"name"`        // "M601"
	Address     uint16 `json:"address"`     // 601
	Type        string `json:"type"`        // "coil" or "register"
	Description string `json:"description"` // "AGV请求PLC控制权"
}

// NewPLCMonitor 创建新的PLC监控器
func NewPLCMonitor(plcController *plc.Controller) *PLCMonitor {
	return &PLCMonitor{
		plcController: plcController,
		currentValues: make(map[string]interface{}),
		subscribers:   make(map[string]func(PLCChangeData)),
		stopChan:      make(chan bool),
	}
}

// GetMonitoredAddresses 获取所有监控地址定义
func (pm *PLCMonitor) GetMonitoredAddresses() []MonitoredAddress {
	return []MonitoredAddress{
		// 控制权相关
		{"M501", 501, "coil", "PLC控制权确认"},
		{"M601", 601, "coil", "AGV请求PLC控制权"},
		{"M607", 607, "coil", "控制权交接请求"},

		// 任务流程相关
		{"M602", 602, "coil", "开始任务信号"},
		{"M509", 509, "coil", "PLC准备接收数据"},
		{"M603", 603, "coil", "数据写入完成"},

		// 数据相关
		{"M604", 604, "coil", "方向数据(0=左侧,1=右侧)"},
		{"D602", 602, "register", "距离数据(低位)"},
		{"D603", 603, "register", "距离数据(高位)"},
		{"D600", 600, "register", "滚筒方向(1=左,2=右)"},

		// 机台完成相关
		{"M606", 606, "coil", "机台完成信号"},
		{"M608", 608, "coil", "调头信号"},
		{"M610", 610, "coil", "回到原点信号"},
		{"M611", 611, "coil", "原点方向"},
		{"D604", 604, "register", "原点码值"},

		// 心跳相关
		{"M500", 500, "coil", "心跳读取地址"},
		{"M600", 600, "coil", "心跳写入地址"},

		// 确认信号
		{"M510", 510, "coil", "AGV到达原点确认"},
		{"M511", 511, "coil", "调头完成确认"},
	}
}

// StartMonitoring 启动监控
func (pm *PLCMonitor) StartMonitoring() error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if pm.isActive {
		return fmt.Errorf("监控已在运行中")
	}

	if !pm.plcController.IsConnected() {
		return fmt.Errorf("PLC未连接")
	}

	pm.isActive = true
	pm.stopChan = make(chan bool)

	// 初始化当前值
	err := pm.readAllAddresses()
	if err != nil {
		pm.isActive = false
		return fmt.Errorf("初始化读取失败: %v", err)
	}

	// 启动监控协程
	go pm.monitoringLoop()

	log.Printf("PLC监控已启动，监控 %d 个地址", len(pm.GetMonitoredAddresses()))
	return nil
}

// StopMonitoring 停止监控
func (pm *PLCMonitor) StopMonitoring() error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if !pm.isActive {
		return fmt.Errorf("监控未运行")
	}

	pm.isActive = false
	close(pm.stopChan)

	log.Printf("PLC监控已停止")
	return nil
}

// AddSubscriber 添加订阅者
func (pm *PLCMonitor) AddSubscriber(clientID string, callback func(PLCChangeData)) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pm.subscribers[clientID] = callback
	log.Printf("添加PLC监控订阅者: %s", clientID)
}

// RemoveSubscriber 移除订阅者
func (pm *PLCMonitor) RemoveSubscriber(clientID string) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	delete(pm.subscribers, clientID)
	log.Printf("移除PLC监控订阅者: %s", clientID)
}

// GetCurrentValues 获取当前所有地址的值
func (pm *PLCMonitor) GetCurrentValues() map[string]interface{} {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	// 创建副本避免并发问题
	result := make(map[string]interface{})
	for k, v := range pm.currentValues {
		result[k] = v
	}

	return result
}

// ManualRefresh 手动刷新所有地址值
func (pm *PLCMonitor) ManualRefresh() error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	return pm.readAllAddresses()
}

// monitoringLoop 监控循环
func (pm *PLCMonitor) monitoringLoop() {
	ticker := time.NewTicker(200 * time.Millisecond) // 200ms监控间隔
	defer ticker.Stop()

	for {
		select {
		case <-pm.stopChan:
			log.Printf("PLC监控循环已停止")
			return
		case <-ticker.C:
			pm.checkForChanges()
		}
	}
}

// checkForChanges 检查地址变化
func (pm *PLCMonitor) checkForChanges() {
	addresses := pm.GetMonitoredAddresses()

	for _, addr := range addresses {
		pm.checkSingleAddress(addr)
	}
}

// checkSingleAddress 检查单个地址变化
func (pm *PLCMonitor) checkSingleAddress(addr MonitoredAddress) {
	newValue, err := pm.readAddress(addr)
	if err != nil {
		// 读取失败，跳过此次检查
		return
	}

	pm.mutex.Lock()
	oldValue, exists := pm.currentValues[addr.Name]

	// 检查是否有变化
	if !exists || !pm.valuesEqual(oldValue, newValue) {
		pm.currentValues[addr.Name] = newValue
		pm.mutex.Unlock()

		// 通知订阅者
		changeData := PLCChangeData{
			Address:     addr.Name,
			OldValue:    oldValue,
			NewValue:    newValue,
			Timestamp:   time.Now().Format("15:04:05.000"),
			Description: addr.Description,
			Message:     pm.formatChangeMessage(addr.Name, oldValue, newValue, addr.Description),
		}

		pm.notifySubscribers(changeData)
	} else {
		pm.mutex.Unlock()
	}
}

// readAddress 读取单个地址
func (pm *PLCMonitor) readAddress(addr MonitoredAddress) (interface{}, error) {
	if addr.Type == "coil" {
		data, err := pm.plcController.ReadCoils(addr.Address, 1)
		if err != nil {
			return nil, err
		}
		if len(data) > 0 {
			return data[0] != 0, nil
		}
		return false, nil
	} else if addr.Type == "register" {
		data, err := pm.plcController.ReadHoldingRegisters(addr.Address, 1)
		if err != nil {
			return nil, err
		}
		if len(data) >= 2 {
			return uint16(data[0])<<8 | uint16(data[1]), nil
		}
		return uint16(0), nil
	}

	return nil, fmt.Errorf("未知的地址类型: %s", addr.Type)
}

// readAllAddresses 读取所有地址
func (pm *PLCMonitor) readAllAddresses() error {
	addresses := pm.GetMonitoredAddresses()

	for _, addr := range addresses {
		value, err := pm.readAddress(addr)
		if err != nil {
			log.Printf("读取地址 %s 失败: %v", addr.Name, err)
			continue
		}
		pm.currentValues[addr.Name] = value
	}

	return nil
}

// valuesEqual 比较两个值是否相等
func (pm *PLCMonitor) valuesEqual(old, new interface{}) bool {
	if old == nil || new == nil {
		return old == new
	}
	return old == new
}

// formatChangeMessage 格式化变化消息
func (pm *PLCMonitor) formatChangeMessage(address string, oldValue, newValue interface{}, description string) string {
	if oldValue == nil {
		return fmt.Sprintf("🔄 地址%s初始值: %v (%s)", address, newValue, description)
	}

	return fmt.Sprintf("📝 地址%s变化: %v → %v (%s)", address, oldValue, newValue, description)
}

// notifySubscribers 通知所有订阅者
func (pm *PLCMonitor) notifySubscribers(changeData PLCChangeData) {
	pm.mutex.RLock()
	subscribers := make(map[string]func(PLCChangeData))
	for k, v := range pm.subscribers {
		subscribers[k] = v
	}
	pm.mutex.RUnlock()

	// 异步通知避免阻塞
	for clientID, callback := range subscribers {
		go func(id string, cb func(PLCChangeData), data PLCChangeData) {
			defer func() {
				if r := recover(); r != nil {
					log.Printf("通知订阅者 %s 时发生错误: %v", id, r)
				}
			}()
			cb(data)
		}(clientID, callback, changeData)
	}
}

// IsActive 检查监控是否运行
func (pm *PLCMonitor) IsActive() bool {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()
	return pm.isActive
}
