package zmq

import (
	"context"
	"testing"
	"time"
)

// TestRequestResponse 测试请求响应模式
func TestRequestResponse(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 启动响应者
	respConfig := DefaultConfig()
	respConfig.ResponderEndpoint = "tcp://*:5559"

	responder, err := NewResponder(respConfig)
	if err != nil {
		t.Fatalf("Create responder failed: %v", err)
	}
	defer responder.Close()

	// 设置处理器
	responder.SetHandler(func(request []byte) ([]byte, error) {
		return append([]byte("Response: "), request...), nil
	})

	// 在goroutine中启动响应者
	go func() {
		if err := responder.Start(ctx); err != nil {
			t.Logf("Responder start error: %v", err)
		}
	}()

	// 等待服务端启动
	time.Sleep(100 * time.Millisecond)

	// 创建请求者
	reqConfig := DefaultConfig()
	reqConfig.RequesterEndpoint = "tcp://localhost:5559"

	requester, err := NewRequester(reqConfig)
	if err != nil {
		t.Fatalf("Create requester failed: %v", err)
	}
	defer requester.Close()

	// 测试基本请求响应
	t.Run("BasicRequest", func(t *testing.T) {
		testData := "Hello World"
		resp, err := requester.RequestString(testData)
		if err != nil {
			t.Errorf("Request failed: %v", err)
		}

		expected := "Response: " + testData
		if resp != expected {
			t.Errorf("Expected %s, got %s", expected, resp)
		}
	})

	// 测试二进制数据
	t.Run("BinaryRequest", func(t *testing.T) {
		testData := []byte{0x01, 0x02, 0x03, 0x04}
		resp, err := requester.Request(testData)
		if err != nil {
			t.Errorf("Request failed: %v", err)
		}

		if len(resp) != len("Response: ")+len(testData) {
			t.Errorf("Unexpected response length: %d", len(resp))
		}
	})

	// 测试多个并发请求
	t.Run("ConcurrentRequests", func(t *testing.T) {
		done := make(chan bool, 5)

		for i := 0; i < 5; i++ {
			go func(id int) {
				defer func() { done <- true }()

				msg := []byte(string(rune('A' + id)))
				resp, err := requester.Request(msg)
				if err != nil {
					t.Errorf("Request %d failed: %v", id, err)
					return
				}

				t.Logf("Request %d: sent %s, received %s", id, msg, resp)
			}(i)
		}

		// 等待所有请求完成
		for i := 0; i < 5; i++ {
			<-done
		}
	})
}

// TestRequestTimeout 测试请求超时
func TestRequestTimeout(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 启动一个慢响应的服务器
	respConfig := DefaultConfig()
	respConfig.ResponderEndpoint = "tcp://*:5560"

	responder, err := NewResponder(respConfig)
	if err != nil {
		t.Fatalf("Create responder failed: %v", err)
	}
	defer responder.Close()

	responder.SetHandler(func(request []byte) ([]byte, error) {
		// 模拟慢处理
		time.Sleep(2 * time.Second)
		return request, nil
	})

	go responder.Start(ctx)
	time.Sleep(100 * time.Millisecond)

	// 创建请求者
	reqConfig := DefaultConfig()
	reqConfig.RequesterEndpoint = "tcp://localhost:5560"

	requester, err := NewRequester(reqConfig)
	if err != nil {
		t.Fatalf("Create requester failed: %v", err)
	}
	defer requester.Close()

	// 测试超时
	_, err = requester.RequestWithTimeout([]byte("test"), 500*time.Millisecond)
	if err == nil {
		t.Error("Expected timeout error, got nil")
	}
}
