package task

import (
	"fmt"
	"time"
)

// 简单的流程状态结构
type SimpleFlowStatus struct {
	CurrentStep    string      `json:"currentStep"`
	CurrentMachine string      `json:"currentMachine"`
	CurrentSpindle *int        `json:"currentSpindle,omitempty"`
	PLCAction      string      `json:"plcAction"`
	PLCAddress     string      `json:"plcAddress"`
	PLCValue       interface{} `json:"plcValue,omitempty"`
	NavigationMode string      `json:"navigationMode"`
	LastUpdateTime time.Time   `json:"lastUpdateTime"`
}

// 全局简单流程状态
var globalSimpleFlowStatus = &SimpleFlowStatus{
	CurrentStep:    "idle",
	NavigationMode: "laser",
	LastUpdateTime: time.Now(),
}

// 流程状态广播回调
var flowStatusBroadcastCallback func()

// SetFlowStatusBroadcastCallback 设置流程状态广播回调
func SetFlowStatusBroadcastCallback(callback func()) {
	flowStatusBroadcastCallback = callback
}

// broadcastFlowStatus 广播流程状态（如果回调已设置）
func broadcastFlowStatus() {
	if flowStatusBroadcastCallback != nil {
		flowStatusBroadcastCallback()
	}
}

// GetSimpleFlowStatus 获取简单流程状态
func GetSimpleFlowStatus() *SimpleFlowStatus {
	return globalSimpleFlowStatus
}

// 简单的更新函数
func updateStep(step string) {
	globalSimpleFlowStatus.CurrentStep = step
	globalSimpleFlowStatus.LastUpdateTime = time.Now()
	broadcastFlowStatus()
}

func updateMachine(machine string) {
	globalSimpleFlowStatus.CurrentMachine = machine
	globalSimpleFlowStatus.LastUpdateTime = time.Now()
	broadcastFlowStatus()
}

func updateSpindle(spindle *int) {
	globalSimpleFlowStatus.CurrentSpindle = spindle
	globalSimpleFlowStatus.LastUpdateTime = time.Now()
	broadcastFlowStatus()
}

func updatePLC(action, address string, value interface{}) {
	globalSimpleFlowStatus.PLCAction = action
	globalSimpleFlowStatus.PLCAddress = address
	globalSimpleFlowStatus.PLCValue = value
	globalSimpleFlowStatus.LastUpdateTime = time.Now()
	broadcastFlowStatus()
}

func updateNavigation(mode string) {
	globalSimpleFlowStatus.NavigationMode = mode
	globalSimpleFlowStatus.LastUpdateTime = time.Now()
	broadcastFlowStatus()
}

// calculateElapsedTime 计算已用时间
func calculateElapsedTime(startTime *time.Time) string {
	if startTime == nil {
		return "未开始"
	}
	elapsed := time.Since(*startTime)

	hours := int(elapsed.Hours())
	minutes := int(elapsed.Minutes()) % 60
	seconds := int(elapsed.Seconds()) % 60

	if hours > 0 {
		return fmt.Sprintf("%d小时%d分钟%d秒", hours, minutes, seconds)
	} else if minutes > 0 {
		return fmt.Sprintf("%d分钟%d秒", minutes, seconds)
	} else {
		return fmt.Sprintf("%d秒", seconds)
	}
}

// abs 返回float32的绝对值
func abs(x float32) float32 {
	if x < 0 {
		return -x
	}
	return x
}

// broadcastTaskStatus 广播任务状态（如果回调已设置）
func broadcastTaskStatus() {
	if taskStatusBroadcastCallback != nil {
		taskStatusBroadcastCallback()
	}
}
