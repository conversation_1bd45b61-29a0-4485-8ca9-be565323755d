package zmq

import (
	"encoding/json"
	"time"
)

// Message 消息结构体
type Message struct {
	Instruction int         `json:"instruction"`       // 指令 (0: 心跳)
	TimeStamp   time.Time   `json:"timeStamp"`         // 时间
	Code        bool        `json:"code"`              // 状态码
	Message     string      `json:"message,omitempty"` // 消息 (可选)
	Content     interface{} `json:"content"`           // 内容
}

// HeartbeatContent 心跳消息的内容
type HeartbeatContent struct {
	RobotNo string `json:"robotNo"` // 机器人编号
	State   bool   `json:"state"`   // 状态值
}

// TaskContent 任务消息的内容
type TaskContent struct {
	RobotNo string `json:"robotNo"` // 机器人编号
	TaskId  string `json:"taskId"`  // 任务编号
	Remark  string `json:"remark"`  // 备注（可选）
}

// 指令常量
const (
	InstructionHeartbeat     = 0   // 心跳
	InstructionReply         = 1   // 回复
	InstructionStatusReport  = 100 // 机器人状态上报
	InstructionTaskStart     = 110 // 任务开始
	InstructionTaskPause     = 120 // 任务暂停
	InstructionTaskComplete  = 130 // 任务完成
	InstructionTaskForceStop = 131 // 任务强制结束

	// 调度系统派发的任务指令
	InstructionTaskAssignment = 200 // 任务下发
	InstructionNoTask         = 201 // 无任务
	InstructionWaitTask       = 202 // 等待任务
	InstructionContinueTask   = 203 // 继续任务
)

// NewMessage 创建新消息
func NewMessage(instruction int, code bool, content interface{}) *Message {
	return &Message{
		Instruction: instruction,
		TimeStamp:   time.Now(),
		Code:        code,
		Content:     content,
	}
}

// NewHeartbeatMessage 创建心跳消息 (instruction = 0)
func NewHeartbeatMessage(robotNo string, state bool) *Message {
	content := HeartbeatContent{
		RobotNo: robotNo,
		State:   state,
	}
	return NewMessage(InstructionHeartbeat, true, content)
}

// NewTaskMessage 创建任务消息
func NewTaskMessage(instruction int, robotNo string, taskId string, remark string) *Message {
	content := TaskContent{
		RobotNo: robotNo,
		TaskId:  taskId,
		Remark:  remark,
	}
	return NewMessage(instruction, true, content)
}

// NewTaskStartMessage 创建任务开始消息
func NewTaskStartMessage(robotNo string, taskId string, remark string) *Message {
	return NewTaskMessage(InstructionTaskStart, robotNo, taskId, remark)
}

// NewTaskPauseMessage 创建任务暂停消息
func NewTaskPauseMessage(robotNo string, taskId string, remark string) *Message {
	return NewTaskMessage(InstructionTaskPause, robotNo, taskId, remark)
}

// NewTaskCompleteMessage 创建任务完成消息
func NewTaskCompleteMessage(robotNo string, taskId string, remark string) *Message {
	return NewTaskMessage(InstructionTaskComplete, robotNo, taskId, remark)
}

// NewTaskForceStopMessage 创建任务强制结束消息
func NewTaskForceStopMessage(robotNo string, taskId string, remark string) *Message {
	return NewTaskMessage(InstructionTaskForceStop, robotNo, taskId, remark)
}

// SerializeMessage 序列化消息为JSON字节数组
func SerializeMessage(message *Message) ([]byte, error) {
	return json.Marshal(message)
}

// DeserializeMessage 从JSON字节数组反序列化消息
func DeserializeMessage(data []byte) (*Message, error) {
	var message Message
	err := json.Unmarshal(data, &message)
	if err != nil {
		return nil, err
	}
	return &message, nil
}
