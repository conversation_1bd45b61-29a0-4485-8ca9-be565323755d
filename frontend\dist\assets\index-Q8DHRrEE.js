var th=Object.defineProperty;var sh=(i,d,h)=>d in i?th(i,d,{enumerable:!0,configurable:!0,writable:!0,value:h}):i[d]=h;var rt=(i,d,h)=>sh(i,typeof d!="symbol"?d+"":d,h);(function(){const d=document.createElement("link").relList;if(d&&d.supports&&d.supports("modulepreload"))return;for(const p of document.querySelectorAll('link[rel="modulepreload"]'))x(p);new MutationObserver(p=>{for(const S of p)if(S.type==="childList")for(const k of S.addedNodes)k.tagName==="LINK"&&k.rel==="modulepreload"&&x(k)}).observe(document,{childList:!0,subtree:!0});function h(p){const S={};return p.integrity&&(S.integrity=p.integrity),p.referrerPolicy&&(S.referrerPolicy=p.referrerPolicy),p.crossOrigin==="use-credentials"?S.credentials="include":p.crossOrigin==="anonymous"?S.credentials="omit":S.credentials="same-origin",S}function x(p){if(p.ep)return;p.ep=!0;const S=h(p);fetch(p.href,S)}})();function wm(i){return i&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}var ur={exports:{}},Ca={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dm;function lh(){if(dm)return Ca;dm=1;var i=Symbol.for("react.transitional.element"),d=Symbol.for("react.fragment");function h(x,p,S){var k=null;if(S!==void 0&&(k=""+S),p.key!==void 0&&(k=""+p.key),"key"in p){S={};for(var A in p)A!=="key"&&(S[A]=p[A])}else S=p;return p=S.ref,{$$typeof:i,type:x,key:k,ref:p!==void 0?p:null,props:S}}return Ca.Fragment=d,Ca.jsx=h,Ca.jsxs=h,Ca}var om;function ah(){return om||(om=1,ur.exports=lh()),ur.exports}var s=ah(),dr={exports:{}},fe={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mm;function nh(){if(mm)return fe;mm=1;var i=Symbol.for("react.transitional.element"),d=Symbol.for("react.portal"),h=Symbol.for("react.fragment"),x=Symbol.for("react.strict_mode"),p=Symbol.for("react.profiler"),S=Symbol.for("react.consumer"),k=Symbol.for("react.context"),A=Symbol.for("react.forward_ref"),b=Symbol.for("react.suspense"),f=Symbol.for("react.memo"),g=Symbol.for("react.lazy"),C=Symbol.iterator;function M(o){return o===null||typeof o!="object"?null:(o=C&&o[C]||o["@@iterator"],typeof o=="function"?o:null)}var J={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},z=Object.assign,D={};function j(o,B,P){this.props=o,this.context=B,this.refs=D,this.updater=P||J}j.prototype.isReactComponent={},j.prototype.setState=function(o,B){if(typeof o!="object"&&typeof o!="function"&&o!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,o,B,"setState")},j.prototype.forceUpdate=function(o){this.updater.enqueueForceUpdate(this,o,"forceUpdate")};function te(){}te.prototype=j.prototype;function I(o,B,P){this.props=o,this.context=B,this.refs=D,this.updater=P||J}var le=I.prototype=new te;le.constructor=I,z(le,j.prototype),le.isPureReactComponent=!0;var ae=Array.isArray,_={H:null,A:null,T:null,S:null,V:null},V=Object.prototype.hasOwnProperty;function u(o,B,P,se,H,ue){return P=ue.ref,{$$typeof:i,type:o,key:B,ref:P!==void 0?P:null,props:ue}}function q(o,B){return u(o.type,B,void 0,void 0,void 0,o.props)}function X(o){return typeof o=="object"&&o!==null&&o.$$typeof===i}function Y(o){var B={"=":"=0",":":"=2"};return"$"+o.replace(/[=:]/g,function(P){return B[P]})}var L=/\/+/g;function K(o,B){return typeof o=="object"&&o!==null&&o.key!=null?Y(""+o.key):B.toString(36)}function ne(){}function w(o){switch(o.status){case"fulfilled":return o.value;case"rejected":throw o.reason;default:switch(typeof o.status=="string"?o.then(ne,ne):(o.status="pending",o.then(function(B){o.status==="pending"&&(o.status="fulfilled",o.value=B)},function(B){o.status==="pending"&&(o.status="rejected",o.reason=B)})),o.status){case"fulfilled":return o.value;case"rejected":throw o.reason}}throw o}function Q(o,B,P,se,H){var ue=typeof o;(ue==="undefined"||ue==="boolean")&&(o=null);var de=!1;if(o===null)de=!0;else switch(ue){case"bigint":case"string":case"number":de=!0;break;case"object":switch(o.$$typeof){case i:case d:de=!0;break;case g:return de=o._init,Q(de(o._payload),B,P,se,H)}}if(de)return H=H(o),de=se===""?"."+K(o,0):se,ae(H)?(P="",de!=null&&(P=de.replace(L,"$&/")+"/"),Q(H,B,P,"",function(tt){return tt})):H!=null&&(X(H)&&(H=q(H,P+(H.key==null||o&&o.key===H.key?"":(""+H.key).replace(L,"$&/")+"/")+de)),B.push(H)),1;de=0;var qe=se===""?".":se+":";if(ae(o))for(var we=0;we<o.length;we++)se=o[we],ue=qe+K(se,we),de+=Q(se,B,P,ue,H);else if(we=M(o),typeof we=="function")for(o=we.call(o),we=0;!(se=o.next()).done;)se=se.value,ue=qe+K(se,we++),de+=Q(se,B,P,ue,H);else if(ue==="object"){if(typeof o.then=="function")return Q(w(o),B,P,se,H);throw B=String(o),Error("Objects are not valid as a React child (found: "+(B==="[object Object]"?"object with keys {"+Object.keys(o).join(", ")+"}":B)+"). If you meant to render a collection of children, use an array instead.")}return de}function E(o,B,P){if(o==null)return o;var se=[],H=0;return Q(o,se,"","",function(ue){return B.call(P,ue,H++)}),se}function F(o){if(o._status===-1){var B=o._result;B=B(),B.then(function(P){(o._status===0||o._status===-1)&&(o._status=1,o._result=P)},function(P){(o._status===0||o._status===-1)&&(o._status=2,o._result=P)}),o._status===-1&&(o._status=0,o._result=B)}if(o._status===1)return o._result.default;throw o._result}var ie=typeof reportError=="function"?reportError:function(o){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var B=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof o=="object"&&o!==null&&typeof o.message=="string"?String(o.message):String(o),error:o});if(!window.dispatchEvent(B))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",o);return}console.error(o)};function ee(){}return fe.Children={map:E,forEach:function(o,B,P){E(o,function(){B.apply(this,arguments)},P)},count:function(o){var B=0;return E(o,function(){B++}),B},toArray:function(o){return E(o,function(B){return B})||[]},only:function(o){if(!X(o))throw Error("React.Children.only expected to receive a single React element child.");return o}},fe.Component=j,fe.Fragment=h,fe.Profiler=p,fe.PureComponent=I,fe.StrictMode=x,fe.Suspense=b,fe.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=_,fe.__COMPILER_RUNTIME={__proto__:null,c:function(o){return _.H.useMemoCache(o)}},fe.cache=function(o){return function(){return o.apply(null,arguments)}},fe.cloneElement=function(o,B,P){if(o==null)throw Error("The argument must be a React element, but you passed "+o+".");var se=z({},o.props),H=o.key,ue=void 0;if(B!=null)for(de in B.ref!==void 0&&(ue=void 0),B.key!==void 0&&(H=""+B.key),B)!V.call(B,de)||de==="key"||de==="__self"||de==="__source"||de==="ref"&&B.ref===void 0||(se[de]=B[de]);var de=arguments.length-2;if(de===1)se.children=P;else if(1<de){for(var qe=Array(de),we=0;we<de;we++)qe[we]=arguments[we+2];se.children=qe}return u(o.type,H,void 0,void 0,ue,se)},fe.createContext=function(o){return o={$$typeof:k,_currentValue:o,_currentValue2:o,_threadCount:0,Provider:null,Consumer:null},o.Provider=o,o.Consumer={$$typeof:S,_context:o},o},fe.createElement=function(o,B,P){var se,H={},ue=null;if(B!=null)for(se in B.key!==void 0&&(ue=""+B.key),B)V.call(B,se)&&se!=="key"&&se!=="__self"&&se!=="__source"&&(H[se]=B[se]);var de=arguments.length-2;if(de===1)H.children=P;else if(1<de){for(var qe=Array(de),we=0;we<de;we++)qe[we]=arguments[we+2];H.children=qe}if(o&&o.defaultProps)for(se in de=o.defaultProps,de)H[se]===void 0&&(H[se]=de[se]);return u(o,ue,void 0,void 0,null,H)},fe.createRef=function(){return{current:null}},fe.forwardRef=function(o){return{$$typeof:A,render:o}},fe.isValidElement=X,fe.lazy=function(o){return{$$typeof:g,_payload:{_status:-1,_result:o},_init:F}},fe.memo=function(o,B){return{$$typeof:f,type:o,compare:B===void 0?null:B}},fe.startTransition=function(o){var B=_.T,P={};_.T=P;try{var se=o(),H=_.S;H!==null&&H(P,se),typeof se=="object"&&se!==null&&typeof se.then=="function"&&se.then(ee,ie)}catch(ue){ie(ue)}finally{_.T=B}},fe.unstable_useCacheRefresh=function(){return _.H.useCacheRefresh()},fe.use=function(o){return _.H.use(o)},fe.useActionState=function(o,B,P){return _.H.useActionState(o,B,P)},fe.useCallback=function(o,B){return _.H.useCallback(o,B)},fe.useContext=function(o){return _.H.useContext(o)},fe.useDebugValue=function(){},fe.useDeferredValue=function(o,B){return _.H.useDeferredValue(o,B)},fe.useEffect=function(o,B,P){var se=_.H;if(typeof P=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return se.useEffect(o,B)},fe.useId=function(){return _.H.useId()},fe.useImperativeHandle=function(o,B,P){return _.H.useImperativeHandle(o,B,P)},fe.useInsertionEffect=function(o,B){return _.H.useInsertionEffect(o,B)},fe.useLayoutEffect=function(o,B){return _.H.useLayoutEffect(o,B)},fe.useMemo=function(o,B){return _.H.useMemo(o,B)},fe.useOptimistic=function(o,B){return _.H.useOptimistic(o,B)},fe.useReducer=function(o,B,P){return _.H.useReducer(o,B,P)},fe.useRef=function(o){return _.H.useRef(o)},fe.useState=function(o){return _.H.useState(o)},fe.useSyncExternalStore=function(o,B,P){return _.H.useSyncExternalStore(o,B,P)},fe.useTransition=function(){return _.H.useTransition()},fe.version="19.1.0",fe}var xm;function gr(){return xm||(xm=1,dr.exports=nh()),dr.exports}var v=gr();const ch=wm(v);var or={exports:{}},Ta={},mr={exports:{}},xr={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fm;function ih(){return fm||(fm=1,function(i){function d(E,F){var ie=E.length;E.push(F);e:for(;0<ie;){var ee=ie-1>>>1,o=E[ee];if(0<p(o,F))E[ee]=F,E[ie]=o,ie=ee;else break e}}function h(E){return E.length===0?null:E[0]}function x(E){if(E.length===0)return null;var F=E[0],ie=E.pop();if(ie!==F){E[0]=ie;e:for(var ee=0,o=E.length,B=o>>>1;ee<B;){var P=2*(ee+1)-1,se=E[P],H=P+1,ue=E[H];if(0>p(se,ie))H<o&&0>p(ue,se)?(E[ee]=ue,E[H]=ie,ee=H):(E[ee]=se,E[P]=ie,ee=P);else if(H<o&&0>p(ue,ie))E[ee]=ue,E[H]=ie,ee=H;else break e}}return F}function p(E,F){var ie=E.sortIndex-F.sortIndex;return ie!==0?ie:E.id-F.id}if(i.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var S=performance;i.unstable_now=function(){return S.now()}}else{var k=Date,A=k.now();i.unstable_now=function(){return k.now()-A}}var b=[],f=[],g=1,C=null,M=3,J=!1,z=!1,D=!1,j=!1,te=typeof setTimeout=="function"?setTimeout:null,I=typeof clearTimeout=="function"?clearTimeout:null,le=typeof setImmediate<"u"?setImmediate:null;function ae(E){for(var F=h(f);F!==null;){if(F.callback===null)x(f);else if(F.startTime<=E)x(f),F.sortIndex=F.expirationTime,d(b,F);else break;F=h(f)}}function _(E){if(D=!1,ae(E),!z)if(h(b)!==null)z=!0,V||(V=!0,K());else{var F=h(f);F!==null&&Q(_,F.startTime-E)}}var V=!1,u=-1,q=5,X=-1;function Y(){return j?!0:!(i.unstable_now()-X<q)}function L(){if(j=!1,V){var E=i.unstable_now();X=E;var F=!0;try{e:{z=!1,D&&(D=!1,I(u),u=-1),J=!0;var ie=M;try{t:{for(ae(E),C=h(b);C!==null&&!(C.expirationTime>E&&Y());){var ee=C.callback;if(typeof ee=="function"){C.callback=null,M=C.priorityLevel;var o=ee(C.expirationTime<=E);if(E=i.unstable_now(),typeof o=="function"){C.callback=o,ae(E),F=!0;break t}C===h(b)&&x(b),ae(E)}else x(b);C=h(b)}if(C!==null)F=!0;else{var B=h(f);B!==null&&Q(_,B.startTime-E),F=!1}}break e}finally{C=null,M=ie,J=!1}F=void 0}}finally{F?K():V=!1}}}var K;if(typeof le=="function")K=function(){le(L)};else if(typeof MessageChannel<"u"){var ne=new MessageChannel,w=ne.port2;ne.port1.onmessage=L,K=function(){w.postMessage(null)}}else K=function(){te(L,0)};function Q(E,F){u=te(function(){E(i.unstable_now())},F)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(E){E.callback=null},i.unstable_forceFrameRate=function(E){0>E||125<E?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):q=0<E?Math.floor(1e3/E):5},i.unstable_getCurrentPriorityLevel=function(){return M},i.unstable_next=function(E){switch(M){case 1:case 2:case 3:var F=3;break;default:F=M}var ie=M;M=F;try{return E()}finally{M=ie}},i.unstable_requestPaint=function(){j=!0},i.unstable_runWithPriority=function(E,F){switch(E){case 1:case 2:case 3:case 4:case 5:break;default:E=3}var ie=M;M=E;try{return F()}finally{M=ie}},i.unstable_scheduleCallback=function(E,F,ie){var ee=i.unstable_now();switch(typeof ie=="object"&&ie!==null?(ie=ie.delay,ie=typeof ie=="number"&&0<ie?ee+ie:ee):ie=ee,E){case 1:var o=-1;break;case 2:o=250;break;case 5:o=1073741823;break;case 4:o=1e4;break;default:o=5e3}return o=ie+o,E={id:g++,callback:F,priorityLevel:E,startTime:ie,expirationTime:o,sortIndex:-1},ie>ee?(E.sortIndex=ie,d(f,E),h(b)===null&&E===h(f)&&(D?(I(u),u=-1):D=!0,Q(_,ie-ee))):(E.sortIndex=o,d(b,E),z||J||(z=!0,V||(V=!0,K()))),E},i.unstable_shouldYield=Y,i.unstable_wrapCallback=function(E){var F=M;return function(){var ie=M;M=F;try{return E.apply(this,arguments)}finally{M=ie}}}}(xr)),xr}var hm;function rh(){return hm||(hm=1,mr.exports=ih()),mr.exports}var fr={exports:{}},Ie={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gm;function uh(){if(gm)return Ie;gm=1;var i=gr();function d(b){var f="https://react.dev/errors/"+b;if(1<arguments.length){f+="?args[]="+encodeURIComponent(arguments[1]);for(var g=2;g<arguments.length;g++)f+="&args[]="+encodeURIComponent(arguments[g])}return"Minified React error #"+b+"; visit "+f+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function h(){}var x={d:{f:h,r:function(){throw Error(d(522))},D:h,C:h,L:h,m:h,X:h,S:h,M:h},p:0,findDOMNode:null},p=Symbol.for("react.portal");function S(b,f,g){var C=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:p,key:C==null?null:""+C,children:b,containerInfo:f,implementation:g}}var k=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function A(b,f){if(b==="font")return"";if(typeof f=="string")return f==="use-credentials"?f:""}return Ie.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=x,Ie.createPortal=function(b,f){var g=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!f||f.nodeType!==1&&f.nodeType!==9&&f.nodeType!==11)throw Error(d(299));return S(b,f,null,g)},Ie.flushSync=function(b){var f=k.T,g=x.p;try{if(k.T=null,x.p=2,b)return b()}finally{k.T=f,x.p=g,x.d.f()}},Ie.preconnect=function(b,f){typeof b=="string"&&(f?(f=f.crossOrigin,f=typeof f=="string"?f==="use-credentials"?f:"":void 0):f=null,x.d.C(b,f))},Ie.prefetchDNS=function(b){typeof b=="string"&&x.d.D(b)},Ie.preinit=function(b,f){if(typeof b=="string"&&f&&typeof f.as=="string"){var g=f.as,C=A(g,f.crossOrigin),M=typeof f.integrity=="string"?f.integrity:void 0,J=typeof f.fetchPriority=="string"?f.fetchPriority:void 0;g==="style"?x.d.S(b,typeof f.precedence=="string"?f.precedence:void 0,{crossOrigin:C,integrity:M,fetchPriority:J}):g==="script"&&x.d.X(b,{crossOrigin:C,integrity:M,fetchPriority:J,nonce:typeof f.nonce=="string"?f.nonce:void 0})}},Ie.preinitModule=function(b,f){if(typeof b=="string")if(typeof f=="object"&&f!==null){if(f.as==null||f.as==="script"){var g=A(f.as,f.crossOrigin);x.d.M(b,{crossOrigin:g,integrity:typeof f.integrity=="string"?f.integrity:void 0,nonce:typeof f.nonce=="string"?f.nonce:void 0})}}else f==null&&x.d.M(b)},Ie.preload=function(b,f){if(typeof b=="string"&&typeof f=="object"&&f!==null&&typeof f.as=="string"){var g=f.as,C=A(g,f.crossOrigin);x.d.L(b,g,{crossOrigin:C,integrity:typeof f.integrity=="string"?f.integrity:void 0,nonce:typeof f.nonce=="string"?f.nonce:void 0,type:typeof f.type=="string"?f.type:void 0,fetchPriority:typeof f.fetchPriority=="string"?f.fetchPriority:void 0,referrerPolicy:typeof f.referrerPolicy=="string"?f.referrerPolicy:void 0,imageSrcSet:typeof f.imageSrcSet=="string"?f.imageSrcSet:void 0,imageSizes:typeof f.imageSizes=="string"?f.imageSizes:void 0,media:typeof f.media=="string"?f.media:void 0})}},Ie.preloadModule=function(b,f){if(typeof b=="string")if(f){var g=A(f.as,f.crossOrigin);x.d.m(b,{as:typeof f.as=="string"&&f.as!=="script"?f.as:void 0,crossOrigin:g,integrity:typeof f.integrity=="string"?f.integrity:void 0})}else x.d.m(b)},Ie.requestFormReset=function(b){x.d.r(b)},Ie.unstable_batchedUpdates=function(b,f){return b(f)},Ie.useFormState=function(b,f,g){return k.H.useFormState(b,f,g)},Ie.useFormStatus=function(){return k.H.useHostTransitionStatus()},Ie.version="19.1.0",Ie}var bm;function dh(){if(bm)return fr.exports;bm=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(d){console.error(d)}}return i(),fr.exports=uh(),fr.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ym;function oh(){if(ym)return Ta;ym=1;var i=rh(),d=gr(),h=dh();function x(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)t+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function p(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function S(e){var t=e,l=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(l=t.return),e=t.return;while(e)}return t.tag===3?l:null}function k(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function A(e){if(S(e)!==e)throw Error(x(188))}function b(e){var t=e.alternate;if(!t){if(t=S(e),t===null)throw Error(x(188));return t!==e?null:e}for(var l=e,a=t;;){var n=l.return;if(n===null)break;var c=n.alternate;if(c===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===c.child){for(c=n.child;c;){if(c===l)return A(n),e;if(c===a)return A(n),t;c=c.sibling}throw Error(x(188))}if(l.return!==a.return)l=n,a=c;else{for(var r=!1,m=n.child;m;){if(m===l){r=!0,l=n,a=c;break}if(m===a){r=!0,a=n,l=c;break}m=m.sibling}if(!r){for(m=c.child;m;){if(m===l){r=!0,l=c,a=n;break}if(m===a){r=!0,a=c,l=n;break}m=m.sibling}if(!r)throw Error(x(189))}}if(l.alternate!==a)throw Error(x(190))}if(l.tag!==3)throw Error(x(188));return l.stateNode.current===l?e:t}function f(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=f(e),t!==null)return t;e=e.sibling}return null}var g=Object.assign,C=Symbol.for("react.element"),M=Symbol.for("react.transitional.element"),J=Symbol.for("react.portal"),z=Symbol.for("react.fragment"),D=Symbol.for("react.strict_mode"),j=Symbol.for("react.profiler"),te=Symbol.for("react.provider"),I=Symbol.for("react.consumer"),le=Symbol.for("react.context"),ae=Symbol.for("react.forward_ref"),_=Symbol.for("react.suspense"),V=Symbol.for("react.suspense_list"),u=Symbol.for("react.memo"),q=Symbol.for("react.lazy"),X=Symbol.for("react.activity"),Y=Symbol.for("react.memo_cache_sentinel"),L=Symbol.iterator;function K(e){return e===null||typeof e!="object"?null:(e=L&&e[L]||e["@@iterator"],typeof e=="function"?e:null)}var ne=Symbol.for("react.client.reference");function w(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===ne?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case z:return"Fragment";case j:return"Profiler";case D:return"StrictMode";case _:return"Suspense";case V:return"SuspenseList";case X:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case J:return"Portal";case le:return(e.displayName||"Context")+".Provider";case I:return(e._context.displayName||"Context")+".Consumer";case ae:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case u:return t=e.displayName||null,t!==null?t:w(e.type)||"Memo";case q:t=e._payload,e=e._init;try{return w(e(t))}catch{}}return null}var Q=Array.isArray,E=d.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,F=h.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ie={pending:!1,data:null,method:null,action:null},ee=[],o=-1;function B(e){return{current:e}}function P(e){0>o||(e.current=ee[o],ee[o]=null,o--)}function se(e,t){o++,ee[o]=e.current,e.current=t}var H=B(null),ue=B(null),de=B(null),qe=B(null);function we(e,t){switch(se(de,t),se(ue,e),se(H,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?qo(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=qo(t),e=Ho(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}P(H),se(H,e)}function tt(){P(H),P(ue),P(de)}function Ut(e){e.memoizedState!==null&&se(qe,e);var t=H.current,l=Ho(t,e.type);t!==l&&(se(ue,e),se(H,l))}function Bs(e){ue.current===e&&(P(H),P(ue)),qe.current===e&&(P(qe),pa._currentValue=ie)}var Jn=Object.prototype.hasOwnProperty,Wn=i.unstable_scheduleCallback,Fn=i.unstable_cancelCallback,Om=i.unstable_shouldYield,km=i.unstable_requestPaint,Rt=i.unstable_now,Dm=i.unstable_getCurrentPriorityLevel,br=i.unstable_ImmediatePriority,yr=i.unstable_UserBlockingPriority,Ea=i.unstable_NormalPriority,zm=i.unstable_LowPriority,vr=i.unstable_IdlePriority,Lm=i.log,Um=i.unstable_setDisableYieldValue,_l=null,ut=null;function It(e){if(typeof Lm=="function"&&Um(e),ut&&typeof ut.setStrictMode=="function")try{ut.setStrictMode(_l,e)}catch{}}var dt=Math.clz32?Math.clz32:Gm,qm=Math.log,Hm=Math.LN2;function Gm(e){return e>>>=0,e===0?32:31-(qm(e)/Hm|0)|0}var Ra=256,Oa=4194304;function Ss(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function ka(e,t,l){var a=e.pendingLanes;if(a===0)return 0;var n=0,c=e.suspendedLanes,r=e.pingedLanes;e=e.warmLanes;var m=a&134217727;return m!==0?(a=m&~c,a!==0?n=Ss(a):(r&=m,r!==0?n=Ss(r):l||(l=m&~e,l!==0&&(n=Ss(l))))):(m=a&~c,m!==0?n=Ss(m):r!==0?n=Ss(r):l||(l=a&~e,l!==0&&(n=Ss(l)))),n===0?0:t!==0&&t!==n&&(t&c)===0&&(c=n&-n,l=t&-t,c>=l||c===32&&(l&4194048)!==0)?t:n}function Ml(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Vm(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function pr(){var e=Ra;return Ra<<=1,(Ra&4194048)===0&&(Ra=256),e}function jr(){var e=Oa;return Oa<<=1,(Oa&62914560)===0&&(Oa=4194304),e}function Pn(e){for(var t=[],l=0;31>l;l++)t.push(e);return t}function El(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Bm(e,t,l,a,n,c){var r=e.pendingLanes;e.pendingLanes=l,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=l,e.entangledLanes&=l,e.errorRecoveryDisabledLanes&=l,e.shellSuspendCounter=0;var m=e.entanglements,y=e.expirationTimes,O=e.hiddenUpdates;for(l=r&~l;0<l;){var $=31-dt(l),W=1<<$;m[$]=0,y[$]=-1;var U=O[$];if(U!==null)for(O[$]=null,$=0;$<U.length;$++){var G=U[$];G!==null&&(G.lane&=-536870913)}l&=~W}a!==0&&Nr(e,a,0),c!==0&&n===0&&e.tag!==0&&(e.suspendedLanes|=c&~(r&~t))}function Nr(e,t,l){e.pendingLanes|=t,e.suspendedLanes&=~t;var a=31-dt(t);e.entangledLanes|=t,e.entanglements[a]=e.entanglements[a]|1073741824|l&4194090}function Sr(e,t){var l=e.entangledLanes|=t;for(e=e.entanglements;l;){var a=31-dt(l),n=1<<a;n&t|e[a]&t&&(e[a]|=t),l&=~n}}function In(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function ec(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function wr(){var e=F.p;return e!==0?e:(e=window.event,e===void 0?32:am(e.type))}function Ym(e,t){var l=F.p;try{return F.p=e,t()}finally{F.p=l}}var es=Math.random().toString(36).slice(2),Fe="__reactFiber$"+es,lt="__reactProps$"+es,Ys="__reactContainer$"+es,tc="__reactEvents$"+es,$m="__reactListeners$"+es,Xm="__reactHandles$"+es,Cr="__reactResources$"+es,Rl="__reactMarker$"+es;function sc(e){delete e[Fe],delete e[lt],delete e[tc],delete e[$m],delete e[Xm]}function $s(e){var t=e[Fe];if(t)return t;for(var l=e.parentNode;l;){if(t=l[Ys]||l[Fe]){if(l=t.alternate,t.child!==null||l!==null&&l.child!==null)for(e=Yo(e);e!==null;){if(l=e[Fe])return l;e=Yo(e)}return t}e=l,l=e.parentNode}return null}function Xs(e){if(e=e[Fe]||e[Ys]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Ol(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(x(33))}function Qs(e){var t=e[Cr];return t||(t=e[Cr]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function $e(e){e[Rl]=!0}var Tr=new Set,Ar={};function ws(e,t){Zs(e,t),Zs(e+"Capture",t)}function Zs(e,t){for(Ar[e]=t,e=0;e<t.length;e++)Tr.add(t[e])}var Qm=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),_r={},Mr={};function Zm(e){return Jn.call(Mr,e)?!0:Jn.call(_r,e)?!1:Qm.test(e)?Mr[e]=!0:(_r[e]=!0,!1)}function Da(e,t,l){if(Zm(t))if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var a=t.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+l)}}function za(e,t,l){if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+l)}}function qt(e,t,l,a){if(a===null)e.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(l);return}e.setAttributeNS(t,l,""+a)}}var lc,Er;function Ks(e){if(lc===void 0)try{throw Error()}catch(l){var t=l.stack.trim().match(/\n( *(at )?)/);lc=t&&t[1]||"",Er=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+lc+e+Er}var ac=!1;function nc(e,t){if(!e||ac)return"";ac=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(t){var W=function(){throw Error()};if(Object.defineProperty(W.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(W,[])}catch(G){var U=G}Reflect.construct(e,[],W)}else{try{W.call()}catch(G){U=G}e.call(W.prototype)}}else{try{throw Error()}catch(G){U=G}(W=e())&&typeof W.catch=="function"&&W.catch(function(){})}}catch(G){if(G&&U&&typeof G.stack=="string")return[G.stack,U.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=a.DetermineComponentFrameRoot(),r=c[0],m=c[1];if(r&&m){var y=r.split(`
`),O=m.split(`
`);for(n=a=0;a<y.length&&!y[a].includes("DetermineComponentFrameRoot");)a++;for(;n<O.length&&!O[n].includes("DetermineComponentFrameRoot");)n++;if(a===y.length||n===O.length)for(a=y.length-1,n=O.length-1;1<=a&&0<=n&&y[a]!==O[n];)n--;for(;1<=a&&0<=n;a--,n--)if(y[a]!==O[n]){if(a!==1||n!==1)do if(a--,n--,0>n||y[a]!==O[n]){var $=`
`+y[a].replace(" at new "," at ");return e.displayName&&$.includes("<anonymous>")&&($=$.replace("<anonymous>",e.displayName)),$}while(1<=a&&0<=n);break}}}finally{ac=!1,Error.prepareStackTrace=l}return(l=e?e.displayName||e.name:"")?Ks(l):""}function Km(e){switch(e.tag){case 26:case 27:case 5:return Ks(e.type);case 16:return Ks("Lazy");case 13:return Ks("Suspense");case 19:return Ks("SuspenseList");case 0:case 15:return nc(e.type,!1);case 11:return nc(e.type.render,!1);case 1:return nc(e.type,!0);case 31:return Ks("Activity");default:return""}}function Rr(e){try{var t="";do t+=Km(e),e=e.return;while(e);return t}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function yt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Or(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Jm(e){var t=Or(e)?"checked":"value",l=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,c=l.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return n.call(this)},set:function(r){a=""+r,c.call(this,r)}}),Object.defineProperty(e,t,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(r){a=""+r},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function La(e){e._valueTracker||(e._valueTracker=Jm(e))}function kr(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var l=t.getValue(),a="";return e&&(a=Or(e)?e.checked?"true":"false":e.value),e=a,e!==l?(t.setValue(e),!0):!1}function Ua(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Wm=/[\n"\\]/g;function vt(e){return e.replace(Wm,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function cc(e,t,l,a,n,c,r,m){e.name="",r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"?e.type=r:e.removeAttribute("type"),t!=null?r==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+yt(t)):e.value!==""+yt(t)&&(e.value=""+yt(t)):r!=="submit"&&r!=="reset"||e.removeAttribute("value"),t!=null?ic(e,r,yt(t)):l!=null?ic(e,r,yt(l)):a!=null&&e.removeAttribute("value"),n==null&&c!=null&&(e.defaultChecked=!!c),n!=null&&(e.checked=n&&typeof n!="function"&&typeof n!="symbol"),m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"?e.name=""+yt(m):e.removeAttribute("name")}function Dr(e,t,l,a,n,c,r,m){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.type=c),t!=null||l!=null){if(!(c!=="submit"&&c!=="reset"||t!=null))return;l=l!=null?""+yt(l):"",t=t!=null?""+yt(t):l,m||t===e.value||(e.value=t),e.defaultValue=t}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,e.checked=m?e.checked:!!a,e.defaultChecked=!!a,r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(e.name=r)}function ic(e,t,l){t==="number"&&Ua(e.ownerDocument)===e||e.defaultValue===""+l||(e.defaultValue=""+l)}function Js(e,t,l,a){if(e=e.options,t){t={};for(var n=0;n<l.length;n++)t["$"+l[n]]=!0;for(l=0;l<e.length;l++)n=t.hasOwnProperty("$"+e[l].value),e[l].selected!==n&&(e[l].selected=n),n&&a&&(e[l].defaultSelected=!0)}else{for(l=""+yt(l),t=null,n=0;n<e.length;n++){if(e[n].value===l){e[n].selected=!0,a&&(e[n].defaultSelected=!0);return}t!==null||e[n].disabled||(t=e[n])}t!==null&&(t.selected=!0)}}function zr(e,t,l){if(t!=null&&(t=""+yt(t),t!==e.value&&(e.value=t),l==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=l!=null?""+yt(l):""}function Lr(e,t,l,a){if(t==null){if(a!=null){if(l!=null)throw Error(x(92));if(Q(a)){if(1<a.length)throw Error(x(93));a=a[0]}l=a}l==null&&(l=""),t=l}l=yt(t),e.defaultValue=l,a=e.textContent,a===l&&a!==""&&a!==null&&(e.value=a)}function Ws(e,t){if(t){var l=e.firstChild;if(l&&l===e.lastChild&&l.nodeType===3){l.nodeValue=t;return}}e.textContent=t}var Fm=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Ur(e,t,l){var a=t.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":a?e.setProperty(t,l):typeof l!="number"||l===0||Fm.has(t)?t==="float"?e.cssFloat=l:e[t]=(""+l).trim():e[t]=l+"px"}function qr(e,t,l){if(t!=null&&typeof t!="object")throw Error(x(62));if(e=e.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||t!=null&&t.hasOwnProperty(a)||(a.indexOf("--")===0?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="");for(var n in t)a=t[n],t.hasOwnProperty(n)&&l[n]!==a&&Ur(e,n,a)}else for(var c in t)t.hasOwnProperty(c)&&Ur(e,c,t[c])}function rc(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Pm=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Im=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function qa(e){return Im.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var uc=null;function dc(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Fs=null,Ps=null;function Hr(e){var t=Xs(e);if(t&&(e=t.stateNode)){var l=e[lt]||null;e:switch(e=t.stateNode,t.type){case"input":if(cc(e,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),t=l.name,l.type==="radio"&&t!=null){for(l=e;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+vt(""+t)+'"][type="radio"]'),t=0;t<l.length;t++){var a=l[t];if(a!==e&&a.form===e.form){var n=a[lt]||null;if(!n)throw Error(x(90));cc(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(t=0;t<l.length;t++)a=l[t],a.form===e.form&&kr(a)}break e;case"textarea":zr(e,l.value,l.defaultValue);break e;case"select":t=l.value,t!=null&&Js(e,!!l.multiple,t,!1)}}}var oc=!1;function Gr(e,t,l){if(oc)return e(t,l);oc=!0;try{var a=e(t);return a}finally{if(oc=!1,(Fs!==null||Ps!==null)&&(wn(),Fs&&(t=Fs,e=Ps,Ps=Fs=null,Hr(t),e)))for(t=0;t<e.length;t++)Hr(e[t])}}function kl(e,t){var l=e.stateNode;if(l===null)return null;var a=l[lt]||null;if(a===null)return null;l=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(l&&typeof l!="function")throw Error(x(231,t,typeof l));return l}var Ht=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),mc=!1;if(Ht)try{var Dl={};Object.defineProperty(Dl,"passive",{get:function(){mc=!0}}),window.addEventListener("test",Dl,Dl),window.removeEventListener("test",Dl,Dl)}catch{mc=!1}var ts=null,xc=null,Ha=null;function Vr(){if(Ha)return Ha;var e,t=xc,l=t.length,a,n="value"in ts?ts.value:ts.textContent,c=n.length;for(e=0;e<l&&t[e]===n[e];e++);var r=l-e;for(a=1;a<=r&&t[l-a]===n[c-a];a++);return Ha=n.slice(e,1<a?1-a:void 0)}function Ga(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Va(){return!0}function Br(){return!1}function at(e){function t(l,a,n,c,r){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=c,this.target=r,this.currentTarget=null;for(var m in e)e.hasOwnProperty(m)&&(l=e[m],this[m]=l?l(c):c[m]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?Va:Br,this.isPropagationStopped=Br,this}return g(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=Va)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=Va)},persist:function(){},isPersistent:Va}),t}var Cs={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ba=at(Cs),zl=g({},Cs,{view:0,detail:0}),ex=at(zl),fc,hc,Ll,Ya=g({},zl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:bc,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Ll&&(Ll&&e.type==="mousemove"?(fc=e.screenX-Ll.screenX,hc=e.screenY-Ll.screenY):hc=fc=0,Ll=e),fc)},movementY:function(e){return"movementY"in e?e.movementY:hc}}),Yr=at(Ya),tx=g({},Ya,{dataTransfer:0}),sx=at(tx),lx=g({},zl,{relatedTarget:0}),gc=at(lx),ax=g({},Cs,{animationName:0,elapsedTime:0,pseudoElement:0}),nx=at(ax),cx=g({},Cs,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),ix=at(cx),rx=g({},Cs,{data:0}),$r=at(rx),ux={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dx={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ox={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function mx(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=ox[e])?!!t[e]:!1}function bc(){return mx}var xx=g({},zl,{key:function(e){if(e.key){var t=ux[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ga(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?dx[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:bc,charCode:function(e){return e.type==="keypress"?Ga(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ga(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),fx=at(xx),hx=g({},Ya,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Xr=at(hx),gx=g({},zl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:bc}),bx=at(gx),yx=g({},Cs,{propertyName:0,elapsedTime:0,pseudoElement:0}),vx=at(yx),px=g({},Ya,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),jx=at(px),Nx=g({},Cs,{newState:0,oldState:0}),Sx=at(Nx),wx=[9,13,27,32],yc=Ht&&"CompositionEvent"in window,Ul=null;Ht&&"documentMode"in document&&(Ul=document.documentMode);var Cx=Ht&&"TextEvent"in window&&!Ul,Qr=Ht&&(!yc||Ul&&8<Ul&&11>=Ul),Zr=" ",Kr=!1;function Jr(e,t){switch(e){case"keyup":return wx.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Wr(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Is=!1;function Tx(e,t){switch(e){case"compositionend":return Wr(t);case"keypress":return t.which!==32?null:(Kr=!0,Zr);case"textInput":return e=t.data,e===Zr&&Kr?null:e;default:return null}}function Ax(e,t){if(Is)return e==="compositionend"||!yc&&Jr(e,t)?(e=Vr(),Ha=xc=ts=null,Is=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Qr&&t.locale!=="ko"?null:t.data;default:return null}}var _x={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Fr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!_x[e.type]:t==="textarea"}function Pr(e,t,l,a){Fs?Ps?Ps.push(a):Ps=[a]:Fs=a,t=En(t,"onChange"),0<t.length&&(l=new Ba("onChange","change",null,l,a),e.push({event:l,listeners:t}))}var ql=null,Hl=null;function Mx(e){ko(e,0)}function $a(e){var t=Ol(e);if(kr(t))return e}function Ir(e,t){if(e==="change")return t}var eu=!1;if(Ht){var vc;if(Ht){var pc="oninput"in document;if(!pc){var tu=document.createElement("div");tu.setAttribute("oninput","return;"),pc=typeof tu.oninput=="function"}vc=pc}else vc=!1;eu=vc&&(!document.documentMode||9<document.documentMode)}function su(){ql&&(ql.detachEvent("onpropertychange",lu),Hl=ql=null)}function lu(e){if(e.propertyName==="value"&&$a(Hl)){var t=[];Pr(t,Hl,e,dc(e)),Gr(Mx,t)}}function Ex(e,t,l){e==="focusin"?(su(),ql=t,Hl=l,ql.attachEvent("onpropertychange",lu)):e==="focusout"&&su()}function Rx(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return $a(Hl)}function Ox(e,t){if(e==="click")return $a(t)}function kx(e,t){if(e==="input"||e==="change")return $a(t)}function Dx(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ot=typeof Object.is=="function"?Object.is:Dx;function Gl(e,t){if(ot(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var l=Object.keys(e),a=Object.keys(t);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!Jn.call(t,n)||!ot(e[n],t[n]))return!1}return!0}function au(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function nu(e,t){var l=au(e);e=0;for(var a;l;){if(l.nodeType===3){if(a=e+l.textContent.length,e<=t&&a>=t)return{node:l,offset:t-e};e=a}e:{for(;l;){if(l.nextSibling){l=l.nextSibling;break e}l=l.parentNode}l=void 0}l=au(l)}}function cu(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?cu(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function iu(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Ua(e.document);t instanceof e.HTMLIFrameElement;){try{var l=typeof t.contentWindow.location.href=="string"}catch{l=!1}if(l)e=t.contentWindow;else break;t=Ua(e.document)}return t}function jc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var zx=Ht&&"documentMode"in document&&11>=document.documentMode,el=null,Nc=null,Vl=null,Sc=!1;function ru(e,t,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;Sc||el==null||el!==Ua(a)||(a=el,"selectionStart"in a&&jc(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),Vl&&Gl(Vl,a)||(Vl=a,a=En(Nc,"onSelect"),0<a.length&&(t=new Ba("onSelect","select",null,t,l),e.push({event:t,listeners:a}),t.target=el)))}function Ts(e,t){var l={};return l[e.toLowerCase()]=t.toLowerCase(),l["Webkit"+e]="webkit"+t,l["Moz"+e]="moz"+t,l}var tl={animationend:Ts("Animation","AnimationEnd"),animationiteration:Ts("Animation","AnimationIteration"),animationstart:Ts("Animation","AnimationStart"),transitionrun:Ts("Transition","TransitionRun"),transitionstart:Ts("Transition","TransitionStart"),transitioncancel:Ts("Transition","TransitionCancel"),transitionend:Ts("Transition","TransitionEnd")},wc={},uu={};Ht&&(uu=document.createElement("div").style,"AnimationEvent"in window||(delete tl.animationend.animation,delete tl.animationiteration.animation,delete tl.animationstart.animation),"TransitionEvent"in window||delete tl.transitionend.transition);function As(e){if(wc[e])return wc[e];if(!tl[e])return e;var t=tl[e],l;for(l in t)if(t.hasOwnProperty(l)&&l in uu)return wc[e]=t[l];return e}var du=As("animationend"),ou=As("animationiteration"),mu=As("animationstart"),Lx=As("transitionrun"),Ux=As("transitionstart"),qx=As("transitioncancel"),xu=As("transitionend"),fu=new Map,Cc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Cc.push("scrollEnd");function At(e,t){fu.set(e,t),ws(t,[e])}var hu=new WeakMap;function pt(e,t){if(typeof e=="object"&&e!==null){var l=hu.get(e);return l!==void 0?l:(t={value:e,source:t,stack:Rr(t)},hu.set(e,t),t)}return{value:e,source:t,stack:Rr(t)}}var jt=[],sl=0,Tc=0;function Xa(){for(var e=sl,t=Tc=sl=0;t<e;){var l=jt[t];jt[t++]=null;var a=jt[t];jt[t++]=null;var n=jt[t];jt[t++]=null;var c=jt[t];if(jt[t++]=null,a!==null&&n!==null){var r=a.pending;r===null?n.next=n:(n.next=r.next,r.next=n),a.pending=n}c!==0&&gu(l,n,c)}}function Qa(e,t,l,a){jt[sl++]=e,jt[sl++]=t,jt[sl++]=l,jt[sl++]=a,Tc|=a,e.lanes|=a,e=e.alternate,e!==null&&(e.lanes|=a)}function Ac(e,t,l,a){return Qa(e,t,l,a),Za(e)}function ll(e,t){return Qa(e,null,null,t),Za(e)}function gu(e,t,l){e.lanes|=l;var a=e.alternate;a!==null&&(a.lanes|=l);for(var n=!1,c=e.return;c!==null;)c.childLanes|=l,a=c.alternate,a!==null&&(a.childLanes|=l),c.tag===22&&(e=c.stateNode,e===null||e._visibility&1||(n=!0)),e=c,c=c.return;return e.tag===3?(c=e.stateNode,n&&t!==null&&(n=31-dt(l),e=c.hiddenUpdates,a=e[n],a===null?e[n]=[t]:a.push(t),t.lane=l|536870912),c):null}function Za(e){if(50<ma)throw ma=0,ki=null,Error(x(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var al={};function Hx(e,t,l,a){this.tag=e,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function mt(e,t,l,a){return new Hx(e,t,l,a)}function _c(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Gt(e,t){var l=e.alternate;return l===null?(l=mt(e.tag,t,e.key,e.mode),l.elementType=e.elementType,l.type=e.type,l.stateNode=e.stateNode,l.alternate=e,e.alternate=l):(l.pendingProps=t,l.type=e.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=e.flags&65011712,l.childLanes=e.childLanes,l.lanes=e.lanes,l.child=e.child,l.memoizedProps=e.memoizedProps,l.memoizedState=e.memoizedState,l.updateQueue=e.updateQueue,t=e.dependencies,l.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},l.sibling=e.sibling,l.index=e.index,l.ref=e.ref,l.refCleanup=e.refCleanup,l}function bu(e,t){e.flags&=65011714;var l=e.alternate;return l===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=l.childLanes,e.lanes=l.lanes,e.child=l.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=l.memoizedProps,e.memoizedState=l.memoizedState,e.updateQueue=l.updateQueue,e.type=l.type,t=l.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Ka(e,t,l,a,n,c){var r=0;if(a=e,typeof e=="function")_c(e)&&(r=1);else if(typeof e=="string")r=Bf(e,l,H.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case X:return e=mt(31,l,t,n),e.elementType=X,e.lanes=c,e;case z:return _s(l.children,n,c,t);case D:r=8,n|=24;break;case j:return e=mt(12,l,t,n|2),e.elementType=j,e.lanes=c,e;case _:return e=mt(13,l,t,n),e.elementType=_,e.lanes=c,e;case V:return e=mt(19,l,t,n),e.elementType=V,e.lanes=c,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case te:case le:r=10;break e;case I:r=9;break e;case ae:r=11;break e;case u:r=14;break e;case q:r=16,a=null;break e}r=29,l=Error(x(130,e===null?"null":typeof e,"")),a=null}return t=mt(r,l,t,n),t.elementType=e,t.type=a,t.lanes=c,t}function _s(e,t,l,a){return e=mt(7,e,a,t),e.lanes=l,e}function Mc(e,t,l){return e=mt(6,e,null,t),e.lanes=l,e}function Ec(e,t,l){return t=mt(4,e.children!==null?e.children:[],e.key,t),t.lanes=l,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var nl=[],cl=0,Ja=null,Wa=0,Nt=[],St=0,Ms=null,Vt=1,Bt="";function Es(e,t){nl[cl++]=Wa,nl[cl++]=Ja,Ja=e,Wa=t}function yu(e,t,l){Nt[St++]=Vt,Nt[St++]=Bt,Nt[St++]=Ms,Ms=e;var a=Vt;e=Bt;var n=32-dt(a)-1;a&=~(1<<n),l+=1;var c=32-dt(t)+n;if(30<c){var r=n-n%5;c=(a&(1<<r)-1).toString(32),a>>=r,n-=r,Vt=1<<32-dt(t)+n|l<<n|a,Bt=c+e}else Vt=1<<c|l<<n|a,Bt=e}function Rc(e){e.return!==null&&(Es(e,1),yu(e,1,0))}function Oc(e){for(;e===Ja;)Ja=nl[--cl],nl[cl]=null,Wa=nl[--cl],nl[cl]=null;for(;e===Ms;)Ms=Nt[--St],Nt[St]=null,Bt=Nt[--St],Nt[St]=null,Vt=Nt[--St],Nt[St]=null}var st=null,De=null,Se=!1,Rs=null,Ot=!1,kc=Error(x(519));function Os(e){var t=Error(x(418,""));throw $l(pt(t,e)),kc}function vu(e){var t=e.stateNode,l=e.type,a=e.memoizedProps;switch(t[Fe]=e,t[lt]=a,l){case"dialog":pe("cancel",t),pe("close",t);break;case"iframe":case"object":case"embed":pe("load",t);break;case"video":case"audio":for(l=0;l<fa.length;l++)pe(fa[l],t);break;case"source":pe("error",t);break;case"img":case"image":case"link":pe("error",t),pe("load",t);break;case"details":pe("toggle",t);break;case"input":pe("invalid",t),Dr(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),La(t);break;case"select":pe("invalid",t);break;case"textarea":pe("invalid",t),Lr(t,a.value,a.defaultValue,a.children),La(t)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||t.textContent===""+l||a.suppressHydrationWarning===!0||Uo(t.textContent,l)?(a.popover!=null&&(pe("beforetoggle",t),pe("toggle",t)),a.onScroll!=null&&pe("scroll",t),a.onScrollEnd!=null&&pe("scrollend",t),a.onClick!=null&&(t.onclick=Rn),t=!0):t=!1,t||Os(e)}function pu(e){for(st=e.return;st;)switch(st.tag){case 5:case 13:Ot=!1;return;case 27:case 3:Ot=!0;return;default:st=st.return}}function Bl(e){if(e!==st)return!1;if(!Se)return pu(e),Se=!0,!1;var t=e.tag,l;if((l=t!==3&&t!==27)&&((l=t===5)&&(l=e.type,l=!(l!=="form"&&l!=="button")||Ji(e.type,e.memoizedProps)),l=!l),l&&De&&Os(e),pu(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(x(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(l=e.data,l==="/$"){if(t===0){De=Mt(e.nextSibling);break e}t--}else l!=="$"&&l!=="$!"&&l!=="$?"||t++;e=e.nextSibling}De=null}}else t===27?(t=De,bs(e.type)?(e=Ii,Ii=null,De=e):De=t):De=st?Mt(e.stateNode.nextSibling):null;return!0}function Yl(){De=st=null,Se=!1}function ju(){var e=Rs;return e!==null&&(it===null?it=e:it.push.apply(it,e),Rs=null),e}function $l(e){Rs===null?Rs=[e]:Rs.push(e)}var Dc=B(null),ks=null,Yt=null;function ss(e,t,l){se(Dc,t._currentValue),t._currentValue=l}function $t(e){e._currentValue=Dc.current,P(Dc)}function zc(e,t,l){for(;e!==null;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===l)break;e=e.return}}function Lc(e,t,l,a){var n=e.child;for(n!==null&&(n.return=e);n!==null;){var c=n.dependencies;if(c!==null){var r=n.child;c=c.firstContext;e:for(;c!==null;){var m=c;c=n;for(var y=0;y<t.length;y++)if(m.context===t[y]){c.lanes|=l,m=c.alternate,m!==null&&(m.lanes|=l),zc(c.return,l,e),a||(r=null);break e}c=m.next}}else if(n.tag===18){if(r=n.return,r===null)throw Error(x(341));r.lanes|=l,c=r.alternate,c!==null&&(c.lanes|=l),zc(r,l,e),r=null}else r=n.child;if(r!==null)r.return=n;else for(r=n;r!==null;){if(r===e){r=null;break}if(n=r.sibling,n!==null){n.return=r.return,r=n;break}r=r.return}n=r}}function Xl(e,t,l,a){e=null;for(var n=t,c=!1;n!==null;){if(!c){if((n.flags&524288)!==0)c=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var r=n.alternate;if(r===null)throw Error(x(387));if(r=r.memoizedProps,r!==null){var m=n.type;ot(n.pendingProps.value,r.value)||(e!==null?e.push(m):e=[m])}}else if(n===qe.current){if(r=n.alternate,r===null)throw Error(x(387));r.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(e!==null?e.push(pa):e=[pa])}n=n.return}e!==null&&Lc(t,e,l,a),t.flags|=262144}function Fa(e){for(e=e.firstContext;e!==null;){if(!ot(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ds(e){ks=e,Yt=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function Pe(e){return Nu(ks,e)}function Pa(e,t){return ks===null&&Ds(e),Nu(e,t)}function Nu(e,t){var l=t._currentValue;if(t={context:t,memoizedValue:l,next:null},Yt===null){if(e===null)throw Error(x(308));Yt=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Yt=Yt.next=t;return l}var Gx=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(l,a){e.push(a)}};this.abort=function(){t.aborted=!0,e.forEach(function(l){return l()})}},Vx=i.unstable_scheduleCallback,Bx=i.unstable_NormalPriority,Be={$$typeof:le,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Uc(){return{controller:new Gx,data:new Map,refCount:0}}function Ql(e){e.refCount--,e.refCount===0&&Vx(Bx,function(){e.controller.abort()})}var Zl=null,qc=0,il=0,rl=null;function Yx(e,t){if(Zl===null){var l=Zl=[];qc=0,il=Gi(),rl={status:"pending",value:void 0,then:function(a){l.push(a)}}}return qc++,t.then(Su,Su),t}function Su(){if(--qc===0&&Zl!==null){rl!==null&&(rl.status="fulfilled");var e=Zl;Zl=null,il=0,rl=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function $x(e,t){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return e.then(function(){a.status="fulfilled",a.value=t;for(var n=0;n<l.length;n++)(0,l[n])(t)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var wu=E.S;E.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&Yx(e,t),wu!==null&&wu(e,t)};var zs=B(null);function Hc(){var e=zs.current;return e!==null?e:Re.pooledCache}function Ia(e,t){t===null?se(zs,zs.current):se(zs,t.pool)}function Cu(){var e=Hc();return e===null?null:{parent:Be._currentValue,pool:e}}var Kl=Error(x(460)),Tu=Error(x(474)),en=Error(x(542)),Gc={then:function(){}};function Au(e){return e=e.status,e==="fulfilled"||e==="rejected"}function tn(){}function _u(e,t,l){switch(l=e[l],l===void 0?e.push(t):l!==t&&(t.then(tn,tn),t=l),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Eu(e),e;default:if(typeof t.status=="string")t.then(tn,tn);else{if(e=Re,e!==null&&100<e.shellSuspendCounter)throw Error(x(482));e=t,e.status="pending",e.then(function(a){if(t.status==="pending"){var n=t;n.status="fulfilled",n.value=a}},function(a){if(t.status==="pending"){var n=t;n.status="rejected",n.reason=a}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Eu(e),e}throw Jl=t,Kl}}var Jl=null;function Mu(){if(Jl===null)throw Error(x(459));var e=Jl;return Jl=null,e}function Eu(e){if(e===Kl||e===en)throw Error(x(483))}var ls=!1;function Vc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Bc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function as(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function ns(e,t,l){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,(Ce&2)!==0){var n=a.pending;return n===null?t.next=t:(t.next=n.next,n.next=t),a.pending=t,t=Za(e),gu(e,null,l),t}return Qa(e,a,t,l),Za(e)}function Wl(e,t,l){if(t=t.updateQueue,t!==null&&(t=t.shared,(l&4194048)!==0)){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,Sr(e,l)}}function Yc(e,t){var l=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,c=null;if(l=l.firstBaseUpdate,l!==null){do{var r={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};c===null?n=c=r:c=c.next=r,l=l.next}while(l!==null);c===null?n=c=t:c=c.next=t}else n=c=t;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:c,shared:a.shared,callbacks:a.callbacks},e.updateQueue=l;return}e=l.lastBaseUpdate,e===null?l.firstBaseUpdate=t:e.next=t,l.lastBaseUpdate=t}var $c=!1;function Fl(){if($c){var e=rl;if(e!==null)throw e}}function Pl(e,t,l,a){$c=!1;var n=e.updateQueue;ls=!1;var c=n.firstBaseUpdate,r=n.lastBaseUpdate,m=n.shared.pending;if(m!==null){n.shared.pending=null;var y=m,O=y.next;y.next=null,r===null?c=O:r.next=O,r=y;var $=e.alternate;$!==null&&($=$.updateQueue,m=$.lastBaseUpdate,m!==r&&(m===null?$.firstBaseUpdate=O:m.next=O,$.lastBaseUpdate=y))}if(c!==null){var W=n.baseState;r=0,$=O=y=null,m=c;do{var U=m.lane&-536870913,G=U!==m.lane;if(G?(je&U)===U:(a&U)===U){U!==0&&U===il&&($c=!0),$!==null&&($=$.next={lane:0,tag:m.tag,payload:m.payload,callback:null,next:null});e:{var xe=e,oe=m;U=t;var Me=l;switch(oe.tag){case 1:if(xe=oe.payload,typeof xe=="function"){W=xe.call(Me,W,U);break e}W=xe;break e;case 3:xe.flags=xe.flags&-65537|128;case 0:if(xe=oe.payload,U=typeof xe=="function"?xe.call(Me,W,U):xe,U==null)break e;W=g({},W,U);break e;case 2:ls=!0}}U=m.callback,U!==null&&(e.flags|=64,G&&(e.flags|=8192),G=n.callbacks,G===null?n.callbacks=[U]:G.push(U))}else G={lane:U,tag:m.tag,payload:m.payload,callback:m.callback,next:null},$===null?(O=$=G,y=W):$=$.next=G,r|=U;if(m=m.next,m===null){if(m=n.shared.pending,m===null)break;G=m,m=G.next,G.next=null,n.lastBaseUpdate=G,n.shared.pending=null}}while(!0);$===null&&(y=W),n.baseState=y,n.firstBaseUpdate=O,n.lastBaseUpdate=$,c===null&&(n.shared.lanes=0),xs|=r,e.lanes=r,e.memoizedState=W}}function Ru(e,t){if(typeof e!="function")throw Error(x(191,e));e.call(t)}function Ou(e,t){var l=e.callbacks;if(l!==null)for(e.callbacks=null,e=0;e<l.length;e++)Ru(l[e],t)}var ul=B(null),sn=B(0);function ku(e,t){e=Ft,se(sn,e),se(ul,t),Ft=e|t.baseLanes}function Xc(){se(sn,Ft),se(ul,ul.current)}function Qc(){Ft=sn.current,P(ul),P(sn)}var cs=0,ge=null,Ae=null,He=null,ln=!1,dl=!1,Ls=!1,an=0,Il=0,ol=null,Xx=0;function Le(){throw Error(x(321))}function Zc(e,t){if(t===null)return!1;for(var l=0;l<t.length&&l<e.length;l++)if(!ot(e[l],t[l]))return!1;return!0}function Kc(e,t,l,a,n,c){return cs=c,ge=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,E.H=e===null||e.memoizedState===null?gd:bd,Ls=!1,c=l(a,n),Ls=!1,dl&&(c=zu(t,l,a,n)),Du(e),c}function Du(e){E.H=on;var t=Ae!==null&&Ae.next!==null;if(cs=0,He=Ae=ge=null,ln=!1,Il=0,ol=null,t)throw Error(x(300));e===null||Xe||(e=e.dependencies,e!==null&&Fa(e)&&(Xe=!0))}function zu(e,t,l,a){ge=e;var n=0;do{if(dl&&(ol=null),Il=0,dl=!1,25<=n)throw Error(x(301));if(n+=1,He=Ae=null,e.updateQueue!=null){var c=e.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}E.H=Px,c=t(l,a)}while(dl);return c}function Qx(){var e=E.H,t=e.useState()[0];return t=typeof t.then=="function"?ea(t):t,e=e.useState()[0],(Ae!==null?Ae.memoizedState:null)!==e&&(ge.flags|=1024),t}function Jc(){var e=an!==0;return an=0,e}function Wc(e,t,l){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l}function Fc(e){if(ln){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}ln=!1}cs=0,He=Ae=ge=null,dl=!1,Il=an=0,ol=null}function nt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return He===null?ge.memoizedState=He=e:He=He.next=e,He}function Ge(){if(Ae===null){var e=ge.alternate;e=e!==null?e.memoizedState:null}else e=Ae.next;var t=He===null?ge.memoizedState:He.next;if(t!==null)He=t,Ae=e;else{if(e===null)throw ge.alternate===null?Error(x(467)):Error(x(310));Ae=e,e={memoizedState:Ae.memoizedState,baseState:Ae.baseState,baseQueue:Ae.baseQueue,queue:Ae.queue,next:null},He===null?ge.memoizedState=He=e:He=He.next=e}return He}function Pc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function ea(e){var t=Il;return Il+=1,ol===null&&(ol=[]),e=_u(ol,e,t),t=ge,(He===null?t.memoizedState:He.next)===null&&(t=t.alternate,E.H=t===null||t.memoizedState===null?gd:bd),e}function nn(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return ea(e);if(e.$$typeof===le)return Pe(e)}throw Error(x(438,String(e)))}function Ic(e){var t=null,l=ge.updateQueue;if(l!==null&&(t=l.memoCache),t==null){var a=ge.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(t={data:a.data.map(function(n){return n.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),l===null&&(l=Pc(),ge.updateQueue=l),l.memoCache=t,l=t.data[t.index],l===void 0)for(l=t.data[t.index]=Array(e),a=0;a<e;a++)l[a]=Y;return t.index++,l}function Xt(e,t){return typeof t=="function"?t(e):t}function cn(e){var t=Ge();return ei(t,Ae,e)}function ei(e,t,l){var a=e.queue;if(a===null)throw Error(x(311));a.lastRenderedReducer=l;var n=e.baseQueue,c=a.pending;if(c!==null){if(n!==null){var r=n.next;n.next=c.next,c.next=r}t.baseQueue=n=c,a.pending=null}if(c=e.baseState,n===null)e.memoizedState=c;else{t=n.next;var m=r=null,y=null,O=t,$=!1;do{var W=O.lane&-536870913;if(W!==O.lane?(je&W)===W:(cs&W)===W){var U=O.revertLane;if(U===0)y!==null&&(y=y.next={lane:0,revertLane:0,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null}),W===il&&($=!0);else if((cs&U)===U){O=O.next,U===il&&($=!0);continue}else W={lane:0,revertLane:O.revertLane,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null},y===null?(m=y=W,r=c):y=y.next=W,ge.lanes|=U,xs|=U;W=O.action,Ls&&l(c,W),c=O.hasEagerState?O.eagerState:l(c,W)}else U={lane:W,revertLane:O.revertLane,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null},y===null?(m=y=U,r=c):y=y.next=U,ge.lanes|=W,xs|=W;O=O.next}while(O!==null&&O!==t);if(y===null?r=c:y.next=m,!ot(c,e.memoizedState)&&(Xe=!0,$&&(l=rl,l!==null)))throw l;e.memoizedState=c,e.baseState=r,e.baseQueue=y,a.lastRenderedState=c}return n===null&&(a.lanes=0),[e.memoizedState,a.dispatch]}function ti(e){var t=Ge(),l=t.queue;if(l===null)throw Error(x(311));l.lastRenderedReducer=e;var a=l.dispatch,n=l.pending,c=t.memoizedState;if(n!==null){l.pending=null;var r=n=n.next;do c=e(c,r.action),r=r.next;while(r!==n);ot(c,t.memoizedState)||(Xe=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),l.lastRenderedState=c}return[c,a]}function Lu(e,t,l){var a=ge,n=Ge(),c=Se;if(c){if(l===void 0)throw Error(x(407));l=l()}else l=t();var r=!ot((Ae||n).memoizedState,l);r&&(n.memoizedState=l,Xe=!0),n=n.queue;var m=Hu.bind(null,a,n,e);if(ta(2048,8,m,[e]),n.getSnapshot!==t||r||He!==null&&He.memoizedState.tag&1){if(a.flags|=2048,ml(9,rn(),qu.bind(null,a,n,l,t),null),Re===null)throw Error(x(349));c||(cs&124)!==0||Uu(a,t,l)}return l}function Uu(e,t,l){e.flags|=16384,e={getSnapshot:t,value:l},t=ge.updateQueue,t===null?(t=Pc(),ge.updateQueue=t,t.stores=[e]):(l=t.stores,l===null?t.stores=[e]:l.push(e))}function qu(e,t,l,a){t.value=l,t.getSnapshot=a,Gu(t)&&Vu(e)}function Hu(e,t,l){return l(function(){Gu(t)&&Vu(e)})}function Gu(e){var t=e.getSnapshot;e=e.value;try{var l=t();return!ot(e,l)}catch{return!0}}function Vu(e){var t=ll(e,2);t!==null&&bt(t,e,2)}function si(e){var t=nt();if(typeof e=="function"){var l=e;if(e=l(),Ls){It(!0);try{l()}finally{It(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Xt,lastRenderedState:e},t}function Bu(e,t,l,a){return e.baseState=l,ei(e,Ae,typeof a=="function"?a:Xt)}function Zx(e,t,l,a,n){if(dn(e))throw Error(x(485));if(e=t.action,e!==null){var c={payload:n,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(r){c.listeners.push(r)}};E.T!==null?l(!0):c.isTransition=!1,a(c),l=t.pending,l===null?(c.next=t.pending=c,Yu(t,c)):(c.next=l.next,t.pending=l.next=c)}}function Yu(e,t){var l=t.action,a=t.payload,n=e.state;if(t.isTransition){var c=E.T,r={};E.T=r;try{var m=l(n,a),y=E.S;y!==null&&y(r,m),$u(e,t,m)}catch(O){li(e,t,O)}finally{E.T=c}}else try{c=l(n,a),$u(e,t,c)}catch(O){li(e,t,O)}}function $u(e,t,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){Xu(e,t,a)},function(a){return li(e,t,a)}):Xu(e,t,l)}function Xu(e,t,l){t.status="fulfilled",t.value=l,Qu(t),e.state=l,t=e.pending,t!==null&&(l=t.next,l===t?e.pending=null:(l=l.next,t.next=l,Yu(e,l)))}function li(e,t,l){var a=e.pending;if(e.pending=null,a!==null){a=a.next;do t.status="rejected",t.reason=l,Qu(t),t=t.next;while(t!==a)}e.action=null}function Qu(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Zu(e,t){return t}function Ku(e,t){if(Se){var l=Re.formState;if(l!==null){e:{var a=ge;if(Se){if(De){t:{for(var n=De,c=Ot;n.nodeType!==8;){if(!c){n=null;break t}if(n=Mt(n.nextSibling),n===null){n=null;break t}}c=n.data,n=c==="F!"||c==="F"?n:null}if(n){De=Mt(n.nextSibling),a=n.data==="F!";break e}}Os(a)}a=!1}a&&(t=l[0])}}return l=nt(),l.memoizedState=l.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Zu,lastRenderedState:t},l.queue=a,l=xd.bind(null,ge,a),a.dispatch=l,a=si(!1),c=ri.bind(null,ge,!1,a.queue),a=nt(),n={state:t,dispatch:null,action:e,pending:null},a.queue=n,l=Zx.bind(null,ge,n,c,l),n.dispatch=l,a.memoizedState=e,[t,l,!1]}function Ju(e){var t=Ge();return Wu(t,Ae,e)}function Wu(e,t,l){if(t=ei(e,t,Zu)[0],e=cn(Xt)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var a=ea(t)}catch(r){throw r===Kl?en:r}else a=t;t=Ge();var n=t.queue,c=n.dispatch;return l!==t.memoizedState&&(ge.flags|=2048,ml(9,rn(),Kx.bind(null,n,l),null)),[a,c,e]}function Kx(e,t){e.action=t}function Fu(e){var t=Ge(),l=Ae;if(l!==null)return Wu(t,l,e);Ge(),t=t.memoizedState,l=Ge();var a=l.queue.dispatch;return l.memoizedState=e,[t,a,!1]}function ml(e,t,l,a){return e={tag:e,create:l,deps:a,inst:t,next:null},t=ge.updateQueue,t===null&&(t=Pc(),ge.updateQueue=t),l=t.lastEffect,l===null?t.lastEffect=e.next=e:(a=l.next,l.next=e,e.next=a,t.lastEffect=e),e}function rn(){return{destroy:void 0,resource:void 0}}function Pu(){return Ge().memoizedState}function un(e,t,l,a){var n=nt();a=a===void 0?null:a,ge.flags|=e,n.memoizedState=ml(1|t,rn(),l,a)}function ta(e,t,l,a){var n=Ge();a=a===void 0?null:a;var c=n.memoizedState.inst;Ae!==null&&a!==null&&Zc(a,Ae.memoizedState.deps)?n.memoizedState=ml(t,c,l,a):(ge.flags|=e,n.memoizedState=ml(1|t,c,l,a))}function Iu(e,t){un(8390656,8,e,t)}function ed(e,t){ta(2048,8,e,t)}function td(e,t){return ta(4,2,e,t)}function sd(e,t){return ta(4,4,e,t)}function ld(e,t){if(typeof t=="function"){e=e();var l=t(e);return function(){typeof l=="function"?l():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function ad(e,t,l){l=l!=null?l.concat([e]):null,ta(4,4,ld.bind(null,t,e),l)}function ai(){}function nd(e,t){var l=Ge();t=t===void 0?null:t;var a=l.memoizedState;return t!==null&&Zc(t,a[1])?a[0]:(l.memoizedState=[e,t],e)}function cd(e,t){var l=Ge();t=t===void 0?null:t;var a=l.memoizedState;if(t!==null&&Zc(t,a[1]))return a[0];if(a=e(),Ls){It(!0);try{e()}finally{It(!1)}}return l.memoizedState=[a,t],a}function ni(e,t,l){return l===void 0||(cs&1073741824)!==0?e.memoizedState=t:(e.memoizedState=l,e=uo(),ge.lanes|=e,xs|=e,l)}function id(e,t,l,a){return ot(l,t)?l:ul.current!==null?(e=ni(e,l,a),ot(e,t)||(Xe=!0),e):(cs&42)===0?(Xe=!0,e.memoizedState=l):(e=uo(),ge.lanes|=e,xs|=e,t)}function rd(e,t,l,a,n){var c=F.p;F.p=c!==0&&8>c?c:8;var r=E.T,m={};E.T=m,ri(e,!1,t,l);try{var y=n(),O=E.S;if(O!==null&&O(m,y),y!==null&&typeof y=="object"&&typeof y.then=="function"){var $=$x(y,a);sa(e,t,$,gt(e))}else sa(e,t,a,gt(e))}catch(W){sa(e,t,{then:function(){},status:"rejected",reason:W},gt())}finally{F.p=c,E.T=r}}function Jx(){}function ci(e,t,l,a){if(e.tag!==5)throw Error(x(476));var n=ud(e).queue;rd(e,n,t,ie,l===null?Jx:function(){return dd(e),l(a)})}function ud(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:ie,baseState:ie,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Xt,lastRenderedState:ie},next:null};var l={};return t.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Xt,lastRenderedState:l},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function dd(e){var t=ud(e).next.queue;sa(e,t,{},gt())}function ii(){return Pe(pa)}function od(){return Ge().memoizedState}function md(){return Ge().memoizedState}function Wx(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var l=gt();e=as(l);var a=ns(t,e,l);a!==null&&(bt(a,t,l),Wl(a,t,l)),t={cache:Uc()},e.payload=t;return}t=t.return}}function Fx(e,t,l){var a=gt();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},dn(e)?fd(t,l):(l=Ac(e,t,l,a),l!==null&&(bt(l,e,a),hd(l,t,a)))}function xd(e,t,l){var a=gt();sa(e,t,l,a)}function sa(e,t,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(dn(e))fd(t,n);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var r=t.lastRenderedState,m=c(r,l);if(n.hasEagerState=!0,n.eagerState=m,ot(m,r))return Qa(e,t,n,0),Re===null&&Xa(),!1}catch{}finally{}if(l=Ac(e,t,n,a),l!==null)return bt(l,e,a),hd(l,t,a),!0}return!1}function ri(e,t,l,a){if(a={lane:2,revertLane:Gi(),action:a,hasEagerState:!1,eagerState:null,next:null},dn(e)){if(t)throw Error(x(479))}else t=Ac(e,l,a,2),t!==null&&bt(t,e,2)}function dn(e){var t=e.alternate;return e===ge||t!==null&&t===ge}function fd(e,t){dl=ln=!0;var l=e.pending;l===null?t.next=t:(t.next=l.next,l.next=t),e.pending=t}function hd(e,t,l){if((l&4194048)!==0){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,Sr(e,l)}}var on={readContext:Pe,use:nn,useCallback:Le,useContext:Le,useEffect:Le,useImperativeHandle:Le,useLayoutEffect:Le,useInsertionEffect:Le,useMemo:Le,useReducer:Le,useRef:Le,useState:Le,useDebugValue:Le,useDeferredValue:Le,useTransition:Le,useSyncExternalStore:Le,useId:Le,useHostTransitionStatus:Le,useFormState:Le,useActionState:Le,useOptimistic:Le,useMemoCache:Le,useCacheRefresh:Le},gd={readContext:Pe,use:nn,useCallback:function(e,t){return nt().memoizedState=[e,t===void 0?null:t],e},useContext:Pe,useEffect:Iu,useImperativeHandle:function(e,t,l){l=l!=null?l.concat([e]):null,un(4194308,4,ld.bind(null,t,e),l)},useLayoutEffect:function(e,t){return un(4194308,4,e,t)},useInsertionEffect:function(e,t){un(4,2,e,t)},useMemo:function(e,t){var l=nt();t=t===void 0?null:t;var a=e();if(Ls){It(!0);try{e()}finally{It(!1)}}return l.memoizedState=[a,t],a},useReducer:function(e,t,l){var a=nt();if(l!==void 0){var n=l(t);if(Ls){It(!0);try{l(t)}finally{It(!1)}}}else n=t;return a.memoizedState=a.baseState=n,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},a.queue=e,e=e.dispatch=Fx.bind(null,ge,e),[a.memoizedState,e]},useRef:function(e){var t=nt();return e={current:e},t.memoizedState=e},useState:function(e){e=si(e);var t=e.queue,l=xd.bind(null,ge,t);return t.dispatch=l,[e.memoizedState,l]},useDebugValue:ai,useDeferredValue:function(e,t){var l=nt();return ni(l,e,t)},useTransition:function(){var e=si(!1);return e=rd.bind(null,ge,e.queue,!0,!1),nt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,l){var a=ge,n=nt();if(Se){if(l===void 0)throw Error(x(407));l=l()}else{if(l=t(),Re===null)throw Error(x(349));(je&124)!==0||Uu(a,t,l)}n.memoizedState=l;var c={value:l,getSnapshot:t};return n.queue=c,Iu(Hu.bind(null,a,c,e),[e]),a.flags|=2048,ml(9,rn(),qu.bind(null,a,c,l,t),null),l},useId:function(){var e=nt(),t=Re.identifierPrefix;if(Se){var l=Bt,a=Vt;l=(a&~(1<<32-dt(a)-1)).toString(32)+l,t="«"+t+"R"+l,l=an++,0<l&&(t+="H"+l.toString(32)),t+="»"}else l=Xx++,t="«"+t+"r"+l.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:ii,useFormState:Ku,useActionState:Ku,useOptimistic:function(e){var t=nt();t.memoizedState=t.baseState=e;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=l,t=ri.bind(null,ge,!0,l),l.dispatch=t,[e,t]},useMemoCache:Ic,useCacheRefresh:function(){return nt().memoizedState=Wx.bind(null,ge)}},bd={readContext:Pe,use:nn,useCallback:nd,useContext:Pe,useEffect:ed,useImperativeHandle:ad,useInsertionEffect:td,useLayoutEffect:sd,useMemo:cd,useReducer:cn,useRef:Pu,useState:function(){return cn(Xt)},useDebugValue:ai,useDeferredValue:function(e,t){var l=Ge();return id(l,Ae.memoizedState,e,t)},useTransition:function(){var e=cn(Xt)[0],t=Ge().memoizedState;return[typeof e=="boolean"?e:ea(e),t]},useSyncExternalStore:Lu,useId:od,useHostTransitionStatus:ii,useFormState:Ju,useActionState:Ju,useOptimistic:function(e,t){var l=Ge();return Bu(l,Ae,e,t)},useMemoCache:Ic,useCacheRefresh:md},Px={readContext:Pe,use:nn,useCallback:nd,useContext:Pe,useEffect:ed,useImperativeHandle:ad,useInsertionEffect:td,useLayoutEffect:sd,useMemo:cd,useReducer:ti,useRef:Pu,useState:function(){return ti(Xt)},useDebugValue:ai,useDeferredValue:function(e,t){var l=Ge();return Ae===null?ni(l,e,t):id(l,Ae.memoizedState,e,t)},useTransition:function(){var e=ti(Xt)[0],t=Ge().memoizedState;return[typeof e=="boolean"?e:ea(e),t]},useSyncExternalStore:Lu,useId:od,useHostTransitionStatus:ii,useFormState:Fu,useActionState:Fu,useOptimistic:function(e,t){var l=Ge();return Ae!==null?Bu(l,Ae,e,t):(l.baseState=e,[e,l.queue.dispatch])},useMemoCache:Ic,useCacheRefresh:md},xl=null,la=0;function mn(e){var t=la;return la+=1,xl===null&&(xl=[]),_u(xl,e,t)}function aa(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function xn(e,t){throw t.$$typeof===C?Error(x(525)):(e=Object.prototype.toString.call(t),Error(x(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function yd(e){var t=e._init;return t(e._payload)}function vd(e){function t(T,N){if(e){var R=T.deletions;R===null?(T.deletions=[N],T.flags|=16):R.push(N)}}function l(T,N){if(!e)return null;for(;N!==null;)t(T,N),N=N.sibling;return null}function a(T){for(var N=new Map;T!==null;)T.key!==null?N.set(T.key,T):N.set(T.index,T),T=T.sibling;return N}function n(T,N){return T=Gt(T,N),T.index=0,T.sibling=null,T}function c(T,N,R){return T.index=R,e?(R=T.alternate,R!==null?(R=R.index,R<N?(T.flags|=67108866,N):R):(T.flags|=67108866,N)):(T.flags|=1048576,N)}function r(T){return e&&T.alternate===null&&(T.flags|=67108866),T}function m(T,N,R,Z){return N===null||N.tag!==6?(N=Mc(R,T.mode,Z),N.return=T,N):(N=n(N,R),N.return=T,N)}function y(T,N,R,Z){var ce=R.type;return ce===z?$(T,N,R.props.children,Z,R.key):N!==null&&(N.elementType===ce||typeof ce=="object"&&ce!==null&&ce.$$typeof===q&&yd(ce)===N.type)?(N=n(N,R.props),aa(N,R),N.return=T,N):(N=Ka(R.type,R.key,R.props,null,T.mode,Z),aa(N,R),N.return=T,N)}function O(T,N,R,Z){return N===null||N.tag!==4||N.stateNode.containerInfo!==R.containerInfo||N.stateNode.implementation!==R.implementation?(N=Ec(R,T.mode,Z),N.return=T,N):(N=n(N,R.children||[]),N.return=T,N)}function $(T,N,R,Z,ce){return N===null||N.tag!==7?(N=_s(R,T.mode,Z,ce),N.return=T,N):(N=n(N,R),N.return=T,N)}function W(T,N,R){if(typeof N=="string"&&N!==""||typeof N=="number"||typeof N=="bigint")return N=Mc(""+N,T.mode,R),N.return=T,N;if(typeof N=="object"&&N!==null){switch(N.$$typeof){case M:return R=Ka(N.type,N.key,N.props,null,T.mode,R),aa(R,N),R.return=T,R;case J:return N=Ec(N,T.mode,R),N.return=T,N;case q:var Z=N._init;return N=Z(N._payload),W(T,N,R)}if(Q(N)||K(N))return N=_s(N,T.mode,R,null),N.return=T,N;if(typeof N.then=="function")return W(T,mn(N),R);if(N.$$typeof===le)return W(T,Pa(T,N),R);xn(T,N)}return null}function U(T,N,R,Z){var ce=N!==null?N.key:null;if(typeof R=="string"&&R!==""||typeof R=="number"||typeof R=="bigint")return ce!==null?null:m(T,N,""+R,Z);if(typeof R=="object"&&R!==null){switch(R.$$typeof){case M:return R.key===ce?y(T,N,R,Z):null;case J:return R.key===ce?O(T,N,R,Z):null;case q:return ce=R._init,R=ce(R._payload),U(T,N,R,Z)}if(Q(R)||K(R))return ce!==null?null:$(T,N,R,Z,null);if(typeof R.then=="function")return U(T,N,mn(R),Z);if(R.$$typeof===le)return U(T,N,Pa(T,R),Z);xn(T,R)}return null}function G(T,N,R,Z,ce){if(typeof Z=="string"&&Z!==""||typeof Z=="number"||typeof Z=="bigint")return T=T.get(R)||null,m(N,T,""+Z,ce);if(typeof Z=="object"&&Z!==null){switch(Z.$$typeof){case M:return T=T.get(Z.key===null?R:Z.key)||null,y(N,T,Z,ce);case J:return T=T.get(Z.key===null?R:Z.key)||null,O(N,T,Z,ce);case q:var be=Z._init;return Z=be(Z._payload),G(T,N,R,Z,ce)}if(Q(Z)||K(Z))return T=T.get(R)||null,$(N,T,Z,ce,null);if(typeof Z.then=="function")return G(T,N,R,mn(Z),ce);if(Z.$$typeof===le)return G(T,N,R,Pa(N,Z),ce);xn(N,Z)}return null}function xe(T,N,R,Z){for(var ce=null,be=null,re=N,me=N=0,Ze=null;re!==null&&me<R.length;me++){re.index>me?(Ze=re,re=null):Ze=re.sibling;var Ne=U(T,re,R[me],Z);if(Ne===null){re===null&&(re=Ze);break}e&&re&&Ne.alternate===null&&t(T,re),N=c(Ne,N,me),be===null?ce=Ne:be.sibling=Ne,be=Ne,re=Ze}if(me===R.length)return l(T,re),Se&&Es(T,me),ce;if(re===null){for(;me<R.length;me++)re=W(T,R[me],Z),re!==null&&(N=c(re,N,me),be===null?ce=re:be.sibling=re,be=re);return Se&&Es(T,me),ce}for(re=a(re);me<R.length;me++)Ze=G(re,T,me,R[me],Z),Ze!==null&&(e&&Ze.alternate!==null&&re.delete(Ze.key===null?me:Ze.key),N=c(Ze,N,me),be===null?ce=Ze:be.sibling=Ze,be=Ze);return e&&re.forEach(function(Ns){return t(T,Ns)}),Se&&Es(T,me),ce}function oe(T,N,R,Z){if(R==null)throw Error(x(151));for(var ce=null,be=null,re=N,me=N=0,Ze=null,Ne=R.next();re!==null&&!Ne.done;me++,Ne=R.next()){re.index>me?(Ze=re,re=null):Ze=re.sibling;var Ns=U(T,re,Ne.value,Z);if(Ns===null){re===null&&(re=Ze);break}e&&re&&Ns.alternate===null&&t(T,re),N=c(Ns,N,me),be===null?ce=Ns:be.sibling=Ns,be=Ns,re=Ze}if(Ne.done)return l(T,re),Se&&Es(T,me),ce;if(re===null){for(;!Ne.done;me++,Ne=R.next())Ne=W(T,Ne.value,Z),Ne!==null&&(N=c(Ne,N,me),be===null?ce=Ne:be.sibling=Ne,be=Ne);return Se&&Es(T,me),ce}for(re=a(re);!Ne.done;me++,Ne=R.next())Ne=G(re,T,me,Ne.value,Z),Ne!==null&&(e&&Ne.alternate!==null&&re.delete(Ne.key===null?me:Ne.key),N=c(Ne,N,me),be===null?ce=Ne:be.sibling=Ne,be=Ne);return e&&re.forEach(function(eh){return t(T,eh)}),Se&&Es(T,me),ce}function Me(T,N,R,Z){if(typeof R=="object"&&R!==null&&R.type===z&&R.key===null&&(R=R.props.children),typeof R=="object"&&R!==null){switch(R.$$typeof){case M:e:{for(var ce=R.key;N!==null;){if(N.key===ce){if(ce=R.type,ce===z){if(N.tag===7){l(T,N.sibling),Z=n(N,R.props.children),Z.return=T,T=Z;break e}}else if(N.elementType===ce||typeof ce=="object"&&ce!==null&&ce.$$typeof===q&&yd(ce)===N.type){l(T,N.sibling),Z=n(N,R.props),aa(Z,R),Z.return=T,T=Z;break e}l(T,N);break}else t(T,N);N=N.sibling}R.type===z?(Z=_s(R.props.children,T.mode,Z,R.key),Z.return=T,T=Z):(Z=Ka(R.type,R.key,R.props,null,T.mode,Z),aa(Z,R),Z.return=T,T=Z)}return r(T);case J:e:{for(ce=R.key;N!==null;){if(N.key===ce)if(N.tag===4&&N.stateNode.containerInfo===R.containerInfo&&N.stateNode.implementation===R.implementation){l(T,N.sibling),Z=n(N,R.children||[]),Z.return=T,T=Z;break e}else{l(T,N);break}else t(T,N);N=N.sibling}Z=Ec(R,T.mode,Z),Z.return=T,T=Z}return r(T);case q:return ce=R._init,R=ce(R._payload),Me(T,N,R,Z)}if(Q(R))return xe(T,N,R,Z);if(K(R)){if(ce=K(R),typeof ce!="function")throw Error(x(150));return R=ce.call(R),oe(T,N,R,Z)}if(typeof R.then=="function")return Me(T,N,mn(R),Z);if(R.$$typeof===le)return Me(T,N,Pa(T,R),Z);xn(T,R)}return typeof R=="string"&&R!==""||typeof R=="number"||typeof R=="bigint"?(R=""+R,N!==null&&N.tag===6?(l(T,N.sibling),Z=n(N,R),Z.return=T,T=Z):(l(T,N),Z=Mc(R,T.mode,Z),Z.return=T,T=Z),r(T)):l(T,N)}return function(T,N,R,Z){try{la=0;var ce=Me(T,N,R,Z);return xl=null,ce}catch(re){if(re===Kl||re===en)throw re;var be=mt(29,re,null,T.mode);return be.lanes=Z,be.return=T,be}finally{}}}var fl=vd(!0),pd=vd(!1),wt=B(null),kt=null;function is(e){var t=e.alternate;se(Ye,Ye.current&1),se(wt,e),kt===null&&(t===null||ul.current!==null||t.memoizedState!==null)&&(kt=e)}function jd(e){if(e.tag===22){if(se(Ye,Ye.current),se(wt,e),kt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(kt=e)}}else rs()}function rs(){se(Ye,Ye.current),se(wt,wt.current)}function Qt(e){P(wt),kt===e&&(kt=null),P(Ye)}var Ye=B(0);function fn(e){for(var t=e;t!==null;){if(t.tag===13){var l=t.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||Pi(l)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function ui(e,t,l,a){t=e.memoizedState,l=l(a,t),l=l==null?t:g({},t,l),e.memoizedState=l,e.lanes===0&&(e.updateQueue.baseState=l)}var di={enqueueSetState:function(e,t,l){e=e._reactInternals;var a=gt(),n=as(a);n.payload=t,l!=null&&(n.callback=l),t=ns(e,n,a),t!==null&&(bt(t,e,a),Wl(t,e,a))},enqueueReplaceState:function(e,t,l){e=e._reactInternals;var a=gt(),n=as(a);n.tag=1,n.payload=t,l!=null&&(n.callback=l),t=ns(e,n,a),t!==null&&(bt(t,e,a),Wl(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var l=gt(),a=as(l);a.tag=2,t!=null&&(a.callback=t),t=ns(e,a,l),t!==null&&(bt(t,e,l),Wl(t,e,l))}};function Nd(e,t,l,a,n,c,r){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,c,r):t.prototype&&t.prototype.isPureReactComponent?!Gl(l,a)||!Gl(n,c):!0}function Sd(e,t,l,a){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(l,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(l,a),t.state!==e&&di.enqueueReplaceState(t,t.state,null)}function Us(e,t){var l=t;if("ref"in t){l={};for(var a in t)a!=="ref"&&(l[a]=t[a])}if(e=e.defaultProps){l===t&&(l=g({},l));for(var n in e)l[n]===void 0&&(l[n]=e[n])}return l}var hn=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function wd(e){hn(e)}function Cd(e){console.error(e)}function Td(e){hn(e)}function gn(e,t){try{var l=e.onUncaughtError;l(t.value,{componentStack:t.stack})}catch(a){setTimeout(function(){throw a})}}function Ad(e,t,l){try{var a=e.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function oi(e,t,l){return l=as(l),l.tag=3,l.payload={element:null},l.callback=function(){gn(e,t)},l}function _d(e){return e=as(e),e.tag=3,e}function Md(e,t,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var c=a.value;e.payload=function(){return n(c)},e.callback=function(){Ad(t,l,a)}}var r=l.stateNode;r!==null&&typeof r.componentDidCatch=="function"&&(e.callback=function(){Ad(t,l,a),typeof n!="function"&&(fs===null?fs=new Set([this]):fs.add(this));var m=a.stack;this.componentDidCatch(a.value,{componentStack:m!==null?m:""})})}function Ix(e,t,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(t=l.alternate,t!==null&&Xl(t,l,n,!0),l=wt.current,l!==null){switch(l.tag){case 13:return kt===null?zi():l.alternate===null&&ze===0&&(ze=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===Gc?l.flags|=16384:(t=l.updateQueue,t===null?l.updateQueue=new Set([a]):t.add(a),Ui(e,a,n)),!1;case 22:return l.flags|=65536,a===Gc?l.flags|=16384:(t=l.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=t):(l=t.retryQueue,l===null?t.retryQueue=new Set([a]):l.add(a)),Ui(e,a,n)),!1}throw Error(x(435,l.tag))}return Ui(e,a,n),zi(),!1}if(Se)return t=wt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=n,a!==kc&&(e=Error(x(422),{cause:a}),$l(pt(e,l)))):(a!==kc&&(t=Error(x(423),{cause:a}),$l(pt(t,l))),e=e.current.alternate,e.flags|=65536,n&=-n,e.lanes|=n,a=pt(a,l),n=oi(e.stateNode,a,n),Yc(e,n),ze!==4&&(ze=2)),!1;var c=Error(x(520),{cause:a});if(c=pt(c,l),oa===null?oa=[c]:oa.push(c),ze!==4&&(ze=2),t===null)return!0;a=pt(a,l),l=t;do{switch(l.tag){case 3:return l.flags|=65536,e=n&-n,l.lanes|=e,e=oi(l.stateNode,a,e),Yc(l,e),!1;case 1:if(t=l.type,c=l.stateNode,(l.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(fs===null||!fs.has(c))))return l.flags|=65536,n&=-n,l.lanes|=n,n=_d(n),Md(n,e,l,a),Yc(l,n),!1}l=l.return}while(l!==null);return!1}var Ed=Error(x(461)),Xe=!1;function Ke(e,t,l,a){t.child=e===null?pd(t,null,l,a):fl(t,e.child,l,a)}function Rd(e,t,l,a,n){l=l.render;var c=t.ref;if("ref"in a){var r={};for(var m in a)m!=="ref"&&(r[m]=a[m])}else r=a;return Ds(t),a=Kc(e,t,l,r,c,n),m=Jc(),e!==null&&!Xe?(Wc(e,t,n),Zt(e,t,n)):(Se&&m&&Rc(t),t.flags|=1,Ke(e,t,a,n),t.child)}function Od(e,t,l,a,n){if(e===null){var c=l.type;return typeof c=="function"&&!_c(c)&&c.defaultProps===void 0&&l.compare===null?(t.tag=15,t.type=c,kd(e,t,c,a,n)):(e=Ka(l.type,null,a,t,t.mode,n),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,!vi(e,n)){var r=c.memoizedProps;if(l=l.compare,l=l!==null?l:Gl,l(r,a)&&e.ref===t.ref)return Zt(e,t,n)}return t.flags|=1,e=Gt(c,a),e.ref=t.ref,e.return=t,t.child=e}function kd(e,t,l,a,n){if(e!==null){var c=e.memoizedProps;if(Gl(c,a)&&e.ref===t.ref)if(Xe=!1,t.pendingProps=a=c,vi(e,n))(e.flags&131072)!==0&&(Xe=!0);else return t.lanes=e.lanes,Zt(e,t,n)}return mi(e,t,l,a,n)}function Dd(e,t,l){var a=t.pendingProps,n=a.children,c=e!==null?e.memoizedState:null;if(a.mode==="hidden"){if((t.flags&128)!==0){if(a=c!==null?c.baseLanes|l:l,e!==null){for(n=t.child=e.child,c=0;n!==null;)c=c|n.lanes|n.childLanes,n=n.sibling;t.childLanes=c&~a}else t.childLanes=0,t.child=null;return zd(e,t,a,l)}if((l&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Ia(t,c!==null?c.cachePool:null),c!==null?ku(t,c):Xc(),jd(t);else return t.lanes=t.childLanes=536870912,zd(e,t,c!==null?c.baseLanes|l:l,l)}else c!==null?(Ia(t,c.cachePool),ku(t,c),rs(),t.memoizedState=null):(e!==null&&Ia(t,null),Xc(),rs());return Ke(e,t,n,l),t.child}function zd(e,t,l,a){var n=Hc();return n=n===null?null:{parent:Be._currentValue,pool:n},t.memoizedState={baseLanes:l,cachePool:n},e!==null&&Ia(t,null),Xc(),jd(t),e!==null&&Xl(e,t,a,!0),null}function bn(e,t){var l=t.ref;if(l===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(x(284));(e===null||e.ref!==l)&&(t.flags|=4194816)}}function mi(e,t,l,a,n){return Ds(t),l=Kc(e,t,l,a,void 0,n),a=Jc(),e!==null&&!Xe?(Wc(e,t,n),Zt(e,t,n)):(Se&&a&&Rc(t),t.flags|=1,Ke(e,t,l,n),t.child)}function Ld(e,t,l,a,n,c){return Ds(t),t.updateQueue=null,l=zu(t,a,l,n),Du(e),a=Jc(),e!==null&&!Xe?(Wc(e,t,c),Zt(e,t,c)):(Se&&a&&Rc(t),t.flags|=1,Ke(e,t,l,c),t.child)}function Ud(e,t,l,a,n){if(Ds(t),t.stateNode===null){var c=al,r=l.contextType;typeof r=="object"&&r!==null&&(c=Pe(r)),c=new l(a,c),t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=di,t.stateNode=c,c._reactInternals=t,c=t.stateNode,c.props=a,c.state=t.memoizedState,c.refs={},Vc(t),r=l.contextType,c.context=typeof r=="object"&&r!==null?Pe(r):al,c.state=t.memoizedState,r=l.getDerivedStateFromProps,typeof r=="function"&&(ui(t,l,r,a),c.state=t.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(r=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),r!==c.state&&di.enqueueReplaceState(c,c.state,null),Pl(t,a,c,n),Fl(),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308),a=!0}else if(e===null){c=t.stateNode;var m=t.memoizedProps,y=Us(l,m);c.props=y;var O=c.context,$=l.contextType;r=al,typeof $=="object"&&$!==null&&(r=Pe($));var W=l.getDerivedStateFromProps;$=typeof W=="function"||typeof c.getSnapshotBeforeUpdate=="function",m=t.pendingProps!==m,$||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(m||O!==r)&&Sd(t,c,a,r),ls=!1;var U=t.memoizedState;c.state=U,Pl(t,a,c,n),Fl(),O=t.memoizedState,m||U!==O||ls?(typeof W=="function"&&(ui(t,l,W,a),O=t.memoizedState),(y=ls||Nd(t,l,y,a,U,O,r))?($||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(t.flags|=4194308)):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=O),c.props=a,c.state=O,c.context=r,a=y):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),a=!1)}else{c=t.stateNode,Bc(e,t),r=t.memoizedProps,$=Us(l,r),c.props=$,W=t.pendingProps,U=c.context,O=l.contextType,y=al,typeof O=="object"&&O!==null&&(y=Pe(O)),m=l.getDerivedStateFromProps,(O=typeof m=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(r!==W||U!==y)&&Sd(t,c,a,y),ls=!1,U=t.memoizedState,c.state=U,Pl(t,a,c,n),Fl();var G=t.memoizedState;r!==W||U!==G||ls||e!==null&&e.dependencies!==null&&Fa(e.dependencies)?(typeof m=="function"&&(ui(t,l,m,a),G=t.memoizedState),($=ls||Nd(t,l,$,a,U,G,y)||e!==null&&e.dependencies!==null&&Fa(e.dependencies))?(O||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(a,G,y),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(a,G,y)),typeof c.componentDidUpdate=="function"&&(t.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof c.componentDidUpdate!="function"||r===e.memoizedProps&&U===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||r===e.memoizedProps&&U===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=G),c.props=a,c.state=G,c.context=y,a=$):(typeof c.componentDidUpdate!="function"||r===e.memoizedProps&&U===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||r===e.memoizedProps&&U===e.memoizedState||(t.flags|=1024),a=!1)}return c=a,bn(e,t),a=(t.flags&128)!==0,c||a?(c=t.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:c.render(),t.flags|=1,e!==null&&a?(t.child=fl(t,e.child,null,n),t.child=fl(t,null,l,n)):Ke(e,t,l,n),t.memoizedState=c.state,e=t.child):e=Zt(e,t,n),e}function qd(e,t,l,a){return Yl(),t.flags|=256,Ke(e,t,l,a),t.child}var xi={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function fi(e){return{baseLanes:e,cachePool:Cu()}}function hi(e,t,l){return e=e!==null?e.childLanes&~l:0,t&&(e|=Ct),e}function Hd(e,t,l){var a=t.pendingProps,n=!1,c=(t.flags&128)!==0,r;if((r=c)||(r=e!==null&&e.memoizedState===null?!1:(Ye.current&2)!==0),r&&(n=!0,t.flags&=-129),r=(t.flags&32)!==0,t.flags&=-33,e===null){if(Se){if(n?is(t):rs(),Se){var m=De,y;if(y=m){e:{for(y=m,m=Ot;y.nodeType!==8;){if(!m){m=null;break e}if(y=Mt(y.nextSibling),y===null){m=null;break e}}m=y}m!==null?(t.memoizedState={dehydrated:m,treeContext:Ms!==null?{id:Vt,overflow:Bt}:null,retryLane:536870912,hydrationErrors:null},y=mt(18,null,null,0),y.stateNode=m,y.return=t,t.child=y,st=t,De=null,y=!0):y=!1}y||Os(t)}if(m=t.memoizedState,m!==null&&(m=m.dehydrated,m!==null))return Pi(m)?t.lanes=32:t.lanes=536870912,null;Qt(t)}return m=a.children,a=a.fallback,n?(rs(),n=t.mode,m=yn({mode:"hidden",children:m},n),a=_s(a,n,l,null),m.return=t,a.return=t,m.sibling=a,t.child=m,n=t.child,n.memoizedState=fi(l),n.childLanes=hi(e,r,l),t.memoizedState=xi,a):(is(t),gi(t,m))}if(y=e.memoizedState,y!==null&&(m=y.dehydrated,m!==null)){if(c)t.flags&256?(is(t),t.flags&=-257,t=bi(e,t,l)):t.memoizedState!==null?(rs(),t.child=e.child,t.flags|=128,t=null):(rs(),n=a.fallback,m=t.mode,a=yn({mode:"visible",children:a.children},m),n=_s(n,m,l,null),n.flags|=2,a.return=t,n.return=t,a.sibling=n,t.child=a,fl(t,e.child,null,l),a=t.child,a.memoizedState=fi(l),a.childLanes=hi(e,r,l),t.memoizedState=xi,t=n);else if(is(t),Pi(m)){if(r=m.nextSibling&&m.nextSibling.dataset,r)var O=r.dgst;r=O,a=Error(x(419)),a.stack="",a.digest=r,$l({value:a,source:null,stack:null}),t=bi(e,t,l)}else if(Xe||Xl(e,t,l,!1),r=(l&e.childLanes)!==0,Xe||r){if(r=Re,r!==null&&(a=l&-l,a=(a&42)!==0?1:In(a),a=(a&(r.suspendedLanes|l))!==0?0:a,a!==0&&a!==y.retryLane))throw y.retryLane=a,ll(e,a),bt(r,e,a),Ed;m.data==="$?"||zi(),t=bi(e,t,l)}else m.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=y.treeContext,De=Mt(m.nextSibling),st=t,Se=!0,Rs=null,Ot=!1,e!==null&&(Nt[St++]=Vt,Nt[St++]=Bt,Nt[St++]=Ms,Vt=e.id,Bt=e.overflow,Ms=t),t=gi(t,a.children),t.flags|=4096);return t}return n?(rs(),n=a.fallback,m=t.mode,y=e.child,O=y.sibling,a=Gt(y,{mode:"hidden",children:a.children}),a.subtreeFlags=y.subtreeFlags&65011712,O!==null?n=Gt(O,n):(n=_s(n,m,l,null),n.flags|=2),n.return=t,a.return=t,a.sibling=n,t.child=a,a=n,n=t.child,m=e.child.memoizedState,m===null?m=fi(l):(y=m.cachePool,y!==null?(O=Be._currentValue,y=y.parent!==O?{parent:O,pool:O}:y):y=Cu(),m={baseLanes:m.baseLanes|l,cachePool:y}),n.memoizedState=m,n.childLanes=hi(e,r,l),t.memoizedState=xi,a):(is(t),l=e.child,e=l.sibling,l=Gt(l,{mode:"visible",children:a.children}),l.return=t,l.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=l,t.memoizedState=null,l)}function gi(e,t){return t=yn({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function yn(e,t){return e=mt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function bi(e,t,l){return fl(t,e.child,null,l),e=gi(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Gd(e,t,l){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t),zc(e.return,t,l)}function yi(e,t,l,a,n){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=a,c.tail=l,c.tailMode=n)}function Vd(e,t,l){var a=t.pendingProps,n=a.revealOrder,c=a.tail;if(Ke(e,t,a.children,l),a=Ye.current,(a&2)!==0)a=a&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Gd(e,l,t);else if(e.tag===19)Gd(e,l,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}switch(se(Ye,a),n){case"forwards":for(l=t.child,n=null;l!==null;)e=l.alternate,e!==null&&fn(e)===null&&(n=l),l=l.sibling;l=n,l===null?(n=t.child,t.child=null):(n=l.sibling,l.sibling=null),yi(t,!1,n,l,c);break;case"backwards":for(l=null,n=t.child,t.child=null;n!==null;){if(e=n.alternate,e!==null&&fn(e)===null){t.child=n;break}e=n.sibling,n.sibling=l,l=n,n=e}yi(t,!0,l,null,c);break;case"together":yi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Zt(e,t,l){if(e!==null&&(t.dependencies=e.dependencies),xs|=t.lanes,(l&t.childLanes)===0)if(e!==null){if(Xl(e,t,l,!1),(l&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(x(153));if(t.child!==null){for(e=t.child,l=Gt(e,e.pendingProps),t.child=l,l.return=t;e.sibling!==null;)e=e.sibling,l=l.sibling=Gt(e,e.pendingProps),l.return=t;l.sibling=null}return t.child}function vi(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Fa(e)))}function ef(e,t,l){switch(t.tag){case 3:we(t,t.stateNode.containerInfo),ss(t,Be,e.memoizedState.cache),Yl();break;case 27:case 5:Ut(t);break;case 4:we(t,t.stateNode.containerInfo);break;case 10:ss(t,t.type,t.memoizedProps.value);break;case 13:var a=t.memoizedState;if(a!==null)return a.dehydrated!==null?(is(t),t.flags|=128,null):(l&t.child.childLanes)!==0?Hd(e,t,l):(is(t),e=Zt(e,t,l),e!==null?e.sibling:null);is(t);break;case 19:var n=(e.flags&128)!==0;if(a=(l&t.childLanes)!==0,a||(Xl(e,t,l,!1),a=(l&t.childLanes)!==0),n){if(a)return Vd(e,t,l);t.flags|=128}if(n=t.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),se(Ye,Ye.current),a)break;return null;case 22:case 23:return t.lanes=0,Dd(e,t,l);case 24:ss(t,Be,e.memoizedState.cache)}return Zt(e,t,l)}function Bd(e,t,l){if(e!==null)if(e.memoizedProps!==t.pendingProps)Xe=!0;else{if(!vi(e,l)&&(t.flags&128)===0)return Xe=!1,ef(e,t,l);Xe=(e.flags&131072)!==0}else Xe=!1,Se&&(t.flags&1048576)!==0&&yu(t,Wa,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,n=a._init;if(a=n(a._payload),t.type=a,typeof a=="function")_c(a)?(e=Us(a,e),t.tag=1,t=Ud(null,t,a,e,l)):(t.tag=0,t=mi(null,t,a,e,l));else{if(a!=null){if(n=a.$$typeof,n===ae){t.tag=11,t=Rd(null,t,a,e,l);break e}else if(n===u){t.tag=14,t=Od(null,t,a,e,l);break e}}throw t=w(a)||a,Error(x(306,t,""))}}return t;case 0:return mi(e,t,t.type,t.pendingProps,l);case 1:return a=t.type,n=Us(a,t.pendingProps),Ud(e,t,a,n,l);case 3:e:{if(we(t,t.stateNode.containerInfo),e===null)throw Error(x(387));a=t.pendingProps;var c=t.memoizedState;n=c.element,Bc(e,t),Pl(t,a,null,l);var r=t.memoizedState;if(a=r.cache,ss(t,Be,a),a!==c.cache&&Lc(t,[Be],l,!0),Fl(),a=r.element,c.isDehydrated)if(c={element:a,isDehydrated:!1,cache:r.cache},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){t=qd(e,t,a,l);break e}else if(a!==n){n=pt(Error(x(424)),t),$l(n),t=qd(e,t,a,l);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(De=Mt(e.firstChild),st=t,Se=!0,Rs=null,Ot=!0,l=pd(t,null,a,l),t.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(Yl(),a===n){t=Zt(e,t,l);break e}Ke(e,t,a,l)}t=t.child}return t;case 26:return bn(e,t),e===null?(l=Zo(t.type,null,t.pendingProps,null))?t.memoizedState=l:Se||(l=t.type,e=t.pendingProps,a=On(de.current).createElement(l),a[Fe]=t,a[lt]=e,We(a,l,e),$e(a),t.stateNode=a):t.memoizedState=Zo(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Ut(t),e===null&&Se&&(a=t.stateNode=$o(t.type,t.pendingProps,de.current),st=t,Ot=!0,n=De,bs(t.type)?(Ii=n,De=Mt(a.firstChild)):De=n),Ke(e,t,t.pendingProps.children,l),bn(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Se&&((n=a=De)&&(a=Mf(a,t.type,t.pendingProps,Ot),a!==null?(t.stateNode=a,st=t,De=Mt(a.firstChild),Ot=!1,n=!0):n=!1),n||Os(t)),Ut(t),n=t.type,c=t.pendingProps,r=e!==null?e.memoizedProps:null,a=c.children,Ji(n,c)?a=null:r!==null&&Ji(n,r)&&(t.flags|=32),t.memoizedState!==null&&(n=Kc(e,t,Qx,null,null,l),pa._currentValue=n),bn(e,t),Ke(e,t,a,l),t.child;case 6:return e===null&&Se&&((e=l=De)&&(l=Ef(l,t.pendingProps,Ot),l!==null?(t.stateNode=l,st=t,De=null,e=!0):e=!1),e||Os(t)),null;case 13:return Hd(e,t,l);case 4:return we(t,t.stateNode.containerInfo),a=t.pendingProps,e===null?t.child=fl(t,null,a,l):Ke(e,t,a,l),t.child;case 11:return Rd(e,t,t.type,t.pendingProps,l);case 7:return Ke(e,t,t.pendingProps,l),t.child;case 8:return Ke(e,t,t.pendingProps.children,l),t.child;case 12:return Ke(e,t,t.pendingProps.children,l),t.child;case 10:return a=t.pendingProps,ss(t,t.type,a.value),Ke(e,t,a.children,l),t.child;case 9:return n=t.type._context,a=t.pendingProps.children,Ds(t),n=Pe(n),a=a(n),t.flags|=1,Ke(e,t,a,l),t.child;case 14:return Od(e,t,t.type,t.pendingProps,l);case 15:return kd(e,t,t.type,t.pendingProps,l);case 19:return Vd(e,t,l);case 31:return a=t.pendingProps,l=t.mode,a={mode:a.mode,children:a.children},e===null?(l=yn(a,l),l.ref=t.ref,t.child=l,l.return=t,t=l):(l=Gt(e.child,a),l.ref=t.ref,t.child=l,l.return=t,t=l),t;case 22:return Dd(e,t,l);case 24:return Ds(t),a=Pe(Be),e===null?(n=Hc(),n===null&&(n=Re,c=Uc(),n.pooledCache=c,c.refCount++,c!==null&&(n.pooledCacheLanes|=l),n=c),t.memoizedState={parent:a,cache:n},Vc(t),ss(t,Be,n)):((e.lanes&l)!==0&&(Bc(e,t),Pl(t,null,null,l),Fl()),n=e.memoizedState,c=t.memoizedState,n.parent!==a?(n={parent:a,cache:a},t.memoizedState=n,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=n),ss(t,Be,a)):(a=c.cache,ss(t,Be,a),a!==n.cache&&Lc(t,[Be],l,!0))),Ke(e,t,t.pendingProps.children,l),t.child;case 29:throw t.pendingProps}throw Error(x(156,t.tag))}function Kt(e){e.flags|=4}function Yd(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Po(t)){if(t=wt.current,t!==null&&((je&4194048)===je?kt!==null:(je&62914560)!==je&&(je&536870912)===0||t!==kt))throw Jl=Gc,Tu;e.flags|=8192}}function vn(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?jr():536870912,e.lanes|=t,yl|=t)}function na(e,t){if(!Se)switch(e.tailMode){case"hidden":t=e.tail;for(var l=null;t!==null;)t.alternate!==null&&(l=t),t=t.sibling;l===null?e.tail=null:l.sibling=null;break;case"collapsed":l=e.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function ke(e){var t=e.alternate!==null&&e.alternate.child===e.child,l=0,a=0;if(t)for(var n=e.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=a,e.childLanes=l,t}function tf(e,t,l){var a=t.pendingProps;switch(Oc(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ke(t),null;case 1:return ke(t),null;case 3:return l=t.stateNode,a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),$t(Be),tt(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(e===null||e.child===null)&&(Bl(t)?Kt(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,ju())),ke(t),null;case 26:return l=t.memoizedState,e===null?(Kt(t),l!==null?(ke(t),Yd(t,l)):(ke(t),t.flags&=-16777217)):l?l!==e.memoizedState?(Kt(t),ke(t),Yd(t,l)):(ke(t),t.flags&=-16777217):(e.memoizedProps!==a&&Kt(t),ke(t),t.flags&=-16777217),null;case 27:Bs(t),l=de.current;var n=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==a&&Kt(t);else{if(!a){if(t.stateNode===null)throw Error(x(166));return ke(t),null}e=H.current,Bl(t)?vu(t):(e=$o(n,a,l),t.stateNode=e,Kt(t))}return ke(t),null;case 5:if(Bs(t),l=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==a&&Kt(t);else{if(!a){if(t.stateNode===null)throw Error(x(166));return ke(t),null}if(e=H.current,Bl(t))vu(t);else{switch(n=On(de.current),e){case 1:e=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:e=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":e=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":e=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":e=n.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}e[Fe]=t,e[lt]=a;e:for(n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break e;for(;n.sibling===null;){if(n.return===null||n.return===t)break e;n=n.return}n.sibling.return=n.return,n=n.sibling}t.stateNode=e;e:switch(We(e,l,a),l){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Kt(t)}}return ke(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==a&&Kt(t);else{if(typeof a!="string"&&t.stateNode===null)throw Error(x(166));if(e=de.current,Bl(t)){if(e=t.stateNode,l=t.memoizedProps,a=null,n=st,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}e[Fe]=t,e=!!(e.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||Uo(e.nodeValue,l)),e||Os(t)}else e=On(e).createTextNode(a),e[Fe]=t,t.stateNode=e}return ke(t),null;case 13:if(a=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(n=Bl(t),a!==null&&a.dehydrated!==null){if(e===null){if(!n)throw Error(x(318));if(n=t.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(x(317));n[Fe]=t}else Yl(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;ke(t),n=!1}else n=ju(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=n),n=!0;if(!n)return t.flags&256?(Qt(t),t):(Qt(t),null)}if(Qt(t),(t.flags&128)!==0)return t.lanes=l,t;if(l=a!==null,e=e!==null&&e.memoizedState!==null,l){a=t.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var c=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(c=a.memoizedState.cachePool.pool),c!==n&&(a.flags|=2048)}return l!==e&&l&&(t.child.flags|=8192),vn(t,t.updateQueue),ke(t),null;case 4:return tt(),e===null&&$i(t.stateNode.containerInfo),ke(t),null;case 10:return $t(t.type),ke(t),null;case 19:if(P(Ye),n=t.memoizedState,n===null)return ke(t),null;if(a=(t.flags&128)!==0,c=n.rendering,c===null)if(a)na(n,!1);else{if(ze!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(c=fn(e),c!==null){for(t.flags|=128,na(n,!1),e=c.updateQueue,t.updateQueue=e,vn(t,e),t.subtreeFlags=0,e=l,l=t.child;l!==null;)bu(l,e),l=l.sibling;return se(Ye,Ye.current&1|2),t.child}e=e.sibling}n.tail!==null&&Rt()>Nn&&(t.flags|=128,a=!0,na(n,!1),t.lanes=4194304)}else{if(!a)if(e=fn(c),e!==null){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,vn(t,e),na(n,!0),n.tail===null&&n.tailMode==="hidden"&&!c.alternate&&!Se)return ke(t),null}else 2*Rt()-n.renderingStartTime>Nn&&l!==536870912&&(t.flags|=128,a=!0,na(n,!1),t.lanes=4194304);n.isBackwards?(c.sibling=t.child,t.child=c):(e=n.last,e!==null?e.sibling=c:t.child=c,n.last=c)}return n.tail!==null?(t=n.tail,n.rendering=t,n.tail=t.sibling,n.renderingStartTime=Rt(),t.sibling=null,e=Ye.current,se(Ye,a?e&1|2:e&1),t):(ke(t),null);case 22:case 23:return Qt(t),Qc(),a=t.memoizedState!==null,e!==null?e.memoizedState!==null!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?(l&536870912)!==0&&(t.flags&128)===0&&(ke(t),t.subtreeFlags&6&&(t.flags|=8192)):ke(t),l=t.updateQueue,l!==null&&vn(t,l.retryQueue),l=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),a=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),a!==l&&(t.flags|=2048),e!==null&&P(zs),null;case 24:return l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),$t(Be),ke(t),null;case 25:return null;case 30:return null}throw Error(x(156,t.tag))}function sf(e,t){switch(Oc(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return $t(Be),tt(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return Bs(t),null;case 13:if(Qt(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(x(340));Yl()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return P(Ye),null;case 4:return tt(),null;case 10:return $t(t.type),null;case 22:case 23:return Qt(t),Qc(),e!==null&&P(zs),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return $t(Be),null;case 25:return null;default:return null}}function $d(e,t){switch(Oc(t),t.tag){case 3:$t(Be),tt();break;case 26:case 27:case 5:Bs(t);break;case 4:tt();break;case 13:Qt(t);break;case 19:P(Ye);break;case 10:$t(t.type);break;case 22:case 23:Qt(t),Qc(),e!==null&&P(zs);break;case 24:$t(Be)}}function ca(e,t){try{var l=t.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&e)===e){a=void 0;var c=l.create,r=l.inst;a=c(),r.destroy=a}l=l.next}while(l!==n)}}catch(m){Ee(t,t.return,m)}}function us(e,t,l){try{var a=t.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var c=n.next;a=c;do{if((a.tag&e)===e){var r=a.inst,m=r.destroy;if(m!==void 0){r.destroy=void 0,n=t;var y=l,O=m;try{O()}catch($){Ee(n,y,$)}}}a=a.next}while(a!==c)}}catch($){Ee(t,t.return,$)}}function Xd(e){var t=e.updateQueue;if(t!==null){var l=e.stateNode;try{Ou(t,l)}catch(a){Ee(e,e.return,a)}}}function Qd(e,t,l){l.props=Us(e.type,e.memoizedProps),l.state=e.memoizedState;try{l.componentWillUnmount()}catch(a){Ee(e,t,a)}}function ia(e,t){try{var l=e.ref;if(l!==null){switch(e.tag){case 26:case 27:case 5:var a=e.stateNode;break;case 30:a=e.stateNode;break;default:a=e.stateNode}typeof l=="function"?e.refCleanup=l(a):l.current=a}}catch(n){Ee(e,t,n)}}function Dt(e,t){var l=e.ref,a=e.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){Ee(e,t,n)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){Ee(e,t,n)}else l.current=null}function Zd(e){var t=e.type,l=e.memoizedProps,a=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break e;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){Ee(e,e.return,n)}}function pi(e,t,l){try{var a=e.stateNode;wf(a,e.type,l,t),a[lt]=t}catch(n){Ee(e,e.return,n)}}function Kd(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&bs(e.type)||e.tag===4}function ji(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Kd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&bs(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ni(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(e,t):(t=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,t.appendChild(e),l=l._reactRootContainer,l!=null||t.onclick!==null||(t.onclick=Rn));else if(a!==4&&(a===27&&bs(e.type)&&(l=e.stateNode,t=null),e=e.child,e!==null))for(Ni(e,t,l),e=e.sibling;e!==null;)Ni(e,t,l),e=e.sibling}function pn(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?l.insertBefore(e,t):l.appendChild(e);else if(a!==4&&(a===27&&bs(e.type)&&(l=e.stateNode),e=e.child,e!==null))for(pn(e,t,l),e=e.sibling;e!==null;)pn(e,t,l),e=e.sibling}function Jd(e){var t=e.stateNode,l=e.memoizedProps;try{for(var a=e.type,n=t.attributes;n.length;)t.removeAttributeNode(n[0]);We(t,a,l),t[Fe]=e,t[lt]=l}catch(c){Ee(e,e.return,c)}}var Jt=!1,Ue=!1,Si=!1,Wd=typeof WeakSet=="function"?WeakSet:Set,Qe=null;function lf(e,t){if(e=e.containerInfo,Zi=qn,e=iu(e),jc(e)){if("selectionStart"in e)var l={start:e.selectionStart,end:e.selectionEnd};else e:{l=(l=e.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,c=a.focusNode;a=a.focusOffset;try{l.nodeType,c.nodeType}catch{l=null;break e}var r=0,m=-1,y=-1,O=0,$=0,W=e,U=null;t:for(;;){for(var G;W!==l||n!==0&&W.nodeType!==3||(m=r+n),W!==c||a!==0&&W.nodeType!==3||(y=r+a),W.nodeType===3&&(r+=W.nodeValue.length),(G=W.firstChild)!==null;)U=W,W=G;for(;;){if(W===e)break t;if(U===l&&++O===n&&(m=r),U===c&&++$===a&&(y=r),(G=W.nextSibling)!==null)break;W=U,U=W.parentNode}W=G}l=m===-1||y===-1?null:{start:m,end:y}}else l=null}l=l||{start:0,end:0}}else l=null;for(Ki={focusedElem:e,selectionRange:l},qn=!1,Qe=t;Qe!==null;)if(t=Qe,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Qe=e;else for(;Qe!==null;){switch(t=Qe,c=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&c!==null){e=void 0,l=t,n=c.memoizedProps,c=c.memoizedState,a=l.stateNode;try{var xe=Us(l.type,n,l.elementType===l.type);e=a.getSnapshotBeforeUpdate(xe,c),a.__reactInternalSnapshotBeforeUpdate=e}catch(oe){Ee(l,l.return,oe)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,l=e.nodeType,l===9)Fi(e);else if(l===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Fi(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(x(163))}if(e=t.sibling,e!==null){e.return=t.return,Qe=e;break}Qe=t.return}}function Fd(e,t,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:ds(e,l),a&4&&ca(5,l);break;case 1:if(ds(e,l),a&4)if(e=l.stateNode,t===null)try{e.componentDidMount()}catch(r){Ee(l,l.return,r)}else{var n=Us(l.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(n,t,e.__reactInternalSnapshotBeforeUpdate)}catch(r){Ee(l,l.return,r)}}a&64&&Xd(l),a&512&&ia(l,l.return);break;case 3:if(ds(e,l),a&64&&(e=l.updateQueue,e!==null)){if(t=null,l.child!==null)switch(l.child.tag){case 27:case 5:t=l.child.stateNode;break;case 1:t=l.child.stateNode}try{Ou(e,t)}catch(r){Ee(l,l.return,r)}}break;case 27:t===null&&a&4&&Jd(l);case 26:case 5:ds(e,l),t===null&&a&4&&Zd(l),a&512&&ia(l,l.return);break;case 12:ds(e,l);break;case 13:ds(e,l),a&4&&eo(e,l),a&64&&(e=l.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(l=xf.bind(null,l),Rf(e,l))));break;case 22:if(a=l.memoizedState!==null||Jt,!a){t=t!==null&&t.memoizedState!==null||Ue,n=Jt;var c=Ue;Jt=a,(Ue=t)&&!c?os(e,l,(l.subtreeFlags&8772)!==0):ds(e,l),Jt=n,Ue=c}break;case 30:break;default:ds(e,l)}}function Pd(e){var t=e.alternate;t!==null&&(e.alternate=null,Pd(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&sc(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Oe=null,ct=!1;function Wt(e,t,l){for(l=l.child;l!==null;)Id(e,t,l),l=l.sibling}function Id(e,t,l){if(ut&&typeof ut.onCommitFiberUnmount=="function")try{ut.onCommitFiberUnmount(_l,l)}catch{}switch(l.tag){case 26:Ue||Dt(l,t),Wt(e,t,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Ue||Dt(l,t);var a=Oe,n=ct;bs(l.type)&&(Oe=l.stateNode,ct=!1),Wt(e,t,l),ga(l.stateNode),Oe=a,ct=n;break;case 5:Ue||Dt(l,t);case 6:if(a=Oe,n=ct,Oe=null,Wt(e,t,l),Oe=a,ct=n,Oe!==null)if(ct)try{(Oe.nodeType===9?Oe.body:Oe.nodeName==="HTML"?Oe.ownerDocument.body:Oe).removeChild(l.stateNode)}catch(c){Ee(l,t,c)}else try{Oe.removeChild(l.stateNode)}catch(c){Ee(l,t,c)}break;case 18:Oe!==null&&(ct?(e=Oe,Bo(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,l.stateNode),wa(e)):Bo(Oe,l.stateNode));break;case 4:a=Oe,n=ct,Oe=l.stateNode.containerInfo,ct=!0,Wt(e,t,l),Oe=a,ct=n;break;case 0:case 11:case 14:case 15:Ue||us(2,l,t),Ue||us(4,l,t),Wt(e,t,l);break;case 1:Ue||(Dt(l,t),a=l.stateNode,typeof a.componentWillUnmount=="function"&&Qd(l,t,a)),Wt(e,t,l);break;case 21:Wt(e,t,l);break;case 22:Ue=(a=Ue)||l.memoizedState!==null,Wt(e,t,l),Ue=a;break;default:Wt(e,t,l)}}function eo(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{wa(e)}catch(l){Ee(t,t.return,l)}}function af(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Wd),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Wd),t;default:throw Error(x(435,e.tag))}}function wi(e,t){var l=af(e);t.forEach(function(a){var n=ff.bind(null,e,a);l.has(a)||(l.add(a),a.then(n,n))})}function xt(e,t){var l=t.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],c=e,r=t,m=r;e:for(;m!==null;){switch(m.tag){case 27:if(bs(m.type)){Oe=m.stateNode,ct=!1;break e}break;case 5:Oe=m.stateNode,ct=!1;break e;case 3:case 4:Oe=m.stateNode.containerInfo,ct=!0;break e}m=m.return}if(Oe===null)throw Error(x(160));Id(c,r,n),Oe=null,ct=!1,c=n.alternate,c!==null&&(c.return=null),n.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)to(t,e),t=t.sibling}var _t=null;function to(e,t){var l=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:xt(t,e),ft(e),a&4&&(us(3,e,e.return),ca(3,e),us(5,e,e.return));break;case 1:xt(t,e),ft(e),a&512&&(Ue||l===null||Dt(l,l.return)),a&64&&Jt&&(e=e.updateQueue,e!==null&&(a=e.callbacks,a!==null&&(l=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=_t;if(xt(t,e),ft(e),a&512&&(Ue||l===null||Dt(l,l.return)),a&4){var c=l!==null?l.memoizedState:null;if(a=e.memoizedState,l===null)if(a===null)if(e.stateNode===null){e:{a=e.type,l=e.memoizedProps,n=n.ownerDocument||n;t:switch(a){case"title":c=n.getElementsByTagName("title")[0],(!c||c[Rl]||c[Fe]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=n.createElement(a),n.head.insertBefore(c,n.querySelector("head > title"))),We(c,a,l),c[Fe]=e,$e(c),a=c;break e;case"link":var r=Wo("link","href",n).get(a+(l.href||""));if(r){for(var m=0;m<r.length;m++)if(c=r[m],c.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&c.getAttribute("rel")===(l.rel==null?null:l.rel)&&c.getAttribute("title")===(l.title==null?null:l.title)&&c.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){r.splice(m,1);break t}}c=n.createElement(a),We(c,a,l),n.head.appendChild(c);break;case"meta":if(r=Wo("meta","content",n).get(a+(l.content||""))){for(m=0;m<r.length;m++)if(c=r[m],c.getAttribute("content")===(l.content==null?null:""+l.content)&&c.getAttribute("name")===(l.name==null?null:l.name)&&c.getAttribute("property")===(l.property==null?null:l.property)&&c.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&c.getAttribute("charset")===(l.charSet==null?null:l.charSet)){r.splice(m,1);break t}}c=n.createElement(a),We(c,a,l),n.head.appendChild(c);break;default:throw Error(x(468,a))}c[Fe]=e,$e(c),a=c}e.stateNode=a}else Fo(n,e.type,e.stateNode);else e.stateNode=Jo(n,a,e.memoizedProps);else c!==a?(c===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):c.count--,a===null?Fo(n,e.type,e.stateNode):Jo(n,a,e.memoizedProps)):a===null&&e.stateNode!==null&&pi(e,e.memoizedProps,l.memoizedProps)}break;case 27:xt(t,e),ft(e),a&512&&(Ue||l===null||Dt(l,l.return)),l!==null&&a&4&&pi(e,e.memoizedProps,l.memoizedProps);break;case 5:if(xt(t,e),ft(e),a&512&&(Ue||l===null||Dt(l,l.return)),e.flags&32){n=e.stateNode;try{Ws(n,"")}catch(G){Ee(e,e.return,G)}}a&4&&e.stateNode!=null&&(n=e.memoizedProps,pi(e,n,l!==null?l.memoizedProps:n)),a&1024&&(Si=!0);break;case 6:if(xt(t,e),ft(e),a&4){if(e.stateNode===null)throw Error(x(162));a=e.memoizedProps,l=e.stateNode;try{l.nodeValue=a}catch(G){Ee(e,e.return,G)}}break;case 3:if(zn=null,n=_t,_t=kn(t.containerInfo),xt(t,e),_t=n,ft(e),a&4&&l!==null&&l.memoizedState.isDehydrated)try{wa(t.containerInfo)}catch(G){Ee(e,e.return,G)}Si&&(Si=!1,so(e));break;case 4:a=_t,_t=kn(e.stateNode.containerInfo),xt(t,e),ft(e),_t=a;break;case 12:xt(t,e),ft(e);break;case 13:xt(t,e),ft(e),e.child.flags&8192&&e.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Ei=Rt()),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,wi(e,a)));break;case 22:n=e.memoizedState!==null;var y=l!==null&&l.memoizedState!==null,O=Jt,$=Ue;if(Jt=O||n,Ue=$||y,xt(t,e),Ue=$,Jt=O,ft(e),a&8192)e:for(t=e.stateNode,t._visibility=n?t._visibility&-2:t._visibility|1,n&&(l===null||y||Jt||Ue||qs(e)),l=null,t=e;;){if(t.tag===5||t.tag===26){if(l===null){y=l=t;try{if(c=y.stateNode,n)r=c.style,typeof r.setProperty=="function"?r.setProperty("display","none","important"):r.display="none";else{m=y.stateNode;var W=y.memoizedProps.style,U=W!=null&&W.hasOwnProperty("display")?W.display:null;m.style.display=U==null||typeof U=="boolean"?"":(""+U).trim()}}catch(G){Ee(y,y.return,G)}}}else if(t.tag===6){if(l===null){y=t;try{y.stateNode.nodeValue=n?"":y.memoizedProps}catch(G){Ee(y,y.return,G)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;l===t&&(l=null),t=t.return}l===t&&(l=null),t.sibling.return=t.return,t=t.sibling}a&4&&(a=e.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,wi(e,l))));break;case 19:xt(t,e),ft(e),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,wi(e,a)));break;case 30:break;case 21:break;default:xt(t,e),ft(e)}}function ft(e){var t=e.flags;if(t&2){try{for(var l,a=e.return;a!==null;){if(Kd(a)){l=a;break}a=a.return}if(l==null)throw Error(x(160));switch(l.tag){case 27:var n=l.stateNode,c=ji(e);pn(e,c,n);break;case 5:var r=l.stateNode;l.flags&32&&(Ws(r,""),l.flags&=-33);var m=ji(e);pn(e,m,r);break;case 3:case 4:var y=l.stateNode.containerInfo,O=ji(e);Ni(e,O,y);break;default:throw Error(x(161))}}catch($){Ee(e,e.return,$)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function so(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;so(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function ds(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Fd(e,t.alternate,t),t=t.sibling}function qs(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:us(4,t,t.return),qs(t);break;case 1:Dt(t,t.return);var l=t.stateNode;typeof l.componentWillUnmount=="function"&&Qd(t,t.return,l),qs(t);break;case 27:ga(t.stateNode);case 26:case 5:Dt(t,t.return),qs(t);break;case 22:t.memoizedState===null&&qs(t);break;case 30:qs(t);break;default:qs(t)}e=e.sibling}}function os(e,t,l){for(l=l&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var a=t.alternate,n=e,c=t,r=c.flags;switch(c.tag){case 0:case 11:case 15:os(n,c,l),ca(4,c);break;case 1:if(os(n,c,l),a=c,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(O){Ee(a,a.return,O)}if(a=c,n=a.updateQueue,n!==null){var m=a.stateNode;try{var y=n.shared.hiddenCallbacks;if(y!==null)for(n.shared.hiddenCallbacks=null,n=0;n<y.length;n++)Ru(y[n],m)}catch(O){Ee(a,a.return,O)}}l&&r&64&&Xd(c),ia(c,c.return);break;case 27:Jd(c);case 26:case 5:os(n,c,l),l&&a===null&&r&4&&Zd(c),ia(c,c.return);break;case 12:os(n,c,l);break;case 13:os(n,c,l),l&&r&4&&eo(n,c);break;case 22:c.memoizedState===null&&os(n,c,l),ia(c,c.return);break;case 30:break;default:os(n,c,l)}t=t.sibling}}function Ci(e,t){var l=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==l&&(e!=null&&e.refCount++,l!=null&&Ql(l))}function Ti(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Ql(e))}function zt(e,t,l,a){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)lo(e,t,l,a),t=t.sibling}function lo(e,t,l,a){var n=t.flags;switch(t.tag){case 0:case 11:case 15:zt(e,t,l,a),n&2048&&ca(9,t);break;case 1:zt(e,t,l,a);break;case 3:zt(e,t,l,a),n&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Ql(e)));break;case 12:if(n&2048){zt(e,t,l,a),e=t.stateNode;try{var c=t.memoizedProps,r=c.id,m=c.onPostCommit;typeof m=="function"&&m(r,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(y){Ee(t,t.return,y)}}else zt(e,t,l,a);break;case 13:zt(e,t,l,a);break;case 23:break;case 22:c=t.stateNode,r=t.alternate,t.memoizedState!==null?c._visibility&2?zt(e,t,l,a):ra(e,t):c._visibility&2?zt(e,t,l,a):(c._visibility|=2,hl(e,t,l,a,(t.subtreeFlags&10256)!==0)),n&2048&&Ci(r,t);break;case 24:zt(e,t,l,a),n&2048&&Ti(t.alternate,t);break;default:zt(e,t,l,a)}}function hl(e,t,l,a,n){for(n=n&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var c=e,r=t,m=l,y=a,O=r.flags;switch(r.tag){case 0:case 11:case 15:hl(c,r,m,y,n),ca(8,r);break;case 23:break;case 22:var $=r.stateNode;r.memoizedState!==null?$._visibility&2?hl(c,r,m,y,n):ra(c,r):($._visibility|=2,hl(c,r,m,y,n)),n&&O&2048&&Ci(r.alternate,r);break;case 24:hl(c,r,m,y,n),n&&O&2048&&Ti(r.alternate,r);break;default:hl(c,r,m,y,n)}t=t.sibling}}function ra(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var l=e,a=t,n=a.flags;switch(a.tag){case 22:ra(l,a),n&2048&&Ci(a.alternate,a);break;case 24:ra(l,a),n&2048&&Ti(a.alternate,a);break;default:ra(l,a)}t=t.sibling}}var ua=8192;function gl(e){if(e.subtreeFlags&ua)for(e=e.child;e!==null;)ao(e),e=e.sibling}function ao(e){switch(e.tag){case 26:gl(e),e.flags&ua&&e.memoizedState!==null&&$f(_t,e.memoizedState,e.memoizedProps);break;case 5:gl(e);break;case 3:case 4:var t=_t;_t=kn(e.stateNode.containerInfo),gl(e),_t=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=ua,ua=16777216,gl(e),ua=t):gl(e));break;default:gl(e)}}function no(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function da(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];Qe=a,io(a,e)}no(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)co(e),e=e.sibling}function co(e){switch(e.tag){case 0:case 11:case 15:da(e),e.flags&2048&&us(9,e,e.return);break;case 3:da(e);break;case 12:da(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,jn(e)):da(e);break;default:da(e)}}function jn(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];Qe=a,io(a,e)}no(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:us(8,t,t.return),jn(t);break;case 22:l=t.stateNode,l._visibility&2&&(l._visibility&=-3,jn(t));break;default:jn(t)}e=e.sibling}}function io(e,t){for(;Qe!==null;){var l=Qe;switch(l.tag){case 0:case 11:case 15:us(8,l,t);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:Ql(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,Qe=a;else e:for(l=e;Qe!==null;){a=Qe;var n=a.sibling,c=a.return;if(Pd(a),a===l){Qe=null;break e}if(n!==null){n.return=c,Qe=n;break e}Qe=c}}}var nf={getCacheForType:function(e){var t=Pe(Be),l=t.data.get(e);return l===void 0&&(l=e(),t.data.set(e,l)),l}},cf=typeof WeakMap=="function"?WeakMap:Map,Ce=0,Re=null,ve=null,je=0,Te=0,ht=null,ms=!1,bl=!1,Ai=!1,Ft=0,ze=0,xs=0,Hs=0,_i=0,Ct=0,yl=0,oa=null,it=null,Mi=!1,Ei=0,Nn=1/0,Sn=null,fs=null,Je=0,hs=null,vl=null,pl=0,Ri=0,Oi=null,ro=null,ma=0,ki=null;function gt(){if((Ce&2)!==0&&je!==0)return je&-je;if(E.T!==null){var e=il;return e!==0?e:Gi()}return wr()}function uo(){Ct===0&&(Ct=(je&536870912)===0||Se?pr():536870912);var e=wt.current;return e!==null&&(e.flags|=32),Ct}function bt(e,t,l){(e===Re&&(Te===2||Te===9)||e.cancelPendingCommit!==null)&&(jl(e,0),gs(e,je,Ct,!1)),El(e,l),((Ce&2)===0||e!==Re)&&(e===Re&&((Ce&2)===0&&(Hs|=l),ze===4&&gs(e,je,Ct,!1)),Lt(e))}function oo(e,t,l){if((Ce&6)!==0)throw Error(x(327));var a=!l&&(t&124)===0&&(t&e.expiredLanes)===0||Ml(e,t),n=a?df(e,t):Li(e,t,!0),c=a;do{if(n===0){bl&&!a&&gs(e,t,0,!1);break}else{if(l=e.current.alternate,c&&!rf(l)){n=Li(e,t,!1),c=!1;continue}if(n===2){if(c=t,e.errorRecoveryDisabledLanes&c)var r=0;else r=e.pendingLanes&-536870913,r=r!==0?r:r&536870912?536870912:0;if(r!==0){t=r;e:{var m=e;n=oa;var y=m.current.memoizedState.isDehydrated;if(y&&(jl(m,r).flags|=256),r=Li(m,r,!1),r!==2){if(Ai&&!y){m.errorRecoveryDisabledLanes|=c,Hs|=c,n=4;break e}c=it,it=n,c!==null&&(it===null?it=c:it.push.apply(it,c))}n=r}if(c=!1,n!==2)continue}}if(n===1){jl(e,0),gs(e,t,0,!0);break}e:{switch(a=e,c=n,c){case 0:case 1:throw Error(x(345));case 4:if((t&4194048)!==t)break;case 6:gs(a,t,Ct,!ms);break e;case 2:it=null;break;case 3:case 5:break;default:throw Error(x(329))}if((t&62914560)===t&&(n=Ei+300-Rt(),10<n)){if(gs(a,t,Ct,!ms),ka(a,0,!0)!==0)break e;a.timeoutHandle=Go(mo.bind(null,a,l,it,Sn,Mi,t,Ct,Hs,yl,ms,c,2,-0,0),n);break e}mo(a,l,it,Sn,Mi,t,Ct,Hs,yl,ms,c,0,-0,0)}}break}while(!0);Lt(e)}function mo(e,t,l,a,n,c,r,m,y,O,$,W,U,G){if(e.timeoutHandle=-1,W=t.subtreeFlags,(W&8192||(W&16785408)===16785408)&&(va={stylesheets:null,count:0,unsuspend:Yf},ao(t),W=Xf(),W!==null)){e.cancelPendingCommit=W(vo.bind(null,e,t,c,l,a,n,r,m,y,$,1,U,G)),gs(e,c,r,!O);return}vo(e,t,c,l,a,n,r,m,y)}function rf(e){for(var t=e;;){var l=t.tag;if((l===0||l===11||l===15)&&t.flags&16384&&(l=t.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],c=n.getSnapshot;n=n.value;try{if(!ot(c(),n))return!1}catch{return!1}}if(l=t.child,t.subtreeFlags&16384&&l!==null)l.return=t,t=l;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function gs(e,t,l,a){t&=~_i,t&=~Hs,e.suspendedLanes|=t,e.pingedLanes&=~t,a&&(e.warmLanes|=t),a=e.expirationTimes;for(var n=t;0<n;){var c=31-dt(n),r=1<<c;a[c]=-1,n&=~r}l!==0&&Nr(e,l,t)}function wn(){return(Ce&6)===0?(xa(0),!1):!0}function Di(){if(ve!==null){if(Te===0)var e=ve.return;else e=ve,Yt=ks=null,Fc(e),xl=null,la=0,e=ve;for(;e!==null;)$d(e.alternate,e),e=e.return;ve=null}}function jl(e,t){var l=e.timeoutHandle;l!==-1&&(e.timeoutHandle=-1,Tf(l)),l=e.cancelPendingCommit,l!==null&&(e.cancelPendingCommit=null,l()),Di(),Re=e,ve=l=Gt(e.current,null),je=t,Te=0,ht=null,ms=!1,bl=Ml(e,t),Ai=!1,yl=Ct=_i=Hs=xs=ze=0,it=oa=null,Mi=!1,(t&8)!==0&&(t|=t&32);var a=e.entangledLanes;if(a!==0)for(e=e.entanglements,a&=t;0<a;){var n=31-dt(a),c=1<<n;t|=e[n],a&=~c}return Ft=t,Xa(),l}function xo(e,t){ge=null,E.H=on,t===Kl||t===en?(t=Mu(),Te=3):t===Tu?(t=Mu(),Te=4):Te=t===Ed?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,ht=t,ve===null&&(ze=1,gn(e,pt(t,e.current)))}function fo(){var e=E.H;return E.H=on,e===null?on:e}function ho(){var e=E.A;return E.A=nf,e}function zi(){ze=4,ms||(je&4194048)!==je&&wt.current!==null||(bl=!0),(xs&134217727)===0&&(Hs&134217727)===0||Re===null||gs(Re,je,Ct,!1)}function Li(e,t,l){var a=Ce;Ce|=2;var n=fo(),c=ho();(Re!==e||je!==t)&&(Sn=null,jl(e,t)),t=!1;var r=ze;e:do try{if(Te!==0&&ve!==null){var m=ve,y=ht;switch(Te){case 8:Di(),r=6;break e;case 3:case 2:case 9:case 6:wt.current===null&&(t=!0);var O=Te;if(Te=0,ht=null,Nl(e,m,y,O),l&&bl){r=0;break e}break;default:O=Te,Te=0,ht=null,Nl(e,m,y,O)}}uf(),r=ze;break}catch($){xo(e,$)}while(!0);return t&&e.shellSuspendCounter++,Yt=ks=null,Ce=a,E.H=n,E.A=c,ve===null&&(Re=null,je=0,Xa()),r}function uf(){for(;ve!==null;)go(ve)}function df(e,t){var l=Ce;Ce|=2;var a=fo(),n=ho();Re!==e||je!==t?(Sn=null,Nn=Rt()+500,jl(e,t)):bl=Ml(e,t);e:do try{if(Te!==0&&ve!==null){t=ve;var c=ht;t:switch(Te){case 1:Te=0,ht=null,Nl(e,t,c,1);break;case 2:case 9:if(Au(c)){Te=0,ht=null,bo(t);break}t=function(){Te!==2&&Te!==9||Re!==e||(Te=7),Lt(e)},c.then(t,t);break e;case 3:Te=7;break e;case 4:Te=5;break e;case 7:Au(c)?(Te=0,ht=null,bo(t)):(Te=0,ht=null,Nl(e,t,c,7));break;case 5:var r=null;switch(ve.tag){case 26:r=ve.memoizedState;case 5:case 27:var m=ve;if(!r||Po(r)){Te=0,ht=null;var y=m.sibling;if(y!==null)ve=y;else{var O=m.return;O!==null?(ve=O,Cn(O)):ve=null}break t}}Te=0,ht=null,Nl(e,t,c,5);break;case 6:Te=0,ht=null,Nl(e,t,c,6);break;case 8:Di(),ze=6;break e;default:throw Error(x(462))}}of();break}catch($){xo(e,$)}while(!0);return Yt=ks=null,E.H=a,E.A=n,Ce=l,ve!==null?0:(Re=null,je=0,Xa(),ze)}function of(){for(;ve!==null&&!Om();)go(ve)}function go(e){var t=Bd(e.alternate,e,Ft);e.memoizedProps=e.pendingProps,t===null?Cn(e):ve=t}function bo(e){var t=e,l=t.alternate;switch(t.tag){case 15:case 0:t=Ld(l,t,t.pendingProps,t.type,void 0,je);break;case 11:t=Ld(l,t,t.pendingProps,t.type.render,t.ref,je);break;case 5:Fc(t);default:$d(l,t),t=ve=bu(t,Ft),t=Bd(l,t,Ft)}e.memoizedProps=e.pendingProps,t===null?Cn(e):ve=t}function Nl(e,t,l,a){Yt=ks=null,Fc(t),xl=null,la=0;var n=t.return;try{if(Ix(e,n,t,l,je)){ze=1,gn(e,pt(l,e.current)),ve=null;return}}catch(c){if(n!==null)throw ve=n,c;ze=1,gn(e,pt(l,e.current)),ve=null;return}t.flags&32768?(Se||a===1?e=!0:bl||(je&536870912)!==0?e=!1:(ms=e=!0,(a===2||a===9||a===3||a===6)&&(a=wt.current,a!==null&&a.tag===13&&(a.flags|=16384))),yo(t,e)):Cn(t)}function Cn(e){var t=e;do{if((t.flags&32768)!==0){yo(t,ms);return}e=t.return;var l=tf(t.alternate,t,Ft);if(l!==null){ve=l;return}if(t=t.sibling,t!==null){ve=t;return}ve=t=e}while(t!==null);ze===0&&(ze=5)}function yo(e,t){do{var l=sf(e.alternate,e);if(l!==null){l.flags&=32767,ve=l;return}if(l=e.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!t&&(e=e.sibling,e!==null)){ve=e;return}ve=e=l}while(e!==null);ze=6,ve=null}function vo(e,t,l,a,n,c,r,m,y){e.cancelPendingCommit=null;do Tn();while(Je!==0);if((Ce&6)!==0)throw Error(x(327));if(t!==null){if(t===e.current)throw Error(x(177));if(c=t.lanes|t.childLanes,c|=Tc,Bm(e,l,c,r,m,y),e===Re&&(ve=Re=null,je=0),vl=t,hs=e,pl=l,Ri=c,Oi=n,ro=a,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,hf(Ea,function(){return wo(),null})):(e.callbackNode=null,e.callbackPriority=0),a=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||a){a=E.T,E.T=null,n=F.p,F.p=2,r=Ce,Ce|=4;try{lf(e,t,l)}finally{Ce=r,F.p=n,E.T=a}}Je=1,po(),jo(),No()}}function po(){if(Je===1){Je=0;var e=hs,t=vl,l=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||l){l=E.T,E.T=null;var a=F.p;F.p=2;var n=Ce;Ce|=4;try{to(t,e);var c=Ki,r=iu(e.containerInfo),m=c.focusedElem,y=c.selectionRange;if(r!==m&&m&&m.ownerDocument&&cu(m.ownerDocument.documentElement,m)){if(y!==null&&jc(m)){var O=y.start,$=y.end;if($===void 0&&($=O),"selectionStart"in m)m.selectionStart=O,m.selectionEnd=Math.min($,m.value.length);else{var W=m.ownerDocument||document,U=W&&W.defaultView||window;if(U.getSelection){var G=U.getSelection(),xe=m.textContent.length,oe=Math.min(y.start,xe),Me=y.end===void 0?oe:Math.min(y.end,xe);!G.extend&&oe>Me&&(r=Me,Me=oe,oe=r);var T=nu(m,oe),N=nu(m,Me);if(T&&N&&(G.rangeCount!==1||G.anchorNode!==T.node||G.anchorOffset!==T.offset||G.focusNode!==N.node||G.focusOffset!==N.offset)){var R=W.createRange();R.setStart(T.node,T.offset),G.removeAllRanges(),oe>Me?(G.addRange(R),G.extend(N.node,N.offset)):(R.setEnd(N.node,N.offset),G.addRange(R))}}}}for(W=[],G=m;G=G.parentNode;)G.nodeType===1&&W.push({element:G,left:G.scrollLeft,top:G.scrollTop});for(typeof m.focus=="function"&&m.focus(),m=0;m<W.length;m++){var Z=W[m];Z.element.scrollLeft=Z.left,Z.element.scrollTop=Z.top}}qn=!!Zi,Ki=Zi=null}finally{Ce=n,F.p=a,E.T=l}}e.current=t,Je=2}}function jo(){if(Je===2){Je=0;var e=hs,t=vl,l=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||l){l=E.T,E.T=null;var a=F.p;F.p=2;var n=Ce;Ce|=4;try{Fd(e,t.alternate,t)}finally{Ce=n,F.p=a,E.T=l}}Je=3}}function No(){if(Je===4||Je===3){Je=0,km();var e=hs,t=vl,l=pl,a=ro;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Je=5:(Je=0,vl=hs=null,So(e,e.pendingLanes));var n=e.pendingLanes;if(n===0&&(fs=null),ec(l),t=t.stateNode,ut&&typeof ut.onCommitFiberRoot=="function")try{ut.onCommitFiberRoot(_l,t,void 0,(t.current.flags&128)===128)}catch{}if(a!==null){t=E.T,n=F.p,F.p=2,E.T=null;try{for(var c=e.onRecoverableError,r=0;r<a.length;r++){var m=a[r];c(m.value,{componentStack:m.stack})}}finally{E.T=t,F.p=n}}(pl&3)!==0&&Tn(),Lt(e),n=e.pendingLanes,(l&4194090)!==0&&(n&42)!==0?e===ki?ma++:(ma=0,ki=e):ma=0,xa(0)}}function So(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Ql(t)))}function Tn(e){return po(),jo(),No(),wo()}function wo(){if(Je!==5)return!1;var e=hs,t=Ri;Ri=0;var l=ec(pl),a=E.T,n=F.p;try{F.p=32>l?32:l,E.T=null,l=Oi,Oi=null;var c=hs,r=pl;if(Je=0,vl=hs=null,pl=0,(Ce&6)!==0)throw Error(x(331));var m=Ce;if(Ce|=4,co(c.current),lo(c,c.current,r,l),Ce=m,xa(0,!1),ut&&typeof ut.onPostCommitFiberRoot=="function")try{ut.onPostCommitFiberRoot(_l,c)}catch{}return!0}finally{F.p=n,E.T=a,So(e,t)}}function Co(e,t,l){t=pt(l,t),t=oi(e.stateNode,t,2),e=ns(e,t,2),e!==null&&(El(e,2),Lt(e))}function Ee(e,t,l){if(e.tag===3)Co(e,e,l);else for(;t!==null;){if(t.tag===3){Co(t,e,l);break}else if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(fs===null||!fs.has(a))){e=pt(l,e),l=_d(2),a=ns(t,l,2),a!==null&&(Md(l,a,t,e),El(a,2),Lt(a));break}}t=t.return}}function Ui(e,t,l){var a=e.pingCache;if(a===null){a=e.pingCache=new cf;var n=new Set;a.set(t,n)}else n=a.get(t),n===void 0&&(n=new Set,a.set(t,n));n.has(l)||(Ai=!0,n.add(l),e=mf.bind(null,e,t,l),t.then(e,e))}function mf(e,t,l){var a=e.pingCache;a!==null&&a.delete(t),e.pingedLanes|=e.suspendedLanes&l,e.warmLanes&=~l,Re===e&&(je&l)===l&&(ze===4||ze===3&&(je&62914560)===je&&300>Rt()-Ei?(Ce&2)===0&&jl(e,0):_i|=l,yl===je&&(yl=0)),Lt(e)}function To(e,t){t===0&&(t=jr()),e=ll(e,t),e!==null&&(El(e,t),Lt(e))}function xf(e){var t=e.memoizedState,l=0;t!==null&&(l=t.retryLane),To(e,l)}function ff(e,t){var l=0;switch(e.tag){case 13:var a=e.stateNode,n=e.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(x(314))}a!==null&&a.delete(t),To(e,l)}function hf(e,t){return Wn(e,t)}var An=null,Sl=null,qi=!1,_n=!1,Hi=!1,Gs=0;function Lt(e){e!==Sl&&e.next===null&&(Sl===null?An=Sl=e:Sl=Sl.next=e),_n=!0,qi||(qi=!0,bf())}function xa(e,t){if(!Hi&&_n){Hi=!0;do for(var l=!1,a=An;a!==null;){if(e!==0){var n=a.pendingLanes;if(n===0)var c=0;else{var r=a.suspendedLanes,m=a.pingedLanes;c=(1<<31-dt(42|e)+1)-1,c&=n&~(r&~m),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(l=!0,Eo(a,c))}else c=je,c=ka(a,a===Re?c:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(c&3)===0||Ml(a,c)||(l=!0,Eo(a,c));a=a.next}while(l);Hi=!1}}function gf(){Ao()}function Ao(){_n=qi=!1;var e=0;Gs!==0&&(Cf()&&(e=Gs),Gs=0);for(var t=Rt(),l=null,a=An;a!==null;){var n=a.next,c=_o(a,t);c===0?(a.next=null,l===null?An=n:l.next=n,n===null&&(Sl=l)):(l=a,(e!==0||(c&3)!==0)&&(_n=!0)),a=n}xa(e)}function _o(e,t){for(var l=e.suspendedLanes,a=e.pingedLanes,n=e.expirationTimes,c=e.pendingLanes&-62914561;0<c;){var r=31-dt(c),m=1<<r,y=n[r];y===-1?((m&l)===0||(m&a)!==0)&&(n[r]=Vm(m,t)):y<=t&&(e.expiredLanes|=m),c&=~m}if(t=Re,l=je,l=ka(e,e===t?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a=e.callbackNode,l===0||e===t&&(Te===2||Te===9)||e.cancelPendingCommit!==null)return a!==null&&a!==null&&Fn(a),e.callbackNode=null,e.callbackPriority=0;if((l&3)===0||Ml(e,l)){if(t=l&-l,t===e.callbackPriority)return t;switch(a!==null&&Fn(a),ec(l)){case 2:case 8:l=yr;break;case 32:l=Ea;break;case 268435456:l=vr;break;default:l=Ea}return a=Mo.bind(null,e),l=Wn(l,a),e.callbackPriority=t,e.callbackNode=l,t}return a!==null&&a!==null&&Fn(a),e.callbackPriority=2,e.callbackNode=null,2}function Mo(e,t){if(Je!==0&&Je!==5)return e.callbackNode=null,e.callbackPriority=0,null;var l=e.callbackNode;if(Tn()&&e.callbackNode!==l)return null;var a=je;return a=ka(e,e===Re?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a===0?null:(oo(e,a,t),_o(e,Rt()),e.callbackNode!=null&&e.callbackNode===l?Mo.bind(null,e):null)}function Eo(e,t){if(Tn())return null;oo(e,t,!0)}function bf(){Af(function(){(Ce&6)!==0?Wn(br,gf):Ao()})}function Gi(){return Gs===0&&(Gs=pr()),Gs}function Ro(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:qa(""+e)}function Oo(e,t){var l=t.ownerDocument.createElement("input");return l.name=t.name,l.value=t.value,e.id&&l.setAttribute("form",e.id),t.parentNode.insertBefore(l,t),e=new FormData(e),l.parentNode.removeChild(l),e}function yf(e,t,l,a,n){if(t==="submit"&&l&&l.stateNode===n){var c=Ro((n[lt]||null).action),r=a.submitter;r&&(t=(t=r[lt]||null)?Ro(t.formAction):r.getAttribute("formAction"),t!==null&&(c=t,r=null));var m=new Ba("action","action",null,a,n);e.push({event:m,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(Gs!==0){var y=r?Oo(n,r):new FormData(n);ci(l,{pending:!0,data:y,method:n.method,action:c},null,y)}}else typeof c=="function"&&(m.preventDefault(),y=r?Oo(n,r):new FormData(n),ci(l,{pending:!0,data:y,method:n.method,action:c},c,y))},currentTarget:n}]})}}for(var Vi=0;Vi<Cc.length;Vi++){var Bi=Cc[Vi],vf=Bi.toLowerCase(),pf=Bi[0].toUpperCase()+Bi.slice(1);At(vf,"on"+pf)}At(du,"onAnimationEnd"),At(ou,"onAnimationIteration"),At(mu,"onAnimationStart"),At("dblclick","onDoubleClick"),At("focusin","onFocus"),At("focusout","onBlur"),At(Lx,"onTransitionRun"),At(Ux,"onTransitionStart"),At(qx,"onTransitionCancel"),At(xu,"onTransitionEnd"),Zs("onMouseEnter",["mouseout","mouseover"]),Zs("onMouseLeave",["mouseout","mouseover"]),Zs("onPointerEnter",["pointerout","pointerover"]),Zs("onPointerLeave",["pointerout","pointerover"]),ws("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),ws("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),ws("onBeforeInput",["compositionend","keypress","textInput","paste"]),ws("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),ws("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),ws("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var fa="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),jf=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(fa));function ko(e,t){t=(t&4)!==0;for(var l=0;l<e.length;l++){var a=e[l],n=a.event;a=a.listeners;e:{var c=void 0;if(t)for(var r=a.length-1;0<=r;r--){var m=a[r],y=m.instance,O=m.currentTarget;if(m=m.listener,y!==c&&n.isPropagationStopped())break e;c=m,n.currentTarget=O;try{c(n)}catch($){hn($)}n.currentTarget=null,c=y}else for(r=0;r<a.length;r++){if(m=a[r],y=m.instance,O=m.currentTarget,m=m.listener,y!==c&&n.isPropagationStopped())break e;c=m,n.currentTarget=O;try{c(n)}catch($){hn($)}n.currentTarget=null,c=y}}}}function pe(e,t){var l=t[tc];l===void 0&&(l=t[tc]=new Set);var a=e+"__bubble";l.has(a)||(Do(t,e,2,!1),l.add(a))}function Yi(e,t,l){var a=0;t&&(a|=4),Do(l,e,a,t)}var Mn="_reactListening"+Math.random().toString(36).slice(2);function $i(e){if(!e[Mn]){e[Mn]=!0,Tr.forEach(function(l){l!=="selectionchange"&&(jf.has(l)||Yi(l,!1,e),Yi(l,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Mn]||(t[Mn]=!0,Yi("selectionchange",!1,t))}}function Do(e,t,l,a){switch(am(t)){case 2:var n=Kf;break;case 8:n=Jf;break;default:n=ar}l=n.bind(null,t,l,e),n=void 0,!mc||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(n=!0),a?n!==void 0?e.addEventListener(t,l,{capture:!0,passive:n}):e.addEventListener(t,l,!0):n!==void 0?e.addEventListener(t,l,{passive:n}):e.addEventListener(t,l,!1)}function Xi(e,t,l,a,n){var c=a;if((t&1)===0&&(t&2)===0&&a!==null)e:for(;;){if(a===null)return;var r=a.tag;if(r===3||r===4){var m=a.stateNode.containerInfo;if(m===n)break;if(r===4)for(r=a.return;r!==null;){var y=r.tag;if((y===3||y===4)&&r.stateNode.containerInfo===n)return;r=r.return}for(;m!==null;){if(r=$s(m),r===null)return;if(y=r.tag,y===5||y===6||y===26||y===27){a=c=r;continue e}m=m.parentNode}}a=a.return}Gr(function(){var O=c,$=dc(l),W=[];e:{var U=fu.get(e);if(U!==void 0){var G=Ba,xe=e;switch(e){case"keypress":if(Ga(l)===0)break e;case"keydown":case"keyup":G=fx;break;case"focusin":xe="focus",G=gc;break;case"focusout":xe="blur",G=gc;break;case"beforeblur":case"afterblur":G=gc;break;case"click":if(l.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":G=Yr;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":G=sx;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":G=bx;break;case du:case ou:case mu:G=nx;break;case xu:G=vx;break;case"scroll":case"scrollend":G=ex;break;case"wheel":G=jx;break;case"copy":case"cut":case"paste":G=ix;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":G=Xr;break;case"toggle":case"beforetoggle":G=Sx}var oe=(t&4)!==0,Me=!oe&&(e==="scroll"||e==="scrollend"),T=oe?U!==null?U+"Capture":null:U;oe=[];for(var N=O,R;N!==null;){var Z=N;if(R=Z.stateNode,Z=Z.tag,Z!==5&&Z!==26&&Z!==27||R===null||T===null||(Z=kl(N,T),Z!=null&&oe.push(ha(N,Z,R))),Me)break;N=N.return}0<oe.length&&(U=new G(U,xe,null,l,$),W.push({event:U,listeners:oe}))}}if((t&7)===0){e:{if(U=e==="mouseover"||e==="pointerover",G=e==="mouseout"||e==="pointerout",U&&l!==uc&&(xe=l.relatedTarget||l.fromElement)&&($s(xe)||xe[Ys]))break e;if((G||U)&&(U=$.window===$?$:(U=$.ownerDocument)?U.defaultView||U.parentWindow:window,G?(xe=l.relatedTarget||l.toElement,G=O,xe=xe?$s(xe):null,xe!==null&&(Me=S(xe),oe=xe.tag,xe!==Me||oe!==5&&oe!==27&&oe!==6)&&(xe=null)):(G=null,xe=O),G!==xe)){if(oe=Yr,Z="onMouseLeave",T="onMouseEnter",N="mouse",(e==="pointerout"||e==="pointerover")&&(oe=Xr,Z="onPointerLeave",T="onPointerEnter",N="pointer"),Me=G==null?U:Ol(G),R=xe==null?U:Ol(xe),U=new oe(Z,N+"leave",G,l,$),U.target=Me,U.relatedTarget=R,Z=null,$s($)===O&&(oe=new oe(T,N+"enter",xe,l,$),oe.target=R,oe.relatedTarget=Me,Z=oe),Me=Z,G&&xe)t:{for(oe=G,T=xe,N=0,R=oe;R;R=wl(R))N++;for(R=0,Z=T;Z;Z=wl(Z))R++;for(;0<N-R;)oe=wl(oe),N--;for(;0<R-N;)T=wl(T),R--;for(;N--;){if(oe===T||T!==null&&oe===T.alternate)break t;oe=wl(oe),T=wl(T)}oe=null}else oe=null;G!==null&&zo(W,U,G,oe,!1),xe!==null&&Me!==null&&zo(W,Me,xe,oe,!0)}}e:{if(U=O?Ol(O):window,G=U.nodeName&&U.nodeName.toLowerCase(),G==="select"||G==="input"&&U.type==="file")var ce=Ir;else if(Fr(U))if(eu)ce=kx;else{ce=Rx;var be=Ex}else G=U.nodeName,!G||G.toLowerCase()!=="input"||U.type!=="checkbox"&&U.type!=="radio"?O&&rc(O.elementType)&&(ce=Ir):ce=Ox;if(ce&&(ce=ce(e,O))){Pr(W,ce,l,$);break e}be&&be(e,U,O),e==="focusout"&&O&&U.type==="number"&&O.memoizedProps.value!=null&&ic(U,"number",U.value)}switch(be=O?Ol(O):window,e){case"focusin":(Fr(be)||be.contentEditable==="true")&&(el=be,Nc=O,Vl=null);break;case"focusout":Vl=Nc=el=null;break;case"mousedown":Sc=!0;break;case"contextmenu":case"mouseup":case"dragend":Sc=!1,ru(W,l,$);break;case"selectionchange":if(zx)break;case"keydown":case"keyup":ru(W,l,$)}var re;if(yc)e:{switch(e){case"compositionstart":var me="onCompositionStart";break e;case"compositionend":me="onCompositionEnd";break e;case"compositionupdate":me="onCompositionUpdate";break e}me=void 0}else Is?Jr(e,l)&&(me="onCompositionEnd"):e==="keydown"&&l.keyCode===229&&(me="onCompositionStart");me&&(Qr&&l.locale!=="ko"&&(Is||me!=="onCompositionStart"?me==="onCompositionEnd"&&Is&&(re=Vr()):(ts=$,xc="value"in ts?ts.value:ts.textContent,Is=!0)),be=En(O,me),0<be.length&&(me=new $r(me,e,null,l,$),W.push({event:me,listeners:be}),re?me.data=re:(re=Wr(l),re!==null&&(me.data=re)))),(re=Cx?Tx(e,l):Ax(e,l))&&(me=En(O,"onBeforeInput"),0<me.length&&(be=new $r("onBeforeInput","beforeinput",null,l,$),W.push({event:be,listeners:me}),be.data=re)),yf(W,e,O,l,$)}ko(W,t)})}function ha(e,t,l){return{instance:e,listener:t,currentTarget:l}}function En(e,t){for(var l=t+"Capture",a=[];e!==null;){var n=e,c=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||c===null||(n=kl(e,l),n!=null&&a.unshift(ha(e,n,c)),n=kl(e,t),n!=null&&a.push(ha(e,n,c))),e.tag===3)return a;e=e.return}return[]}function wl(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function zo(e,t,l,a,n){for(var c=t._reactName,r=[];l!==null&&l!==a;){var m=l,y=m.alternate,O=m.stateNode;if(m=m.tag,y!==null&&y===a)break;m!==5&&m!==26&&m!==27||O===null||(y=O,n?(O=kl(l,c),O!=null&&r.unshift(ha(l,O,y))):n||(O=kl(l,c),O!=null&&r.push(ha(l,O,y)))),l=l.return}r.length!==0&&e.push({event:t,listeners:r})}var Nf=/\r\n?/g,Sf=/\u0000|\uFFFD/g;function Lo(e){return(typeof e=="string"?e:""+e).replace(Nf,`
`).replace(Sf,"")}function Uo(e,t){return t=Lo(t),Lo(e)===t}function Rn(){}function _e(e,t,l,a,n,c){switch(l){case"children":typeof a=="string"?t==="body"||t==="textarea"&&a===""||Ws(e,a):(typeof a=="number"||typeof a=="bigint")&&t!=="body"&&Ws(e,""+a);break;case"className":za(e,"class",a);break;case"tabIndex":za(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":za(e,l,a);break;case"style":qr(e,a,c);break;case"data":if(t!=="object"){za(e,"data",a);break}case"src":case"href":if(a===""&&(t!=="a"||l!=="href")){e.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=qa(""+a),e.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){e.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(l==="formAction"?(t!=="input"&&_e(e,t,"name",n.name,n,null),_e(e,t,"formEncType",n.formEncType,n,null),_e(e,t,"formMethod",n.formMethod,n,null),_e(e,t,"formTarget",n.formTarget,n,null)):(_e(e,t,"encType",n.encType,n,null),_e(e,t,"method",n.method,n,null),_e(e,t,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=qa(""+a),e.setAttribute(l,a);break;case"onClick":a!=null&&(e.onclick=Rn);break;case"onScroll":a!=null&&pe("scroll",e);break;case"onScrollEnd":a!=null&&pe("scrollend",e);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(x(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(x(60));e.innerHTML=l}}break;case"multiple":e.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":e.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){e.removeAttribute("xlink:href");break}l=qa(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""+a):e.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""):e.removeAttribute(l);break;case"capture":case"download":a===!0?e.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,a):e.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?e.setAttribute(l,a):e.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?e.removeAttribute(l):e.setAttribute(l,a);break;case"popover":pe("beforetoggle",e),pe("toggle",e),Da(e,"popover",a);break;case"xlinkActuate":qt(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":qt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":qt(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":qt(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":qt(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":qt(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":qt(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":qt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":qt(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Da(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=Pm.get(l)||l,Da(e,l,a))}}function Qi(e,t,l,a,n,c){switch(l){case"style":qr(e,a,c);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(x(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(x(60));e.innerHTML=l}}break;case"children":typeof a=="string"?Ws(e,a):(typeof a=="number"||typeof a=="bigint")&&Ws(e,""+a);break;case"onScroll":a!=null&&pe("scroll",e);break;case"onScrollEnd":a!=null&&pe("scrollend",e);break;case"onClick":a!=null&&(e.onclick=Rn);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Ar.hasOwnProperty(l))e:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),t=l.slice(2,n?l.length-7:void 0),c=e[lt]||null,c=c!=null?c[l]:null,typeof c=="function"&&e.removeEventListener(t,c,n),typeof a=="function")){typeof c!="function"&&c!==null&&(l in e?e[l]=null:e.hasAttribute(l)&&e.removeAttribute(l)),e.addEventListener(t,a,n);break e}l in e?e[l]=a:a===!0?e.setAttribute(l,""):Da(e,l,a)}}}function We(e,t,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":pe("error",e),pe("load",e);var a=!1,n=!1,c;for(c in l)if(l.hasOwnProperty(c)){var r=l[c];if(r!=null)switch(c){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(x(137,t));default:_e(e,t,c,r,l,null)}}n&&_e(e,t,"srcSet",l.srcSet,l,null),a&&_e(e,t,"src",l.src,l,null);return;case"input":pe("invalid",e);var m=c=r=n=null,y=null,O=null;for(a in l)if(l.hasOwnProperty(a)){var $=l[a];if($!=null)switch(a){case"name":n=$;break;case"type":r=$;break;case"checked":y=$;break;case"defaultChecked":O=$;break;case"value":c=$;break;case"defaultValue":m=$;break;case"children":case"dangerouslySetInnerHTML":if($!=null)throw Error(x(137,t));break;default:_e(e,t,a,$,l,null)}}Dr(e,c,m,y,O,r,n,!1),La(e);return;case"select":pe("invalid",e),a=r=c=null;for(n in l)if(l.hasOwnProperty(n)&&(m=l[n],m!=null))switch(n){case"value":c=m;break;case"defaultValue":r=m;break;case"multiple":a=m;default:_e(e,t,n,m,l,null)}t=c,l=r,e.multiple=!!a,t!=null?Js(e,!!a,t,!1):l!=null&&Js(e,!!a,l,!0);return;case"textarea":pe("invalid",e),c=n=a=null;for(r in l)if(l.hasOwnProperty(r)&&(m=l[r],m!=null))switch(r){case"value":a=m;break;case"defaultValue":n=m;break;case"children":c=m;break;case"dangerouslySetInnerHTML":if(m!=null)throw Error(x(91));break;default:_e(e,t,r,m,l,null)}Lr(e,a,n,c),La(e);return;case"option":for(y in l)if(l.hasOwnProperty(y)&&(a=l[y],a!=null))switch(y){case"selected":e.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:_e(e,t,y,a,l,null)}return;case"dialog":pe("beforetoggle",e),pe("toggle",e),pe("cancel",e),pe("close",e);break;case"iframe":case"object":pe("load",e);break;case"video":case"audio":for(a=0;a<fa.length;a++)pe(fa[a],e);break;case"image":pe("error",e),pe("load",e);break;case"details":pe("toggle",e);break;case"embed":case"source":case"link":pe("error",e),pe("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(O in l)if(l.hasOwnProperty(O)&&(a=l[O],a!=null))switch(O){case"children":case"dangerouslySetInnerHTML":throw Error(x(137,t));default:_e(e,t,O,a,l,null)}return;default:if(rc(t)){for($ in l)l.hasOwnProperty($)&&(a=l[$],a!==void 0&&Qi(e,t,$,a,l,void 0));return}}for(m in l)l.hasOwnProperty(m)&&(a=l[m],a!=null&&_e(e,t,m,a,l,null))}function wf(e,t,l,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,c=null,r=null,m=null,y=null,O=null,$=null;for(G in l){var W=l[G];if(l.hasOwnProperty(G)&&W!=null)switch(G){case"checked":break;case"value":break;case"defaultValue":y=W;default:a.hasOwnProperty(G)||_e(e,t,G,null,a,W)}}for(var U in a){var G=a[U];if(W=l[U],a.hasOwnProperty(U)&&(G!=null||W!=null))switch(U){case"type":c=G;break;case"name":n=G;break;case"checked":O=G;break;case"defaultChecked":$=G;break;case"value":r=G;break;case"defaultValue":m=G;break;case"children":case"dangerouslySetInnerHTML":if(G!=null)throw Error(x(137,t));break;default:G!==W&&_e(e,t,U,G,a,W)}}cc(e,r,m,y,O,$,c,n);return;case"select":G=r=m=U=null;for(c in l)if(y=l[c],l.hasOwnProperty(c)&&y!=null)switch(c){case"value":break;case"multiple":G=y;default:a.hasOwnProperty(c)||_e(e,t,c,null,a,y)}for(n in a)if(c=a[n],y=l[n],a.hasOwnProperty(n)&&(c!=null||y!=null))switch(n){case"value":U=c;break;case"defaultValue":m=c;break;case"multiple":r=c;default:c!==y&&_e(e,t,n,c,a,y)}t=m,l=r,a=G,U!=null?Js(e,!!l,U,!1):!!a!=!!l&&(t!=null?Js(e,!!l,t,!0):Js(e,!!l,l?[]:"",!1));return;case"textarea":G=U=null;for(m in l)if(n=l[m],l.hasOwnProperty(m)&&n!=null&&!a.hasOwnProperty(m))switch(m){case"value":break;case"children":break;default:_e(e,t,m,null,a,n)}for(r in a)if(n=a[r],c=l[r],a.hasOwnProperty(r)&&(n!=null||c!=null))switch(r){case"value":U=n;break;case"defaultValue":G=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(x(91));break;default:n!==c&&_e(e,t,r,n,a,c)}zr(e,U,G);return;case"option":for(var xe in l)if(U=l[xe],l.hasOwnProperty(xe)&&U!=null&&!a.hasOwnProperty(xe))switch(xe){case"selected":e.selected=!1;break;default:_e(e,t,xe,null,a,U)}for(y in a)if(U=a[y],G=l[y],a.hasOwnProperty(y)&&U!==G&&(U!=null||G!=null))switch(y){case"selected":e.selected=U&&typeof U!="function"&&typeof U!="symbol";break;default:_e(e,t,y,U,a,G)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var oe in l)U=l[oe],l.hasOwnProperty(oe)&&U!=null&&!a.hasOwnProperty(oe)&&_e(e,t,oe,null,a,U);for(O in a)if(U=a[O],G=l[O],a.hasOwnProperty(O)&&U!==G&&(U!=null||G!=null))switch(O){case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(x(137,t));break;default:_e(e,t,O,U,a,G)}return;default:if(rc(t)){for(var Me in l)U=l[Me],l.hasOwnProperty(Me)&&U!==void 0&&!a.hasOwnProperty(Me)&&Qi(e,t,Me,void 0,a,U);for($ in a)U=a[$],G=l[$],!a.hasOwnProperty($)||U===G||U===void 0&&G===void 0||Qi(e,t,$,U,a,G);return}}for(var T in l)U=l[T],l.hasOwnProperty(T)&&U!=null&&!a.hasOwnProperty(T)&&_e(e,t,T,null,a,U);for(W in a)U=a[W],G=l[W],!a.hasOwnProperty(W)||U===G||U==null&&G==null||_e(e,t,W,U,a,G)}var Zi=null,Ki=null;function On(e){return e.nodeType===9?e:e.ownerDocument}function qo(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Ho(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Ji(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Wi=null;function Cf(){var e=window.event;return e&&e.type==="popstate"?e===Wi?!1:(Wi=e,!0):(Wi=null,!1)}var Go=typeof setTimeout=="function"?setTimeout:void 0,Tf=typeof clearTimeout=="function"?clearTimeout:void 0,Vo=typeof Promise=="function"?Promise:void 0,Af=typeof queueMicrotask=="function"?queueMicrotask:typeof Vo<"u"?function(e){return Vo.resolve(null).then(e).catch(_f)}:Go;function _f(e){setTimeout(function(){throw e})}function bs(e){return e==="head"}function Bo(e,t){var l=t,a=0,n=0;do{var c=l.nextSibling;if(e.removeChild(l),c&&c.nodeType===8)if(l=c.data,l==="/$"){if(0<a&&8>a){l=a;var r=e.ownerDocument;if(l&1&&ga(r.documentElement),l&2&&ga(r.body),l&4)for(l=r.head,ga(l),r=l.firstChild;r;){var m=r.nextSibling,y=r.nodeName;r[Rl]||y==="SCRIPT"||y==="STYLE"||y==="LINK"&&r.rel.toLowerCase()==="stylesheet"||l.removeChild(r),r=m}}if(n===0){e.removeChild(c),wa(t);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=c}while(l);wa(t)}function Fi(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var l=t;switch(t=t.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":Fi(l),sc(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}e.removeChild(l)}}function Mf(e,t,l,a){for(;e.nodeType===1;){var n=l;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(a){if(!e[Rl])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(c=e.getAttribute("rel"),c==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(c!==n.rel||e.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||e.getAttribute("title")!==(n.title==null?null:n.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(c=e.getAttribute("src"),(c!==(n.src==null?null:n.src)||e.getAttribute("type")!==(n.type==null?null:n.type)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&c&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var c=n.name==null?null:""+n.name;if(n.type==="hidden"&&e.getAttribute("name")===c)return e}else return e;if(e=Mt(e.nextSibling),e===null)break}return null}function Ef(e,t,l){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!l||(e=Mt(e.nextSibling),e===null))return null;return e}function Pi(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function Rf(e,t){var l=e.ownerDocument;if(e.data!=="$?"||l.readyState==="complete")t();else{var a=function(){t(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),e._reactRetry=a}}function Mt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Ii=null;function Yo(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var l=e.data;if(l==="$"||l==="$!"||l==="$?"){if(t===0)return e;t--}else l==="/$"&&t++}e=e.previousSibling}return null}function $o(e,t,l){switch(t=On(l),e){case"html":if(e=t.documentElement,!e)throw Error(x(452));return e;case"head":if(e=t.head,!e)throw Error(x(453));return e;case"body":if(e=t.body,!e)throw Error(x(454));return e;default:throw Error(x(451))}}function ga(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);sc(e)}var Tt=new Map,Xo=new Set;function kn(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Pt=F.d;F.d={f:Of,r:kf,D:Df,C:zf,L:Lf,m:Uf,X:Hf,S:qf,M:Gf};function Of(){var e=Pt.f(),t=wn();return e||t}function kf(e){var t=Xs(e);t!==null&&t.tag===5&&t.type==="form"?dd(t):Pt.r(e)}var Cl=typeof document>"u"?null:document;function Qo(e,t,l){var a=Cl;if(a&&typeof t=="string"&&t){var n=vt(t);n='link[rel="'+e+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),Xo.has(n)||(Xo.add(n),e={rel:e,crossOrigin:l,href:t},a.querySelector(n)===null&&(t=a.createElement("link"),We(t,"link",e),$e(t),a.head.appendChild(t)))}}function Df(e){Pt.D(e),Qo("dns-prefetch",e,null)}function zf(e,t){Pt.C(e,t),Qo("preconnect",e,t)}function Lf(e,t,l){Pt.L(e,t,l);var a=Cl;if(a&&e&&t){var n='link[rel="preload"][as="'+vt(t)+'"]';t==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+vt(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+vt(l.imageSizes)+'"]')):n+='[href="'+vt(e)+'"]';var c=n;switch(t){case"style":c=Tl(e);break;case"script":c=Al(e)}Tt.has(c)||(e=g({rel:"preload",href:t==="image"&&l&&l.imageSrcSet?void 0:e,as:t},l),Tt.set(c,e),a.querySelector(n)!==null||t==="style"&&a.querySelector(ba(c))||t==="script"&&a.querySelector(ya(c))||(t=a.createElement("link"),We(t,"link",e),$e(t),a.head.appendChild(t)))}}function Uf(e,t){Pt.m(e,t);var l=Cl;if(l&&e){var a=t&&typeof t.as=="string"?t.as:"script",n='link[rel="modulepreload"][as="'+vt(a)+'"][href="'+vt(e)+'"]',c=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=Al(e)}if(!Tt.has(c)&&(e=g({rel:"modulepreload",href:e},t),Tt.set(c,e),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(ya(c)))return}a=l.createElement("link"),We(a,"link",e),$e(a),l.head.appendChild(a)}}}function qf(e,t,l){Pt.S(e,t,l);var a=Cl;if(a&&e){var n=Qs(a).hoistableStyles,c=Tl(e);t=t||"default";var r=n.get(c);if(!r){var m={loading:0,preload:null};if(r=a.querySelector(ba(c)))m.loading=5;else{e=g({rel:"stylesheet",href:e,"data-precedence":t},l),(l=Tt.get(c))&&er(e,l);var y=r=a.createElement("link");$e(y),We(y,"link",e),y._p=new Promise(function(O,$){y.onload=O,y.onerror=$}),y.addEventListener("load",function(){m.loading|=1}),y.addEventListener("error",function(){m.loading|=2}),m.loading|=4,Dn(r,t,a)}r={type:"stylesheet",instance:r,count:1,state:m},n.set(c,r)}}}function Hf(e,t){Pt.X(e,t);var l=Cl;if(l&&e){var a=Qs(l).hoistableScripts,n=Al(e),c=a.get(n);c||(c=l.querySelector(ya(n)),c||(e=g({src:e,async:!0},t),(t=Tt.get(n))&&tr(e,t),c=l.createElement("script"),$e(c),We(c,"link",e),l.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},a.set(n,c))}}function Gf(e,t){Pt.M(e,t);var l=Cl;if(l&&e){var a=Qs(l).hoistableScripts,n=Al(e),c=a.get(n);c||(c=l.querySelector(ya(n)),c||(e=g({src:e,async:!0,type:"module"},t),(t=Tt.get(n))&&tr(e,t),c=l.createElement("script"),$e(c),We(c,"link",e),l.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},a.set(n,c))}}function Zo(e,t,l,a){var n=(n=de.current)?kn(n):null;if(!n)throw Error(x(446));switch(e){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(t=Tl(l.href),l=Qs(n).hoistableStyles,a=l.get(t),a||(a={type:"style",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){e=Tl(l.href);var c=Qs(n).hoistableStyles,r=c.get(e);if(r||(n=n.ownerDocument||n,r={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,r),(c=n.querySelector(ba(e)))&&!c._p&&(r.instance=c,r.state.loading=5),Tt.has(e)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},Tt.set(e,l),c||Vf(n,e,l,r.state))),t&&a===null)throw Error(x(528,""));return r}if(t&&a!==null)throw Error(x(529,""));return null;case"script":return t=l.async,l=l.src,typeof l=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Al(l),l=Qs(n).hoistableScripts,a=l.get(t),a||(a={type:"script",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(x(444,e))}}function Tl(e){return'href="'+vt(e)+'"'}function ba(e){return'link[rel="stylesheet"]['+e+"]"}function Ko(e){return g({},e,{"data-precedence":e.precedence,precedence:null})}function Vf(e,t,l,a){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?a.loading=1:(t=e.createElement("link"),a.preload=t,t.addEventListener("load",function(){return a.loading|=1}),t.addEventListener("error",function(){return a.loading|=2}),We(t,"link",l),$e(t),e.head.appendChild(t))}function Al(e){return'[src="'+vt(e)+'"]'}function ya(e){return"script[async]"+e}function Jo(e,t,l){if(t.count++,t.instance===null)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+vt(l.href)+'"]');if(a)return t.instance=a,$e(a),a;var n=g({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(e.ownerDocument||e).createElement("style"),$e(a),We(a,"style",n),Dn(a,l.precedence,e),t.instance=a;case"stylesheet":n=Tl(l.href);var c=e.querySelector(ba(n));if(c)return t.state.loading|=4,t.instance=c,$e(c),c;a=Ko(l),(n=Tt.get(n))&&er(a,n),c=(e.ownerDocument||e).createElement("link"),$e(c);var r=c;return r._p=new Promise(function(m,y){r.onload=m,r.onerror=y}),We(c,"link",a),t.state.loading|=4,Dn(c,l.precedence,e),t.instance=c;case"script":return c=Al(l.src),(n=e.querySelector(ya(c)))?(t.instance=n,$e(n),n):(a=l,(n=Tt.get(c))&&(a=g({},l),tr(a,n)),e=e.ownerDocument||e,n=e.createElement("script"),$e(n),We(n,"link",a),e.head.appendChild(n),t.instance=n);case"void":return null;default:throw Error(x(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(a=t.instance,t.state.loading|=4,Dn(a,l.precedence,e));return t.instance}function Dn(e,t,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,c=n,r=0;r<a.length;r++){var m=a[r];if(m.dataset.precedence===t)c=m;else if(c!==n)break}c?c.parentNode.insertBefore(e,c.nextSibling):(t=l.nodeType===9?l.head:l,t.insertBefore(e,t.firstChild))}function er(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function tr(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var zn=null;function Wo(e,t,l){if(zn===null){var a=new Map,n=zn=new Map;n.set(l,a)}else n=zn,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(e))return a;for(a.set(e,null),l=l.getElementsByTagName(e),n=0;n<l.length;n++){var c=l[n];if(!(c[Rl]||c[Fe]||e==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var r=c.getAttribute(t)||"";r=e+r;var m=a.get(r);m?m.push(c):a.set(r,[c])}}return a}function Fo(e,t,l){e=e.ownerDocument||e,e.head.insertBefore(l,t==="title"?e.querySelector("head > title"):null)}function Bf(e,t,l){if(l===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Po(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var va=null;function Yf(){}function $f(e,t,l){if(va===null)throw Error(x(475));var a=va;if(t.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var n=Tl(l.href),c=e.querySelector(ba(n));if(c){e=c._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(a.count++,a=Ln.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=c,$e(c);return}c=e.ownerDocument||e,l=Ko(l),(n=Tt.get(n))&&er(l,n),c=c.createElement("link"),$e(c);var r=c;r._p=new Promise(function(m,y){r.onload=m,r.onerror=y}),We(c,"link",l),t.instance=c}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(a.count++,t=Ln.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}function Xf(){if(va===null)throw Error(x(475));var e=va;return e.stylesheets&&e.count===0&&sr(e,e.stylesheets),0<e.count?function(t){var l=setTimeout(function(){if(e.stylesheets&&sr(e,e.stylesheets),e.unsuspend){var a=e.unsuspend;e.unsuspend=null,a()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(l)}}:null}function Ln(){if(this.count--,this.count===0){if(this.stylesheets)sr(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Un=null;function sr(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Un=new Map,t.forEach(Qf,e),Un=null,Ln.call(e))}function Qf(e,t){if(!(t.state.loading&4)){var l=Un.get(e);if(l)var a=l.get(null);else{l=new Map,Un.set(e,l);for(var n=e.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<n.length;c++){var r=n[c];(r.nodeName==="LINK"||r.getAttribute("media")!=="not all")&&(l.set(r.dataset.precedence,r),a=r)}a&&l.set(null,a)}n=t.instance,r=n.getAttribute("data-precedence"),c=l.get(r)||a,c===a&&l.set(null,n),l.set(r,n),this.count++,a=Ln.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),c?c.parentNode.insertBefore(n,c.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(n,e.firstChild)),t.state.loading|=4}}var pa={$$typeof:le,Provider:null,Consumer:null,_currentValue:ie,_currentValue2:ie,_threadCount:0};function Zf(e,t,l,a,n,c,r,m){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Pn(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Pn(0),this.hiddenUpdates=Pn(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=c,this.onRecoverableError=r,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=m,this.incompleteTransitions=new Map}function Io(e,t,l,a,n,c,r,m,y,O,$,W){return e=new Zf(e,t,l,r,m,y,O,W),t=1,c===!0&&(t|=24),c=mt(3,null,null,t),e.current=c,c.stateNode=e,t=Uc(),t.refCount++,e.pooledCache=t,t.refCount++,c.memoizedState={element:a,isDehydrated:l,cache:t},Vc(c),e}function em(e){return e?(e=al,e):al}function tm(e,t,l,a,n,c){n=em(n),a.context===null?a.context=n:a.pendingContext=n,a=as(t),a.payload={element:l},c=c===void 0?null:c,c!==null&&(a.callback=c),l=ns(e,a,t),l!==null&&(bt(l,e,t),Wl(l,e,t))}function sm(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var l=e.retryLane;e.retryLane=l!==0&&l<t?l:t}}function lr(e,t){sm(e,t),(e=e.alternate)&&sm(e,t)}function lm(e){if(e.tag===13){var t=ll(e,67108864);t!==null&&bt(t,e,67108864),lr(e,67108864)}}var qn=!0;function Kf(e,t,l,a){var n=E.T;E.T=null;var c=F.p;try{F.p=2,ar(e,t,l,a)}finally{F.p=c,E.T=n}}function Jf(e,t,l,a){var n=E.T;E.T=null;var c=F.p;try{F.p=8,ar(e,t,l,a)}finally{F.p=c,E.T=n}}function ar(e,t,l,a){if(qn){var n=nr(a);if(n===null)Xi(e,t,a,Hn,l),nm(e,a);else if(Ff(n,e,t,l,a))a.stopPropagation();else if(nm(e,a),t&4&&-1<Wf.indexOf(e)){for(;n!==null;){var c=Xs(n);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var r=Ss(c.pendingLanes);if(r!==0){var m=c;for(m.pendingLanes|=2,m.entangledLanes|=2;r;){var y=1<<31-dt(r);m.entanglements[1]|=y,r&=~y}Lt(c),(Ce&6)===0&&(Nn=Rt()+500,xa(0))}}break;case 13:m=ll(c,2),m!==null&&bt(m,c,2),wn(),lr(c,2)}if(c=nr(a),c===null&&Xi(e,t,a,Hn,l),c===n)break;n=c}n!==null&&a.stopPropagation()}else Xi(e,t,a,null,l)}}function nr(e){return e=dc(e),cr(e)}var Hn=null;function cr(e){if(Hn=null,e=$s(e),e!==null){var t=S(e);if(t===null)e=null;else{var l=t.tag;if(l===13){if(e=k(t),e!==null)return e;e=null}else if(l===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Hn=e,null}function am(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Dm()){case br:return 2;case yr:return 8;case Ea:case zm:return 32;case vr:return 268435456;default:return 32}default:return 32}}var ir=!1,ys=null,vs=null,ps=null,ja=new Map,Na=new Map,js=[],Wf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function nm(e,t){switch(e){case"focusin":case"focusout":ys=null;break;case"dragenter":case"dragleave":vs=null;break;case"mouseover":case"mouseout":ps=null;break;case"pointerover":case"pointerout":ja.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Na.delete(t.pointerId)}}function Sa(e,t,l,a,n,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:l,eventSystemFlags:a,nativeEvent:c,targetContainers:[n]},t!==null&&(t=Xs(t),t!==null&&lm(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,n!==null&&t.indexOf(n)===-1&&t.push(n),e)}function Ff(e,t,l,a,n){switch(t){case"focusin":return ys=Sa(ys,e,t,l,a,n),!0;case"dragenter":return vs=Sa(vs,e,t,l,a,n),!0;case"mouseover":return ps=Sa(ps,e,t,l,a,n),!0;case"pointerover":var c=n.pointerId;return ja.set(c,Sa(ja.get(c)||null,e,t,l,a,n)),!0;case"gotpointercapture":return c=n.pointerId,Na.set(c,Sa(Na.get(c)||null,e,t,l,a,n)),!0}return!1}function cm(e){var t=$s(e.target);if(t!==null){var l=S(t);if(l!==null){if(t=l.tag,t===13){if(t=k(l),t!==null){e.blockedOn=t,Ym(e.priority,function(){if(l.tag===13){var a=gt();a=In(a);var n=ll(l,a);n!==null&&bt(n,l,a),lr(l,a)}});return}}else if(t===3&&l.stateNode.current.memoizedState.isDehydrated){e.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Gn(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var l=nr(e.nativeEvent);if(l===null){l=e.nativeEvent;var a=new l.constructor(l.type,l);uc=a,l.target.dispatchEvent(a),uc=null}else return t=Xs(l),t!==null&&lm(t),e.blockedOn=l,!1;t.shift()}return!0}function im(e,t,l){Gn(e)&&l.delete(t)}function Pf(){ir=!1,ys!==null&&Gn(ys)&&(ys=null),vs!==null&&Gn(vs)&&(vs=null),ps!==null&&Gn(ps)&&(ps=null),ja.forEach(im),Na.forEach(im)}function Vn(e,t){e.blockedOn===t&&(e.blockedOn=null,ir||(ir=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,Pf)))}var Bn=null;function rm(e){Bn!==e&&(Bn=e,i.unstable_scheduleCallback(i.unstable_NormalPriority,function(){Bn===e&&(Bn=null);for(var t=0;t<e.length;t+=3){var l=e[t],a=e[t+1],n=e[t+2];if(typeof a!="function"){if(cr(a||l)===null)continue;break}var c=Xs(l);c!==null&&(e.splice(t,3),t-=3,ci(c,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function wa(e){function t(y){return Vn(y,e)}ys!==null&&Vn(ys,e),vs!==null&&Vn(vs,e),ps!==null&&Vn(ps,e),ja.forEach(t),Na.forEach(t);for(var l=0;l<js.length;l++){var a=js[l];a.blockedOn===e&&(a.blockedOn=null)}for(;0<js.length&&(l=js[0],l.blockedOn===null);)cm(l),l.blockedOn===null&&js.shift();if(l=(e.ownerDocument||e).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],c=l[a+1],r=n[lt]||null;if(typeof c=="function")r||rm(l);else if(r){var m=null;if(c&&c.hasAttribute("formAction")){if(n=c,r=c[lt]||null)m=r.formAction;else if(cr(n)!==null)continue}else m=r.action;typeof m=="function"?l[a+1]=m:(l.splice(a,3),a-=3),rm(l)}}}function rr(e){this._internalRoot=e}Yn.prototype.render=rr.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(x(409));var l=t.current,a=gt();tm(l,a,e,t,null,null)},Yn.prototype.unmount=rr.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;tm(e.current,2,null,e,null,null),wn(),t[Ys]=null}};function Yn(e){this._internalRoot=e}Yn.prototype.unstable_scheduleHydration=function(e){if(e){var t=wr();e={blockedOn:null,target:e,priority:t};for(var l=0;l<js.length&&t!==0&&t<js[l].priority;l++);js.splice(l,0,e),l===0&&cm(e)}};var um=d.version;if(um!=="19.1.0")throw Error(x(527,um,"19.1.0"));F.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(x(188)):(e=Object.keys(e).join(","),Error(x(268,e)));return e=b(t),e=e!==null?f(e):null,e=e===null?null:e.stateNode,e};var If={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:E,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var $n=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!$n.isDisabled&&$n.supportsFiber)try{_l=$n.inject(If),ut=$n}catch{}}return Ta.createRoot=function(e,t){if(!p(e))throw Error(x(299));var l=!1,a="",n=wd,c=Cd,r=Td,m=null;return t!=null&&(t.unstable_strictMode===!0&&(l=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onUncaughtError!==void 0&&(n=t.onUncaughtError),t.onCaughtError!==void 0&&(c=t.onCaughtError),t.onRecoverableError!==void 0&&(r=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(m=t.unstable_transitionCallbacks)),t=Io(e,1,!1,null,null,l,a,n,c,r,m,null),e[Ys]=t.current,$i(e),new rr(t)},Ta.hydrateRoot=function(e,t,l){if(!p(e))throw Error(x(299));var a=!1,n="",c=wd,r=Cd,m=Td,y=null,O=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(c=l.onUncaughtError),l.onCaughtError!==void 0&&(r=l.onCaughtError),l.onRecoverableError!==void 0&&(m=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(y=l.unstable_transitionCallbacks),l.formState!==void 0&&(O=l.formState)),t=Io(e,1,!0,t,l??null,a,n,c,r,m,y,O),t.context=em(null),l=t.current,a=gt(),a=In(a),n=as(a),n.callback=null,ns(l,n,a),l=a,t.current.lanes=l,El(t,l),Lt(t),e[Ys]=t.current,$i(e),new Yn(t)},Ta.version="19.1.0",Ta}var vm;function mh(){if(vm)return or.exports;vm=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(d){console.error(d)}}return i(),or.exports=oh(),or.exports}var xh=mh();const fh=wm(xh),Cm=v.createContext(void 0);function Tm(){const i=v.useContext(Cm);if(i===void 0)throw new Error("useSettings必须在SettingsProvider内使用");return i}function hh({children:i}){const[d,h]=v.useState(1e3),[x,p]=v.useState(!1),[S,k]=v.useState(!1),[A,b]=v.useState(!0),[f,g]=v.useState(3);return s.jsx(Cm.Provider,{value:{statusUpdateInterval:d,setStatusUpdateInterval:h,autoConnect:x,setAutoConnect:p,showDebugInfo:S,setShowDebugInfo:k,saveOperationHistory:A,setSaveOperationHistory:b,dataPrecision:f,setDataPrecision:g},children:i})}const pm="ws://localhost:8080/ws";function Kn(){const i=Tm(),[d,h]=v.useState(!1),[x,p]=v.useState(null),[S,k]=v.useState(null),[A,b]=v.useState(null),[f,g]=v.useState(null),C=v.useRef(null),M=v.useRef(void 0),J=v.useRef(void 0),z=v.useRef(0),D=5,j=v.useRef(!1),te=v.useCallback(()=>{J.current&&(clearInterval(J.current),J.current=void 0,i.showDebugInfo&&console.log("状态更新定时器已停止"))},[i.showDebugInfo]),I=v.useCallback(()=>{te();const V=()=>{if(C.current&&C.current.readyState===WebSocket.OPEN)try{C.current.send(JSON.stringify({type:"request_status"})),C.current.send(JSON.stringify({type:"request_task_status"}))}catch(u){console.warn("请求状态更新失败:",u)}};V(),J.current=setInterval(V,i.statusUpdateInterval),i.showDebugInfo&&console.log(`状态更新定时器已启动，间隔: ${i.statusUpdateInterval}ms`)},[i.statusUpdateInterval,i.showDebugInfo,te]),le=v.useCallback(()=>{if(j.current||C.current&&C.current.readyState===WebSocket.CONNECTING){console.log("WebSocket正在连接中，跳过重复连接请求");return}try{j.current=!0,g(null),C.current&&(C.current.close(),C.current=null),console.log("尝试连接WebSocket:",pm),C.current=new WebSocket(pm),C.current.onopen=()=>{console.log("✅ WebSocket连接已建立"),h(!0),z.current=0,j.current=!1,I()},C.current.onmessage=V=>{try{const u=JSON.parse(V.data);switch(b(u),u.type){case"agv_status":u.status&&p(u.status);break;case"task_status":u.status&&k(u.status);break;case"flow_status":i.showDebugInfo&&console.log("收到流程状态更新:",u.data);break;case"error":g(u.message||"未知错误");break;default:console.log("收到WebSocket消息:",u)}}catch(u){console.error("解析WebSocket消息失败:",u)}},C.current.onclose=V=>{if(console.log("WebSocket连接已关闭, 代码:",V.code,"原因:",V.reason),h(!1),j.current=!1,V.code!==1e3&&z.current<D){const u=Math.min(Math.pow(2,z.current)*1e3,1e4);console.log(`${u}ms后尝试重连... (${z.current+1}/${D})`),M.current=setTimeout(()=>{z.current+=1,le()},u)}else z.current>=D&&g("WebSocket连接失败，请检查后端服务是否启动")},C.current.onerror=V=>{console.error("WebSocket错误:",V),g("WebSocket连接错误"),j.current=!1}}catch(V){console.error("创建WebSocket连接失败:",V),g("无法创建WebSocket连接"),j.current=!1}},[I]),ae=v.useCallback(()=>{M.current&&clearTimeout(M.current),te(),C.current&&(C.current.close(1e3,"客户端主动断开"),C.current=null),h(!1),z.current=0,j.current=!1},[te]),_=v.useCallback(V=>{C.current&&C.current.readyState===WebSocket.OPEN?C.current.send(JSON.stringify(V)):console.warn("WebSocket未连接，无法发送消息")},[]);return v.useEffect(()=>{const V=setTimeout(()=>{le()},500);return()=>{clearTimeout(V),ae()}},[le,ae]),v.useEffect(()=>{d&&I()},[i.statusUpdateInterval,d,I]),{isConnected:d,agvStatus:x,taskStatus:S,lastMessage:A,error:f,sendMessage:_,reconnect:le,disconnect:ae}}const jm=window.location.protocol==="file:"||window.location.hostname==="localhost"?"http://localhost:8080/api":"/api";class gh{async request(d,h={}){try{const x=await fetch(`${jm}${d}`,{headers:{"Content-Type":"application/json",...h.headers},...h}),p=await x.json();if(!x.ok)throw new Error(p.error||"请求失败");return p}catch(x){throw console.error(`API请求失败 [${d}]:`,x),x}}async connectAGV(d){return this.request("/agv/connect",{method:"POST",body:JSON.stringify({authCode:d.authCode,ip:d.ip,port:d.port})})}async disconnectAGV(){return this.request("/agv/disconnect",{method:"POST"})}async getAGVStatus(){return this.request("/agv/status")}async navigateAGV(d){return this.request("/agv/navigate",{method:"POST",body:JSON.stringify(d)})}async subscribeAGV(){return this.request("/agv/subscribe",{method:"POST"})}async setWorkMode(d){return this.request("/agv/work-mode",{method:"POST",body:JSON.stringify(d)})}async setPosition(d){return this.request("/agv/position",{method:"POST",body:JSON.stringify(d)})}async confirmPosition(){return this.request("/agv/confirm-position",{method:"POST"})}async initializeLaser(d){return this.request("/agv/laser-init",{method:"POST",body:JSON.stringify(d)})}async getAGVNavigationStatus(){return this.request("/agv/navigation-status")}async connectPLC(d){return this.request("/plc/connect",{method:"POST",body:JSON.stringify({ip:d.ip,port:d.port})})}async disconnectPLC(){return this.request("/plc/disconnect",{method:"POST"})}async getPLCStatus(){return this.request("/plc/status")}async readPLC(d){return this.request("/plc/read",{method:"POST",body:JSON.stringify(d)})}async writePLC(d){return this.request("/plc/write",{method:"POST",body:JSON.stringify(d)})}async startWatchTask(d){return this.request("/task/start-watch",{method:"POST",body:JSON.stringify({robots:d})})}async stopWatchTask(){return this.request("/task/stop-watch",{method:"POST"})}async getWatchTaskStatus(){return this.request("/task/watch-status")}async queryLogs(d){const h=new URLSearchParams;return d.module&&h.set("module",d.module),d.level&&h.set("level",d.level),d.startTime&&h.set("startTime",d.startTime),d.endTime&&h.set("endTime",d.endTime),d.search&&h.set("search",d.search),d.limit&&h.set("limit",d.limit.toString()),d.offset&&h.set("offset",d.offset.toString()),d.reverse!==void 0&&h.set("reverse",d.reverse.toString()),this.request(`/logs/query?${h.toString()}`)}async getLogStats(){return this.request("/logs/stats")}async getLogModules(){return this.request("/logs/modules")}async getLogLevels(){return this.request("/logs/levels")}async exportLogs(d){const h=new URLSearchParams;if(h.set("format",d.format),d.filters.module!=="ALL"&&h.set("module",d.filters.module),d.filters.level!=="ALL"&&h.set("level",d.filters.level),d.filters.search&&h.set("search",d.filters.search),d.filters.timeRange==="today"){const p=new Date;p.setHours(0,0,0,0),h.set("startTime",p.toISOString())}else if(d.filters.timeRange==="hour"){const p=new Date(Date.now()-36e5);h.set("startTime",p.toISOString())}else d.filters.timeRange==="custom"&&(d.filters.customStartTime&&h.set("startTime",d.filters.customStartTime.toISOString()),d.filters.customEndTime&&h.set("endTime",d.filters.customEndTime.toISOString()));const x=`${jm}/logs/export?${h.toString()}`;window.open(x,"_blank")}async clearLogCache(){return this.request("/logs/clear-cache",{method:"POST"})}async getDefaultConfig(){return this.request("/config/defaults")}async testAGVConnection(d){return this.request("/config/test-agv",{method:"POST",body:JSON.stringify({authCode:d.authCode,ip:d.ip,port:d.port})})}async testPLCConnection(d){return this.request("/config/test-plc",{method:"POST",body:JSON.stringify({ip:d.ip,port:d.port})})}async connectAll(){return this.request("/config/connect-all",{method:"POST"})}async disconnectAll(){return this.request("/config/disconnect-all",{method:"POST"})}async getConnectionStatus(){return this.request("/status/connections")}async getFlowStatus(){return this.request("/monitor/flow-status")}async startCache(d){return this.request("/cache/start",{method:"POST",body:JSON.stringify(d)})}async stopCache(){return this.request("/cache/stop",{method:"POST"})}async getCacheStatus(){return this.request("/cache/status")}async getCacheData(){return this.request("/cache/data")}async clearCache(){return this.request("/cache/clear",{method:"POST"})}async startAutoWatch(){return this.request("/auto-watch/start",{method:"POST"})}async stopAutoWatch(){return this.request("/auto-watch/stop",{method:"POST"})}async getAutoWatchStatus(){return this.request("/auto-watch/status")}async oneClickOnline(){return this.request("/agv/one-click-online",{method:"POST"})}async getTaskConfig(){return this.request("/config/task-config")}async saveTaskConfig(d){return this.request("/config/task-config",{method:"POST",body:JSON.stringify(d)})}async validateTaskConfig(d){return this.request("/config/task-config/validate",{method:"POST",body:JSON.stringify(d)})}}const ye=new gh,{connectAGV:K0,disconnectAGV:J0,connectPLC:W0,disconnectPLC:F0,startAutoWatch:bh,stopAutoWatch:yh,getAutoWatchStatus:vh,oneClickOnline:Am}=ye;function ph(){const[i,d]=v.useState(!1),[h,x]=v.useState(""),[p,S]=v.useState(""),[k,A]=v.useState({x:"",y:"",angle:""}),b=async()=>{if(!p.trim()){x("请输入目标点ID");return}d(!0);try{await ye.navigateAGV({pointId:parseInt(p)}),x(`导航命令已发送到点ID: ${p}`)}catch(z){x(`导航失败: ${z.message}`)}finally{d(!1)}},f=async z=>{d(!0);try{await ye.setWorkMode({isAuto:z}),x(`工作模式已切换到${z?"自动":"手动"}模式`)}catch(D){x(`模式切换失败: ${D.message}`)}finally{d(!1)}},g=async()=>{const{x:z,y:D,angle:j}=k;if(!z||!D||!j){x("请填写完整的位置信息");return}d(!0);try{await ye.setPosition({x:parseFloat(z),y:parseFloat(D),angle:parseFloat(j)}),x("手动定位命令已发送")}catch(te){x(`定位失败: ${te.message}`)}finally{d(!1)}},C=async()=>{d(!0);try{await ye.confirmPosition(),x("重定位确认命令已发送")}catch(z){x(`确认失败: ${z.message}`)}finally{d(!1)}},M=async()=>{const{x:z,y:D,angle:j}=k;if(!z||!D||!j){x("请填写完整的初始化位置信息");return}d(!0);try{await ye.initializeLaser({x:parseFloat(z),y:parseFloat(D),angle:parseFloat(j)}),x("激光导航初始化命令已发送")}catch(te){x(`初始化失败: ${te.message}`)}finally{d(!1)}},J=async()=>{d(!0);try{await ye.getAGVStatus(),x("状态查询命令已发送")}catch(z){x(`状态查询失败: ${z.message}`)}finally{d(!1)}};return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"AGV控制"}),s.jsx("div",{className:"text-sm text-gray-500",children:"请先在设置页面连接AGV设备"})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[s.jsxs("button",{onClick:()=>f(!1),disabled:i,className:"btn btn-warning h-16 flex flex-col items-center justify-center",children:[s.jsx("span",{className:"text-lg mb-1",children:"🔧"}),s.jsx("span",{children:"手动模式"})]}),s.jsxs("button",{onClick:()=>f(!0),disabled:i,className:"btn btn-success h-16 flex flex-col items-center justify-center",children:[s.jsx("span",{className:"text-lg mb-1",children:"🤖"}),s.jsx("span",{children:"自动模式"})]}),s.jsxs("button",{onClick:J,disabled:i,className:"btn btn-primary h-16 flex flex-col items-center justify-center",children:[s.jsx("span",{className:"text-lg mb-1",children:"📊"}),s.jsx("span",{children:"查询状态"})]}),s.jsxs("button",{onClick:C,disabled:i,className:"btn btn-secondary h-16 flex flex-col items-center justify-center",children:[s.jsx("span",{className:"text-lg mb-1",children:"✅"}),s.jsx("span",{children:"确认重定位"})]})]}),s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"🗺️"}),"导航控制"]}),s.jsxs("div",{className:"flex space-x-4 items-end",children:[s.jsxs("div",{className:"flex-1",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"目标点ID"}),s.jsx("input",{type:"number",className:"input w-full",value:p,onChange:z=>S(z.target.value),placeholder:"输入目标点ID"})]}),s.jsx("button",{onClick:b,disabled:i||!p.trim(),className:"btn btn-primary px-8",children:i?"执行中...":"开始导航"})]}),s.jsx("div",{className:"mt-3 text-sm text-gray-600",children:"输入目标路径点ID，AGV将自动导航到指定位置"})]}),s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"📍"}),"位置控制"]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"X坐标 (米)"}),s.jsx("input",{type:"number",step:"0.001",className:"input w-full",value:k.x,onChange:z=>A(D=>({...D,x:z.target.value})),placeholder:"0.000"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Y坐标 (米)"}),s.jsx("input",{type:"number",step:"0.001",className:"input w-full",value:k.y,onChange:z=>A(D=>({...D,y:z.target.value})),placeholder:"0.000"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"角度 (弧度)"}),s.jsx("input",{type:"number",step:"0.001",className:"input w-full",value:k.angle,onChange:z=>A(D=>({...D,angle:z.target.value})),placeholder:"0.000"})]})]}),s.jsxs("div",{className:"flex space-x-3",children:[s.jsx("button",{onClick:g,disabled:i,className:"btn btn-primary",children:"手动定位"}),s.jsx("button",{onClick:M,disabled:i,className:"btn btn-warning",children:"激光导航初始化"}),s.jsx("button",{onClick:()=>A({x:"",y:"",angle:""}),className:"btn btn-secondary",children:"清空"})]}),s.jsx("div",{className:"mt-3 text-sm text-gray-600",children:"手动设置AGV位置和方向，或初始化激光导航系统"})]}),s.jsxs("div",{className:"card bg-blue-50 border-blue-200",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-3 text-blue-900 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"💡"}),"操作提示"]}),s.jsxs("div",{className:"text-sm text-blue-800 space-y-2",children:[s.jsxs("div",{className:"flex items-start space-x-2",children:[s.jsx("span",{className:"font-medium",children:"1."}),s.jsx("span",{children:"激光导航流程：手动模式 → 设置位置 → 确认重定位 → 自动模式"})]}),s.jsxs("div",{className:"flex items-start space-x-2",children:[s.jsx("span",{className:"font-medium",children:"2."}),s.jsx("span",{children:"点对点导航需要在自动模式下进行，确保AGV已完成定位"})]}),s.jsxs("div",{className:"flex items-start space-x-2",children:[s.jsx("span",{className:"font-medium",children:"3."}),s.jsx("span",{children:"角度使用弧度制，π ≈ 3.14159 (180度)"})]}),s.jsxs("div",{className:"flex items-start space-x-2",children:[s.jsx("span",{className:"font-medium",children:"4."}),s.jsx("span",{children:"建议定期查询状态以获取最新的AGV运行信息"})]})]})]}),h&&s.jsx("div",{className:`p-4 rounded-lg ${h.includes("失败")||h.includes("错误")?"bg-red-50 text-red-700 border border-red-200":"bg-green-50 text-green-700 border border-green-200"}`,children:s.jsxs("div",{className:"flex items-center",children:[s.jsx("span",{className:"text-lg mr-2",children:h.includes("失败")||h.includes("错误")?"❌":"✅"}),h]})})]})}function jh(){const[i,d]=v.useState(!1),[h,x]=v.useState(""),[p,S]=v.useState({type:"holding",address:"",quantity:"1"}),[k,A]=v.useState(null),[b,f]=v.useState({type:"register",address:"",value:""}),g=[{name:"状态寄存器",type:"holding",address:"0",quantity:"10"},{name:"输入寄存器",type:"input",address:"0",quantity:"10"},{name:"线圈状态",type:"coils",address:"0",quantity:"16"},{name:"离散输入",type:"discrete",address:"0",quantity:"16"}],C=async()=>{if(!p.address.trim()){x("请输入读取地址");return}d(!0);try{const D=await ye.readPLC({type:p.type,address:parseInt(p.address),quantity:parseInt(p.quantity)});A(D.data),x(`读取成功: 地址${p.address}，数量${p.quantity}`)}catch(D){x(`读取失败: ${D.message}`),A(null)}finally{d(!1)}},M=async()=>{if(!b.address.trim()||!b.value.trim()){x("请输入完整的写入信息");return}d(!0);try{let D;b.type==="coil"?D=b.value.toLowerCase()==="true"||b.value==="1":D=parseInt(b.value),await ye.writePLC({type:b.type,address:parseInt(b.address),value:D}),x(`写入成功: 地址${b.address} = ${b.value}`)}catch(D){x(`写入失败: ${D.message}`)}finally{d(!1)}},J=D=>{S({type:D.type,address:D.address,quantity:D.quantity})},z=(D,j)=>D?j==="coils"||j==="discrete"?s.jsxs("div",{className:"space-y-2",children:[s.jsx("div",{className:"text-sm font-medium text-gray-700",children:"位状态:"}),s.jsx("div",{className:"grid grid-cols-8 gap-1 text-xs",children:D.map((te,I)=>s.jsxs("div",{className:`w-10 h-10 flex flex-col items-center justify-center rounded border ${te?"bg-green-500 text-white border-green-600":"bg-gray-100 text-gray-600 border-gray-300"}`,children:[s.jsx("div",{className:"text-xs",children:parseInt(p.address)+I}),s.jsx("div",{className:"font-bold",children:te?"1":"0"})]},I))})]}):s.jsxs("div",{className:"space-y-2",children:[s.jsx("div",{className:"text-sm font-medium text-gray-700",children:"寄存器值:"}),s.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 text-sm",children:D.map((te,I)=>s.jsxs("div",{className:"bg-gray-50 p-3 rounded border",children:[s.jsxs("div",{className:"font-medium text-gray-900",children:["地址 ",parseInt(p.address)+I]}),s.jsx("div",{className:"font-mono text-lg text-blue-600",children:te}),s.jsxs("div",{className:"text-xs text-gray-500",children:["0x",te.toString(16).toUpperCase().padStart(4,"0")]})]},I))})]}):null;return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"PLC控制"}),s.jsx("div",{className:"text-sm text-gray-500",children:"请先在设置页面连接PLC设备"})]}),s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"⚡"}),"快速读取"]}),s.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:g.map((D,j)=>s.jsxs("button",{onClick:()=>J(D),disabled:i,className:"btn btn-secondary text-sm h-16 flex flex-col items-center justify-center",children:[s.jsx("span",{className:"font-medium",children:D.name}),s.jsxs("span",{className:"text-xs text-gray-600",children:[D.address,"~",parseInt(D.address)+parseInt(D.quantity)-1]})]},j))})]}),s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"📖"}),"数据读取"]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"数据类型"}),s.jsxs("select",{className:"input w-full",value:p.type,onChange:D=>S(j=>({...j,type:D.target.value})),children:[s.jsx("option",{value:"coils",children:"线圈 (Coils)"}),s.jsx("option",{value:"discrete",children:"离散输入 (Discrete)"}),s.jsx("option",{value:"holding",children:"保持寄存器 (Holding)"}),s.jsx("option",{value:"input",children:"输入寄存器 (Input)"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"起始地址"}),s.jsx("input",{type:"number",className:"input w-full",value:p.address,onChange:D=>S(j=>({...j,address:D.target.value})),placeholder:"0"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"数量"}),s.jsx("input",{type:"number",className:"input w-full",value:p.quantity,onChange:D=>S(j=>({...j,quantity:D.target.value})),placeholder:"1",min:"1",max:p.type==="coils"||p.type==="discrete"?"2000":"125"})]}),s.jsx("div",{className:"flex items-end",children:s.jsx("button",{onClick:C,disabled:i||!p.address.trim(),className:"btn btn-primary w-full",children:i?"读取中...":"读取"})})]}),k&&s.jsx("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg border",children:z(k.data,p.type)})]}),s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"✏️"}),"数据写入"]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"数据类型"}),s.jsxs("select",{className:"input w-full",value:b.type,onChange:D=>f(j=>({...j,type:D.target.value})),children:[s.jsx("option",{value:"coil",children:"线圈 (Coil)"}),s.jsx("option",{value:"register",children:"寄存器 (Register)"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"地址"}),s.jsx("input",{type:"number",className:"input w-full",value:b.address,onChange:D=>f(j=>({...j,address:D.target.value})),placeholder:"0"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:b.type==="coil"?"值 (true/false)":"值 (0-65535)"}),s.jsx("input",{type:"text",className:"input w-full",value:b.value,onChange:D=>f(j=>({...j,value:D.target.value})),placeholder:b.type==="coil"?"true 或 false":"数值"})]}),s.jsx("div",{className:"flex items-end",children:s.jsx("button",{onClick:M,disabled:i||!b.address.trim()||!b.value.trim(),className:"btn btn-warning w-full",children:i?"写入中...":"写入"})})]}),s.jsxs("div",{className:"text-sm text-gray-600",children:[s.jsx("span",{className:"font-medium",children:"注意："}),"线圈值请输入 true/false 或 1/0；寄存器值请输入 0-65535 之间的整数"]})]}),s.jsxs("div",{className:"card bg-orange-50 border-orange-200",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-3 text-orange-900 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"📚"}),"Modbus地址说明"]}),s.jsx("div",{className:"text-sm text-orange-800 space-y-2",children:s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("div",{className:"font-medium mb-2",children:"读取功能："}),s.jsxs("div",{className:"space-y-1 text-xs",children:[s.jsx("div",{children:"• 线圈 (Coils): 可读写的布尔值"}),s.jsx("div",{children:"• 离散输入: 只读的布尔值"}),s.jsx("div",{children:"• 保持寄存器: 可读写的16位数值"}),s.jsx("div",{children:"• 输入寄存器: 只读的16位数值"})]})]}),s.jsxs("div",{children:[s.jsx("div",{className:"font-medium mb-2",children:"写入功能："}),s.jsxs("div",{className:"space-y-1 text-xs",children:[s.jsx("div",{children:"• 线圈写入: 单个布尔值"}),s.jsx("div",{children:"• 寄存器写入: 单个16位数值"}),s.jsx("div",{children:"• 地址范围: 通常0-65535"}),s.jsx("div",{children:"• 批量操作: 支持读取多个连续地址"})]})]})]})})]}),h&&s.jsx("div",{className:`p-4 rounded-lg ${h.includes("失败")||h.includes("错误")?"bg-red-50 text-red-700 border border-red-200":"bg-green-50 text-green-700 border border-green-200"}`,children:s.jsxs("div",{className:"flex items-center",children:[s.jsx("span",{className:"text-lg mr-2",children:h.includes("失败")||h.includes("错误")?"❌":"✅"}),h]})})]})}function Nh({agvStatus:i}){if(!i)return s.jsxs("div",{className:"flex flex-col items-center justify-center h-96 text-gray-500",children:[s.jsx("div",{className:"text-6xl mb-4",children:"📊"}),s.jsx("div",{className:"text-xl font-medium mb-2",children:"暂无状态数据"}),s.jsx("div",{className:"text-sm",children:"请先连接AGV设备并订阅状态更新"})]});const d=A=>({0:{text:"待机",color:"text-gray-600 bg-gray-100",icon:"⏸️"},1:{text:"手动",color:"text-yellow-700 bg-yellow-100",icon:"🔧"},2:{text:"半自动",color:"text-blue-700 bg-blue-100",icon:"🔄"},3:{text:"自动",color:"text-green-700 bg-green-100",icon:"🤖"},4:{text:"示教",color:"text-purple-700 bg-purple-100",icon:"📚"},5:{text:"服务",color:"text-indigo-700 bg-indigo-100",icon:"🔧"},6:{text:"维修",color:"text-red-700 bg-red-100",icon:"⚠️"}})[A]||{text:"未知",color:"text-gray-600 bg-gray-100",icon:"❓"},h=A=>A===0?{text:"正常",color:"text-green-700 bg-green-100",icon:"✅"}:{text:`事件: ${A}`,color:"text-red-700 bg-red-100",icon:"⚠️"},x=A=>A>.7?{color:"text-green-600",bgColor:"bg-green-500",icon:"🔋"}:A>.3?{color:"text-yellow-600",bgColor:"bg-yellow-500",icon:"🪫"}:{color:"text-red-600",bgColor:"bg-red-500",icon:"🔴"},p=d(i.workMode||0),S=h(i.eventCode||0),k=x(i.batteryPercent||0);return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"实时状态"}),s.jsxs("div",{className:"text-sm text-gray-500",children:["最后更新: ",new Date().toLocaleTimeString()]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[s.jsxs("div",{className:"card",children:[s.jsxs("div",{className:"flex items-center justify-between mb-3",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"工作模式"}),s.jsx("span",{className:"text-2xl",children:p.icon})]}),s.jsx("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${p.color}`,children:p.text}),s.jsxs("div",{className:"mt-2 text-xs text-gray-500",children:["模式代码: ",i.workMode||0]})]}),s.jsxs("div",{className:"card",children:[s.jsxs("div",{className:"flex items-center justify-between mb-3",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"电池状态"}),s.jsx("span",{className:"text-2xl",children:k.icon})]}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"flex-1 bg-gray-200 rounded-full h-3",children:s.jsx("div",{className:`h-3 rounded-full transition-all duration-300 ${k.bgColor}`,style:{width:`${Math.max((i.batteryPercent||0)*100,2)}%`}})}),s.jsxs("span",{className:`font-bold text-lg ${k.color}`,children:[((i.batteryPercent||0)*100).toFixed(1),"%"]})]}),s.jsxs("div",{className:"mt-2 text-xs text-gray-500",children:["电池电压: ",(i.batteryVoltage||0).toFixed(2),"V"]})]}),s.jsxs("div",{className:"card",children:[s.jsxs("div",{className:"flex items-center justify-between mb-3",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"系统状态"}),s.jsx("span",{className:"text-2xl",children:S.icon})]}),s.jsx("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${S.color}`,children:S.text}),s.jsxs("div",{className:"mt-2 text-xs text-gray-500",children:["事件代码: ",i.eventCode||0]})]})]}),s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"📍"}),"位置信息"]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-blue-600 mb-1",children:(i.posX||0).toFixed(3)}),s.jsx("div",{className:"text-sm text-gray-600",children:"X坐标 (米)"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-blue-600 mb-1",children:(i.posY||0).toFixed(3)}),s.jsx("div",{className:"text-sm text-gray-600",children:"Y坐标 (米)"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-blue-600 mb-1",children:(i.angle||0).toFixed(3)}),s.jsx("div",{className:"text-sm text-gray-600",children:"角度 (弧度)"})]})]}),s.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200",children:s.jsxs("div",{className:"text-sm text-gray-600",children:["角度转换: ",((i.angle||0)*180/Math.PI).toFixed(1),"°"]})})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"⚡"}),"速度状态"]}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-gray-600",children:"X方向速度"}),s.jsxs("span",{className:"font-mono text-lg text-blue-600",children:[(i.velX||0).toFixed(3)," m/s"]})]}),s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-gray-600",children:"Y方向速度"}),s.jsxs("span",{className:"font-mono text-lg text-blue-600",children:[(i.velY||0).toFixed(3)," m/s"]})]}),s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-gray-600",children:"角速度"}),s.jsxs("span",{className:"font-mono text-lg text-blue-600",children:[(i.angVel||0).toFixed(3)," rad/s"]})]}),s.jsx("div",{className:"pt-2 border-t border-gray-200",children:s.jsxs("div",{className:"text-xs text-gray-500",children:["角速度转换: ",((i.angVel||0)*180/Math.PI).toFixed(1),"°/s"]})})]})]}),s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"🎯"}),"路径信息"]}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-gray-600",children:"最后点位ID"}),s.jsx("span",{className:"font-mono text-lg text-green-600",children:i.lastPointID||0})]}),s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-gray-600",children:"最后路径ID"}),s.jsx("span",{className:"font-mono text-lg text-green-600",children:i.lastPathID||0})]}),s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-gray-600",children:"序列ID"}),s.jsx("span",{className:"font-mono text-lg text-green-600",children:i.pointSequenceID||0})]}),s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{className:"text-gray-600",children:"置信度"}),s.jsxs("span",{className:"font-mono text-lg text-green-600",children:[((i.confidence||0)*100).toFixed(1),"%"]})]})]})]})]}),s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"🔧"}),"其他状态"]}),s.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[s.jsxs("div",{className:"bg-gray-50 p-3 rounded",children:[s.jsx("div",{className:"font-medium text-gray-900 mb-1",children:"AGV状态"}),s.jsx("div",{className:"font-mono text-blue-600",children:i.agvState||0})]}),s.jsxs("div",{className:"bg-gray-50 p-3 rounded",children:[s.jsx("div",{className:"font-medium text-gray-900 mb-1",children:"能力设置"}),s.jsx("div",{className:"font-mono text-blue-600",children:i.capabilitySetStatus||0})]}),s.jsxs("div",{className:"bg-gray-50 p-3 rounded",children:[s.jsx("div",{className:"font-medium text-gray-900 mb-1",children:"订单ID"}),s.jsx("div",{className:"font-mono text-blue-600",children:i.orderID||0})]}),s.jsxs("div",{className:"bg-gray-50 p-3 rounded",children:[s.jsx("div",{className:"font-medium text-gray-900 mb-1",children:"充电状态"}),s.jsx("div",{className:"font-mono text-blue-600",children:i.chargingStatus||0})]}),s.jsxs("div",{className:"bg-gray-50 p-3 rounded",children:[s.jsx("div",{className:"font-medium text-gray-900 mb-1",children:"任务键"}),s.jsx("div",{className:"font-mono text-blue-600",children:i.taskKey||0})]}),s.jsxs("div",{className:"bg-gray-50 p-3 rounded",children:[s.jsx("div",{className:"font-medium text-gray-900 mb-1",children:"电池电流"}),s.jsxs("div",{className:"font-mono text-blue-600",children:[(i.batteryCurrent||0).toFixed(2),"A"]})]}),s.jsxs("div",{className:"bg-gray-50 p-3 rounded",children:[s.jsx("div",{className:"font-medium text-gray-900 mb-1",children:"事件等级"}),s.jsx("div",{className:"font-mono text-blue-600",children:i.eventLevel||0})]})]})]}),s.jsxs("div",{className:"card bg-gray-50",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsxs("h3",{className:"text-lg font-semibold text-gray-900 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"📋"}),"原始数据"]}),s.jsx("button",{onClick:()=>{navigator.clipboard.writeText(JSON.stringify(i,null,2))},className:"btn btn-secondary btn-sm",children:"复制JSON"})]}),s.jsx("pre",{className:"text-xs text-gray-700 bg-white p-4 rounded border overflow-x-auto",children:JSON.stringify(i,null,2)})]})]})}function Sh(){var te,I,le,ae,_,V;const[i,d]=v.useState(null),[h,x]=v.useState(!0),[p,S]=v.useState(null),[k,A]=v.useState(null),[b,f]=v.useState(!1),[g,C]=v.useState(!1);v.useEffect(()=>{M()},[]);const M=async()=>{try{x(!0),S(null);const u=await ye.getTaskConfig();u.success&&u.data?d(u.data.config):S("获取配置失败")}catch(u){console.error("加载配置失败:",u),S(`加载配置失败: ${u.message}`)}finally{x(!1)}},J=async()=>{if(!i)return!1;try{C(!0);const u=await ye.validateTaskConfig({config:i});return u.success&&u.data?u.data.valid?(A("配置验证通过"),S(null),!0):(S(`配置验证失败: ${u.data.error}`),!1):(S("配置验证失败"),!1)}catch(u){return console.error("验证配置失败:",u),S(`验证配置失败: ${u.message}`),!1}finally{C(!1)}},z=async()=>{if(!(!i||!await J()))try{f(!0),S(null),(await ye.saveTaskConfig({config:i})).success?A("配置保存成功"):S("配置保存失败")}catch(q){console.error("保存配置失败:",q),S(`保存配置失败: ${q.message}`)}finally{f(!1)}},D=()=>{M(),A("已重置为默认配置"),S(null)},j=(u,q)=>{if(!i)return;const X=u.split("."),Y=JSON.parse(JSON.stringify(i));let L=Y;for(let K=0;K<X.length-1;K++)L[X[K]]||(L[X[K]]={}),L=L[X[K]];L[X[X.length-1]]=q,d(Y),A(null),S(null)};return h?s.jsx("div",{className:"flex items-center justify-center p-8",children:s.jsx("div",{className:"text-gray-500",children:"加载配置中..."})}):i?s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"任务配置管理"}),s.jsxs("div",{className:"flex space-x-3",children:[s.jsx("button",{onClick:J,disabled:g,className:"btn btn-secondary",children:g?"验证中...":"验证配置"}),s.jsx("button",{onClick:D,disabled:b||g,className:"btn btn-secondary",children:"重置默认"}),s.jsx("button",{onClick:z,disabled:b||g,className:"btn btn-primary",children:b?"保存中...":"保存配置"})]})]}),k&&s.jsx("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg",children:k}),p&&s.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",children:p}),s.jsxs("div",{className:"card",children:[s.jsx("h3",{className:"text-lg font-semibold mb-4",children:"断头数据源配置"}),s.jsx("div",{className:"text-sm text-gray-600 mb-4",children:"配置自动看车系统获取断头数据的方式。可选择MES API或MySQL数据库作为数据源。"}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"数据源类型"}),s.jsxs("select",{value:i.spindleDataSource||"mes",onChange:u=>j("spindleDataSource",u.target.value),className:"input",children:[s.jsx("option",{value:"mes",children:"MES API"}),s.jsx("option",{value:"mysql",children:"MySQL数据库"})]})]}),i.spindleDataSource==="mysql"&&s.jsxs("div",{className:"space-y-4 p-4 bg-gray-50 rounded-lg",children:[s.jsx("h4",{className:"text-md font-medium text-gray-900",children:"MySQL数据源配置"}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"主机地址"}),s.jsx("input",{type:"text",value:((te=i.mysqlSpindleConfig)==null?void 0:te.host)||"localhost",onChange:u=>j("mysqlSpindleConfig.host",u.target.value),className:"input",placeholder:"localhost"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"端口"}),s.jsx("input",{type:"text",value:((I=i.mysqlSpindleConfig)==null?void 0:I.port)||"3306",onChange:u=>j("mysqlSpindleConfig.port",u.target.value),className:"input",placeholder:"3306"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"用户名"}),s.jsx("input",{type:"text",value:((le=i.mysqlSpindleConfig)==null?void 0:le.username)||"agv_user",onChange:u=>j("mysqlSpindleConfig.username",u.target.value),className:"input",placeholder:"agv_user"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"密码"}),s.jsx("input",{type:"password",value:((ae=i.mysqlSpindleConfig)==null?void 0:ae.password)||"",onChange:u=>j("mysqlSpindleConfig.password",u.target.value),className:"input",placeholder:"请输入密码"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"数据库名"}),s.jsx("input",{type:"text",value:((_=i.mysqlSpindleConfig)==null?void 0:_.database)||"spindle",onChange:u=>j("mysqlSpindleConfig.database",u.target.value),className:"input",placeholder:"spindle"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"连接超时 (如: 30s)"}),s.jsx("input",{type:"text",value:((V=i.mysqlSpindleConfig)==null?void 0:V.timeout)||"30s",onChange:u=>j("mysqlSpindleConfig.timeout",u.target.value),className:"input",placeholder:"30s"})]})]}),s.jsx("div",{className:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:s.jsxs("div",{className:"text-sm text-blue-700",children:[s.jsx("strong",{children:"MySQL数据源说明："}),s.jsxs("ul",{className:"mt-2 ml-4 list-disc",children:[s.jsxs("li",{children:["表命名规则：data","{机器号}","_disconnection（如：data61_disconnection）"]}),s.jsx("li",{children:'通过deviceId字段判断L/R侧面（"left"/"right"）'}),s.jsx("li",{children:"查询条件：endTime IS NULL（未结束的断头）"}),s.jsx("li",{children:"需要确保数据库连接正常且具有相应权限"})]})]})})]}),i.spindleDataSource==="mes"&&s.jsx("div",{className:"p-3 bg-blue-50 border border-blue-200 rounded-lg",children:s.jsxs("div",{className:"text-sm text-blue-700",children:[s.jsx("strong",{children:"MES API说明："}),"系统将使用默认的MES API端点获取断头数据，通过锭号范围过滤L/R侧面（R侧：0-600，L侧：601-1200）。"]})})]})]}),s.jsxs("div",{className:"card",children:[s.jsx("h3",{className:"text-lg font-semibold mb-4",children:"允许的细纱机面配置"}),s.jsx("div",{className:"text-sm text-gray-600 mb-4",children:"配置AGV允许工作的细纱机面范围。如果不勾选任何机面，表示允许所有机面工作。 此配置将用于ZMQ状态上报中的AllowList字段。"}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-3",children:[s.jsx("h4",{className:"text-md font-medium",children:"机面选择"}),s.jsxs("div",{className:"flex space-x-2",children:[s.jsx("button",{onClick:()=>{const u=[];for(let q=42;q<=63;q++)u.push(`${q}L`,`${q}R`);j("allowedMachines",u)},className:"btn btn-secondary btn-sm",children:"全选"}),s.jsx("button",{onClick:()=>{j("allowedMachines",[])},className:"btn btn-secondary btn-sm",children:"清空"})]})]}),s.jsx("div",{className:"grid grid-cols-8 gap-3",children:(()=>{const u=[];for(let q=42;q<=63;q++)u.push(`${q}L`,`${q}R`);return u})().map(u=>s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("input",{type:"checkbox",id:`machine-${u}`,className:"w-4 h-4 text-primary-600 rounded",checked:i.allowedMachines.includes(u),onChange:q=>{let X;q.target.checked?X=[...i.allowedMachines,u]:X=i.allowedMachines.filter(Y=>Y!==u),j("allowedMachines",X)}}),s.jsx("label",{htmlFor:`machine-${u}`,className:"text-sm font-medium text-gray-700 cursor-pointer",children:u})]},u))}),s.jsx("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg",children:s.jsxs("div",{className:"text-sm text-gray-700",children:[s.jsx("strong",{children:"当前配置："}),i.allowedMachines.length===0?s.jsx("span",{className:"text-green-600 ml-1",children:"允许所有机面"}):s.jsxs("span",{className:"ml-1",children:["仅允许 ",i.allowedMachines.sort().join(", "),s.jsxs("span",{className:"text-gray-500",children:["（共 ",i.allowedMachines.length," 个机面）"]})]})]})})]})]}),s.jsxs("div",{className:"card",children:[s.jsx("h3",{className:"text-lg font-semibold mb-4",children:"系统信息"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"机器人编号"}),s.jsx("input",{type:"text",className:"input w-full",value:i.system.robot_no,onChange:u=>j("system.robot_no",u.target.value),placeholder:"AGV001"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"系统描述"}),s.jsx("input",{type:"text",className:"input w-full",value:i.system.description,onChange:u=>j("system.description",u.target.value),placeholder:"AGV导航控制系统"})]})]})]}),s.jsxs("div",{className:"card",children:[s.jsx("h3",{className:"text-lg font-semibold mb-4",children:"停车点配置"}),s.jsx("div",{className:"text-sm text-gray-600 mb-4",children:"配置任务完成后AGV的停车位置坐标。这些参数将用于一键上线功能的激光导航初始化。"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"停车点ID"}),s.jsx("input",{type:"number",className:"input w-full",value:i.parkingPoint.id,onChange:u=>j("parkingPoint.id",parseInt(u.target.value)||1),placeholder:"1",min:1})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"X坐标 (米)"}),s.jsx("input",{type:"number",step:"0.001",className:"input w-full",value:i.parkingPoint.x,onChange:u=>j("parkingPoint.x",parseFloat(u.target.value)||0),placeholder:"0.000"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Y坐标 (米)"}),s.jsx("input",{type:"number",step:"0.001",className:"input w-full",value:i.parkingPoint.y,onChange:u=>j("parkingPoint.y",parseFloat(u.target.value)||0),placeholder:"0.000"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"角度 (弧度)"}),s.jsx("input",{type:"number",step:"0.001",className:"input w-full",value:i.parkingPoint.angle,onChange:u=>j("parkingPoint.angle",parseFloat(u.target.value)||0),placeholder:"0.000"})]})]})]}),s.jsxs("div",{className:"card",children:[s.jsx("h3",{className:"text-lg font-semibold mb-4",children:"MySQL数据库配置"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"主机地址"}),s.jsx("input",{type:"text",className:"input w-full",value:i.mysql.host,onChange:u=>j("mysql.host",u.target.value),placeholder:"localhost"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"端口"}),s.jsx("input",{type:"text",className:"input w-full",value:i.mysql.port,onChange:u=>j("mysql.port",u.target.value),placeholder:"3306"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"用户名"}),s.jsx("input",{type:"text",className:"input w-full",value:i.mysql.username,onChange:u=>j("mysql.username",u.target.value),placeholder:"root"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"密码"}),s.jsx("input",{type:"password",className:"input w-full",value:i.mysql.password,onChange:u=>j("mysql.password",u.target.value),placeholder:"留空表示无密码"})]}),s.jsxs("div",{className:"md:col-span-2",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"数据库名"}),s.jsx("input",{type:"text",className:"input w-full",value:i.mysql.database,onChange:u=>j("mysql.database",u.target.value),placeholder:"agv_system"})]})]})]}),s.jsxs("div",{className:"card",children:[s.jsx("h3",{className:"text-lg font-semibold mb-4",children:"重试设置"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"最大重试次数"}),s.jsx("input",{type:"number",className:"input w-full",value:i.maxRetries,onChange:u=>j("maxRetries",parseInt(u.target.value)||0),min:0,max:10})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"重试延迟基准 (如: 5s, 1m)"}),s.jsx("input",{type:"text",className:"input w-full",value:i.retryDelayBase,onChange:u=>j("retryDelayBase",u.target.value),placeholder:"5s"})]})]})]}),s.jsxs("div",{className:"card",children:[s.jsx("h3",{className:"text-lg font-semibold mb-4",children:"日志设置"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"日志级别"}),s.jsxs("select",{className:"input w-full",value:i.logging.level,onChange:u=>j("logging.level",u.target.value),children:[s.jsx("option",{value:"DEBUG",children:"DEBUG"}),s.jsx("option",{value:"INFO",children:"INFO"}),s.jsx("option",{value:"WARN",children:"WARN"}),s.jsx("option",{value:"ERROR",children:"ERROR"}),s.jsx("option",{value:"FATAL",children:"FATAL"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"日志文件路径"}),s.jsx("input",{type:"text",className:"input w-full",value:i.logging.filePath,onChange:u=>j("logging.filePath",u.target.value),placeholder:"logs/agv_nav.log"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"文件最大大小 (MB)"}),s.jsx("input",{type:"number",className:"input w-full",value:i.logging.maxSize,onChange:u=>j("logging.maxSize",parseInt(u.target.value)||0),min:1,max:100})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"保留文件数量"}),s.jsx("input",{type:"number",className:"input w-full",value:i.logging.maxBackups,onChange:u=>j("logging.maxBackups",parseInt(u.target.value)||0),min:1,max:30})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"保留天数"}),s.jsx("input",{type:"number",className:"input w-full",value:i.logging.maxAge,onChange:u=>j("logging.maxAge",parseInt(u.target.value)||0),min:1,max:365})]})]}),s.jsxs("div",{className:"mt-4 space-y-3",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"font-medium text-gray-900",children:"启用控制台输出"}),s.jsx("div",{className:"text-sm text-gray-500",children:"在控制台显示日志"})]}),s.jsx("input",{type:"checkbox",className:"w-4 h-4 text-primary-600 rounded",checked:i.logging.enableConsole,onChange:u=>j("logging.enableConsole",u.target.checked)})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"font-medium text-gray-900",children:"启用文件输出"}),s.jsx("div",{className:"text-sm text-gray-500",children:"将日志写入文件"})]}),s.jsx("input",{type:"checkbox",className:"w-4 h-4 text-primary-600 rounded",checked:i.logging.enableFile,onChange:u=>j("logging.enableFile",u.target.checked)})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"font-medium text-gray-900",children:"结构化日志"}),s.jsx("div",{className:"text-sm text-gray-500",children:"使用JSON格式记录日志"})]}),s.jsx("input",{type:"checkbox",className:"w-4 h-4 text-primary-600 rounded",checked:i.logging.enableStructured,onChange:u=>j("logging.enableStructured",u.target.checked)})]})]})]}),s.jsxs("div",{className:"card",children:[s.jsx("h3",{className:"text-lg font-semibold mb-4",children:"PLC地址配置"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"控制请求地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.controlRequestAddress,onChange:u=>j("plcAddresses.controlRequestAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"控制确认地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.controlConfirmAddress,onChange:u=>j("plcAddresses.controlConfirmAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"任务开始地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.taskStartAddress,onChange:u=>j("plcAddresses.taskStartAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"就绪状态地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.readyAddress,onChange:u=>j("plcAddresses.readyAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"方向地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.directionAddress,onChange:u=>j("plcAddresses.directionAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"距离地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.distanceAddress,onChange:u=>j("plcAddresses.distanceAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"滚筒地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.rollerAddress,onChange:u=>j("plcAddresses.rollerAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"完成地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.completeAddress,onChange:u=>j("plcAddresses.completeAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"验证方向地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.verifyDirectionAddress,onChange:u=>j("plcAddresses.verifyDirectionAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"验证滚筒地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.verifyRollerAddress,onChange:u=>j("plcAddresses.verifyRollerAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"验证距离地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.verifyDistanceAddress,onChange:u=>j("plcAddresses.verifyDistanceAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"验证完成地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.verificationCompleteAddress,onChange:u=>j("plcAddresses.verificationCompleteAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"工作状态地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.workStatusAddress,onChange:u=>j("plcAddresses.workStatusAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"机器人启动信号地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.robotStartSignalAddress,onChange:u=>j("plcAddresses.robotStartSignalAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"机器完成地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.machineCompleteAddress,onChange:u=>j("plcAddresses.machineCompleteAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"返回原点地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.returnToOriginAddress,onChange:u=>j("plcAddresses.returnToOriginAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"原点方向地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.originDirectionAddress,onChange:u=>j("plcAddresses.originDirectionAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"原点代码值地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.originCodeValueAddress,onChange:u=>j("plcAddresses.originCodeValueAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"原点到达地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.originArrivalAddress,onChange:u=>j("plcAddresses.originArrivalAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"控制交接地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.controlHandoverAddress,onChange:u=>j("plcAddresses.controlHandoverAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"掉头地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.turnAroundAddress,onChange:u=>j("plcAddresses.turnAroundAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"掉头完成地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.turnCompleteAddress,onChange:u=>j("plcAddresses.turnCompleteAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"出巷道地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.exitLaneAddress,onChange:u=>j("plcAddresses.exitLaneAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"巷道出口完成地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.plcAddresses.laneExitCompleteAddress,onChange:u=>j("plcAddresses.laneExitCompleteAddress",parseInt(u.target.value)||0),min:0,max:9999})]})]})]}),s.jsxs("div",{className:"card",children:[s.jsx("h3",{className:"text-lg font-semibold mb-4",children:"超时设置"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"导航超时 (如: 20m0s)"}),s.jsx("input",{type:"text",className:"input w-full",value:i.timeouts.navigationTimeout,onChange:u=>j("timeouts.navigationTimeout",u.target.value),placeholder:"20m0s"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"导航检查间隔"}),s.jsx("input",{type:"text",className:"input w-full",value:i.timeouts.navigationCheckInterval,onChange:u=>j("timeouts.navigationCheckInterval",u.target.value),placeholder:"2s"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"PLC就绪超时"}),s.jsx("input",{type:"text",className:"input w-full",value:i.timeouts.plcReadyTimeout,onChange:u=>j("timeouts.plcReadyTimeout",u.target.value),placeholder:"5m0s"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"PLC就绪检查间隔"}),s.jsx("input",{type:"text",className:"input w-full",value:i.timeouts.plcReadyCheckInterval,onChange:u=>j("timeouts.plcReadyCheckInterval",u.target.value),placeholder:"500ms"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"PLC工作超时"}),s.jsx("input",{type:"text",className:"input w-full",value:i.timeouts.plcWorkTimeout,onChange:u=>j("timeouts.plcWorkTimeout",u.target.value),placeholder:"5m0s"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"PLC工作检查间隔"}),s.jsx("input",{type:"text",className:"input w-full",value:i.timeouts.plcWorkCheckInterval,onChange:u=>j("timeouts.plcWorkCheckInterval",u.target.value),placeholder:"3s"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"掉头超时"}),s.jsx("input",{type:"text",className:"input w-full",value:i.timeouts.turnAroundTimeout,onChange:u=>j("timeouts.turnAroundTimeout",u.target.value),placeholder:"10m0s"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"掉头检查间隔"}),s.jsx("input",{type:"text",className:"input w-full",value:i.timeouts.turnAroundCheckInterval,onChange:u=>j("timeouts.turnAroundCheckInterval",u.target.value),placeholder:"3s"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"巷道出口超时"}),s.jsx("input",{type:"text",className:"input w-full",value:i.timeouts.laneExitTimeout,onChange:u=>j("timeouts.laneExitTimeout",u.target.value),placeholder:"60s"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"巷道出口检查间隔"}),s.jsx("input",{type:"text",className:"input w-full",value:i.timeouts.laneExitCheckInterval,onChange:u=>j("timeouts.laneExitCheckInterval",u.target.value),placeholder:"500ms"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"原点到达超时"}),s.jsx("input",{type:"text",className:"input w-full",value:i.timeouts.originArrivalTimeout,onChange:u=>j("timeouts.originArrivalTimeout",u.target.value),placeholder:"10m0s"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"原点到达检查间隔"}),s.jsx("input",{type:"text",className:"input w-full",value:i.timeouts.originArrivalCheckInterval,onChange:u=>j("timeouts.originArrivalCheckInterval",u.target.value),placeholder:"3s"})]})]})]}),s.jsxs("div",{className:"card",children:[s.jsx("h3",{className:"text-lg font-semibold mb-4",children:"巷道配置"}),s.jsxs("div",{className:"mb-6",children:[s.jsx("h4",{className:"text-md font-medium mb-3",children:"巷道规则"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"默认配对规则"}),s.jsx("input",{type:"text",className:"input w-full",value:i.lane_configuration.lane_rules.default_pairing,onChange:u=>j("lane_configuration.lane_rules.default_pairing",u.target.value),placeholder:"nL_with_n-1R"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"进入侧"}),s.jsxs("select",{className:"input w-full",value:i.lane_configuration.lane_rules.entry_side,onChange:u=>j("lane_configuration.lane_rules.entry_side",u.target.value),children:[s.jsx("option",{value:"L",children:"左侧 (L)"}),s.jsx("option",{value:"R",children:"右侧 (R)"})]})]})]})]}),s.jsxs("div",{children:[s.jsxs("div",{className:"flex items-center justify-between mb-3",children:[s.jsx("h4",{className:"text-md font-medium",children:"特殊巷道"}),s.jsx("button",{onClick:()=>{const u={machine:"",type:"central_aisle",strategy:"single_only",reason:""},q=[...i.lane_configuration.special_lanes,u];j("lane_configuration.special_lanes",q)},className:"btn btn-secondary btn-sm",children:"添加特殊巷道"})]}),s.jsx("div",{className:"space-y-4",children:i.lane_configuration.special_lanes.map((u,q)=>s.jsxs("div",{className:"border border-gray-200 rounded-lg p-4",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"机器编号"}),s.jsx("input",{type:"text",className:"input w-full",value:u.machine,onChange:X=>{const Y=[...i.lane_configuration.special_lanes];Y[q].machine=X.target.value,j("lane_configuration.special_lanes",Y)},placeholder:"61R"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"类型"}),s.jsxs("select",{className:"input w-full",value:u.type,onChange:X=>{const Y=[...i.lane_configuration.special_lanes];Y[q].type=X.target.value,j("lane_configuration.special_lanes",Y)},children:[s.jsx("option",{value:"central_aisle",children:"中央通道"}),s.jsx("option",{value:"skip",children:"跳过"}),s.jsx("option",{value:"special",children:"特殊处理"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"策略"}),s.jsxs("select",{className:"input w-full",value:u.strategy,onChange:X=>{const Y=[...i.lane_configuration.special_lanes];Y[q].strategy=X.target.value,j("lane_configuration.special_lanes",Y)},children:[s.jsx("option",{value:"single_only",children:"仅单侧"}),s.jsx("option",{value:"skip",children:"跳过"}),s.jsx("option",{value:"custom",children:"自定义"})]})]}),s.jsx("div",{className:"flex items-end",children:s.jsx("button",{onClick:()=>{const X=i.lane_configuration.special_lanes.filter((Y,L)=>L!==q);j("lane_configuration.special_lanes",X)},className:"btn btn-error btn-sm",children:"删除"})})]}),s.jsxs("div",{className:"mt-3",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"原因说明"}),s.jsx("input",{type:"text",className:"input w-full",value:u.reason,onChange:X=>{const Y=[...i.lane_configuration.special_lanes];Y[q].reason=X.target.value,j("lane_configuration.special_lanes",Y)},placeholder:"说明为什么这个巷道需要特殊处理"})]})]},q))})]})]}),s.jsxs("div",{className:"card",children:[s.jsx("h3",{className:"text-lg font-semibold mb-4",children:"安全转向配置"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"最小锭子数"}),s.jsx("input",{type:"number",className:"input w-full",value:i.safeTurnConfig.minSpindle,onChange:u=>j("safeTurnConfig.minSpindle",parseInt(u.target.value)||0),min:1,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"最大锭子数"}),s.jsx("input",{type:"number",className:"input w-full",value:i.safeTurnConfig.maxSpindle,onChange:u=>j("safeTurnConfig.maxSpindle",parseInt(u.target.value)||0),min:1,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"开始地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.safeTurnConfig.startAddress,onChange:u=>j("safeTurnConfig.startAddress",parseInt(u.target.value)||0),min:0,max:9999})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"结束地址"}),s.jsx("input",{type:"number",className:"input w-full",value:i.safeTurnConfig.endAddress,onChange:u=>j("safeTurnConfig.endAddress",parseInt(u.target.value)||0),min:0,max:9999})]})]})]}),s.jsxs("div",{className:"card",children:[s.jsx("h3",{className:"text-lg font-semibold mb-4",children:"缓存配置"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"默认间隔 (分钟)"}),s.jsx("input",{type:"number",className:"input w-full",value:i.cache.default_interval,onChange:u=>j("cache.default_interval",parseInt(u.target.value)||0),min:1,max:60})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"过期时间 (分钟)"}),s.jsx("input",{type:"number",className:"input w-full",value:i.cache.expiry_minutes,onChange:u=>j("cache.expiry_minutes",parseInt(u.target.value)||0),min:1,max:1440})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"最大重试次数"}),s.jsx("input",{type:"number",className:"input w-full",value:i.cache.max_retries,onChange:u=>j("cache.max_retries",parseInt(u.target.value)||0),min:0,max:10})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"重试间隔 (秒)"}),s.jsx("input",{type:"number",className:"input w-full",value:i.cache.retry_interval_seconds,onChange:u=>j("cache.retry_interval_seconds",parseInt(u.target.value)||0),min:1,max:60})]}),s.jsxs("div",{className:"md:col-span-2",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"数据存储路径"}),s.jsx("input",{type:"text",className:"input w-full",value:i.cache.data_storage_path,onChange:u=>j("cache.data_storage_path",u.target.value),placeholder:"backend/data/cache_data.json"})]})]}),s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"默认缓存机器列表"}),s.jsx("div",{className:"flex flex-wrap gap-2 mb-2",children:i.cache.default_machines.map((u,q)=>s.jsxs("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800",children:[u,s.jsx("button",{onClick:()=>{const X=i.cache.default_machines.filter((Y,L)=>L!==q);j("cache.default_machines",X)},className:"ml-2 text-blue-600 hover:text-blue-800",children:"×"})]},q))}),s.jsxs("div",{className:"flex space-x-2",children:[s.jsx("input",{type:"text",className:"input flex-1",placeholder:"输入机器编号 (如: 61)",onKeyPress:u=>{if(u.key==="Enter"){const q=u.target;if(q.value.trim()&&!i.cache.default_machines.includes(q.value.trim())){const X=[...i.cache.default_machines,q.value.trim()];j("cache.default_machines",X),q.value=""}}}}),s.jsx("button",{onClick:u=>{const q=u.target.previousElementSibling;if(q.value.trim()&&!i.cache.default_machines.includes(q.value.trim())){const X=[...i.cache.default_machines,q.value.trim()];j("cache.default_machines",X),q.value=""}},className:"btn btn-secondary",children:"添加"})]})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"font-medium text-gray-900",children:"启用缓存"}),s.jsx("div",{className:"text-sm text-gray-500",children:"开启数据缓存功能"})]}),s.jsx("input",{type:"checkbox",className:"w-4 h-4 text-primary-600 rounded",checked:i.cache.enable,onChange:u=>j("cache.enable",u.target.checked)})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"font-medium text-gray-900",children:"启用文件存储"}),s.jsx("div",{className:"text-sm text-gray-500",children:"将缓存数据保存到文件"})]}),s.jsx("input",{type:"checkbox",className:"w-4 h-4 text-primary-600 rounded",checked:i.cache.enable_file_storage,onChange:u=>j("cache.enable_file_storage",u.target.checked)})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"font-medium text-gray-900",children:"启动时自动开始"}),s.jsx("div",{className:"text-sm text-gray-500",children:"系统启动时自动开启缓存"})]}),s.jsx("input",{type:"checkbox",className:"w-4 h-4 text-primary-600 rounded",checked:i.cache.auto_start_on_boot,onChange:u=>j("cache.auto_start_on_boot",u.target.checked)})]})]})]})]}):s.jsx("div",{className:"flex items-center justify-center p-8",children:s.jsx("div",{className:"text-red-500",children:"配置加载失败"})})}function wh(){const[i,d]=v.useState("agv"),h=Tm(),[x,p]=v.useState({authCode:"",ip:"",port:""}),[S,k]=v.useState(!1),[A,b]=v.useState(!1),[f,g]=v.useState(""),[C,M]=v.useState({ip:"",port:""}),[J,z]=v.useState(!1),[D,j]=v.useState(!1),[te,I]=v.useState(""),[le,ae]=v.useState(null),[_,V]=v.useState(!0);v.useEffect(()=>{(async()=>{try{V(!0);const Q=await ye.getDefaultConfig();Q.success&&Q.data&&(ae(Q.data),p({authCode:Q.data.agv.authCode,ip:Q.data.agv.ip,port:Q.data.agv.port}),M({ip:Q.data.plc.ip,port:Q.data.plc.port}))}catch(Q){console.error("加载默认配置失败:",Q);const E={agv:{authCode:"8e8e48298e28ba40b18096ed0acfee74",ip:"***************",port:"17804"},plc:{ip:"**************",port:"502"}};ae(E),p(E.agv),M(E.plc)}finally{V(!1)}})()},[]);const u=[{id:"agv",label:"AGV设置",icon:"🚛"},{id:"plc",label:"PLC设置",icon:"⚙️"},{id:"config",label:"系统配置",icon:"📋"},{id:"general",label:"通用设置",icon:"🔧"}],q=async()=>{b(!0),g("");try{await ye.connectAGV(x),k(!0),g("AGV连接成功"),await ye.subscribeAGV(),g("AGV连接成功，状态订阅已激活")}catch(w){g(`连接失败: ${w.message}`)}finally{b(!1)}},X=async()=>{b(!0);try{await ye.disconnectAGV(),k(!1),g("AGV已断开连接")}catch(w){g(`断开连接失败: ${w.message}`)}finally{b(!1)}},Y=async()=>{j(!0),I("");try{await ye.connectPLC(C),z(!0),I("PLC连接成功")}catch(w){I(`连接失败: ${w.message}`)}finally{j(!1)}},L=async()=>{j(!0);try{await ye.disconnectPLC(),z(!1),I("PLC已断开连接")}catch(w){I(`断开连接失败: ${w.message}`)}finally{j(!1)}},K=async()=>{b(!0),g("");try{const w=await ye.testAGVConnection(x);w.success&&w.data?g(`连接测试${w.data.connected?"成功":"失败"}: ${w.data.message}`):g("连接测试失败")}catch(w){g(`连接测试失败: ${w.message}`)}finally{b(!1)}},ne=async()=>{j(!0),I("");try{const w=await ye.testPLCConnection(C);w.success&&w.data?I(`连接测试${w.data.connected?"成功":"失败"}: ${w.data.message}`):I("连接测试失败")}catch(w){I(`连接测试失败: ${w.message}`)}finally{j(!1)}};return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"系统设置"}),s.jsx("div",{className:"text-sm text-gray-500",children:"在这里配置设备连接和系统参数"})]}),s.jsx("div",{className:"border-b border-gray-200",children:s.jsx("nav",{className:"flex space-x-8","aria-label":"设置标签",children:u.map(w=>s.jsx("button",{onClick:()=>d(w.id),className:`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors ${i===w.id?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:s.jsxs("span",{className:"flex items-center space-x-2",children:[s.jsx("span",{children:w.icon}),s.jsx("span",{children:w.label})]})},w.id))})}),i==="agv"&&s.jsx("div",{className:"space-y-6",children:s.jsxs("div",{className:"card",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h3",{className:"text-lg font-semibold",children:"AGV连接配置"}),s.jsx("div",{className:`status-indicator ${S?"status-connected":"status-disconnected"}`,children:S?"已连接":"未连接"})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"授权码"}),s.jsx("input",{type:"text",className:"input w-full",value:x.authCode,onChange:w=>p(Q=>({...Q,authCode:w.target.value})),disabled:S,placeholder:"32位十六进制授权码"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"IP地址"}),s.jsx("input",{type:"text",className:"input w-full",value:x.ip,onChange:w=>p(Q=>({...Q,ip:w.target.value})),disabled:S,placeholder:"***************"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"端口"}),s.jsx("input",{type:"text",className:"input w-full",value:x.port,onChange:w=>p(Q=>({...Q,port:w.target.value})),disabled:S,placeholder:"17804"})]})]}),s.jsxs("div",{className:"flex space-x-3",children:[S?s.jsx("button",{onClick:X,disabled:A,className:"btn btn-error",children:"断开连接"}):s.jsxs(s.Fragment,{children:[s.jsx("button",{onClick:q,disabled:A||_,className:"btn btn-primary",children:A?"连接中...":"连接AGV"}),s.jsx("button",{onClick:K,disabled:A||_,className:"btn btn-secondary",children:A?"测试中...":"测试连接"})]}),s.jsx("button",{onClick:()=>{le&&p({authCode:le.agv.authCode,ip:le.agv.ip,port:le.agv.port})},disabled:S||_||!le,className:"btn btn-secondary",children:"恢复默认"})]}),f&&s.jsx("div",{className:`mt-4 p-3 rounded-lg text-sm ${f.includes("失败")||f.includes("错误")?"bg-red-50 text-red-700 border border-red-200":"bg-green-50 text-green-700 border border-green-200"}`,children:f})]})}),i==="plc"&&s.jsx("div",{className:"space-y-6",children:s.jsxs("div",{className:"card",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h3",{className:"text-lg font-semibold",children:"PLC连接配置"}),s.jsx("div",{className:`status-indicator ${J?"status-connected":"status-disconnected"}`,children:J?"已连接":"未连接"})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"IP地址"}),s.jsx("input",{type:"text",className:"input w-full",value:C.ip,onChange:w=>M(Q=>({...Q,ip:w.target.value})),disabled:J,placeholder:(le==null?void 0:le.plc.ip)||"**************"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"端口"}),s.jsx("input",{type:"text",className:"input w-full",value:C.port,onChange:w=>M(Q=>({...Q,port:w.target.value})),disabled:J,placeholder:"502"})]})]}),s.jsxs("div",{className:"flex space-x-3",children:[J?s.jsx("button",{onClick:L,disabled:D,className:"btn btn-error",children:"断开连接"}):s.jsxs(s.Fragment,{children:[s.jsx("button",{onClick:Y,disabled:D||_,className:"btn btn-primary",children:D?"连接中...":"连接PLC"}),s.jsx("button",{onClick:ne,disabled:D||_,className:"btn btn-secondary",children:D?"测试中...":"测试连接"})]}),s.jsx("button",{onClick:()=>{le&&M({ip:le.plc.ip,port:le.plc.port})},disabled:J||_||!le,className:"btn btn-secondary",children:"恢复默认"})]}),te&&s.jsx("div",{className:`mt-4 p-3 rounded-lg text-sm ${te.includes("失败")||te.includes("错误")?"bg-red-50 text-red-700 border border-red-200":"bg-green-50 text-green-700 border border-green-200"}`,children:te})]})}),i==="config"&&s.jsx(Sh,{}),i==="general"&&s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"card",children:[s.jsx("h3",{className:"text-lg font-semibold mb-4",children:"应用设置"}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"font-medium text-gray-900",children:"启动时自动连接"}),s.jsx("div",{className:"text-sm text-gray-500",children:"应用启动时自动尝试连接设备"})]}),s.jsx("input",{type:"checkbox",className:"w-4 h-4 text-primary-600 rounded",checked:h.autoConnect,onChange:w=>h.setAutoConnect(w.target.checked)})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"font-medium text-gray-900",children:"显示调试信息"}),s.jsx("div",{className:"text-sm text-gray-500",children:"在控制台显示详细的调试日志"})]}),s.jsx("input",{type:"checkbox",className:"w-4 h-4 text-primary-600 rounded",checked:h.showDebugInfo,onChange:w=>h.setShowDebugInfo(w.target.checked)})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"font-medium text-gray-900",children:"保存操作历史"}),s.jsx("div",{className:"text-sm text-gray-500",children:"记录用户操作历史以便追踪"})]}),s.jsx("input",{type:"checkbox",className:"w-4 h-4 text-primary-600 rounded",checked:h.saveOperationHistory,onChange:w=>h.setSaveOperationHistory(w.target.checked)})]})]})]}),s.jsxs("div",{className:"card",children:[s.jsx("h3",{className:"text-lg font-semibold mb-4",children:"实时状态设置"}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"状态更新间隔 (毫秒)"}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("input",{type:"number",className:"input w-32",value:h.statusUpdateInterval,onChange:w=>h.setStatusUpdateInterval(parseInt(w.target.value)||200),min:100,max:5e3,step:100}),s.jsxs("div",{className:"text-sm text-gray-500",children:["当前: ",(1e3/h.statusUpdateInterval).toFixed(1)," FPS"]})]}),s.jsx("div",{className:"mt-2 text-xs text-gray-600",children:"推荐范围: 100-1000ms，值越小更新越快但占用更多资源"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"数据精度 (小数位)"}),s.jsx("input",{type:"number",className:"input w-32",value:h.dataPrecision,onChange:w=>h.setDataPrecision(parseInt(w.target.value)||3),min:0,max:6})]})]})]})]})]})}const Ch=()=>{var ne;const[i,d]=v.useState([]),[h,x]=v.useState("61-60"),[p,S]=v.useState(!1),[k,A]=v.useState(""),[b,f]=v.useState(""),[g,C]=v.useState(null),[M,J]=v.useState(!1),[z,D]=v.useState(""),{taskStatus:j,lastMessage:te}=Kn();v.useEffect(()=>{(async()=>{try{const Q=await ye.getConnectionStatus();Q.success&&Q.data&&C(Q.data)}catch(Q){console.error("加载连接状态失败:",Q)}})()},[]),v.useEffect(()=>{te&&te.type==="connection_status"&&C(te.data)},[te]);const I=[{groupName:"61-60",robots:["61R","61L","60R","60L"]},{groupName:"59-55",robots:["59R","59L","58R","58L","57R","57L","56R","56L","55R","55L"]},{groupName:"54-50",robots:["54R","54L","53R","53L","52R","52L","51R","51L","50R","50L"]}],le=w=>({idle:"空闲",running:"运行中",paused:"已暂停",stopped:"已停止"})[w]||"未知",ae=w=>({idle:"bg-gray-50 text-gray-600",running:"bg-green-50 text-green-600",paused:"bg-yellow-50 text-yellow-600",stopped:"bg-red-50 text-red-600"})[w]||"bg-gray-50 text-gray-600",_=()=>{var w;return((w=I.find(Q=>Q.groupName===h))==null?void 0:w.robots)||[]},V=w=>{d(Q=>Q.includes(w)?Q.filter(E=>E!==w):[...Q,w])},u=async()=>{if(i.length===0){alert("请先选择要看车的细纱机");return}try{A(""),f("");const w=await ye.startWatchTask(i);S(!0),f(`看车任务启动成功: ${JSON.stringify(w)}`),console.log("成功开始看车任务:",i,w)}catch(w){const Q=w.message;A(`开始看车任务失败: ${Q}`),console.error("开始看车任务失败:",w),alert("开始看车任务失败: "+Q)}},q=async()=>{try{A(""),f("");const w=await ye.stopWatchTask();S(!1),f(`看车任务停止成功: ${JSON.stringify(w)}`),console.log("成功停止看车任务",w)}catch(w){const Q=w.message;A(`停止看车任务失败: ${Q}`),console.error("停止看车任务失败:",w),alert("停止看车任务失败: "+Q)}},X=async()=>{J(!0),D("");try{const w=await ye.connectAll();w.success?D("设备连接成功"):D("部分设备连接失败"),console.log("批量连接结果:",w)}catch(w){const Q=w.message;D(`连接失败: ${Q}`),console.error("批量连接失败:",w)}finally{J(!1)}},Y=async()=>{J(!0),D("");try{const w=await ye.disconnectAll();D("设备已断开连接"),console.log("批量断开结果:",w)}catch(w){const Q=w.message;D(`断开连接失败: ${Q}`),console.error("批量断开失败:",w)}finally{J(!1)}},L=async()=>{J(!0),D("");try{const w=await Am();D(`一键上线成功: ${JSON.stringify(w)}`),console.log("一键上线成功:",w)}catch(w){const Q=w.message;D(`一键上线失败: ${Q}`),console.error("一键上线失败:",w)}finally{J(!1)}},K=()=>(g==null?void 0:g.allConnected)&&i.length>0&&!p&&(j==null?void 0:j.status)!=="running";return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"card",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h2",{className:"text-xl font-bold text-gray-900",children:"🔗 设备连接状态"}),s.jsx("div",{className:`px-3 py-1 rounded-full text-sm font-medium ${g!=null&&g.allConnected?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:g!=null&&g.allConnected?"全部已连接":"未完全连接"})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[s.jsxs("div",{className:`p-4 rounded-lg border ${g!=null&&g.agv.connected?"border-green-200 bg-green-50":"border-red-200 bg-red-50"}`,children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-2xl",children:"🚛"}),s.jsxs("div",{children:[s.jsx("div",{className:"font-semibold text-gray-900",children:"AGV"}),s.jsxs("div",{className:"text-sm text-gray-600",children:[g==null?void 0:g.agv.ip,":",g==null?void 0:g.agv.port]})]})]}),s.jsx("div",{className:`w-3 h-3 rounded-full ${g!=null&&g.agv.connected?"bg-green-500":"bg-red-500"}`})]}),s.jsx("div",{className:`mt-2 text-sm font-medium ${g!=null&&g.agv.connected?"text-green-700":"text-red-700"}`,children:g!=null&&g.agv.connected?"已连接":"未连接"})]}),s.jsxs("div",{className:`p-4 rounded-lg border ${g!=null&&g.plc.connected?"border-green-200 bg-green-50":"border-red-200 bg-red-50"}`,children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-2xl",children:"⚙️"}),s.jsxs("div",{children:[s.jsx("div",{className:"font-semibold text-gray-900",children:"PLC"}),s.jsxs("div",{className:"text-sm text-gray-600",children:[g==null?void 0:g.plc.ip,":",g==null?void 0:g.plc.port]})]})]}),s.jsx("div",{className:`w-3 h-3 rounded-full ${g!=null&&g.plc.connected?"bg-green-500":"bg-red-500"}`})]}),s.jsx("div",{className:`mt-2 text-sm font-medium ${g!=null&&g.plc.connected?"text-green-700":"text-red-700"}`,children:g!=null&&g.plc.connected?"已连接":"未连接"})]})]}),s.jsxs("div",{className:"flex items-center space-x-3",children:[g!=null&&g.allConnected?s.jsx("button",{onClick:Y,disabled:M,className:"btn btn-secondary",children:M?"断开中...":"断开所有设备"}):s.jsx("button",{onClick:X,disabled:M,className:"btn btn-primary",children:M?"连接中...":"连接所有设备"}),s.jsx("button",{onClick:()=>{(async()=>{try{const Q=await ye.getConnectionStatus();Q.success&&Q.data&&C(Q.data)}catch(Q){console.error("刷新连接状态失败:",Q)}})()},className:"btn btn-secondary",children:"🔄 刷新状态"})]}),z&&s.jsx("div",{className:`mt-4 p-3 rounded-lg text-sm ${z.includes("失败")||z.includes("错误")?"bg-red-50 text-red-700 border border-red-200":"bg-green-50 text-green-700 border border-green-200"}`,children:z})]}),s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"📊 机器人工作信息"}),j?s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"text-xs text-gray-400 bg-gray-50 p-2 rounded",children:["调试: 状态=",j.status,", 进度=",j.progress,", 总计=",j.total]}),k&&s.jsxs("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded text-sm",children:["❌ ",k]}),b&&s.jsxs("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded text-sm",children:["✅ ",b]}),s.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[s.jsxs("div",{className:`p-4 rounded-lg ${ae(j.status)}`,children:[s.jsx("div",{className:"text-sm text-gray-600",children:"任务状态"}),s.jsx("div",{className:"text-lg font-semibold",children:le(j.status)})]}),s.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"当前细纱机"}),s.jsx("div",{className:"text-lg font-semibold text-blue-600",children:j.current_machine||"准备中..."})]}),s.jsxs("div",{className:"bg-purple-50 p-4 rounded-lg",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"完成进度"}),s.jsxs("div",{className:"text-lg font-semibold text-purple-600",children:[j.progress,"/",j.total]})]}),s.jsxs("div",{className:"bg-orange-50 p-4 rounded-lg",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"剩余数量"}),s.jsx("div",{className:"text-lg font-semibold text-orange-600",children:((ne=j.remaining)==null?void 0:ne.length)||0})]})]}),j.total>0&&s.jsx("div",{className:"w-full bg-gray-200 rounded-full h-3",children:s.jsx("div",{className:"bg-blue-600 h-3 rounded-full transition-all duration-500",style:{width:`${Math.min(j.progress/j.total*100,100)}%`}})}),j.remaining&&j.remaining.length>0&&s.jsxs("div",{className:"mt-4",children:[s.jsx("div",{className:"text-sm text-gray-600 mb-2",children:"待处理细纱机队列:"}),s.jsx("div",{className:"flex flex-wrap gap-2",children:j.remaining.map((w,Q)=>s.jsx("span",{className:"px-2 py-1 bg-gray-100 text-gray-700 rounded text-sm",children:w},`${w}-${Q}`))})]})]}):s.jsxs("div",{className:"text-center py-8 text-gray-500",children:[s.jsx("div",{className:"text-4xl mb-4",children:"🤖"}),s.jsx("p",{className:"text-lg",children:"等待任务启动"}),s.jsx("p",{className:"text-sm mt-2",children:'选择细纱机范围并点击"开始看车"后，机器人工作信息将在此显示'}),s.jsxs("div",{className:"text-xs text-gray-400 mt-2",children:["调试: taskStatus = ",j?"true":"false"]})]})]}),s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"细纱机看车范围选择"}),s.jsxs("div",{className:"mb-6",children:[s.jsx("div",{className:"flex flex-wrap gap-2 mb-4",children:I.map(w=>s.jsx("button",{onClick:()=>x(w.groupName),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${h===w.groupName?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:w.groupName},w.groupName))}),s.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[s.jsx("button",{onClick:()=>{const w=_(),Q=w.every(E=>i.includes(E));d(Q?E=>E.filter(F=>!w.includes(F)):E=>[...E,...w.filter(F=>!E.includes(F))])},className:"text-sm text-blue-600 hover:text-blue-800 font-medium",children:_().every(w=>i.includes(w))&&_().length>0?`取消全选 ${h}`:`全选 ${h}`}),s.jsxs("span",{className:"text-xs text-gray-500",children:["(",_().filter(w=>i.includes(w)).length,"/",_().length,")"]})]})]}),s.jsx("div",{className:"grid grid-cols-2 md:grid-cols-5 lg:grid-cols-6 gap-3",children:_().map(w=>s.jsxs("label",{className:"flex items-center space-x-2 p-3 rounded-lg border border-gray-200 hover:bg-gray-50 cursor-pointer",children:[s.jsx("input",{type:"checkbox",checked:i.includes(w),onChange:()=>V(w),className:"form-checkbox h-4 w-4 text-blue-600 rounded focus:ring-blue-500"}),s.jsx("span",{className:"text-sm font-medium text-gray-700",children:w})]},w))}),i.length>0&&s.jsx("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg",children:s.jsxs("p",{className:"text-sm text-blue-700",children:["已选择细纱机 (",i.length,"): ",i.join(", ")]})})]}),s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"任务控制"}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("button",{onClick:L,disabled:M,className:`px-6 py-3 rounded-lg font-medium transition-colors bg-green-500 text-white hover:bg-green-600 focus:ring-2 focus:ring-green-400 ${M?"opacity-50 cursor-not-allowed":""}`,children:s.jsxs("span",{className:"flex items-center space-x-2",children:[s.jsx("span",{children:"🚀"}),s.jsx("span",{children:M?"上线中...":"一键上线"})]})}),s.jsx("button",{onClick:u,disabled:!K(),className:`px-6 py-3 rounded-lg font-medium transition-colors ${K()?"bg-blue-600 text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,title:g!=null&&g.allConnected?i.length===0?"请先选择细纱机":"开始看车任务":"请先连接所有设备",children:s.jsxs("span",{className:"flex items-center space-x-2",children:[s.jsx("span",{children:"📹"}),s.jsx("span",{children:"开始看车"})]})}),s.jsx("button",{onClick:q,disabled:!p&&(j==null?void 0:j.status)!=="running",className:`px-6 py-3 rounded-lg font-medium transition-colors ${!p&&(j==null?void 0:j.status)!=="running"?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-red-600 text-white hover:bg-red-700 focus:ring-2 focus:ring-red-500"}`,children:s.jsxs("span",{className:"flex items-center space-x-2",children:[s.jsx("span",{children:"⏹️"}),s.jsx("span",{children:"停止看车"})]})}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:`w-3 h-3 rounded-full ${(j==null?void 0:j.status)==="running"?"bg-green-500 animate-pulse":"bg-gray-300"}`}),s.jsx("span",{className:"text-sm text-gray-600",children:(j==null?void 0:j.status)==="running"?"运行中":"已停止"})]})]})]})]})},Th=()=>{var g;const[i,d]=v.useState(null),[h,x]=v.useState(!1),{lastMessage:p}=Kn();v.useEffect(()=>{p&&p.type==="flow_status"&&(d(p.data),x(!0))},[p]);const S=C=>C&&typeof C.currentStep=="string"&&!C.currentTask,k=C=>C.includes("导航")||C.includes("AGV")?"text-blue-600 bg-blue-50":C.includes("PLC")||C.includes("控制")?"text-purple-600 bg-purple-50":C.includes("锭号")||C.includes("处理")?"text-green-600 bg-green-50":C.includes("完成")||C.includes("idle")?"text-gray-600 bg-gray-50":"text-orange-600 bg-orange-50";if(!h||!i)return s.jsx("div",{className:"card",children:s.jsxs("div",{className:"text-center py-8",children:[s.jsx("div",{className:"animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"}),s.jsx("p",{className:"text-gray-500",children:"等待流程状态数据..."})]})});if(S(i))return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"🔄 实时流程监控"}),s.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-2",children:[s.jsx("div",{className:"text-green-800 font-semibold",children:"✅ 流程状态已连接"}),s.jsxs("div",{className:"text-sm text-green-600",children:["更新时间: ",new Date(i.lastUpdateTime).toLocaleTimeString()]})]}),s.jsx("div",{className:"text-sm text-green-700",children:"正在实时监控AGV看车作业流程..."})]})]}),s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"⚡ 当前执行步骤"}),s.jsxs("div",{className:`p-4 rounded-lg ${k(i.currentStep)}`,children:[s.jsx("div",{className:"font-semibold text-lg mb-2",children:i.currentStep}),s.jsxs("div",{className:"text-sm opacity-75",children:["状态: ",i.currentStep==="idle"?"空闲中":"执行中"]})]})]}),(i.currentMachine||i.currentSpindle)&&s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"🏭 作业信息"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[i.currentMachine&&s.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"当前机器"}),s.jsx("div",{className:"font-semibold text-lg",children:i.currentMachine})]}),i.currentSpindle&&s.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"当前锭号"}),s.jsxs("div",{className:"font-semibold text-lg",children:["#",i.currentSpindle]})]})]})]}),s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"🧭 导航状态"}),s.jsx("div",{className:"bg-gray-50 p-4 rounded-lg",children:s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:`w-3 h-3 rounded-full ${i.navigationMode==="laser"?"bg-blue-500":"bg-purple-500"}`}),s.jsx("div",{className:"font-semibold",children:i.navigationMode==="laser"?"🎯 激光控制模式":"🤖 PLC控制模式"})]})})]}),(i.plcAction||i.plcAddress)&&s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"🔌 PLC通信状态"}),s.jsx("div",{className:"bg-gray-50 p-4 rounded-lg",children:s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[i.plcAction&&s.jsxs("div",{children:[s.jsx("div",{className:"text-sm text-gray-600",children:"最后操作"}),s.jsx("div",{className:"font-semibold",children:i.plcAction==="write"?"✏️ 写入":i.plcAction==="read"?"📖 读取":i.plcAction})]}),i.plcAddress&&s.jsxs("div",{children:[s.jsx("div",{className:"text-sm text-gray-600",children:"地址"}),s.jsx("div",{className:"font-semibold font-mono",children:i.plcAddress})]}),i.plcValue!==void 0&&s.jsxs("div",{children:[s.jsx("div",{className:"text-sm text-gray-600",children:"值"}),s.jsx("div",{className:"font-semibold font-mono",children:typeof i.plcValue=="string"?i.plcValue:JSON.stringify(i.plcValue)})]})]})})]})]});const A=(C,M)=>({M501:"控制权确认信号",M502:"机器人运行条件",M505:"方向验证信号",M509:"PLC准备信号",M510:"AGV到达原点信号",M511:"调头完成信号",M601:"控制权请求信号",M602:"任务开始信号",M603:"数据写入完成信号",M604:"码带方向信号",M605:"验证完成信号",M606:"机台完成信号",M607:"切换回激光控制",M608:"调头信号",M610:"回到原点信号",M611:"原点方向信号",M612:"强制取得控制权",D500:"PLC工作状态 (0=工作中, 1=成功, 2=失败)",D501:"皮辊验证 (1=左, 2=右)",D502:"码值验证(低位)",D503:"码值验证(高位)",D600:"皮辊方向 (1=左, 2=右)",D602:"码值(低位)",D603:"码值(高位)",D604:"原点码值"})[C]||`${M}地址 ${C}`,b=(C,M)=>M==="none"?"text-gray-500":C?"text-green-600":"text-red-600",f=k;return!h||!i?s.jsx("div",{className:"card",children:s.jsxs("div",{className:"text-center py-8",children:[s.jsx("div",{className:"animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"}),s.jsx("p",{className:"text-gray-500",children:"等待流程状态数据..."})]})}):s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"📋 任务总览"}),s.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[s.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"任务类型"}),s.jsx("div",{className:"font-semibold",children:i.currentTask.type==="single"?"单侧任务":"双侧任务"})]}),s.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"当前机器"}),s.jsx("div",{className:"font-semibold",children:i.currentTask.machine}),i.currentTask.side&&s.jsxs("div",{className:"text-xs text-gray-500",children:[i.currentTask.side,"侧"]})]}),s.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"机器进度"}),s.jsxs("div",{className:"font-semibold",children:[i.currentTask.currentMachineIndex,"/",i.currentTask.totalMachines]})]}),s.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"总进度"}),s.jsxs("div",{className:"font-semibold",children:[i.progress.overallProgress,"%"]}),s.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-1",children:s.jsx("div",{className:"bg-blue-500 h-2 rounded-full",style:{width:`${i.progress.overallProgress}%`}})})]})]})]}),s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"⚡ 当前执行步骤"}),s.jsxs("div",{className:`p-4 rounded-lg ${f(i.currentStep.phase)}`,children:[s.jsxs("div",{className:"flex items-center justify-between mb-2",children:[s.jsx("div",{className:"font-semibold text-lg",children:i.currentStep.phase}),s.jsxs("div",{className:"text-sm",children:["步骤 ",i.currentStep.stepNumber,"/",i.currentStep.totalSteps]})]}),s.jsx("div",{className:"text-sm mb-2",children:i.currentStep.step}),s.jsxs("div",{className:"flex items-center justify-between text-xs",children:[s.jsxs("div",{children:["开始时间: ",new Date(i.currentStep.startTime).toLocaleTimeString()]}),i.progress.estimatedTimeRemaining&&s.jsxs("div",{children:["预计剩余: ",Math.round(i.progress.estimatedTimeRemaining),"秒"]})]}),s.jsx("div",{className:"w-full bg-white bg-opacity-50 rounded-full h-2 mt-2",children:s.jsx("div",{className:"bg-current h-2 rounded-full",style:{width:`${i.progress.currentPhaseProgress}%`}})})]})]}),s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"🔌 PLC通信状态"}),s.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("div",{className:"text-sm text-gray-600",children:"最后操作"}),s.jsx("div",{className:`font-semibold ${b(i.plcCommunication.success,i.plcCommunication.lastAction)}`,children:i.plcCommunication.lastAction==="read"?"📖 读取":i.plcCommunication.lastAction==="write"?"✏️ 写入":"⏸️ 无操作"})]}),s.jsxs("div",{children:[s.jsx("div",{className:"text-sm text-gray-600",children:"地址"}),s.jsx("div",{className:"font-semibold font-mono",children:i.plcCommunication.address||"-"})]}),s.jsxs("div",{children:[s.jsx("div",{className:"text-sm text-gray-600",children:"值"}),s.jsx("div",{className:"font-semibold font-mono",children:i.plcCommunication.value!==void 0?JSON.stringify(i.plcCommunication.value):"-"})]}),s.jsxs("div",{children:[s.jsx("div",{className:"text-sm text-gray-600",children:"时间"}),s.jsx("div",{className:"text-sm",children:i.plcCommunication.timestamp?new Date(i.plcCommunication.timestamp).toLocaleTimeString():"-"})]})]}),i.plcCommunication.address&&s.jsxs("div",{className:"mt-3 p-3 bg-white rounded border-l-4 border-blue-500",children:[s.jsx("div",{className:"text-sm font-medium text-gray-900",children:"地址含义"}),s.jsx("div",{className:"text-sm text-gray-600",children:A(i.plcCommunication.address,i.plcCommunication.addressType)})]}),i.plcCommunication.error&&s.jsx("div",{className:"mt-3 p-3 bg-red-50 border border-red-200 rounded",children:s.jsx("div",{className:"text-sm text-red-800",children:i.plcCommunication.error})})]})]}),s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"🧭 导航状态"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[s.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"控制模式"}),s.jsx("div",{className:`font-semibold ${i.navigation.controlMode==="laser"?"text-green-600":"text-blue-600"}`,children:i.navigation.controlMode==="laser"?"🎯 激光控制":"🔗 PLC控制"})]}),s.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"导航状态"}),s.jsx("div",{className:`font-semibold ${i.navigation.navigationStatus==="arrived"?"text-green-600":i.navigation.navigationStatus==="moving"?"text-blue-600":i.navigation.navigationStatus==="failed"?"text-red-600":"text-gray-600"}`,children:i.navigation.navigationStatus==="idle"?"空闲":i.navigation.navigationStatus==="moving"?"移动中":i.navigation.navigationStatus==="arrived"?"已到达":"失败"})]}),s.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"目标点"}),s.jsx("div",{className:"font-semibold",children:((g=i.navigation.targetPoint)==null?void 0:g.name)||"-"})]})]}),(i.navigation.currentPosition||i.navigation.targetPoint)&&s.jsxs("div",{className:"mt-4 grid grid-cols-1 md:grid-cols-2 gap-4",children:[i.navigation.currentPosition&&s.jsxs("div",{className:"bg-blue-50 p-3 rounded-lg",children:[s.jsx("div",{className:"text-sm text-blue-600 font-medium mb-1",children:"当前位置"}),s.jsxs("div",{className:"text-xs text-blue-800 font-mono",children:["X: ",i.navigation.currentPosition.x.toFixed(2),", Y: ",i.navigation.currentPosition.y.toFixed(2),", 角度: ",i.navigation.currentPosition.angle.toFixed(1),"°"]})]}),i.navigation.targetPoint&&s.jsxs("div",{className:"bg-green-50 p-3 rounded-lg",children:[s.jsx("div",{className:"text-sm text-green-600 font-medium mb-1",children:"目标位置"}),s.jsxs("div",{className:"text-xs text-green-800 font-mono",children:["X: ",i.navigation.targetPoint.x.toFixed(2),", Y: ",i.navigation.targetPoint.y.toFixed(2),", 角度: ",i.navigation.targetPoint.angle.toFixed(1),"°"]})]})]})]}),s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"🎯 锭号处理状态"}),s.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[s.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"当前锭号"}),s.jsx("div",{className:"font-semibold text-lg",children:i.spindleProcessing.currentSpindle||"-"})]}),s.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"处理进度"}),s.jsxs("div",{className:"font-semibold",children:[i.spindleProcessing.processedSpindles,"/",i.spindleProcessing.totalSpindles]})]}),s.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"验证状态"}),s.jsx("div",{className:`font-semibold ${i.spindleProcessing.verificationStatus==="success"?"text-green-600":i.spindleProcessing.verificationStatus==="failed"?"text-red-600":i.spindleProcessing.verificationStatus==="verifying"?"text-blue-600":"text-gray-600"}`,children:i.spindleProcessing.verificationStatus==="pending"?"等待中":i.spindleProcessing.verificationStatus==="verifying"?"验证中":i.spindleProcessing.verificationStatus==="success"?"验证成功":"验证失败"})]}),s.jsxs("div",{className:"bg-gray-50 p-3 rounded-lg",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"验证次数"}),s.jsxs("div",{className:"font-semibold",children:[i.spindleProcessing.verificationAttempt,"/",i.spindleProcessing.maxAttempts]})]})]}),i.spindleProcessing.currentSpindleData&&s.jsxs("div",{className:"bg-blue-50 p-3 rounded-lg",children:[s.jsx("div",{className:"text-sm text-blue-600 font-medium mb-2",children:"当前锭号数据"}),s.jsxs("div",{className:"grid grid-cols-3 gap-4 text-xs",children:[s.jsxs("div",{children:[s.jsx("span",{className:"text-blue-800",children:"方向: "}),s.jsx("span",{className:"font-mono",children:i.spindleProcessing.currentSpindleData.direction})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-blue-800",children:"距离: "}),s.jsx("span",{className:"font-mono",children:i.spindleProcessing.currentSpindleData.distance})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-blue-800",children:"皮辊: "}),s.jsxs("span",{className:"font-mono",children:[i.spindleProcessing.currentSpindleData.roller===1?"左":"右","(",i.spindleProcessing.currentSpindleData.roller,")"]})]})]})]}),i.spindleProcessing.spindleList.length>0&&s.jsxs("div",{className:"mt-4",children:[s.jsx("div",{className:"text-sm text-gray-600 mb-2",children:"锭号列表:"}),s.jsx("div",{className:"flex flex-wrap gap-2",children:i.spindleProcessing.spindleList.map((C,M)=>s.jsx("span",{className:`px-2 py-1 rounded text-xs font-mono ${C===i.spindleProcessing.currentSpindle?"bg-blue-500 text-white":M<i.spindleProcessing.processedSpindles?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"}`,children:C},C))})]})]}),i.errors.length>0&&s.jsxs("div",{className:"card",children:[s.jsx("h2",{className:"text-xl font-bold text-red-900 mb-4",children:"⚠️ 错误信息"}),s.jsx("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:i.errors.slice(0,10).map((C,M)=>s.jsx("div",{className:`p-3 rounded border-l-4 ${C.type==="critical"?"bg-red-50 border-red-500":C.type==="error"?"bg-orange-50 border-orange-500":"bg-yellow-50 border-yellow-500"}`,children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:`font-medium ${C.type==="critical"?"text-red-800":C.type==="error"?"text-orange-800":"text-yellow-800"}`,children:[C.type==="critical"?"🔴":C.type==="error"?"🟠":"🟡"," ",C.message]}),s.jsx("div",{className:"text-xs text-gray-500",children:new Date(C.timestamp).toLocaleTimeString()})]})},M))})]})]})};function Ah({isConnected:i,error:d}){return d?s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"w-3 h-3 bg-red-500 rounded-full animate-pulse"}),s.jsx("span",{className:"text-sm font-medium text-red-700",children:"连接错误"}),s.jsx("span",{className:"text-xs text-red-600",title:d,children:d.length>20?`${d.substring(0,20)}...`:d})]}):s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:`w-3 h-3 rounded-full ${i?"bg-green-500":"bg-yellow-500 animate-pulse"}`}),s.jsx("span",{className:`text-sm font-medium ${i?"text-green-700":"text-yellow-700"}`,children:i?"已连接":"连接中..."})]})}/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _h=i=>i.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Mh=i=>i.replace(/^([A-Z])|[\s-_]+(\w)/g,(d,h,x)=>x?x.toUpperCase():h.toLowerCase()),Nm=i=>{const d=Mh(i);return d.charAt(0).toUpperCase()+d.slice(1)},_m=(...i)=>i.filter((d,h,x)=>!!d&&d.trim()!==""&&x.indexOf(d)===h).join(" ").trim(),Eh=i=>{for(const d in i)if(d.startsWith("aria-")||d==="role"||d==="title")return!0};/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Rh={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oh=v.forwardRef(({color:i="currentColor",size:d=24,strokeWidth:h=2,absoluteStrokeWidth:x,className:p="",children:S,iconNode:k,...A},b)=>v.createElement("svg",{ref:b,...Rh,width:d,height:d,stroke:i,strokeWidth:x?Number(h)*24/Number(d):h,className:_m("lucide",p),...!S&&!Eh(A)&&{"aria-hidden":"true"},...A},[...k.map(([f,g])=>v.createElement(f,g)),...Array.isArray(S)?S:[S]]));/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ve=(i,d)=>{const h=v.forwardRef(({className:x,...p},S)=>v.createElement(Oh,{ref:S,iconNode:d,className:_m(`lucide-${_h(Nm(i))}`,`lucide-${i}`,x),...p}));return h.displayName=Nm(i),h};/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kh=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],Mm=Ve("calendar",kh);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dh=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],Em=Ve("chart-column",Dh);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zh=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],Xn=Ve("check",zh);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lh=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],Uh=Ve("chevron-down",Lh);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qh=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],Hh=Ve("circle-alert",qh);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gh=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],Vh=Ve("clock",Gh);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bh=[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]],Yh=Ve("code",Bh);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $h=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]],Qn=Ve("copy",$h);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xh=[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]],Qh=Ve("download",Xh);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zh=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],Kh=Ve("file-text",Zh);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jh=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],Wh=Ve("funnel",Jh);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fh=[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]],Ph=Ve("hard-drive",Fh);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ih=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],e0=Ve("map-pin",Ih);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const t0=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],s0=Ve("message-square",t0);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const l0=[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]],a0=Ve("pause",l0);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n0=[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]],c0=Ve("play",n0);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const i0=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],hr=Ve("refresh-cw",i0);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const r0=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],u0=Ve("search",r0);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const d0=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],o0=Ve("trash-2",d0);/**
 * @license lucide-react v0.518.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m0=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Rm=Ve("x",m0);function x0({filters:i,autoLoad:d=!0,pageSize:h=50}){const[x,p]=v.useState([]),[S,k]=v.useState(!1),[A,b]=v.useState(null),[f,g]=v.useState(!0),[C,M]=v.useState(0),[J,z]=v.useState(0),[D,j]=v.useState(null),[te,I]=v.useState(["ALL"]),[le,ae]=v.useState(["ALL","DEBUG","INFO","WARN","ERROR","FATAL"]),_=v.useCallback((K=0)=>{const ne={limit:h,offset:K,reverse:!0};if(i.level&&i.level!=="ALL"&&(ne.level=i.level),i.module&&i.module!=="ALL"&&(ne.module=i.module),i.search&&(ne.search=i.search),i.timeRange==="today"){const w=new Date;w.setHours(0,0,0,0),ne.startTime=w.toISOString()}else if(i.timeRange==="hour"){const w=new Date(Date.now()-36e5);ne.startTime=w.toISOString()}else i.timeRange==="custom"&&(i.customStartTime&&(ne.startTime=i.customStartTime.toISOString()),i.customEndTime&&(ne.endTime=i.customEndTime.toISOString()));return ne},[i,h]),V=v.useCallback(async(K=!1)=>{if(!S){k(!0),b(null);try{const w=_(K?0:J),Q=await ye.queryLogs(w);if(Q.success&&Q.data){const E=Q.data.logs;K?(p(E),z(E.length)):(p(F=>[...F,...E]),z(F=>F+E.length)),g(Q.data.hasMore),M(Q.data.total)}else b(Q.error||"查询日志失败")}catch(ne){b(ne instanceof Error?ne.message:"查询日志失败")}finally{k(!1)}}},[S,J,_]),u=v.useCallback(async()=>{!f||S||await V(!1)},[f,S,V]),q=v.useCallback(K=>{try{ye.exportLogs({format:K,filters:i,includeDetails:!0})}catch(ne){b(ne instanceof Error?ne.message:"导出日志失败")}},[i]),X=v.useCallback(async()=>{try{const K=await ye.clearLogCache();K.success?await V(!0):b(K.error||"清除缓存失败")}catch(K){b(K instanceof Error?K.message:"清除缓存失败")}},[V]),Y=v.useCallback(async()=>{try{const K=await ye.getLogStats();K.success&&K.data&&j(K.data)}catch(K){console.error("加载统计信息失败:",K)}},[]),L=v.useCallback(async()=>{try{const K=await ye.getLogModules();K.success&&K.data&&I(["ALL",...K.data])}catch(K){console.error("加载模块列表失败:",K)}},[]);return v.useEffect(()=>{d&&(z(0),V(!0))},[i,d]),v.useEffect(()=>{d&&(Y(),L())},[d,Y,L]),{logs:x,loading:S,error:A,hasMore:f,total:C,stats:D,modules:te,levels:le,loadLogs:V,loadMore:u,exportLogs:q,clearCache:X}}function f0({maxLogs:i=1e3,autoSubscribe:d=!1,filterLevel:h,filterModule:x}={}){const[p,S]=v.useState([]),[k,A]=v.useState(!1),[b,f]=v.useState(i),{sendMessage:g,lastMessage:C}=Kn(),M=v.useRef(!1),J=v.useCallback(ae=>!(h&&ae.level!==h||x&&ae.module!==x),[h,x]),z=v.useCallback(ae=>{J(ae)&&S(_=>[ae,..._].slice(0,b))},[J,b]),D=v.useCallback(()=>{M.current||(console.log("订阅实时日志"),g({type:"subscribe_logs",data:{timestamp:new Date().toISOString()}}),M.current=!0)},[g]),j=v.useCallback(()=>{M.current&&(console.log("取消订阅实时日志"),g({type:"unsubscribe_logs",data:{timestamp:new Date().toISOString()}}),M.current=!1,A(!1))},[g]),te=v.useCallback(()=>{S([])},[]),I=v.useCallback(ae=>p.filter(_=>_.level===ae),[p]),le=v.useCallback(ae=>p.filter(_=>_.module===ae),[p]);return v.useEffect(()=>{var ae,_;if(C)try{const V=typeof C=="string"?JSON.parse(C):C;switch(V.type){case"log_entry":V.data&&k&&z(V.data);break;case"log_subscription_confirmed":console.log("日志订阅已确认:",(ae=V.data)==null?void 0:ae.message),A(!0);break;case"log_unsubscription_confirmed":console.log("日志订阅已取消:",(_=V.data)==null?void 0:_.message),A(!1);break;default:break}}catch(V){console.error("解析WebSocket消息失败:",V)}},[C,k,z]),v.useEffect(()=>(d&&D(),()=>{M.current&&j()}),[d,D,j]),v.useEffect(()=>{S(ae=>ae.slice(0,b))},[b]),{realtimeLogs:p,isSubscribed:k,maxLogCount:b,subscribe:D,unsubscribe:j,clearLogs:te,setMaxLogCount:f,getLogsByLevel:I,getLogsByModule:le}}const h0=({filters:i,modules:d,levels:h,onFiltersChange:x,onSearch:p})=>{const[S,k]=v.useState(i.search),[A,b]=v.useState(i.timeRange==="custom"),f=v.useCallback(z=>{z.preventDefault(),p(S)},[S,p]),g=z=>{b(z==="custom"),x({timeRange:z})},C=(z,D)=>{x(z==="start"?{customStartTime:D?new Date(D):void 0}:{customEndTime:D?new Date(D):void 0})},M=()=>{k(""),p("")},J=z=>{if(!z)return"";const D=z.getTimezoneOffset();return new Date(z.getTime()-D*60*1e3).toISOString().slice(0,16)};return s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("form",{onSubmit:f,className:"flex-1 max-w-md",children:s.jsxs("div",{className:"relative",children:[s.jsx(u0,{size:16,className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),s.jsx("input",{type:"text",value:S,onChange:z=>k(z.target.value),placeholder:"搜索日志内容...",className:"w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),S&&s.jsx("button",{type:"button",onClick:M,className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:s.jsx(Rm,{size:16})})]})}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(Mm,{size:16,className:"text-gray-400"}),s.jsxs("select",{value:i.timeRange,onChange:z=>g(z.target.value),className:"border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[s.jsx("option",{value:"all",children:"全部时间"}),s.jsx("option",{value:"hour",children:"最近1小时"}),s.jsx("option",{value:"today",children:"今天"}),s.jsx("option",{value:"custom",children:"自定义范围"})]})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(Wh,{size:16,className:"text-gray-400"}),s.jsx("select",{value:i.level,onChange:z=>x({level:z.target.value}),className:"border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:h.map(z=>s.jsx("option",{value:z,children:z==="ALL"?"全部级别":z},z))})]}),s.jsx("select",{value:i.module,onChange:z=>x({module:z.target.value}),className:"border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:d.map(z=>s.jsx("option",{value:z,children:z==="ALL"?"全部模块":z},z))})]}),A&&s.jsxs("div",{className:"flex items-center space-x-4 p-3 bg-gray-50 rounded-md",children:[s.jsx("span",{className:"text-sm text-gray-600 whitespace-nowrap",children:"时间范围:"}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-sm text-gray-500",children:"从"}),s.jsx("input",{type:"datetime-local",value:J(i.customStartTime),onChange:z=>C("start",z.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-sm text-gray-500",children:"到"}),s.jsx("input",{type:"datetime-local",value:J(i.customEndTime),onChange:z=>C("end",z.target.value),className:"border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),s.jsx("button",{onClick:()=>{x({customStartTime:void 0,customEndTime:void 0})},className:"text-sm text-gray-500 hover:text-gray-700 px-2 py-1 rounded hover:bg-gray-100",children:"清除"})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-sm text-gray-500",children:"快速过滤:"}),s.jsx("button",{onClick:()=>x({level:"ERROR"}),className:`px-2 py-1 text-xs rounded-full transition-colors ${i.level==="ERROR"?"bg-red-100 text-red-700 border border-red-200":"bg-gray-100 text-gray-600 hover:bg-red-50 hover:text-red-600"}`,children:"🔴 错误"}),s.jsx("button",{onClick:()=>x({level:"WARN"}),className:`px-2 py-1 text-xs rounded-full transition-colors ${i.level==="WARN"?"bg-yellow-100 text-yellow-700 border border-yellow-200":"bg-gray-100 text-gray-600 hover:bg-yellow-50 hover:text-yellow-600"}`,children:"🟡 警告"}),d.includes("task")&&s.jsx("button",{onClick:()=>x({module:"task"}),className:`px-2 py-1 text-xs rounded-full transition-colors ${i.module==="task"?"bg-blue-100 text-blue-700 border border-blue-200":"bg-gray-100 text-gray-600 hover:bg-blue-50 hover:text-blue-600"}`,children:"📋 任务"}),d.includes("plc")&&s.jsx("button",{onClick:()=>x({module:"plc"}),className:`px-2 py-1 text-xs rounded-full transition-colors ${i.module==="plc"?"bg-green-100 text-green-700 border border-green-200":"bg-gray-100 text-gray-600 hover:bg-green-50 hover:text-green-600"}`,children:"⚙️ PLC"}),d.includes("agv")&&s.jsx("button",{onClick:()=>x({module:"agv"}),className:`px-2 py-1 text-xs rounded-full transition-colors ${i.module==="agv"?"bg-purple-100 text-purple-700 border border-purple-200":"bg-gray-100 text-gray-600 hover:bg-purple-50 hover:text-purple-600"}`,children:"🚛 AGV"}),(i.level!=="ALL"||i.module!=="ALL"||i.search||i.timeRange!=="all")&&s.jsx("button",{onClick:()=>{k(""),x({level:"ALL",module:"ALL",search:"",timeRange:"all",customStartTime:void 0,customEndTime:void 0}),p("")},className:"px-2 py-1 text-xs text-gray-500 hover:text-gray-700 border border-gray-300 rounded-full hover:bg-gray-50",children:"🗑️ 清除过滤"})]})]})},g0=({logs:i,loading:d,error:h,hasMore:x,total:p,viewMode:S,onLogSelect:k,onLoadMore:A})=>{const[b,f]=v.useState(null),[g,C]=v.useState(!0),M=v.useRef(null),J=v.useRef(null),z=_=>{switch(_){case"DEBUG":return"text-gray-600 bg-gray-50 border-l-gray-300";case"INFO":return"text-blue-600 bg-blue-50 border-l-blue-300";case"WARN":return"text-yellow-600 bg-yellow-50 border-l-yellow-300";case"ERROR":return"text-red-600 bg-red-50 border-l-red-400";case"FATAL":return"text-white bg-red-600 border-l-red-700";default:return"text-gray-600 bg-gray-50 border-l-gray-300"}},D=_=>{switch(_){case"DEBUG":return"🔍";case"INFO":return"ℹ️";case"WARN":return"⚠️";case"ERROR":return"❌";case"FATAL":return"💀";default:return"📝"}},j=_=>new Date(_).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",fractionalSecondDigits:3}),te=(_,V)=>`${_.timestamp}-${_.module}-${V}`,I=(_,V)=>{const u=te(_,V);f(u),k(_)};v.useEffect(()=>{g&&S==="realtime"&&M.current&&(M.current.scrollTop=0)},[i,g,S]),v.useEffect(()=>{if(!x||d||S==="realtime")return;const _=new IntersectionObserver(V=>{V[0].isIntersecting&&A()},{threshold:.1});return J.current&&_.observe(J.current),()=>_.disconnect()},[x,d,A,S]);const le=v.useCallback(()=>{if(!M.current||S!=="realtime")return;const{scrollTop:_,scrollHeight:V,clientHeight:u}=M.current,q=_<100;C(q)},[S]),ae=(_,V)=>_;return h?s.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-gray-500",children:[s.jsx(Hh,{size:48,className:"text-red-400 mb-4"}),s.jsx("p",{className:"text-lg font-medium mb-2",children:"加载日志时出错"}),s.jsx("p",{className:"text-sm text-center max-w-md",children:h})]}):!d&&i.length===0?s.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-gray-500",children:[s.jsx("div",{className:"text-6xl mb-4",children:"📝"}),s.jsx("p",{className:"text-lg font-medium mb-2",children:"暂无日志数据"}),s.jsx("p",{className:"text-sm text-center max-w-md",children:S==="realtime"?"等待新的日志条目...":"尝试调整过滤条件或时间范围"})]}):s.jsxs("div",{className:"h-full flex flex-col",children:[S==="realtime"&&s.jsxs("div",{className:"bg-blue-50 border-b border-blue-200 px-4 py-2 flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-2 text-sm text-blue-700",children:[s.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-pulse"}),s.jsx("span",{children:"实时日志模式 - 新日志将自动显示"})]}),s.jsx("div",{className:"flex items-center space-x-2",children:s.jsxs("label",{className:"flex items-center space-x-1 text-sm text-blue-700",children:[s.jsx("input",{type:"checkbox",checked:g,onChange:_=>C(_.target.checked),className:"rounded border-blue-300 text-blue-600 focus:ring-blue-500"}),s.jsx("span",{children:"自动滚动"})]})})]}),s.jsx("div",{ref:M,onScroll:le,className:"flex-1 overflow-auto",children:s.jsxs("div",{className:"divide-y divide-gray-200",children:[i.map((_,V)=>{const u=te(_,V),q=b===u;return s.jsx("div",{onClick:()=>I(_,V),className:`
                  p-3 border-l-4 cursor-pointer transition-all duration-150
                  ${z(_.level)}
                  ${q?"ring-2 ring-blue-300 bg-blue-100":"hover:bg-opacity-70"}
                `,children:s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx("div",{className:"flex-shrink-0 text-lg mt-0.5",children:D(_.level)}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsxs("div",{className:"flex items-center space-x-2 text-xs font-medium mb-1",children:[s.jsx("span",{className:"text-gray-500",children:j(_.timestamp)}),s.jsx("span",{className:"px-1.5 py-0.5 rounded text-xs font-bold",children:_.level}),s.jsx("span",{className:"px-1.5 py-0.5 bg-gray-200 text-gray-700 rounded text-xs",children:_.module}),_.caller&&s.jsx("span",{className:"text-gray-400 font-mono text-xs",children:_.caller})]}),s.jsx("div",{className:"text-sm font-medium leading-relaxed",children:ae(_.message)})]}),s.jsx("div",{className:"flex-shrink-0",children:s.jsx(Uh,{size:16,className:`text-gray-400 transition-transform ${q?"rotate-180":""}`})})]})},u)}),S==="history"&&x&&s.jsx("div",{ref:J,className:"p-4 text-center",children:d?s.jsxs("div",{className:"flex items-center justify-center space-x-2 text-gray-500",children:[s.jsx(hr,{size:16,className:"animate-spin"}),s.jsx("span",{children:"加载更多日志..."})]}):s.jsx("button",{onClick:A,className:"px-4 py-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors",children:"点击加载更多"})}),S==="history"&&!x&&i.length>0&&s.jsxs("div",{className:"p-4 text-center text-gray-500 text-sm",children:["📄 已显示全部 ",p," 条日志"]})]})})]})},b0=({stats:i})=>{const d=b=>{if(b===0)return"0 B";const f=1024,g=["B","KB","MB","GB"],C=Math.floor(Math.log(b)/Math.log(f));return parseFloat((b/Math.pow(f,C)).toFixed(1))+" "+g[C]},h=b=>new Date(b).toLocaleString("zh-CN"),x=b=>i.totalLogs>0?(b/i.totalLogs*100).toFixed(1):"0",p=b=>{switch(b){case"DEBUG":return"bg-gray-200 text-gray-700";case"INFO":return"bg-blue-200 text-blue-700";case"WARN":return"bg-yellow-200 text-yellow-700";case"ERROR":return"bg-red-200 text-red-700";case"FATAL":return"bg-red-300 text-red-800";default:return"bg-gray-200 text-gray-700"}},S=b=>{const f=["bg-blue-200 text-blue-700","bg-green-200 text-green-700","bg-purple-200 text-purple-700","bg-pink-200 text-pink-700","bg-indigo-200 text-indigo-700","bg-yellow-200 text-yellow-700"],g=b.split("").reduce((C,M)=>C+M.charCodeAt(0),0);return f[g%f.length]},k=Object.entries(i.logsByLevel).sort(([,b],[,f])=>f-b),A=Object.entries(i.logsByModule).sort(([,b],[,f])=>f-b);return s.jsx("div",{className:"bg-white p-4",children:s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[s.jsxs("div",{className:"space-y-4",children:[s.jsxs("h4",{className:"font-medium text-gray-900 flex items-center space-x-2",children:[s.jsx(Em,{size:16}),s.jsx("span",{children:"总体统计"})]}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"bg-blue-50 rounded-lg p-3",children:[s.jsx("div",{className:"text-2xl font-bold text-blue-600",children:i.totalLogs.toLocaleString()}),s.jsx("div",{className:"text-sm text-blue-600",children:"总日志条数"})]}),s.jsxs("div",{className:"bg-green-50 rounded-lg p-3",children:[s.jsx("div",{className:"text-lg font-semibold text-green-600",children:i.fileInfos.length}),s.jsx("div",{className:"text-sm text-green-600",children:"日志文件数"})]}),s.jsxs("div",{className:"bg-purple-50 rounded-lg p-3",children:[s.jsxs("div",{className:"text-sm font-medium text-purple-600 flex items-center space-x-1",children:[s.jsx(Vh,{size:14}),s.jsx("span",{children:"最后更新"})]}),s.jsx("div",{className:"text-xs text-purple-600 mt-1",children:h(i.lastUpdate)})]})]})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsx("h4",{className:"font-medium text-gray-900",children:"按级别分布"}),s.jsx("div",{className:"space-y-2",children:k.map(([b,f])=>s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:`px-2 py-1 rounded text-xs font-medium ${p(b)}`,children:b}),s.jsx("span",{className:"text-sm text-gray-600",children:f.toLocaleString()})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"w-16 bg-gray-200 rounded-full h-2",children:s.jsx("div",{className:`h-2 rounded-full ${p(b).split(" ")[0]}`,style:{width:`${x(f)}%`}})}),s.jsxs("span",{className:"text-xs text-gray-500 w-8 text-right",children:[x(f),"%"]})]})]},b))})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsx("h4",{className:"font-medium text-gray-900",children:"按模块分布"}),s.jsxs("div",{className:"space-y-2",children:[A.slice(0,6).map(([b,f])=>s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:`px-2 py-1 rounded text-xs font-medium ${S(b)}`,children:b}),s.jsx("span",{className:"text-sm text-gray-600",children:f.toLocaleString()})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"w-16 bg-gray-200 rounded-full h-2",children:s.jsx("div",{className:`h-2 rounded-full ${S(b).split(" ")[0]}`,style:{width:`${x(f)}%`}})}),s.jsxs("span",{className:"text-xs text-gray-500 w-8 text-right",children:[x(f),"%"]})]})]},b)),A.length>6&&s.jsxs("div",{className:"text-xs text-gray-500 text-center pt-2",children:["还有 ",A.length-6," 个模块..."]})]})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("h4",{className:"font-medium text-gray-900 flex items-center space-x-2",children:[s.jsx(Kh,{size:16}),s.jsx("span",{children:"文件信息"})]}),s.jsx("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:i.fileInfos.map((b,f)=>s.jsxs("div",{className:"bg-gray-50 rounded-lg p-3",children:[s.jsxs("div",{className:"flex items-center justify-between mb-2",children:[s.jsx("span",{className:"text-sm font-medium text-gray-900 truncate",children:b.name}),s.jsxs("div",{className:"flex items-center space-x-1",children:[b.isStructured&&s.jsx("span",{className:"px-1.5 py-0.5 bg-blue-100 text-blue-600 rounded text-xs",children:"JSON"}),s.jsx(Ph,{size:12,className:"text-gray-400"})]})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-2 text-xs text-gray-600",children:[s.jsxs("div",{children:[s.jsx("span",{className:"font-medium",children:"大小: "}),d(b.size)]}),s.jsxs("div",{children:[s.jsx("span",{className:"font-medium",children:"行数: "}),b.lineCount.toLocaleString()]}),s.jsxs("div",{className:"col-span-2",children:[s.jsx("span",{className:"font-medium",children:"修改: "}),h(b.modTime)]})]})]},f))}),i.fileInfos.length===0&&s.jsx("div",{className:"text-center text-gray-500 text-sm py-4",children:"暂无文件信息"})]})]})})},y0=({log:i,onClose:d})=>{const[h,x]=v.useState(null),p=async(f,g)=>{try{await navigator.clipboard.writeText(f),x(g),setTimeout(()=>x(null),2e3)}catch(C){console.error("复制失败:",C)}},S=f=>{const g=new Date(f);return{date:g.toLocaleDateString("zh-CN"),time:`${g.getHours().toString().padStart(2,"0")}:${g.getMinutes().toString().padStart(2,"0")}:${g.getSeconds().toString().padStart(2,"0")}.${g.getMilliseconds().toString().padStart(3,"0")}`,iso:g.toISOString()}},k=f=>{switch(f){case"DEBUG":return"bg-gray-100 text-gray-700 border-gray-300";case"INFO":return"bg-blue-100 text-blue-700 border-blue-300";case"WARN":return"bg-yellow-100 text-yellow-700 border-yellow-300";case"ERROR":return"bg-red-100 text-red-700 border-red-300";case"FATAL":return"bg-red-200 text-red-800 border-red-400";default:return"bg-gray-100 text-gray-700 border-gray-300"}},A=f=>{switch(f){case"DEBUG":return"🔍";case"INFO":return"ℹ️";case"WARN":return"⚠️";case"ERROR":return"❌";case"FATAL":return"💀";default:return"📝"}},b=S(i.timestamp);return s.jsxs("div",{className:"h-full flex flex-col bg-white",children:[s.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"日志详情"}),s.jsx("button",{onClick:d,className:"p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors",children:s.jsx(Rm,{size:20})})]}),s.jsxs("div",{className:"flex-1 overflow-auto p-4 space-y-6",children:[s.jsxs("div",{className:"space-y-4",children:[s.jsxs("h4",{className:"font-medium text-gray-900 flex items-center space-x-2",children:[s.jsx(Mm,{size:16}),s.jsx("span",{children:"基本信息"})]}),s.jsxs("div",{className:"bg-gray-50 rounded-lg p-4 space-y-3",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-sm font-medium text-gray-600",children:"时间:"}),s.jsxs("div",{className:"text-right",children:[s.jsxs("div",{className:"text-sm font-mono",children:[b.date," ",b.time]}),s.jsx("div",{className:"text-xs text-gray-500",children:b.iso})]})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-sm font-medium text-gray-600",children:"级别:"}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-lg",children:A(i.level)}),s.jsx("span",{className:`px-2 py-1 rounded-md text-sm font-medium border ${k(i.level)}`,children:i.level})]})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-sm font-medium text-gray-600",children:"模块:"}),s.jsx("span",{className:"px-2 py-1 bg-blue-100 text-blue-700 rounded-md text-sm font-medium",children:i.module})]}),i.caller&&s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("span",{className:"text-sm font-medium text-gray-600 flex items-center space-x-1",children:[s.jsx(e0,{size:14}),s.jsx("span",{children:"位置:"})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("code",{className:"px-2 py-1 bg-gray-200 text-gray-800 rounded text-sm font-mono",children:i.caller}),s.jsx("button",{onClick:()=>p(i.caller,"caller"),className:"p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-200 rounded transition-colors",children:h==="caller"?s.jsx(Xn,{size:14,className:"text-green-500"}):s.jsx(Qn,{size:14})})]})]})]})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("h4",{className:"font-medium text-gray-900 flex items-center space-x-2",children:[s.jsx(s0,{size:16}),s.jsx("span",{children:"日志消息"})]}),s.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:s.jsxs("div",{className:"flex items-start justify-between",children:[s.jsx("div",{className:"flex-1 text-sm leading-relaxed text-gray-800 whitespace-pre-wrap break-words",children:i.message}),s.jsx("button",{onClick:()=>p(i.message,"message"),className:"ml-2 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-200 rounded transition-colors flex-shrink-0",children:h==="message"?s.jsx(Xn,{size:14,className:"text-green-500"}):s.jsx(Qn,{size:14})})]})})]}),i.raw&&i.raw!==i.message&&s.jsxs("div",{className:"space-y-4",children:[s.jsxs("h4",{className:"font-medium text-gray-900 flex items-center space-x-2",children:[s.jsx(Yh,{size:16}),s.jsx("span",{children:"原始日志"})]}),s.jsx("div",{className:"bg-gray-900 rounded-lg p-4",children:s.jsxs("div",{className:"flex items-start justify-between",children:[s.jsx("pre",{className:"flex-1 text-sm font-mono text-green-400 whitespace-pre-wrap break-words overflow-x-auto",children:i.raw}),s.jsx("button",{onClick:()=>p(i.raw,"raw"),className:"ml-2 p-1 text-gray-400 hover:text-gray-300 hover:bg-gray-800 rounded transition-colors flex-shrink-0",children:h==="raw"?s.jsx(Xn,{size:14,className:"text-green-400"}):s.jsx(Qn,{size:14})})]})})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsx("h4",{className:"font-medium text-gray-900",children:"操作"}),s.jsxs("div",{className:"flex flex-wrap gap-2",children:[s.jsxs("button",{onClick:()=>p(JSON.stringify(i,null,2),"json"),className:"flex items-center space-x-1 px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors text-sm",children:[h==="json"?s.jsx(Xn,{size:14}):s.jsx(Qn,{size:14}),s.jsx("span",{children:"复制JSON"})]}),s.jsxs("button",{onClick:()=>{const f=new URLSearchParams({level:i.level,module:i.module,timeRange:"hour"});console.log("过滤相似日志:",f.toString())},className:"flex items-center space-x-1 px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors text-sm",children:[s.jsx("span",{children:"🔍"}),s.jsx("span",{children:"查找相似"})]}),s.jsxs("button",{onClick:()=>{console.log("导出单条日志")},className:"flex items-center space-x-1 px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors text-sm",children:[s.jsx("span",{children:"📤"}),s.jsx("span",{children:"导出"})]})]})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsx("h4",{className:"font-medium text-gray-900",children:"元数据"}),s.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:s.jsxs("div",{className:"grid grid-cols-1 gap-2 text-sm",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"字符数:"}),s.jsx("span",{className:"font-mono",children:i.message.length})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"原始大小:"}),s.jsxs("span",{className:"font-mono",children:[new Blob([i.raw||i.message]).size," bytes"]})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"时间戳格式:"}),s.jsx("span",{className:"font-mono",children:"ISO 8601"})]})]})})]})]})]})},v0=()=>{const[i,d]=v.useState({level:"ALL",module:"ALL",search:"",timeRange:"today"}),[h,x]=v.useState("history"),[p,S]=v.useState(null),[k,A]=v.useState(!1),{logs:b,loading:f,error:g,hasMore:C,total:M,stats:J,modules:z,levels:D,loadLogs:j,loadMore:te,exportLogs:I,clearCache:le}=x0({filters:i,autoLoad:!0,pageSize:100}),{realtimeLogs:ae,isSubscribed:_,subscribe:V,unsubscribe:u,clearLogs:q}=f0({maxLogs:500,autoSubscribe:!1}),X=v.useMemo(()=>h==="history"?b:ae,[h,b,ae]),Y=E=>{d(F=>({...F,search:E}))},L=E=>{d(F=>({...F,...E}))},K=()=>{const E=h==="history"?"realtime":"history";x(E),E==="realtime"&&!_?V():E==="history"&&_&&u()},ne=E=>{I(E)},w=()=>{h==="realtime"?q():le()},Q=()=>{h==="history"?j(!0):q()};return s.jsxs("div",{className:"h-full flex flex-col bg-gray-50",children:[s.jsxs("div",{className:"bg-white border-b border-gray-200 p-4 space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsxs("div",{className:"flex bg-gray-100 rounded-lg p-1",children:[s.jsx("button",{onClick:()=>h!=="history"&&K(),className:`px-3 py-1 rounded-md text-sm font-medium transition-colors ${h==="history"?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-800"}`,children:"📚 历史日志 (自动存储)"}),s.jsxs("button",{onClick:()=>h!=="realtime"&&K(),className:`px-3 py-1 rounded-md text-sm font-medium transition-colors ${h==="realtime"?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-800"}`,children:["📡 实时日志 (订阅推送)",_&&s.jsx("span",{className:"ml-1 w-2 h-2 bg-green-500 rounded-full inline-block"})]})]}),h==="realtime"&&s.jsx("div",{className:"flex items-center space-x-2",children:_?s.jsxs("button",{onClick:u,className:"flex items-center space-x-1 px-3 py-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors",children:[s.jsx(a0,{size:14}),s.jsx("span",{children:"暂停"})]}):s.jsxs("button",{onClick:V,className:"flex items-center space-x-1 px-3 py-1 bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors",children:[s.jsx(c0,{size:14}),s.jsx("span",{children:"开始"})]})})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsxs("button",{onClick:()=>A(!k),className:"flex items-center space-x-1 px-3 py-1 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors",children:[s.jsx(Em,{size:16}),s.jsx("span",{children:"统计"})]}),s.jsxs("div",{className:"relative group",children:[s.jsxs("button",{className:"flex items-center space-x-1 px-3 py-1 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors",children:[s.jsx(Qh,{size:16}),s.jsx("span",{children:"导出"})]}),s.jsxs("div",{className:"absolute right-0 mt-1 w-32 bg-white rounded-md shadow-lg border border-gray-200 py-1 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10",children:[s.jsx("button",{onClick:()=>ne("json"),className:"w-full text-left px-3 py-1 text-sm text-gray-700 hover:bg-gray-100",children:"JSON格式"}),s.jsx("button",{onClick:()=>ne("csv"),className:"w-full text-left px-3 py-1 text-sm text-gray-700 hover:bg-gray-100",children:"CSV格式"}),s.jsx("button",{onClick:()=>ne("txt"),className:"w-full text-left px-3 py-1 text-sm text-gray-700 hover:bg-gray-100",children:"文本格式"})]})]}),s.jsxs("button",{onClick:w,className:"flex items-center space-x-1 px-3 py-1 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors",children:[s.jsx(o0,{size:16}),s.jsx("span",{children:"清空"})]}),s.jsxs("button",{onClick:Q,className:"flex items-center space-x-1 px-3 py-1 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors",children:[s.jsx(hr,{size:16}),s.jsx("span",{children:"刷新"})]})]})]}),s.jsx(h0,{filters:i,modules:z,levels:D,onFiltersChange:L,onSearch:Y})]}),s.jsxs("div",{className:"flex-1 flex overflow-hidden",children:[s.jsxs("div",{className:`${p?"w-2/3":"w-full"} flex flex-col transition-all duration-300`,children:[k&&J&&s.jsx("div",{className:"border-b border-gray-200",children:s.jsx(b0,{stats:J})}),s.jsx("div",{className:"flex-1 overflow-hidden",children:s.jsx(g0,{logs:X,loading:f,error:g,hasMore:C&&h==="history",total:M,viewMode:h,onLogSelect:S,onLoadMore:te})})]}),p&&s.jsx("div",{className:"w-1/3 border-l border-gray-200 bg-white",children:s.jsx(y0,{log:p,onClose:()=>S(null)})})]}),s.jsx("div",{className:"bg-white border-t border-gray-200 px-4 py-2",children:s.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-600",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsxs("span",{className:"font-medium",children:["📊 总计: ",h==="history"?M:ae.length," 条"]}),s.jsxs("span",{children:["👁️ 当前显示: ",X.length," 条"]}),s.jsx("span",{className:"text-xs text-gray-500",children:h==="history"?"• 历史日志来自后端自动存储的日志文件":"• 实时日志通过WebSocket订阅获取"}),h==="realtime"&&s.jsxs("span",{className:"flex items-center space-x-1",children:[s.jsx("span",{className:`w-2 h-2 rounded-full ${_?"bg-green-500":"bg-gray-400"}`}),s.jsx("span",{children:_?"已连接":"未连接"})]})]}),s.jsxs("div",{className:"flex items-center space-x-4",children:[g&&s.jsxs("span",{className:"text-red-600",children:["❌ ",g]}),f&&s.jsxs("span",{className:"flex items-center space-x-1",children:[s.jsx(hr,{size:14,className:"animate-spin"}),s.jsx("span",{children:"加载中..."})]})]})]})})]})},p0=()=>{const[i,d]=v.useState(null),[h,x]=v.useState(null),[p,S]=v.useState([]),[k,A]=v.useState(5),[b,f]=v.useState(!1),[g,C]=v.useState(""),M=v.useRef(!1),J=["61","60","59","58","57"],z=async()=>{try{const _=await ye.getCacheStatus();if(_.success&&_.data){const V=_.data;d(V),M.current||(S(V.enabled_machines||[]),M.current=!0),A(Math.round(V.interval/6e10)||5)}}catch(_){console.error("加载缓存状态失败:",_)}},D=async()=>{try{const _=await ye.getCacheData();_.success&&_.data&&x(_.data)}catch(_){console.error("加载缓存数据失败:",_)}};v.useEffect(()=>{const _=async()=>{await z(),await D()};_();const V=setInterval(_,3e3);return()=>clearInterval(V)},[]);const j=async()=>{if(p.length===0){C("请选择要缓存的细纱机");return}f(!0),C("");try{const _=await ye.startCache({machines:p,interval:k});_.success?(C("缓存服务已启动"),await z(),await D()):C(`启动失败: ${_.error}`)}catch(_){C(`启动失败: ${_.message}`)}finally{f(!1)}},te=async()=>{f(!0),C("");try{const _=await ye.stopCache();_.success?(C("缓存服务已停止"),await z(),await D()):C(`停止失败: ${_.error}`)}catch(_){C(`停止失败: ${_.message}`)}finally{f(!1)}},I=async()=>{f(!0),C("");try{const _=await ye.clearCache();_.success?(C("缓存已清空"),await z(),await D()):C(`清空失败: ${_.error}`)}catch(_){C(`清空失败: ${_.message}`)}finally{f(!1)}},le=_=>{S(V=>V.includes(_)?V.filter(u=>u!==_):[...V,_])},ae=_=>{var Y,L;if(!(i!=null&&i.machine_status[_]))return{status:"未启用",color:"text-gray-500",bg:"bg-gray-50",icon:"⚫"};const V=i.machine_status[_],u=new Date(V.last_update),X=Math.floor((new Date().getTime()-u.getTime())/6e4);return V.is_valid?X>30?{status:`过期 (${X}分钟前)`,color:"text-orange-600",bg:"bg-orange-50",icon:"⚠️",detail:`数据${((Y=V.data)==null?void 0:Y.length)||0}条`}:{status:`正常 (${X}分钟前)`,color:"text-green-600",bg:"bg-green-50",icon:"✅",detail:`数据${((L=V.data)==null?void 0:L.length)||0}条`}:{status:`错误 (${V.error_count}次)`,color:"text-red-600",bg:"bg-red-50",icon:"❌",detail:V.last_error}};return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"断头数据缓存"}),s.jsx("div",{className:"text-sm text-gray-500",children:"车间网络故障时使用缓存数据"})]}),s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"⚙️"}),"缓存配置"]}),s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"选择要缓存的细纱机："}),s.jsx("div",{className:"grid grid-cols-5 gap-3",children:J.map(_=>s.jsx("button",{onClick:()=>le(_),disabled:i==null?void 0:i.is_running,className:`p-3 rounded-lg border-2 transition-colors ${p.includes(_)?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-300 hover:border-gray-400"} ${i!=null&&i.is_running?"opacity-50 cursor-not-allowed":""}`,children:s.jsxs("div",{className:"text-lg font-bold",children:[_,"号机"]})},_))})]}),s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"缓存间隔时间（分钟）："}),s.jsx("input",{type:"number",min:"1",max:"60",value:k,onChange:_=>A(parseInt(_.target.value)||5),disabled:i==null?void 0:i.is_running,className:"input w-32"})]}),s.jsxs("div",{className:"flex space-x-3",children:[s.jsx("button",{onClick:j,disabled:b||(i==null?void 0:i.is_running),className:"btn btn-primary",children:b?"启动中...":"开始缓存"}),s.jsx("button",{onClick:te,disabled:b||!(i!=null&&i.is_running),className:"btn btn-warning",children:b?"停止中...":"停止缓存"}),s.jsx("button",{onClick:I,disabled:b,className:"btn btn-secondary",children:b?"清理中...":"清空缓存"})]}),g&&s.jsx("div",{className:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:s.jsx("p",{className:"text-blue-800",children:g})})]}),s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"📊"}),"缓存状态",(i==null?void 0:i.is_running)&&s.jsx("span",{className:"ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"运行中"})]}),i&&s.jsxs("div",{className:"grid grid-cols-3 gap-4 mb-6",children:[s.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[s.jsx("div",{className:"text-2xl font-bold text-blue-600",children:i.total_hits}),s.jsx("div",{className:"text-sm text-gray-600",children:"缓存命中"})]}),s.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[s.jsx("div",{className:"text-2xl font-bold text-orange-600",children:i.total_misses}),s.jsx("div",{className:"text-sm text-gray-600",children:"缓存未命中"})]}),s.jsxs("div",{className:"text-center p-3 bg-gray-50 rounded-lg",children:[s.jsxs("div",{className:"text-2xl font-bold text-green-600",children:[i.total_hits+i.total_misses>0?Math.round(i.total_hits/(i.total_hits+i.total_misses)*100):0,"%"]}),s.jsx("div",{className:"text-sm text-gray-600",children:"命中率"})]})]}),s.jsx("div",{className:"space-y-3",children:J.map(_=>{const V=ae(_);return s.jsx("div",{className:`p-3 rounded-lg border ${V.bg}`,children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("span",{className:"text-lg",children:V.icon}),s.jsxs("span",{className:"font-medium",children:[_,"号机"]}),s.jsx("span",{className:`text-sm ${V.color}`,children:V.status})]}),V.detail&&s.jsx("span",{className:"text-sm text-gray-600",children:V.detail})]})},_)})})]}),h&&s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"🗃️"}),"缓存数据详情"]}),s.jsx("div",{className:"space-y-4",children:Object.entries(h).map(([_,V])=>s.jsxs("div",{className:"border rounded-lg p-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-3",children:[s.jsxs("h4",{className:"font-medium text-lg",children:[_,"号机"]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:`px-2 py-1 rounded text-xs ${V.is_valid?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:V.is_valid?"数据有效":"数据无效"}),s.jsx("span",{className:"text-sm text-gray-500",children:new Date(V.last_update).toLocaleString()})]})]}),V.data&&V.data.length>0?s.jsxs("div",{children:[s.jsxs("div",{className:"text-sm text-gray-600 mb-2",children:["断头数据 (",V.data.length," 条)"]}),s.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 max-h-40 overflow-y-auto",children:V.data.slice(0,20).map((u,q)=>s.jsxs("div",{className:"bg-gray-50 p-2 rounded text-sm",children:[s.jsxs("div",{className:"font-medium",children:["锭号: ",u.spindleNo]}),s.jsx("div",{className:"text-gray-600",children:u.side===1?"左侧":"右侧"}),s.jsx("div",{className:"text-xs text-gray-500",children:new Date(u.startTime).toLocaleString()})]},q))}),V.data.length>20&&s.jsxs("div",{className:"text-center text-sm text-gray-500 mt-2",children:["还有 ",V.data.length-20," 条数据..."]})]}):s.jsx("div",{className:"text-gray-500 text-center py-4",children:"暂无缓存数据"}),V.last_error&&s.jsxs("div",{className:"mt-3 p-2 bg-red-50 border border-red-200 rounded",children:[s.jsxs("div",{className:"text-red-800 text-sm",children:[s.jsx("strong",{children:"最近错误:"})," ",V.last_error]}),s.jsxs("div",{className:"text-red-600 text-xs mt-1",children:["错误次数: ",V.error_count]})]})]},_))})]})]})},j0=()=>{const[i,d]=v.useState({isRunning:!1,isConnected:!1,robotNo:"AGV001",currentTask:null,statistics:{totalTasks:0,completedTasks:0,failedTasks:0,totalRunTime:0},lastHeartbeat:null,connectionStatus:"disconnected"}),[h,x]=v.useState(!1),[p,S]=v.useState(null),[k,A]=v.useState([]),[b,f]=v.useState({isRunning:!1,successCount:0,failCount:0}),[g,C]=v.useState({taskTestEnabled:!1,totalTasks:0,successCount:0,failCount:0,recentTasks:[]}),[M,J]=v.useState({currentTaskId:"",taskState:"none",startNotifications:[],completeNotifications:[]}),[z,D]=v.useState(!1),j=async()=>{try{D(!0);const L=await(await fetch("/api/zmq/heartbeat-test/start",{method:"POST",headers:{"Content-Type":"application/json"}})).json();L.success?(u("✅ 心跳测试已启动 - 每1分钟发送一次心跳消息"),u("📡 连接目标: tcp://172.28.8.3:5556"),u('🔄 第一条消息: {"instruction":0,"code":true,"content":{"robotNo":"AGV001","state":true}}'),u("🎯 任务下发测试已同时启用 - 可接收调度系统任务指令(200-203)"),u("⏰ 任务生命周期测试已启用 - 收到任务后45秒发送开始，再45秒发送完成"),I()):u(`❌ 启动心跳测试失败: ${L.error}`)}catch(Y){u(`启动心跳测试失败: ${Y.message}`)}finally{D(!1)}},te=async()=>{try{D(!0);const L=await(await fetch("/api/zmq/heartbeat-test/stop",{method:"POST",headers:{"Content-Type":"application/json"}})).json();L.success?(u("✅ 心跳测试已停止"),u("🎯 任务下发测试已同时停止"),u("⏰ 任务生命周期测试已同时停止"),I()):u(`停止心跳测试失败: ${L.error}`)}catch(Y){u(`停止心跳测试失败: ${Y.message}`)}finally{D(!1)}},I=async()=>{try{const L=await(await fetch("/api/zmq/heartbeat-test/status")).json();if(L.success&&L.data){const K=L.data;K.heartbeat&&f(K.heartbeat),K.task&&C(K.task),K.taskLifecycle&&J(K.taskLifecycle)}}catch(Y){console.error("获取测试状态失败:",Y)}},le=async()=>{try{S(null);const Y=await vh();d(Y),u("状态更新成功")}catch(Y){const L="获取自动看车状态失败: "+Y.message;console.error(L),S(L),u(L)}},ae=async()=>{x(!0),S(null);try{await bh(),u("自动看车功能已启动"),d(Y=>({...Y,isRunning:!0}))}catch(Y){S("启动失败: "+Y.message),u("启动失败: "+Y.message)}finally{x(!1)}},_=async()=>{x(!0),S(null);try{await yh(),u("自动看车功能已停止"),d(Y=>({...Y,isRunning:!1,isConnected:!1}))}catch(Y){S("停止失败: "+Y.message),u("停止失败: "+Y.message)}finally{x(!1)}},V=async()=>{x(!0),S(null);try{const Y=await Am();u(`一键上线成功: ${JSON.stringify(Y)}`),console.log("一键上线成功:",Y)}catch(Y){const L=Y.message;S(`一键上线失败: ${L}`),u(`一键上线失败: ${L}`),console.error("一键上线失败:",Y)}finally{x(!1)}},u=Y=>{const L=new Date().toLocaleTimeString();A(K=>[`[${L}] ${Y}`,...K].slice(0,100))};v.useEffect(()=>{le();const Y=setInterval(()=>{b.isRunning&&I()},5e3);return()=>clearInterval(Y)},[b.isRunning]);const q=()=>{switch(i.connectionStatus){case"connected":return"bg-green-100 text-green-800";case"connecting":return"bg-yellow-100 text-yellow-800";case"error":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},X=Y=>{const L=Math.floor(Y/3600),K=Math.floor(Y%3600/60),ne=Y%60;return`${L}小时 ${K}分 ${ne}秒`};return s.jsxs("div",{className:"p-6 space-y-6",children:[s.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[s.jsxs("div",{className:"flex items-center justify-between mb-6",children:[s.jsxs("div",{children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-800",children:"自动看车控制中心"}),s.jsx("p",{className:"text-gray-600 mt-1",children:"与调度系统协同，自动执行看车任务"})]}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:`px-4 py-2 rounded-full text-sm font-medium ${q()}`,children:i.connectionStatus==="connected"?"🟢 已连接":i.connectionStatus==="connecting"?"🟡 连接中":i.connectionStatus==="error"?"🔴 连接错误":"⚪ 未连接"}),s.jsx("button",{onClick:le,className:"px-4 py-2 rounded-lg font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 transition-colors",children:"🔄 刷新状态"}),s.jsx("button",{onClick:V,disabled:h,className:`px-6 py-3 rounded-lg font-medium text-white transition-all transform hover:scale-105 bg-green-500 hover:bg-green-600 ${h?"opacity-50 cursor-not-allowed":""}`,children:h?s.jsxs("span",{className:"flex items-center",children:[s.jsxs("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[s.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),s.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"处理中..."]}):"🚀 一键上线"}),s.jsx("button",{onClick:i.isRunning?_:ae,disabled:h,className:`px-8 py-3 rounded-lg font-medium text-white transition-all transform hover:scale-105 ${i.isRunning?"bg-red-500 hover:bg-red-600":"bg-blue-500 hover:bg-blue-600"} ${h?"opacity-50 cursor-not-allowed":""}`,children:h?s.jsxs("span",{className:"flex items-center",children:[s.jsxs("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[s.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),s.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"处理中..."]}):i.isRunning?"停止自动看车":"开始自动看车"})]})]}),p&&s.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg",children:p})]}),s.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"🫀 ZMQ心跳通信测试"}),s.jsx("p",{className:"text-gray-600 text-sm mb-4",children:"独立测试与调度系统的ZMQ心跳通信，每1分钟发送一次心跳"}),s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("button",{onClick:j,disabled:b.isRunning||z,className:`px-4 py-2 rounded-lg font-medium text-white transition-colors ${b.isRunning||z?"bg-gray-400 cursor-not-allowed":"bg-green-500 hover:bg-green-600"}`,children:z?"启动中...":"▶️ 开始测试"}),s.jsx("button",{onClick:te,disabled:!b.isRunning||z,className:`px-4 py-2 rounded-lg font-medium text-white transition-colors ${!b.isRunning||z?"bg-gray-400 cursor-not-allowed":"bg-red-500 hover:bg-red-600"}`,children:z?"停止中...":"⏹️ 停止测试"})]}),s.jsx("div",{className:`px-3 py-1 rounded-full text-sm font-medium ${b.isRunning?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:b.isRunning?"🟢 测试中":"⚪ 已停止"})]}),s.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-green-600",children:b.successCount}),s.jsx("div",{className:"text-sm text-gray-600",children:"成功次数"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-red-600",children:b.failCount}),s.jsx("div",{className:"text-sm text-gray-600",children:"失败次数"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-blue-600",children:b.lastResult?`${Math.round(b.lastResult.responseTime/1e6)}ms`:"-"}),s.jsx("div",{className:"text-sm text-gray-600",children:"响应时间"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-purple-600",children:b.nextTestTime?new Date(b.nextTestTime).toLocaleTimeString():"-"}),s.jsx("div",{className:"text-sm text-gray-600",children:"下次测试"})]})]}),b.lastResult&&s.jsxs("div",{className:`p-3 rounded-lg text-sm ${b.lastResult.success?"bg-green-50 border border-green-200 text-green-800":"bg-red-50 border border-red-200 text-red-800"}`,children:[s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("span",{children:b.lastResult.success?"✅ 最后测试成功":"❌ 最后测试失败"}),s.jsx("span",{className:"text-xs",children:new Date(b.lastResult.timestamp).toLocaleString()})]}),b.lastResult.error&&s.jsxs("div",{className:"mt-2 text-xs opacity-75",children:["错误: ",b.lastResult.error]})]})]}),s.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"🎯 ZMQ任务下发测试"}),s.jsx("p",{className:"text-gray-600 text-sm mb-4",children:"测试接收调度系统的任务下发消息（指令200-203），与心跳测试并行运行"}),s.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:`text-2xl font-bold ${g.taskTestEnabled?"text-green-600":"text-gray-400"}`,children:g.taskTestEnabled?"✅":"❌"}),s.jsx("div",{className:"text-sm text-gray-600",children:"测试状态"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-blue-600",children:g.totalTasks}),s.jsx("div",{className:"text-sm text-gray-600",children:"总任务数"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-green-600",children:g.successCount}),s.jsx("div",{className:"text-sm text-gray-600",children:"成功处理"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-red-600",children:g.failCount}),s.jsx("div",{className:"text-sm text-gray-600",children:"处理失败"})]})]}),g.recentTasks&&g.recentTasks.length>0&&s.jsxs("div",{className:"mt-4",children:[s.jsx("h4",{className:"text-md font-medium text-gray-700 mb-2",children:"最近接收的任务"}),s.jsx("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:g.recentTasks.slice(0,5).map((Y,L)=>s.jsx("div",{className:`p-3 rounded-lg text-sm border ${Y.success?"bg-green-50 border-green-200 text-green-800":"bg-red-50 border-red-200 text-red-800"}`,children:s.jsxs("div",{className:"flex justify-between items-start",children:[s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"font-medium",children:["指令",Y.instruction,": ",Y.taskId||"无任务ID"]}),s.jsx("div",{className:"text-xs opacity-75 mt-1",children:Y.message}),Y.laneNos&&Y.laneNos.length>0&&s.jsxs("div",{className:"text-xs opacity-75 mt-1",children:["车道: ",Y.laneNos.join(", ")]})]}),s.jsxs("div",{className:"text-xs text-right",children:[s.jsx("div",{children:new Date(Y.timestamp).toLocaleTimeString()}),s.jsxs("div",{className:"mt-1",children:[Math.round(Y.responseTime/1e6),"ms"]})]})]})},L))})]}),(!g.recentTasks||g.recentTasks.length===0)&&g.taskTestEnabled&&s.jsx("div",{className:"text-center py-4 text-gray-500",children:"等待调度系统发送任务下发消息..."})]}),s.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"⏰ ZMQ任务生命周期测试"}),s.jsx("p",{className:"text-gray-600 text-sm mb-4",children:"测试完整的任务生命周期：接收任务下发 → 45秒后发送任务开始(110) → 45秒后发送任务完成(130)"}),s.jsx("div",{className:"mb-4 p-4 rounded-lg bg-gray-50",children:s.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-lg font-bold text-blue-600",children:M.currentTaskId||"无"}),s.jsx("div",{className:"text-sm text-gray-600",children:"当前任务ID"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:`text-lg font-bold ${M.taskState==="none"?"text-gray-400":M.taskState==="received"?"text-yellow-600":M.taskState==="started"?"text-blue-600":M.taskState==="completed"?"text-green-600":"text-gray-400"}`,children:M.taskState==="none"?"无任务":M.taskState==="received"?"已接收":M.taskState==="started"?"已开始":M.taskState==="completed"?"已完成":"未知"}),s.jsx("div",{className:"text-sm text-gray-600",children:"任务状态"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-lg font-bold text-purple-600",children:M.taskStartTime?new Date(M.taskStartTime).toLocaleTimeString():"-"}),s.jsx("div",{className:"text-sm text-gray-600",children:"开始时间"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-lg font-bold text-green-600",children:M.taskCompleteTime?new Date(M.taskCompleteTime).toLocaleTimeString():"-"}),s.jsx("div",{className:"text-sm text-gray-600",children:"完成时间"})]})]})}),M.startNotifications&&M.startNotifications.length>0&&s.jsxs("div",{className:"mb-4",children:[s.jsx("h4",{className:"text-md font-medium text-gray-700 mb-2",children:"任务开始通知历史 (指令110)"}),s.jsx("div",{className:"space-y-2 max-h-32 overflow-y-auto",children:M.startNotifications.slice(-3).map((Y,L)=>s.jsx("div",{className:`p-3 rounded-lg text-sm border ${Y.success?"bg-green-50 border-green-200 text-green-800":"bg-red-50 border-red-200 text-red-800"}`,children:s.jsxs("div",{className:"flex justify-between items-start",children:[s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"font-medium",children:[Y.success?"✅":"❌"," 任务开始通知: ",Y.taskId]}),s.jsx("div",{className:"text-xs opacity-75 mt-1",children:Y.message})]}),s.jsxs("div",{className:"text-xs text-right",children:[s.jsx("div",{children:new Date(Y.timestamp).toLocaleTimeString()}),s.jsxs("div",{className:"mt-1",children:[Math.round(Y.responseTime/1e6),"ms"]})]})]})},L))})]}),M.completeNotifications&&M.completeNotifications.length>0&&s.jsxs("div",{className:"mb-4",children:[s.jsx("h4",{className:"text-md font-medium text-gray-700 mb-2",children:"任务完成通知历史 (指令130)"}),s.jsx("div",{className:"space-y-2 max-h-32 overflow-y-auto",children:M.completeNotifications.slice(-3).map((Y,L)=>s.jsx("div",{className:`p-3 rounded-lg text-sm border ${Y.success?"bg-green-50 border-green-200 text-green-800":"bg-red-50 border-red-200 text-red-800"}`,children:s.jsxs("div",{className:"flex justify-between items-start",children:[s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"font-medium",children:[Y.success?"✅":"❌"," 任务完成通知: ",Y.taskId]}),s.jsx("div",{className:"text-xs opacity-75 mt-1",children:Y.message})]}),s.jsxs("div",{className:"text-xs text-right",children:[s.jsx("div",{children:new Date(Y.timestamp).toLocaleTimeString()}),s.jsxs("div",{className:"mt-1",children:[Math.round(Y.responseTime/1e6),"ms"]})]})]})},L))})]}),M.taskState==="none"&&s.jsx("div",{className:"text-center py-4 text-gray-500",children:"等待调度系统发送任务下发消息以启动生命周期测试..."}),M.taskState==="received"&&s.jsx("div",{className:"text-center py-4 text-yellow-600",children:"⏳ 任务已接收，等待45秒后发送任务开始通知..."}),M.taskState==="started"&&s.jsx("div",{className:"text-center py-4 text-blue-600",children:"⏳ 任务已开始，等待45秒后发送任务完成通知..."}),M.taskState==="completed"&&s.jsx("div",{className:"text-center py-4 text-green-600",children:"🎉 任务生命周期已完成！"})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[s.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"机器人信息"}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"机器人编号"}),s.jsx("span",{className:"font-medium",children:i.robotNo})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"运行状态"}),s.jsx("span",{className:`font-medium ${i.isRunning?"text-green-600":"text-gray-500"}`,children:i.isRunning?"运行中":"已停止"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"最后心跳"}),s.jsx("span",{className:"font-medium text-sm",children:i.lastHeartbeat||"无"})]})]})]}),s.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"当前任务"}),i.currentTask?s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"任务ID"}),s.jsx("span",{className:"font-medium",children:i.currentTask.taskId})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"目标机器"}),s.jsx("span",{className:"font-medium",children:i.currentTask.machineNo})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"任务类型"}),s.jsx("span",{className:"font-medium",children:i.currentTask.taskType})]}),s.jsxs("div",{className:"mt-4",children:[s.jsxs("div",{className:"flex justify-between mb-1",children:[s.jsx("span",{className:"text-gray-600",children:"进度"}),s.jsxs("span",{className:"font-medium",children:[i.currentTask.progress,"%"]})]}),s.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:s.jsx("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${i.currentTask.progress}%`}})})]})]}):s.jsx("div",{className:"text-gray-500 text-center py-8",children:"暂无执行中的任务"})]}),s.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"任务统计"}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"总任务数"}),s.jsx("span",{className:"font-medium",children:i.statistics.totalTasks})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"已完成"}),s.jsx("span",{className:"font-medium text-green-600",children:i.statistics.completedTasks})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"失败任务"}),s.jsx("span",{className:"font-medium text-red-600",children:i.statistics.failedTasks})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"总运行时间"}),s.jsx("span",{className:"font-medium text-sm",children:X(i.statistics.totalRunTime)})]})]})]})]}),s.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"运行日志"}),s.jsx("div",{className:"bg-gray-50 rounded-lg p-4 h-64 overflow-y-auto",children:k.length>0?s.jsx("div",{className:"space-y-1",children:k.map((Y,L)=>s.jsx("div",{className:"text-sm text-gray-700 font-mono",children:Y},L))}):s.jsx("div",{className:"text-gray-500 text-center py-8",children:"暂无日志信息"})})]})]})},N0=["M501","M601","M607","M602","M509","M603","M604","D602","D603","D600","M606","M608","M610","M611","D604","M500","M600","M510","M511"],_a=["connect_test","request_control","start_task","data_prepare","data_write","machine_complete","release_control"],Ma={autoStartMonitoring:!1,monitoringInterval:200,stepTimeout:30,showDetailedLogs:!0,enableNotifications:!0,theme:"auto"},Sm={M501:"PLC控制权确认",M601:"AGV请求PLC控制权",M607:"控制权交接请求",M602:"开始任务信号",M509:"PLC准备接收数据",M603:"数据写入完成",M604:"方向数据(0=左侧,1=右侧)",D602:"距离数据(低位)",D603:"距离数据(高位)",D600:"滚筒方向(1=左,2=右)",M606:"机台完成信号",M608:"调头信号",M610:"回到原点信号",M611:"原点方向",D604:"原点码值",M500:"心跳读取地址",M600:"心跳写入地址",M510:"AGV到达原点确认",M511:"调头完成确认"},Vs={connect_test:"测试PLC连接和心跳机制",request_control:"向PLC请求控制权 (M601=1 → M501=1)",start_task:"发送任务开始信号 (M602=1)",data_prepare:"检查PLC是否准备接收数据 (M509=1)",data_write:"写入方向和距离数据 (M604, D602-D603, D600)",machine_complete:"发送机台完成信号 (M606, M608, M610)",release_control:"释放PLC控制权 (M607=1 → M501=0)"},S0="http://localhost:8080/api",w0="ws://localhost:8080/api",C0={"Content-Type":"application/json",Accept:"application/json"},T0=3e4,Zn={maxAttempts:3,initialDelay:1e3,backoffFactor:2},Aa={maxAttempts:5,initialDelay:1e3,maxDelay:3e4,backoffFactor:2};class et extends Error{constructor(d,h,x){super(d),this.statusCode=h,this.response=x,this.name="PLCTestAPIError"}}class A0{constructor(d=S0,h=w0){rt(this,"wsConnection",null);rt(this,"wsReconnectAttempts",0);rt(this,"wsReconnectTimer",null);rt(this,"wsEventHandlers",[]);rt(this,"wsErrorHandlers",[]);rt(this,"wsCloseHandlers",[]);this.baseURL=d,this.wsBaseURL=h}async executeStep(d){const h=await this.makeRequest("POST",`/plc-test/step/${d}`);if(h.error||!h.result)throw new et(h.message||"执行步骤失败");return h.result}async executeWorkflow(){const d=await this.makeRequest("POST","/plc-test/workflow");if(d.error)throw new et(d.message||"启动工作流程失败")}async stopWorkflow(){const d=await this.makeRequest("POST","/plc-test/workflow/stop");if(d.error)throw new et(d.message||"停止工作流程失败")}async getStatus(){const d=await this.makeRequest("GET","/plc-test/status");if(d.error||!d.status)throw new et(d.message||"获取状态失败");return d.status}async getWorkflowSteps(){const d=await this.makeRequest("GET","/plc-test/steps");if(d.error||!d.steps)throw new et(d.message||"获取工作流程步骤失败");return d.steps}async getCurrentValues(){const d=await this.makeRequest("GET","/plc-test/monitor/values");if(d.error||!d.values)throw new et(d.message||"获取监控值失败");return d.values}async startMonitoring(){const d=await this.makeRequest("POST","/plc-test/monitor/start");if(d.error)throw new et(d.message||"启动监控失败")}async stopMonitoring(){const d=await this.makeRequest("POST","/plc-test/monitor/stop");if(d.error)throw new et(d.message||"停止监控失败")}async refreshValues(){const d=await this.makeRequest("POST","/plc-test/monitor/refresh");if(d.error)throw new et(d.message||"刷新监控值失败")}async setAddress(d){const h=await this.makeRequest("POST","/plc-test/tools/set-address",d);if(h.error)throw new et(h.message||"设置地址值失败")}async getAddress(d,h){const x=await this.makeRequest("GET",`/plc-test/tools/get-address/${d}?type=${h}`);if(x.error)throw new et(x.message||"读取地址值失败");return x.value}connectWebSocket(){return new Promise((d,h)=>{var p;if(((p=this.wsConnection)==null?void 0:p.readyState)===WebSocket.OPEN){d();return}this.disconnectWebSocket();const x=`${this.wsBaseURL}/plc-test/monitor/ws`;console.log("连接WebSocket:",x),this.wsConnection=new WebSocket(x),this.wsConnection.onopen=()=>{console.log("WebSocket连接已建立"),this.wsReconnectAttempts=0,d()},this.wsConnection.onmessage=S=>{try{const k=JSON.parse(S.data);this.wsEventHandlers.forEach(A=>A(k))}catch(k){console.error("解析WebSocket消息失败:",k)}},this.wsConnection.onerror=S=>{console.error("WebSocket错误:",S),this.wsErrorHandlers.forEach(k=>k(S)),h(new et("WebSocket连接失败"))},this.wsConnection.onclose=S=>{console.log("WebSocket连接已关闭:",S.code,S.reason),this.wsCloseHandlers.forEach(k=>k(S)),S.code!==1e3&&this.wsReconnectAttempts<Aa.maxAttempts&&this.scheduleReconnect()}})}disconnectWebSocket(){this.wsReconnectTimer&&(clearTimeout(this.wsReconnectTimer),this.wsReconnectTimer=null),this.wsConnection&&(this.wsConnection.close(1e3,"Manual disconnect"),this.wsConnection=null)}scheduleReconnect(){this.wsReconnectTimer&&clearTimeout(this.wsReconnectTimer);const d=Math.min(Aa.initialDelay*Math.pow(Aa.backoffFactor,this.wsReconnectAttempts),Aa.maxDelay);console.log(`WebSocket重连尝试 ${this.wsReconnectAttempts+1}/${Aa.maxAttempts}，${d}ms后重试`),this.wsReconnectTimer=setTimeout(()=>{this.wsReconnectAttempts++,this.connectWebSocket().catch(h=>{console.error("WebSocket重连失败:",h)})},d)}onWebSocketData(d){return this.wsEventHandlers.push(d),()=>{const h=this.wsEventHandlers.indexOf(d);h>-1&&this.wsEventHandlers.splice(h,1)}}onWebSocketError(d){return this.wsErrorHandlers.push(d),()=>{const h=this.wsErrorHandlers.indexOf(d);h>-1&&this.wsErrorHandlers.splice(h,1)}}onWebSocketClose(d){return this.wsCloseHandlers.push(d),()=>{const h=this.wsCloseHandlers.indexOf(d);h>-1&&this.wsCloseHandlers.splice(h,1)}}getWebSocketState(){var d;return((d=this.wsConnection)==null?void 0:d.readyState)??WebSocket.CLOSED}async makeRequest(d,h,x){return this.makeRequestWithRetry(d,h,x,Zn.maxAttempts)}async makeRequestWithRetry(d,h,x,p=1){const S=`${this.baseURL}${h}`,k={method:d,headers:C0,signal:AbortSignal.timeout(T0)};x&&d!=="GET"&&(k.body=JSON.stringify(x));try{console.log(`发起${d}请求:`,S);const A=await fetch(S,k);if(!A.ok)throw new et(`HTTP错误: ${A.status} ${A.statusText}`,A.status);const b=await A.json();return console.log(`${d}请求成功:`,S,b),b}catch(A){if(console.error(`${d}请求失败:`,S,A),p>1&&this.isRetryableError(A)){const b=Zn.initialDelay*Math.pow(Zn.backoffFactor,Zn.maxAttempts-p);return console.log(`${b}ms后重试 (剩余 ${p-1} 次)`),await this.delay(b),this.makeRequestWithRetry(d,h,x,p-1)}throw A instanceof et?A:new et(A instanceof Error?A.message:"请求失败")}}isRetryableError(d){return d.name==="AbortError"||d.name==="TimeoutError"?!0:d instanceof et&&d.statusCode?d.statusCode>=500:!!(d instanceof TypeError&&d.message.includes("fetch"))}delay(d){return new Promise(h=>setTimeout(h,d))}dispose(){this.disconnectWebSocket(),this.wsEventHandlers=[],this.wsErrorHandlers=[],this.wsCloseHandlers=[]}}const Et=new A0,{executeStep:P0,executeWorkflow:I0,stopWorkflow:eg,getStatus:tg,getWorkflowSteps:sg,getCurrentValues:lg,startMonitoring:ag,stopMonitoring:ng,refreshValues:cg,setAddress:ig,getAddress:rg,connectWebSocket:ug,disconnectWebSocket:dg,onWebSocketData:og,onWebSocketError:mg,onWebSocketClose:xg,getWebSocketState:fg}=Et,_0=({monitoringState:i,onStateChange:d,config:h=Ma,onNotification:x})=>{const[p,S]=v.useState(!1),[k,A]=v.useState({connected:!1,errorCount:0}),[b,f]=v.useState({}),[g,C]=v.useState(new Set),[M,J]=v.useState([]),z=()=>`${Date.now()}-${Math.random().toString(36).substr(2,9)}`,D=v.useCallback((u,q,X)=>{if(x&&h.enableNotifications){const Y={id:z(),type:u,title:q,message:X,timestamp:new Date().toISOString(),duration:u==="error"?5e3:3e3};x(Y)}},[x,h.enableNotifications]),j=v.useCallback(u=>{const{address:q,oldValue:X,newValue:Y,timestamp:L,message:K}=u;f(w=>{const Q=w[q]||{address:q,changes:[]},E={timestamp:L,oldValue:X,newValue:Y,source:"monitor"};return{...w,[q]:{...Q,changes:[E,...Q.changes].slice(0,50)}}}),C(w=>new Set(w).add(q)),setTimeout(()=>{C(w=>{const Q=new Set(w);return Q.delete(q),Q})},2e3),J(w=>[u,...w.slice(0,19)]),d({...i,lastUpdate:L,addresses:{...i.addresses,[q]:Y}}),["M501","M601","M602","M606"].includes(q)&&D("info","重要地址变化",K)},[i,d,h.enableNotifications,D]),te=v.useCallback(async()=>{if(!i.isActive){S(!0);try{const u=Et.onWebSocketData(j);await Et.connectWebSocket(),await Et.startMonitoring();const q={isActive:!0,lastUpdate:new Date().toISOString(),addresses:i.addresses};return d(q),A(X=>({...X,connected:!0,lastConnectTime:new Date().toISOString()})),D("success","监控已启动","正在实时监控PLC地址变化"),u}catch(u){const q=u instanceof Error?u.message:"未知错误";A(X=>({...X,connected:!1,errorCount:X.errorCount+1,lastError:q})),D("error","监控启动失败",q)}finally{S(!1)}}},[i,d,j,D]),I=v.useCallback(async()=>{if(i.isActive)try{await Et.stopMonitoring(),Et.disconnectWebSocket();const u={...i,isActive:!1};d(u),A(q=>({...q,connected:!1})),D("info","监控已停止","已停止PLC地址监控并断开连接")}catch(u){const q=u instanceof Error?u.message:"未知错误";D("error","停止监控失败",q)}},[i,d,D]),le=v.useCallback(async()=>{try{const u=await Et.getCurrentValues();d({...i,lastUpdate:new Date().toISOString(),addresses:{...i.addresses,...u}}),D("success","刷新成功","已更新所有地址的当前值")}catch(u){const q=u instanceof Error?u.message:"未知错误";D("error","刷新失败",q)}},[i,d,D]),ae=v.useMemo(()=>N0.map(u=>{var Y;const q=((Y=b[u])==null?void 0:Y.changes)||[],X=q[0];return{address:u,description:Sm[u],currentValue:i.addresses[u],lastChanged:(X==null?void 0:X.timestamp)||"",isChanged:g.has(u),history:q.slice(0,5)}}),[i.addresses,b,g]);v.useEffect(()=>{h.autoStartMonitoring&&!i.isActive&&te()},[h.autoStartMonitoring,i.isActive,te]);const _=u=>u==null?"未知":typeof u=="boolean"?u?"真":"假":typeof u=="number"?u.toString():String(u),V=(u,q)=>{const X="px-2 py-1 rounded text-sm font-mono";return q?`${X} bg-yellow-200 text-yellow-800 animate-pulse`:typeof u=="boolean"?u?`${X} bg-green-100 text-green-800`:`${X} bg-gray-100 text-gray-800`:`${X} bg-blue-100 text-blue-800`};return s.jsxs("div",{className:"p-6 bg-white rounded-lg shadow-lg",children:[s.jsxs("div",{className:"flex justify-between items-center mb-6",children:[s.jsxs("div",{children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-800",children:"PLC实时监控"}),s.jsx("p",{className:"text-gray-600 mt-1",children:"监控18个关键PLC地址的实时变化"})]}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:`w-3 h-3 rounded-full ${k.connected?"bg-green-400":"bg-red-400"}`}),s.jsx("span",{className:"text-sm text-gray-600",children:k.connected?"已连接":"未连接"})]}),s.jsx("button",{onClick:le,disabled:!k.connected,className:"px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300",children:"刷新"}),i.isActive?s.jsx("button",{onClick:I,className:"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600",children:"停止监控"}):s.jsx("button",{onClick:te,disabled:p,className:"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-300",children:p?"连接中...":"开始监控"})]})]}),i.lastUpdate&&s.jsxs("div",{className:"mb-4 p-3 bg-gray-50 rounded text-sm text-gray-600",children:["最后更新时间: ",new Date(i.lastUpdate).toLocaleString("zh-CN"),k.errorCount>0&&s.jsxs("span",{className:"ml-4 text-red-600",children:["错误次数: ",k.errorCount]})]}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[s.jsxs("div",{className:"lg:col-span-2",children:[s.jsx("h3",{className:"text-lg font-semibold mb-3",children:"地址监控"}),s.jsx("div",{className:"overflow-hidden border border-gray-200 rounded-lg",children:s.jsx("div",{className:"overflow-x-auto",children:s.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[s.jsx("thead",{className:"bg-gray-50",children:s.jsxs("tr",{children:[s.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"地址"}),s.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"描述"}),s.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"当前值"}),s.jsx("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"最后变化"})]})}),s.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:ae.map(u=>s.jsxs("tr",{className:u.isChanged?"bg-yellow-50":"hover:bg-gray-50",children:[s.jsx("td",{className:"px-4 py-3 whitespace-nowrap",children:s.jsx("span",{className:"font-mono text-sm font-medium text-gray-900",children:u.address})}),s.jsx("td",{className:"px-4 py-3",children:s.jsx("span",{className:"text-sm text-gray-700",children:u.description})}),s.jsx("td",{className:"px-4 py-3 whitespace-nowrap",children:s.jsx("span",{className:V(u.currentValue,u.isChanged),children:_(u.currentValue)})}),s.jsx("td",{className:"px-4 py-3 whitespace-nowrap text-sm text-gray-500",children:u.lastChanged?new Date(u.lastChanged).toLocaleTimeString("zh-CN"):"无变化"})]},u.address))})]})})})]}),s.jsxs("div",{className:"lg:col-span-1",children:[s.jsx("h3",{className:"text-lg font-semibold mb-3",children:"最近变化"}),s.jsx("div",{className:"space-y-2 max-h-96 overflow-y-auto",children:M.length===0?s.jsx("div",{className:"text-center text-gray-500 py-4",children:"暂无变化记录"}):M.map((u,q)=>s.jsxs("div",{className:"p-3 bg-gray-50 rounded border-l-4 border-blue-400",children:[s.jsxs("div",{className:"flex justify-between items-start mb-1",children:[s.jsx("span",{className:"font-mono text-sm font-medium",children:u.address}),s.jsx("span",{className:"text-xs text-gray-500",children:new Date(u.timestamp).toLocaleTimeString("zh-CN")})]}),s.jsx("div",{className:"text-sm text-gray-700 mb-1",children:Sm[u.address]}),s.jsxs("div",{className:"text-xs text-gray-600",children:[s.jsx("span",{className:"text-red-600",children:_(u.oldValue)}),s.jsx("span",{className:"mx-2",children:"→"}),s.jsx("span",{className:"text-green-600",children:_(u.newValue)})]}),u.message&&s.jsx("div",{className:"text-xs text-blue-600 mt-1",children:u.message})]},q))})]})]}),k.lastError&&s.jsx("div",{className:"mt-4 p-3 bg-red-50 border border-red-200 rounded",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:"text-red-400 mr-2",children:"⚠️"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-red-800",children:"连接错误"}),s.jsx("p",{className:"text-sm text-red-600",children:k.lastError})]})]})})]})},M0=({workflowState:i,onStateChange:d,config:h=Ma,onNotification:x})=>{const[p,S]=v.useState("connect_test"),[k,A]=v.useState({}),[b,f]=v.useState(new Set),[g,C]=v.useState(new Set),M=()=>`${Date.now()}-${Math.random().toString(36).substr(2,9)}`,J=v.useCallback((u,q,X)=>{if(x){const Y={id:M(),type:u,title:q,message:X,timestamp:new Date().toISOString(),duration:u==="error"?5e3:3e3};x(Y)}},[x]),z=v.useCallback(async u=>{if(!b.has(u)){f(q=>new Set(q).add(u));try{J("info","开始执行步骤",`正在执行步骤: ${Vs[u]}`);const q=await Et.executeStep(u);A(Y=>({...Y,[u]:q})),C(Y=>new Set(Y).add(u));const X=[...i.logs,`${new Date().toLocaleTimeString("zh-CN")} - 步骤 ${u} 执行${q.status==="success"?"成功":"失败"}`];d({...i,currentStep:u,logs:X.slice(-50)}),q.status==="success"?J("success","步骤执行成功",`步骤 ${Vs[u]} 执行完成`):J("error","步骤执行失败",q.error||"步骤执行过程中发生错误")}catch(q){const X=q instanceof Error?q.message:"未知错误";J("error","步骤执行失败",X);const Y=[...i.logs,`${new Date().toLocaleTimeString("zh-CN")} - 步骤 ${u} 执行失败: ${X}`];d({...i,logs:Y.slice(-50)})}finally{f(q=>{const X=new Set(q);return X.delete(u),X})}}},[b,i,d,J]),D=v.useCallback(u=>{A(q=>{const X={...q};return delete X[u],X}),C(q=>{const X=new Set(q);return X.delete(u),X})},[]),j=v.useCallback(()=>{A({}),C(new Set),d({...i,logs:[]}),J("info","已清除","已清除所有步骤执行结果")},[i,d,J]),te=v.useCallback(u=>{C(q=>{const X=new Set(q);return X.has(u)?X.delete(u):X.add(u),X})},[]),I=v.useMemo(()=>_a.map(u=>{const q=k[u];return{stepId:u,name:u,description:Vs[u],isRunning:b.has(u),result:q,lastExecuted:q==null?void 0:q.endTime}}),[k,b]),le=u=>{if(u.isRunning)return"border-yellow-300 bg-yellow-50";if(u.result)switch(u.result.status){case"success":return"border-green-300 bg-green-50";case"failed":return"border-red-300 bg-red-50";default:return"border-gray-300 bg-gray-50"}return"border-gray-300 bg-white"},ae=u=>{if(u.isRunning)return"⏳";if(u.result)switch(u.result.status){case"success":return"✅";case"failed":return"❌";default:return"⚪"}return"⚪"},_=u=>({write:"写入",read:"读取",wait:"等待",summary:"总结",check:"检查"})[u.operation]||u.operation,V=u=>{const q="px-2 py-1 rounded text-xs";return u.success?`${q} bg-green-100 text-green-800`:`${q} bg-red-100 text-red-800`};return s.jsxs("div",{className:"p-6 bg-white rounded-lg shadow-lg",children:[s.jsxs("div",{className:"flex justify-between items-center mb-6",children:[s.jsxs("div",{children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-800",children:"单步控制器"}),s.jsx("p",{className:"text-gray-600 mt-1",children:"选择并执行单个PLC工作流程步骤"})]}),s.jsx("div",{className:"flex items-center space-x-3",children:s.jsx("button",{onClick:j,className:"px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600",children:"清除所有结果"})})]}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-semibold mb-3",children:"步骤选择"}),s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"选择要执行的步骤:"}),s.jsx("select",{value:p,onChange:u=>S(u.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:_a.map(u=>s.jsxs("option",{value:u,children:[u," - ",Vs[u]]},u))})]}),s.jsxs("div",{className:"mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200",children:[s.jsx("h4",{className:"font-medium text-blue-800 mb-2",children:"步骤详情"}),s.jsxs("div",{className:"text-sm text-blue-700",children:[s.jsxs("p",{children:[s.jsx("strong",{children:"步骤ID:"})," ",p]}),s.jsxs("p",{children:[s.jsx("strong",{children:"描述:"})," ",Vs[p]]})]})]}),s.jsx("button",{onClick:()=>z(p),disabled:b.has(p)||i.isRunning,className:"w-full px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed",children:b.has(p)?"执行中...":`执行步骤: ${p}`}),i.isRunning&&s.jsx("p",{className:"mt-2 text-sm text-orange-600",children:"⚠️ 工作流程正在运行，请等待完成后再执行单步操作"})]}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-semibold mb-3",children:"执行日志"}),s.jsx("div",{className:"h-64 p-3 bg-gray-50 rounded border overflow-y-auto",children:i.logs.length===0?s.jsx("div",{className:"text-center text-gray-500 py-8",children:"暂无执行日志"}):s.jsx("div",{className:"space-y-1",children:i.logs.slice(-20).map((u,q)=>s.jsx("div",{className:"text-sm text-gray-700 font-mono",children:u},q))})})]})]}),s.jsxs("div",{className:"mt-8",children:[s.jsx("h3",{className:"text-lg font-semibold mb-4",children:"步骤执行状态"}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:I.map(u=>{var q;return s.jsxs("div",{className:`p-4 rounded-lg border-2 ${le(u)}`,children:[s.jsxs("div",{className:"flex justify-between items-start mb-2",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-lg",children:ae(u)}),s.jsx("span",{className:"font-medium text-sm",children:u.stepId})]}),u.result&&s.jsxs("div",{className:"flex space-x-1",children:[s.jsx("button",{onClick:()=>te(u.stepId),className:"text-xs text-blue-600 hover:text-blue-800",children:g.has(u.stepId)?"收起":"详情"}),s.jsx("button",{onClick:()=>D(u.stepId),className:"text-xs text-red-600 hover:text-red-800",children:"清除"})]})]}),s.jsx("p",{className:"text-xs text-gray-600 mb-2",children:u.description}),u.result&&s.jsxs("div",{className:"text-xs",children:[s.jsxs("div",{className:"flex justify-between items-center mb-1",children:[s.jsx("span",{className:`px-2 py-1 rounded ${u.result.status==="success"?"bg-green-100 text-green-800":u.result.status==="failed"?"bg-red-100 text-red-800":"bg-gray-100 text-gray-800"}`,children:u.result.status==="success"?"成功":u.result.status==="failed"?"失败":"运行中"}),s.jsxs("span",{className:"text-gray-500",children:["耗时: ",u.result.duration,"ms"]})]}),u.lastExecuted&&s.jsxs("p",{className:"text-gray-500",children:["执行时间: ",new Date(u.lastExecuted).toLocaleTimeString("zh-CN")]})]}),s.jsx("button",{onClick:()=>z(u.stepId),disabled:u.isRunning||i.isRunning,className:"mt-2 w-full px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300",children:u.isRunning?"执行中...":"执行"}),u.result&&g.has(u.stepId)&&s.jsxs("div",{className:"mt-3 pt-3 border-t border-gray-200",children:[s.jsx("h5",{className:"text-xs font-medium text-gray-700 mb-2",children:"操作详情:"}),s.jsx("div",{className:"space-y-2 max-h-32 overflow-y-auto",children:u.result.operations.map((X,Y)=>s.jsxs("div",{className:"p-2 bg-white rounded border text-xs",children:[s.jsxs("div",{className:"flex justify-between items-start mb-1",children:[s.jsx("span",{className:V(X),children:_(X)}),s.jsx("span",{className:"text-gray-500",children:new Date(X.timestamp).toLocaleTimeString("zh-CN")})]}),X.address&&s.jsxs("p",{className:"text-gray-600",children:["地址: ",s.jsx("span",{className:"font-mono",children:X.address}),X.value!==void 0&&s.jsxs("span",{children:[" = ",JSON.stringify(X.value)]})]}),s.jsx("p",{className:"text-gray-700 mt-1",children:X.message}),X.duration&&s.jsxs("p",{className:"text-gray-500 text-xs",children:["耗时: ",X.duration]})]},Y))})]}),((q=u.result)==null?void 0:q.error)&&s.jsx("div",{className:"mt-2 p-2 bg-red-50 border border-red-200 rounded",children:s.jsx("p",{className:"text-xs text-red-800",children:u.result.error})})]},u.stepId)})})]})]})},E0=({workflowState:i,onStateChange:d,config:h=Ma,onNotification:x})=>{const[p,S]=v.useState(!1),[k,A]=v.useState({}),[b,f]=v.useState(-1),[g,C]=v.useState(0),[M,J]=v.useState(null),[z,D]=v.useState(!1),j=()=>`${Date.now()}-${Math.random().toString(36).substr(2,9)}`,te=v.useCallback((L,K,ne)=>{if(x){const w={id:j(),type:L,title:K,message:ne,timestamp:new Date().toISOString(),duration:L==="error"?5e3:3e3};x(w)}},[x]),I=v.useCallback(async()=>{try{const L=await Et.getStatus(),K={...i,isRunning:L.isRunning};if(L.isRunning&&!i.isRunning)J(new Date),te("info","工作流程已启动","开始执行7步PLC工作流程");else if(!L.isRunning&&i.isRunning){if(M){const ne=Date.now()-M.getTime();C(ne)}te("success","工作流程已完成","7步PLC工作流程执行完成"),S(!1)}d(K)}catch(L){console.error("获取工作流程状态失败:",L)}},[i,d,M,te]);v.useEffect(()=>{let L;return z&&(L=setInterval(I,1e3)),()=>{L&&clearInterval(L)}},[z,I]);const le=v.useCallback(async()=>{if(!(i.isRunning||p)){S(!0),A({}),f(0),J(new Date),D(!0);try{await Et.executeWorkflow();const L={...i,isRunning:!0,startTime:new Date().toISOString(),logs:[...i.logs,`${new Date().toLocaleTimeString("zh-CN")} - 开始执行完整工作流程`].slice(-50)};d(L),te("info","工作流程启动中","正在启动7步PLC工作流程...")}catch(L){const K=L instanceof Error?L.message:"未知错误";te("error","启动工作流程失败",K),S(!1),D(!1);const ne=[...i.logs,`${new Date().toLocaleTimeString("zh-CN")} - 工作流程启动失败: ${K}`];d({...i,logs:ne.slice(-50)})}}},[i,d,p,te]),ae=v.useCallback(async()=>{if(i.isRunning)try{await Et.stopWorkflow();const L={...i,isRunning:!1};d(L),S(!1),D(!1),te("warning","工作流程已停止","工作流程已被手动停止");const K=[...i.logs,`${new Date().toLocaleTimeString("zh-CN")} - 工作流程已停止`];d({...i,logs:K.slice(-50)})}catch(L){const K=L instanceof Error?L.message:"未知错误";te("error","停止工作流程失败",K)}},[i,d,te]),_=v.useCallback(()=>{A({}),f(-1),C(0),J(null),S(!1),D(!1),d({...i,isRunning:!1,currentStep:"",logs:[]}),te("info","状态已重置","工作流程状态已重置")},[i,d,te]),V=v.useMemo(()=>_a.map((L,K)=>{const ne=k[L];let w="pending";return ne?w=ne.status==="success"?"completed":ne.status==="failed"?"failed":"running":i.isRunning&&K===b?w="running":K<b&&(w="completed"),{id:L,name:L,description:Vs[L],addresses:[],status:w,result:ne,isActive:i.currentStep===L}}),[k,b,i]),u=(L,K)=>{switch(L.status){case"running":return s.jsx("div",{className:"w-8 h-8 rounded-full bg-yellow-400 flex items-center justify-center text-white font-bold animate-pulse",children:K+1});case"completed":return s.jsx("div",{className:"w-8 h-8 rounded-full bg-green-500 flex items-center justify-center text-white",children:"✓"});case"failed":return s.jsx("div",{className:"w-8 h-8 rounded-full bg-red-500 flex items-center justify-center text-white",children:"✗"});default:return s.jsx("div",{className:"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-gray-600",children:K+1})}},q=(L,K)=>K?L.status==="completed"&&(K.status==="completed"||K.status==="running")?"bg-green-500":L.status==="completed"||L.status==="running"?"bg-yellow-400":"bg-gray-300":"",X=v.useMemo(()=>{const L=V.filter(ne=>ne.status==="completed").length,K=V.filter(ne=>ne.status==="running").length;return Math.round((L+K*.5)/_a.length*100)},[V]),Y=L=>{const K=Math.floor(L/1e3),ne=Math.floor(K/60),w=K%60;return ne>0?`${ne}:${w.toString().padStart(2,"0")}`:`${w}秒`};return s.jsxs("div",{className:"p-6 bg-white rounded-lg shadow-lg",children:[s.jsxs("div",{className:"flex justify-between items-center mb-6",children:[s.jsxs("div",{children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-800",children:"工作流程查看器"}),s.jsx("p",{className:"text-gray-600 mt-1",children:"执行和监控完整的7步PLC工作流程"})]}),s.jsx("div",{className:"flex items-center space-x-3",children:i.isRunning?s.jsx("button",{onClick:ae,className:"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600",children:"停止工作流程"}):s.jsxs(s.Fragment,{children:[s.jsx("button",{onClick:_,disabled:p,className:"px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600 disabled:bg-gray-300",children:"重置"}),s.jsx("button",{onClick:le,disabled:p,className:"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:bg-gray-300",children:p?"启动中...":"开始工作流程"})]})})]}),s.jsxs("div",{className:"mb-6",children:[s.jsxs("div",{className:"flex justify-between items-center mb-2",children:[s.jsx("span",{className:"text-sm font-medium text-gray-700",children:"整体进度"}),s.jsxs("span",{className:"text-sm text-gray-500",children:[X,"% 完成"]})]}),s.jsx("div",{className:"w-full bg-gray-200 rounded-full h-3",children:s.jsx("div",{className:"bg-blue-500 h-3 rounded-full transition-all duration-500",style:{width:`${X}%`}})})]}),(i.isRunning||M)&&s.jsx("div",{className:"mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200",children:s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[s.jsxs("div",{children:[s.jsx("span",{className:"font-medium text-blue-800",children:"状态: "}),s.jsx("span",{className:i.isRunning?"text-green-600":"text-gray-600",children:i.isRunning?"运行中":"已停止"})]}),s.jsxs("div",{children:[s.jsx("span",{className:"font-medium text-blue-800",children:"开始时间: "}),s.jsx("span",{className:"text-blue-700",children:M?M.toLocaleTimeString("zh-CN"):"未知"})]}),s.jsxs("div",{children:[s.jsx("span",{className:"font-medium text-blue-800",children:"执行时长: "}),s.jsx("span",{className:"text-blue-700",children:i.isRunning&&M?Y(Date.now()-M.getTime()):g>0?Y(g):"0秒"})]})]})}),s.jsxs("div",{className:"mb-8",children:[s.jsx("h3",{className:"text-lg font-semibold mb-4",children:"流程步骤"}),s.jsx("div",{className:"relative",children:s.jsx("div",{className:"flex items-center justify-between",children:V.map((L,K)=>s.jsxs("div",{className:"flex items-center",children:[s.jsxs("div",{className:"flex flex-col items-center",children:[u(L,K),s.jsxs("div",{className:"mt-2 text-center max-w-24",children:[s.jsx("div",{className:"text-xs font-medium text-gray-700",children:L.name}),s.jsx("div",{className:"text-xs text-gray-500 mt-1 leading-tight",children:L.description.length>20?`${L.description.substring(0,20)}...`:L.description}),L.result&&s.jsxs("div",{className:"text-xs text-gray-400 mt-1",children:[L.result.duration,"ms"]})]})]}),K<V.length-1&&s.jsx("div",{className:`flex-1 h-1 mx-4 ${q(L,V[K+1])}`})]},L.id))})})]}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-semibold mb-3",children:"步骤详情"}),s.jsx("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:V.map((L,K)=>s.jsxs("div",{className:`p-3 rounded border-l-4 ${L.status==="running"?"border-yellow-400 bg-yellow-50":L.status==="completed"?"border-green-400 bg-green-50":L.status==="failed"?"border-red-400 bg-red-50":"border-gray-400 bg-gray-50"}`,children:[s.jsxs("div",{className:"flex justify-between items-start mb-2",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsxs("span",{className:"text-sm font-medium",children:[K+1,". ",L.name]}),L.status==="running"&&s.jsx("div",{className:"w-2 h-2 bg-yellow-400 rounded-full animate-pulse"})]}),s.jsx("span",{className:`text-xs px-2 py-1 rounded ${L.status==="completed"?"bg-green-100 text-green-800":L.status==="failed"?"bg-red-100 text-red-800":L.status==="running"?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:L.status==="completed"?"已完成":L.status==="failed"?"失败":L.status==="running"?"运行中":"等待中"})]}),s.jsx("p",{className:"text-sm text-gray-600 mb-2",children:L.description}),L.result&&s.jsxs("div",{className:"text-xs space-y-1",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{children:"执行时间:"}),s.jsx("span",{children:new Date(L.result.endTime).toLocaleTimeString("zh-CN")})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{children:"耗时:"}),s.jsxs("span",{children:[L.result.duration,"ms"]})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{children:"操作数:"}),s.jsx("span",{children:L.result.operations.length})]}),L.result.error&&s.jsx("div",{className:"mt-2 p-2 bg-red-100 rounded text-red-800",children:L.result.error})]})]},L.id))})]}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-semibold mb-3",children:"执行日志"}),s.jsx("div",{className:"h-96 p-3 bg-gray-50 rounded border overflow-y-auto",children:i.logs.length===0?s.jsx("div",{className:"text-center text-gray-500 py-8",children:"暂无执行日志"}):s.jsx("div",{className:"space-y-1",children:i.logs.slice(-30).map((L,K)=>s.jsx("div",{className:"text-sm text-gray-700 font-mono",children:L},K))})})]})]}),Object.keys(k).length>0&&s.jsxs("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:[s.jsx("h4",{className:"font-medium text-gray-800 mb-3",children:"执行统计"}),s.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-green-600",children:V.filter(L=>L.status==="completed").length}),s.jsx("div",{className:"text-gray-600",children:"已完成"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-red-600",children:V.filter(L=>L.status==="failed").length}),s.jsx("div",{className:"text-gray-600",children:"失败"})]}),s.jsxs("div",{className:"text-center",children:[s.jsxs("div",{className:"text-2xl font-bold text-blue-600",children:[Object.values(k).reduce((L,K)=>L+K.duration,0),"ms"]}),s.jsx("div",{className:"text-gray-600",children:"总耗时"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-purple-600",children:Object.values(k).reduce((L,K)=>L+K.operations.length,0)}),s.jsx("div",{className:"text-gray-600",children:"总操作数"})]})]})]})]})},R0=({notification:i,onClose:d})=>{const[h,x]=v.useState(!0);v.useEffect(()=>{const k=setTimeout(()=>{x(!1),setTimeout(()=>d(i.id),300)},i.duration||3e3);return()=>clearTimeout(k)},[i.id,i.duration,d]);const p=()=>{const k="fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg border transition-all duration-300",A={success:"bg-green-50 border-green-200 text-green-800",error:"bg-red-50 border-red-200 text-red-800",warning:"bg-yellow-50 border-yellow-200 text-yellow-800",info:"bg-blue-50 border-blue-200 text-blue-800"},b=h?"translate-x-0 opacity-100":"translate-x-full opacity-0";return`${k} ${A[i.type]} ${b}`},S=()=>({success:"✅",error:"❌",warning:"⚠️",info:"ℹ️"})[i.type];return s.jsx("div",{className:p(),children:s.jsxs("div",{className:"flex items-start space-x-3",children:[s.jsx("span",{className:"text-lg",children:S()}),s.jsxs("div",{className:"flex-1",children:[s.jsx("p",{className:"font-medium",children:i.title}),s.jsx("p",{className:"text-sm mt-1",children:i.message}),s.jsx("p",{className:"text-xs text-gray-500 mt-2",children:new Date(i.timestamp).toLocaleTimeString("zh-CN")})]}),s.jsx("button",{onClick:()=>{x(!1),setTimeout(()=>d(i.id),300)},className:"text-gray-400 hover:text-gray-600",children:"✕"})]})})},O0=({config:i,onConfigChange:d,isOpen:h,onClose:x})=>{const[p,S]=v.useState(i);v.useEffect(()=>{S(i)},[i]);const k=()=>{d(p),x()};return h?s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 flex items-center justify-center",children:s.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[s.jsxs("div",{className:"flex justify-between items-center mb-4",children:[s.jsx("h3",{className:"text-lg font-semibold",children:"配置设置"}),s.jsx("button",{onClick:x,className:"text-gray-400 hover:text-gray-600",children:"✕"})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsx("div",{children:s.jsxs("label",{className:"flex items-center",children:[s.jsx("input",{type:"checkbox",checked:p.autoStartMonitoring,onChange:A=>S({...p,autoStartMonitoring:A.target.checked}),className:"mr-2"}),"自动启动监控"]})}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"监控间隔 (毫秒)"}),s.jsx("input",{type:"number",value:p.monitoringInterval,onChange:A=>S({...p,monitoringInterval:parseInt(A.target.value)||200}),className:"w-full px-3 py-2 border border-gray-300 rounded",min:"100",max:"5000",step:"100"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"步骤超时 (秒)"}),s.jsx("input",{type:"number",value:p.stepTimeout,onChange:A=>S({...p,stepTimeout:parseInt(A.target.value)||30}),className:"w-full px-3 py-2 border border-gray-300 rounded",min:"5",max:"300",step:"5"})]}),s.jsx("div",{children:s.jsxs("label",{className:"flex items-center",children:[s.jsx("input",{type:"checkbox",checked:p.showDetailedLogs,onChange:A=>S({...p,showDetailedLogs:A.target.checked}),className:"mr-2"}),"显示详细日志"]})}),s.jsx("div",{children:s.jsxs("label",{className:"flex items-center",children:[s.jsx("input",{type:"checkbox",checked:p.enableNotifications,onChange:A=>S({...p,enableNotifications:A.target.checked}),className:"mr-2"}),"启用通知"]})}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"主题"}),s.jsxs("select",{value:p.theme,onChange:A=>S({...p,theme:A.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded",children:[s.jsx("option",{value:"light",children:"浅色"}),s.jsx("option",{value:"dark",children:"深色"}),s.jsx("option",{value:"auto",children:"自动"})]})]})]}),s.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[s.jsx("button",{onClick:x,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50",children:"取消"}),s.jsx("button",{onClick:k,className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"保存"})]})]})}):null},k0=()=>{const[i,d]=v.useState({activeTab:"monitor",config:Ma,monitoringState:{isActive:!1,lastUpdate:"",addresses:{}},workflowState:{isRunning:!1,currentStep:"",steps:_a.map(M=>({id:M,name:M,description:Vs[M],addresses:[]})),startTime:"",logs:[]},notifications:[]}),[h,x]=v.useState(!1),p=v.useCallback(M=>{d(J=>({...J,monitoringState:M}))},[]),S=v.useCallback(M=>{d(J=>({...J,workflowState:M}))},[]),k=v.useCallback(M=>{d(J=>({...J,config:M})),localStorage.setItem("plcTestConfig",JSON.stringify(M))},[]),A=v.useCallback(M=>{d(J=>({...J,notifications:[M,...J.notifications.slice(0,4)]}))},[]),b=v.useCallback(M=>{d(J=>({...J,notifications:J.notifications.filter(z=>z.id!==M)}))},[]),f=v.useCallback(M=>{d(J=>({...J,activeTab:M}))},[]);v.useEffect(()=>{const M=localStorage.getItem("plcTestConfig");if(M)try{const J=JSON.parse(M);k({...Ma,...J})}catch(J){console.error("加载配置失败:",J)}},[k]);const g=M=>`px-4 py-2 font-medium rounded-t-lg transition-colors ${i.activeTab===M?"bg-blue-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,C=()=>{switch(i.activeTab){case"monitor":return s.jsx(_0,{monitoringState:i.monitoringState,onStateChange:p,config:i.config,onNotification:A});case"steps":return s.jsx(M0,{workflowState:i.workflowState,onStateChange:S,config:i.config,onNotification:A});case"workflow":return s.jsx(E0,{workflowState:i.workflowState,onStateChange:S,config:i.config,onNotification:A});default:return null}};return s.jsxs("div",{className:"min-h-screen bg-gray-100",children:[s.jsx("header",{className:"bg-white shadow-sm border-b",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:s.jsxs("div",{className:"flex justify-between items-center h-16",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"PLC测试系统"}),s.jsx("p",{className:"text-sm text-gray-600",children:"独立PLC测试和监控工具"})]}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsxs("div",{className:"flex items-center space-x-2 text-sm",children:[s.jsxs("div",{className:"flex items-center space-x-1",children:[s.jsx("div",{className:`w-2 h-2 rounded-full ${i.monitoringState.isActive?"bg-green-400":"bg-gray-400"}`}),s.jsx("span",{className:"text-gray-600",children:"监控"})]}),s.jsxs("div",{className:"flex items-center space-x-1",children:[s.jsx("div",{className:`w-2 h-2 rounded-full ${i.workflowState.isRunning?"bg-blue-400":"bg-gray-400"}`}),s.jsx("span",{className:"text-gray-600",children:"工作流程"})]})]}),s.jsx("button",{onClick:()=>x(!0),className:"px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600",children:"配置"})]})]})})}),s.jsxs("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[s.jsx("div",{className:"mb-6",children:s.jsxs("nav",{className:"flex space-x-1",children:[s.jsx("button",{onClick:()=>f("monitor"),className:g("monitor"),children:"实时监控"}),s.jsx("button",{onClick:()=>f("steps"),className:g("steps"),children:"单步控制"}),s.jsx("button",{onClick:()=>f("workflow"),className:g("workflow"),children:"工作流程"})]})}),s.jsx("div",{className:"tab-content",children:C()})]}),s.jsx("div",{className:"notification-area",children:i.notifications.map(M=>s.jsx(R0,{notification:M,onClose:b},M.id))}),s.jsx(O0,{config:i.config,onConfigChange:k,isOpen:h,onClose:()=>x(!1)})]})},D0="http://localhost:8080/api/agv-test";class z0{constructor(){rt(this,"baseUrl",D0);rt(this,"wsConnection",null);rt(this,"wsReconnectAttempts",0);rt(this,"maxReconnectAttempts",5);rt(this,"reconnectDelay",2e3);rt(this,"wsListeners",new Map);rt(this,"wsEventListeners",new Map)}async request(d,h={}){try{const x=`${this.baseUrl}${d}`;console.log(`AGV测试API请求: ${h.method||"GET"} ${x}`);const p=await fetch(x,{headers:{"Content-Type":"application/json",...h.headers},...h}),S=await p.json();if(!p.ok){const k=S.error||`HTTP ${p.status}: ${p.statusText}`;throw console.error(`AGV测试API错误 [${d}]:`,k),new Error(k)}return console.log(`AGV测试API响应 [${d}]:`,S),S}catch(x){throw console.error(`AGV测试API请求失败 [${d}]:`,x),x}}async connect(){return this.request("/connect",{method:"POST"})}async disconnect(){return this.request("/disconnect",{method:"POST"})}async getStatus(){return this.request("/status",{method:"GET"})}async getConfig(){return this.request("/config",{method:"GET"})}async updateConfig(d){return this.request("/config",{method:"PUT",body:JSON.stringify(d)})}async navigateToPoint(d){return this.request("/navigate/point",{method:"POST",body:JSON.stringify(d)})}async navigateToMachine(d){return this.request("/navigate/machine",{method:"POST",body:JSON.stringify(d)})}async queryNavigationPoint(d){return this.request("/navigate/query-point",{method:"POST",body:JSON.stringify(d)})}async switchMode(d){return this.request("/mode/switch",{method:"POST",body:JSON.stringify(d)})}async laserInit(d){return this.request("/mode/laser-init",{method:"POST",body:JSON.stringify(d)})}async startMonitoring(d){return this.request("/monitor/start",{method:"POST",body:d?JSON.stringify(d):void 0})}async stopMonitoring(){return this.request("/monitor/stop",{method:"POST"})}async getMonitorHistory(d){const h=d?`?limit=${d}`:"";return this.request(`/monitor/history${h}`,{method:"GET"})}async getConnectionEvents(d){const h=d?`?limit=${d}`:"";return this.request(`/monitor/events${h}`,{method:"GET"})}async getMonitorStats(){return this.request("/monitor/stats",{method:"GET"})}async clearMonitorHistory(){return this.request("/monitor/clear",{method:"POST"})}async getWorkflowTemplates(){return this.request("/workflow/templates",{method:"GET"})}async startWorkflow(d){return this.request("/workflow/start",{method:"POST",body:JSON.stringify(d)})}async stopWorkflow(){return this.request("/workflow/stop",{method:"POST"})}async getCurrentWorkflow(){return this.request("/workflow/current",{method:"GET"})}async getWorkflowHistory(d){const h=d?`?limit=${d}`:"";return this.request(`/workflow/history${h}`,{method:"GET"})}async getTestResults(){return this.request("/results",{method:"GET"})}async clearTestResults(){return this.request("/results/clear",{method:"POST"})}connectWebSocket(){return new Promise((d,h)=>{try{this.disconnectWebSocket();const x="ws://localhost:8080/api/agv-test/ws";console.log("连接AGV测试WebSocket:",x),this.wsConnection=new WebSocket(x),this.wsConnection.onopen=p=>{console.log("AGV测试WebSocket连接成功"),this.wsReconnectAttempts=0,this.triggerWsEventListeners("open",p),d()},this.wsConnection.onmessage=p=>{try{const S=JSON.parse(p.data);console.log("AGV测试WebSocket消息:",S),this.handleWebSocketMessage(S)}catch(S){console.error("解析AGV测试WebSocket消息失败:",S)}},this.wsConnection.onclose=p=>{console.log("AGV测试WebSocket连接关闭:",p.code,p.reason),this.wsConnection=null,this.triggerWsEventListeners("close",p),!p.wasClean&&this.wsReconnectAttempts<this.maxReconnectAttempts&&setTimeout(()=>{this.wsReconnectAttempts++,console.log(`AGV测试WebSocket重连尝试 ${this.wsReconnectAttempts}/${this.maxReconnectAttempts}`),this.connectWebSocket().catch(console.error)},this.reconnectDelay)},this.wsConnection.onerror=p=>{console.error("AGV测试WebSocket连接错误:",p),this.triggerWsEventListeners("error",p),h(new Error("WebSocket连接失败"))}}catch(x){console.error("创建AGV测试WebSocket失败:",x),h(x)}})}disconnectWebSocket(){this.wsConnection&&(console.log("断开AGV测试WebSocket连接"),this.wsConnection.close(1e3,"手动断开"),this.wsConnection=null)}isWebSocketConnected(){var d;return((d=this.wsConnection)==null?void 0:d.readyState)===WebSocket.OPEN}handleWebSocketMessage(d){(this.wsListeners.get(d.type)||[]).forEach(x=>{try{x(d.data)}catch(p){console.error(`处理AGV测试WebSocket消息失败 [${d.type}]:`,p)}})}addWebSocketListener(d,h){this.wsListeners.has(d)||this.wsListeners.set(d,[]),this.wsListeners.get(d).push(h)}removeWebSocketListener(d,h){const x=this.wsListeners.get(d)||[],p=x.indexOf(h);p>-1&&x.splice(p,1)}addWebSocketEventListener(d,h){this.wsEventListeners.has(d)||this.wsEventListeners.set(d,[]),this.wsEventListeners.get(d).push(h)}removeWebSocketEventListener(d,h){const x=this.wsEventListeners.get(d)||[],p=x.indexOf(h);p>-1&&x.splice(p,1)}triggerWsEventListeners(d,h){(this.wsEventListeners.get(d)||[]).forEach(p=>{try{p(h)}catch(S){console.error(`处理AGV测试WebSocket事件失败 [${d}]:`,S)}})}clearAllListeners(){this.wsListeners.clear(),this.wsEventListeners.clear()}setBaseUrl(d){this.baseUrl=d,console.log("AGV测试API基础URL已更新:",this.baseUrl)}getBaseUrl(){return this.baseUrl}getWebSocketUrl(){return this.baseUrl.replace("http","ws")+"/ws"}resetWebSocketReconnectAttempts(){this.wsReconnectAttempts=0}getWebSocketReconnectStatus(){return{attempts:this.wsReconnectAttempts,maxAttempts:this.maxReconnectAttempts,isReconnecting:this.wsReconnectAttempts>0&&this.wsReconnectAttempts<this.maxReconnectAttempts}}async healthCheck(){try{return await this.getStatus(),!0}catch(d){return console.error("AGV测试API健康检查失败:",d),!1}}}const he=new z0,L0={pending:"#FFA500",running:"#1890FF",completed:"#52C41A",failed:"#FF4D4F",cancelled:"#8C8C8C"},U0={pending:"等待中",running:"运行中",completed:"已完成",failed:"失败",cancelled:"已取消"},q0={connected:"#52C41A",disconnected:"#FF4D4F",error:"#FF4D4F",timeout:"#FFA500"},H0=[{label:"100ms (高频)",value:100},{label:"500ms (中频)",value:500},{label:"1s (默认)",value:1e3},{label:"2s (低频)",value:2e3},{label:"5s (很低频)",value:5e3}];function G0({connected:i,onConnect:d,onDisconnect:h,onStartMonitoring:x,onStopMonitoring:p,onClearHistory:S}){const[k,A]=v.useState(!1),[b,f]=v.useState(""),[g,C]=v.useState(null),[M,J]=v.useState(!1),[z,D]=v.useState(1e3),[j,te]=v.useState([]),[I,le]=v.useState([]),[ae,_]=v.useState(null),[V,u]=v.useState({showPositionChanges:!0,showAngleChanges:!0,showModeChanges:!0,showBatteryChanges:!0,showEventChanges:!0,showConnectionEvents:!0,maxHistoryItems:50}),[q,X]=v.useState(!1);v.useEffect(()=>{const H=setInterval(async()=>{if(i)try{await Y(),await L()}catch(ue){console.error("定期刷新失败:",ue)}},5e3);return()=>clearInterval(H)},[i]),v.useEffect(()=>{const H=tt=>{C(tt.status),M&&te(Ut=>[...Ut.slice(-(V.maxHistoryItems-1)),tt])},ue=tt=>{le(Ut=>[...Ut.slice(-19),tt])};he.addWebSocketListener("agv_status_update",H),he.addWebSocketListener("agv_connection_change",ue);const de=()=>X(!0),qe=()=>X(!1),we=()=>X(!1);return he.addWebSocketEventListener("open",de),he.addWebSocketEventListener("close",qe),he.addWebSocketEventListener("error",we),()=>{he.removeWebSocketListener("agv_status_update",H),he.removeWebSocketListener("agv_connection_change",ue),he.removeWebSocketEventListener("open",de),he.removeWebSocketEventListener("close",qe),he.removeWebSocketEventListener("error",we)}},[V.maxHistoryItems,M]);const Y=v.useCallback(async()=>{try{const H=await he.getStatus();H.success&&H.data&&C(H.data.status)}catch(H){console.error("刷新状态失败:",H)}},[]),L=v.useCallback(async()=>{try{const H=await he.getMonitorStats();H.success&&H.data&&(_(H.data),J(H.data.monitoring))}catch(H){console.error("刷新统计失败:",H)}},[]),K=async()=>{A(!0);try{await he.connect(),await he.connectWebSocket(),d(),f("AGV连接成功"),await Y()}catch(H){f(`连接失败: ${H.message}`)}finally{A(!1)}},ne=async()=>{A(!0);try{await he.disconnect(),he.disconnectWebSocket(),h(),f("AGV连接已断开"),C(null)}catch(H){f(`断开失败: ${H.message}`)}finally{A(!1)}},w=async()=>{A(!0);try{await he.startMonitoring({interval_ms:z}),x(z),f(`监控已启动 (间隔: ${z}ms)`),await L()}catch(H){f(`启动监控失败: ${H.message}`)}finally{A(!1)}},Q=async()=>{A(!0);try{await he.stopMonitoring(),te([]),le([]),p(),f("监控已停止，已清空监控数据"),await L()}catch(H){f(`停止监控失败: ${H.message}`)}finally{A(!1)}},E=async()=>{A(!0);try{const H=await ye.getAGVNavigationStatus();if(H.success&&H.data){const{status:ue,targetPointID:de,passedCount:qe,remainingCount:we,isNavigating:tt}=H.data,Ut=`
导航状态: ${ue}
目标点: ${de||"无"}号点
进度: 已过${qe}点 | 剩余${we}点
状态: ${tt?"导航中":"空闲"}
        `.trim();f(Ut)}else f("查询导航状态失败")}catch(H){f(`查询导航状态失败: ${H.message}`)}finally{A(!1)}},F=async()=>{A(!0);try{await he.clearMonitorHistory(),te([]),le([]),S(),f("监控历史已清空")}catch(H){f(`清空历史失败: ${H.message}`)}finally{A(!1)}},ie=async()=>{A(!0);try{const[H,ue]=await Promise.all([he.getMonitorHistory(V.maxHistoryItems),he.getConnectionEvents(20)]);H.success&&H.data&&te(H.data),ue.success&&ue.data&&le(ue.data),f("历史数据加载完成")}catch(H){f(`加载历史失败: ${H.message}`)}finally{A(!1)}},ee=H=>new Date(H).toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"}),o=(H,ue=2)=>H==null||isNaN(H)?"--":H.toFixed(ue),B=H=>{if(H==null)return"未知";switch(H){case 1:return"手动模式";case 3:return"自动模式";default:return`未知模式(${H})`}},P=H=>{if(H==null)return"未知";switch(H){case 0:return"空闲";case 1:return"导航中";case 2:return"充电中";case 6:return"导航失败";default:return`状态${H}`}},se=j.filter(H=>!(!V.showPositionChanges&&H.position_changed||!V.showAngleChanges&&H.angle_changed||!V.showModeChanges&&H.mode_changed||!V.showBatteryChanges&&H.battery_changed||!V.showEventChanges&&H.event_changed));return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"AGV状态监控"}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:`w-3 h-3 rounded-full ${i?"bg-green-500":"bg-red-500"}`}),s.jsxs("span",{className:"text-sm text-gray-600",children:["AGV: ",i?"已连接":"未连接"]}),s.jsx("div",{className:`w-3 h-3 rounded-full ${q?"bg-blue-500":"bg-gray-400"}`}),s.jsxs("span",{className:"text-sm text-gray-600",children:["WebSocket: ",q?"已连接":"未连接"]})]})]}),b&&s.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-3",children:s.jsx("p",{className:"text-sm text-blue-800",children:b})}),s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"🔌"}),"连接控制"]}),s.jsxs("div",{className:"flex space-x-4",children:[s.jsx("button",{onClick:K,disabled:k||i,className:"btn btn-success",children:k?"连接中...":"连接AGV"}),s.jsx("button",{onClick:ne,disabled:k||!i,className:"btn btn-error",children:k?"断开中...":"断开AGV"}),s.jsx("button",{onClick:Y,disabled:k||!i,className:"btn btn-secondary",children:"刷新状态"}),s.jsx("button",{onClick:E,disabled:k||!i,className:"btn btn-info",children:"查询导航状态"})]})]}),g&&s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"📊"}),"当前状态"]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[s.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"位置坐标"}),s.jsxs("div",{className:"text-lg font-mono",children:["X: ",o(g.posX),s.jsx("br",{}),"Y: ",o(g.posY),s.jsx("br",{}),"角度: ",o(g.angle),"°"]})]}),s.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"运动状态"}),s.jsxs("div",{className:"text-lg font-mono",children:["VX: ",o(g.velX),s.jsx("br",{}),"VY: ",o(g.velY),s.jsx("br",{}),"角速度: ",o(g.angVel)]})]}),s.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"工作状态"}),s.jsxs("div",{className:"text-lg",children:["模式: ",B(g.workMode),s.jsx("br",{}),"状态: ",P(g.agvState),s.jsx("br",{}),"点位: ",g.lastPointID??"--"]})]}),s.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"电池状态"}),s.jsxs("div",{className:"text-lg",children:["电量: ",o(g.batteryPercent),"%",s.jsx("br",{}),"电压: ",o(g.batteryVoltage),"V",s.jsx("br",{}),"电流: ",o(g.batteryCurrent),"A"]})]}),s.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"任务信息"}),s.jsxs("div",{className:"text-lg font-mono",children:["订单: ",g.orderID??"--",s.jsx("br",{}),"任务: ",g.taskKey??"--",s.jsx("br",{}),"序列: ",g.pointSequenceID??"--"]})]}),s.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[s.jsx("div",{className:"text-sm text-gray-600",children:"异常事件"}),s.jsxs("div",{className:"text-lg",children:["代码: 0x",g.eventCode?g.eventCode.toString(16).toUpperCase():"--",s.jsx("br",{}),"级别: ",g.eventLevel??"--",s.jsx("br",{}),"置信度: ",o(g.confidence)]})]})]})]}),s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"🔍"}),"监控控制"]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("label",{className:"text-sm font-medium text-gray-700",children:"监控间隔:"}),s.jsx("select",{value:z,onChange:H=>D(parseInt(H.target.value)),className:"select select-bordered",children:H0.map(H=>s.jsx("option",{value:H.value,children:H.label},H.value))})]}),s.jsxs("div",{className:"flex space-x-4",children:[s.jsx("button",{onClick:w,disabled:k||!i||M,className:"btn btn-primary",children:M?"监控中...":"启动监控"}),s.jsx("button",{onClick:Q,disabled:k||!M,className:"btn btn-warning",children:"停止监控"}),s.jsx("button",{onClick:ie,disabled:k,className:"btn btn-secondary",children:"加载历史"}),s.jsx("button",{onClick:F,disabled:k,className:"btn btn-error",children:"清空历史"})]})]}),ae&&s.jsxs("div",{className:"mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[s.jsxs("div",{className:"bg-blue-50 p-3 rounded",children:[s.jsx("div",{className:"text-sm text-blue-600",children:"监控状态"}),s.jsx("div",{className:"text-lg font-semibold",children:ae.monitoring?"运行中":"已停止"})]}),s.jsxs("div",{className:"bg-green-50 p-3 rounded",children:[s.jsx("div",{className:"text-sm text-green-600",children:"更新次数"}),s.jsx("div",{className:"text-lg font-semibold",children:ae.total_updates})]}),s.jsxs("div",{className:"bg-yellow-50 p-3 rounded",children:[s.jsx("div",{className:"text-sm text-yellow-600",children:"更新频率"}),s.jsxs("div",{className:"text-lg font-semibold",children:[o(ae.updates_per_second,1),"/s"]})]}),s.jsxs("div",{className:"bg-purple-50 p-3 rounded",children:[s.jsx("div",{className:"text-sm text-purple-600",children:"运行时长"}),s.jsxs("div",{className:"text-lg font-semibold",children:[Math.round(ae.duration_ms/1e3),"s"]})]})]})]}),s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"🔧"}),"显示过滤器"]}),s.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4",children:[{key:"showPositionChanges",label:"位置变化"},{key:"showAngleChanges",label:"角度变化"},{key:"showModeChanges",label:"模式变化"},{key:"showBatteryChanges",label:"电池变化"},{key:"showEventChanges",label:"事件变化"},{key:"showConnectionEvents",label:"连接事件"}].map(({key:H,label:ue})=>s.jsxs("label",{className:"flex items-center",children:[s.jsx("input",{type:"checkbox",checked:V[H],onChange:de=>u(qe=>({...qe,[H]:de.target.checked})),className:"checkbox checkbox-sm mr-2"}),s.jsx("span",{className:"text-sm",children:ue})]},H))}),s.jsx("div",{className:"mt-4",children:s.jsxs("label",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-sm font-medium",children:"历史记录数量:"}),s.jsx("input",{type:"number",min:"10",max:"200",value:V.maxHistoryItems,onChange:H=>u(ue=>({...ue,maxHistoryItems:parseInt(H.target.value)||50})),className:"input input-bordered input-sm w-20"})]})})]}),se.length>0&&s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"📈"}),"状态历史 (",se.length,"条)"]}),s.jsx("div",{className:"max-h-96 overflow-y-auto",children:s.jsxs("table",{className:"table table-zebra w-full",children:[s.jsx("thead",{children:s.jsxs("tr",{children:[s.jsx("th",{children:"时间"}),s.jsx("th",{children:"位置"}),s.jsx("th",{children:"角度"}),s.jsx("th",{children:"模式"}),s.jsx("th",{children:"状态"}),s.jsx("th",{children:"电池"}),s.jsx("th",{children:"变化"})]})}),s.jsx("tbody",{children:se.slice(-20).reverse().map((H,ue)=>s.jsxs("tr",{className:"hover",children:[s.jsx("td",{className:"font-mono text-xs",children:ee(H.timestamp)}),s.jsxs("td",{className:"font-mono text-xs",children:["(",o(H.status.posX,1),", ",o(H.status.posY,1),")"]}),s.jsxs("td",{className:"font-mono text-xs",children:[o(H.status.angle,1),"°"]}),s.jsx("td",{className:"text-xs",children:B(H.status.workMode)}),s.jsx("td",{className:"text-xs",children:P(H.status.agvState)}),s.jsxs("td",{className:"font-mono text-xs",children:[o(H.status.batteryPercent,0),"%"]}),s.jsx("td",{className:"text-xs",children:s.jsxs("div",{className:"flex space-x-1",children:[H.position_changed&&s.jsx("span",{className:"badge badge-info badge-xs",children:"位置"}),H.angle_changed&&s.jsx("span",{className:"badge badge-warning badge-xs",children:"角度"}),H.mode_changed&&s.jsx("span",{className:"badge badge-success badge-xs",children:"模式"}),H.battery_changed&&s.jsx("span",{className:"badge badge-error badge-xs",children:"电池"}),H.event_changed&&s.jsx("span",{className:"badge badge-secondary badge-xs",children:"事件"})]})})]},`${H.timestamp}-${ue}`))})]})})]}),V.showConnectionEvents&&I.length>0&&s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"🔗"}),"连接事件 (",I.length,"条)"]}),s.jsx("div",{className:"max-h-64 overflow-y-auto",children:s.jsx("div",{className:"space-y-2",children:I.slice(-10).reverse().map((H,ue)=>s.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:q0[H.event_type]}}),s.jsxs("div",{children:[s.jsx("div",{className:"text-sm font-medium",children:H.message}),s.jsxs("div",{className:"text-xs text-gray-500",children:[ee(H.timestamp),H.duration_ms&&` • 耗时: ${H.duration_ms}ms`]})]})]}),s.jsx("span",{className:"badge badge-outline text-xs",children:H.event_type})]},`${H.timestamp}-${ue}`))})})]})]})}function V0({connected:i,onNavigateToPoint:d,onNavigateToMachine:h,onQueryNavigationPoint:x,onSwitchMode:p,onLaserInit:S}){var Q,E,F,ie;const[k,A]=v.useState(!1),[b,f]=v.useState(""),[g,C]=v.useState({type:"point",pointId:void 0,machine:"",autoMode:!0}),[M,J]=v.useState({x:0,y:0,angle:0}),[z,D]=v.useState(null),[j,te]=v.useState(null),[I,le]=v.useState(null),[ae]=v.useState(["61L","61R","62L","62R","63L","63R","64L","64R","65L","65R","66L","66R"]),[_]=v.useState([1,2,3,4,5,10,15,20,25,30,50,100]),V=async()=>{if(!g.pointId){f("请输入有效的点位ID");return}A(!0);try{const ee=await he.navigateToPoint({point_id:g.pointId});ee.success&&ee.data?(D(ee.data),f(`导航到点位 ${g.pointId} 成功`),d(g.pointId)):f(`导航失败: ${ee.error||"未知错误"}`)}catch(ee){f(`导航失败: ${ee.message}`)}finally{A(!1)}},u=async()=>{var ee;if(!((ee=g.machine)!=null&&ee.trim())){f("请输入有效的机器编号");return}A(!0);try{const o=await he.navigateToMachine({machine:g.machine.trim()});o.success&&o.data?(D(o.data),f(`导航到机器 ${g.machine} 成功`),h(g.machine.trim())):f(`导航失败: ${o.error||"未知错误"}`)}catch(o){f(`导航失败: ${o.message}`)}finally{A(!1)}},q=async()=>{var ee;if(!((ee=g.machine)!=null&&ee.trim())){f("请输入有效的机器编号");return}A(!0);try{const o=await he.queryNavigationPoint({machine:g.machine.trim()});o.success&&o.data?(le(o.data),f(`查询成功: 机器 ${o.data.machine} 的导航点为 ${o.data.navigation_point}`),x(g.machine.trim())):f(`查询失败: ${o.error||"未知错误"}`)}catch(o){f(`查询失败: ${o.message}`)}finally{A(!1)}},X=async ee=>{A(!0);try{const o=await he.switchMode({is_auto:ee});o.success&&o.data?(te(o.data),f(`模式切换成功: ${ee?"自动模式":"手动模式"}`),p(ee),C(B=>({...B,autoMode:ee}))):f(`模式切换失败: ${o.error||"未知错误"}`)}catch(o){f(`模式切换失败: ${o.message}`)}finally{A(!1)}},Y=async()=>{A(!0);try{await ye.initializeLaser({x:M.x,y:M.y,angle:M.angle}),f(`激光导航初始化成功: (${M.x}, ${M.y}, ${M.angle}°)`),S(M.x,M.y,M.angle)}catch(ee){f(`激光导航初始化失败: ${ee.message}`)}finally{A(!1)}},L=ee=>{C(o=>({...o,machine:ee,type:"machine"}))},K=ee=>{C(o=>({...o,pointId:ee,type:"point"}))},ne=ee=>new Date(ee).toLocaleTimeString("zh-CN",{hour12:!1}),w=ee=>ee<1e3?`${ee}ms`:`${(ee/1e3).toFixed(1)}s`;return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"AGV导航控制"}),s.jsx("div",{className:`px-3 py-1 rounded-full text-sm ${i?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:i?"✅ 已连接":"❌ 未连接"})]}),b&&s.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-3",children:s.jsx("p",{className:"text-sm text-blue-800",children:b})}),s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"⚙️"}),"工作模式控制"]}),s.jsxs("div",{className:"flex space-x-4",children:[s.jsx("button",{onClick:()=>X(!1),disabled:k||!i,className:`btn ${g.autoMode?"btn-outline":"btn-warning"} flex-1`,children:"🔧 手动模式"}),s.jsx("button",{onClick:()=>X(!0),disabled:k||!i,className:`btn ${g.autoMode?"btn-success":"btn-outline"} flex-1`,children:"🤖 自动模式"})]}),j&&s.jsxs("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg",children:[s.jsx("div",{className:"text-sm font-medium mb-2",children:"最近模式切换结果:"}),s.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"状态:"}),s.jsx("span",{className:`ml-1 ${j.success?"text-green-600":"text-red-600"}`,children:j.success?"成功":"失败"})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"耗时:"}),s.jsx("span",{className:"ml-1 font-mono",children:w(j.duration_ms)})]}),s.jsxs("div",{className:"col-span-2",children:[s.jsx("span",{className:"text-gray-600",children:"消息:"}),s.jsx("span",{className:"ml-1",children:j.message})]})]})]})]}),s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"🗺️"}),"导航控制"]}),s.jsxs("div",{className:"flex mb-4",children:[s.jsx("button",{onClick:()=>C(ee=>({...ee,type:"point"})),className:`btn ${g.type==="point"?"btn-primary":"btn-outline"} rounded-r-none`,children:"📍 点位导航"}),s.jsx("button",{onClick:()=>C(ee=>({...ee,type:"machine"})),className:`btn ${g.type==="machine"?"btn-primary":"btn-outline"} rounded-l-none`,children:"🏭 机器导航"})]}),g.type==="point"&&s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"目标点位ID"}),s.jsxs("div",{className:"flex space-x-2",children:[s.jsx("input",{type:"number",className:"input input-bordered flex-1",value:g.pointId||"",onChange:ee=>C(o=>({...o,pointId:ee.target.value?parseInt(ee.target.value):void 0})),placeholder:"输入点位ID"}),s.jsx("button",{onClick:V,disabled:k||!i||!g.pointId,className:"btn btn-primary px-8",children:k?"导航中...":"开始导航"})]})]}),s.jsxs("div",{children:[s.jsx("div",{className:"text-sm font-medium text-gray-700 mb-2",children:"常用点位:"}),s.jsx("div",{className:"flex flex-wrap gap-2",children:_.map(ee=>s.jsx("button",{onClick:()=>K(ee),className:`btn btn-sm ${g.pointId===ee?"btn-primary":"btn-outline"}`,children:ee},ee))})]})]}),g.type==="machine"&&s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"机器编号"}),s.jsxs("div",{className:"flex space-x-2",children:[s.jsx("input",{type:"text",className:"input input-bordered flex-1",value:g.machine,onChange:ee=>C(o=>({...o,machine:ee.target.value})),placeholder:"输入机器编号 (例: 61L)"}),s.jsx("button",{onClick:q,disabled:k||!i||!((Q=g.machine)!=null&&Q.trim()),className:"btn btn-secondary",children:"查询点位"}),s.jsx("button",{onClick:u,disabled:k||!i||!((E=g.machine)!=null&&E.trim()),className:"btn btn-primary",children:k?"导航中...":"开始导航"})]})]}),s.jsxs("div",{children:[s.jsx("div",{className:"text-sm font-medium text-gray-700 mb-2",children:"常用机器:"}),s.jsx("div",{className:"grid grid-cols-6 gap-2",children:ae.map(ee=>s.jsx("button",{onClick:()=>L(ee),className:`btn btn-sm ${g.machine===ee?"btn-primary":"btn-outline"}`,children:ee},ee))})]}),I&&s.jsx("div",{className:"p-3 bg-green-50 border border-green-200 rounded-lg",children:s.jsxs("div",{className:"text-sm font-medium text-green-800",children:["🎯 机器 ",I.machine," 的导航点: ",I.navigation_point]})})]})]}),s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"🔦"}),"激光导航初始化"]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"X坐标 (mm)"}),s.jsx("input",{type:"number",step:"0.1",className:"input input-bordered w-full",value:M.x,onChange:ee=>J(o=>({...o,x:parseFloat(ee.target.value)||0})),placeholder:"X坐标"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Y坐标 (mm)"}),s.jsx("input",{type:"number",step:"0.1",className:"input input-bordered w-full",value:M.y,onChange:ee=>J(o=>({...o,y:parseFloat(ee.target.value)||0})),placeholder:"Y坐标"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"角度 (度)"}),s.jsx("input",{type:"number",step:"0.1",min:"-180",max:"180",className:"input input-bordered w-full",value:M.angle,onChange:ee=>J(o=>({...o,angle:parseFloat(ee.target.value)||0})),placeholder:"角度"})]})]}),s.jsx("div",{className:"flex justify-center",children:s.jsx("button",{onClick:Y,disabled:k||!i,className:"btn btn-warning px-8",children:k?"初始化中...":"🔦 执行激光导航初始化"})}),s.jsx("div",{className:"text-sm text-gray-600 text-center",children:"激光导航初始化将设置AGV的初始位置和角度，请确保输入正确的坐标"})]})]}),z&&s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"📊"}),"最近导航结果"]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"状态:"}),s.jsx("span",{className:`font-medium ${z.success?"text-green-600":"text-red-600"}`,children:z.success?"✅ 成功":"❌ 失败"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"目标点位:"}),s.jsx("span",{className:"font-mono",children:z.target_point})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"开始时间:"}),s.jsx("span",{className:"font-mono text-sm",children:ne(z.start_time)})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"结束时间:"}),s.jsx("span",{className:"font-mono text-sm",children:ne(z.end_time)})]})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"耗时:"}),s.jsx("span",{className:"font-mono",children:w(z.duration_ms)})]}),((F=z.data)==null?void 0:F.final_position)&&s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"最终位置:"}),s.jsxs("span",{className:"font-mono text-sm",children:["(",z.data.final_position.x.toFixed(1),", ",z.data.final_position.y.toFixed(1),")"]})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"最终角度:"}),s.jsxs("span",{className:"font-mono",children:[z.data.final_position.angle.toFixed(1),"°"]})]})]}),((ie=z.data)==null?void 0:ie.last_point_id)&&s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"最后点位ID:"}),s.jsx("span",{className:"font-mono",children:z.data.last_point_id})]})]})]}),s.jsxs("div",{className:"mt-4 p-3 bg-gray-50 rounded",children:[s.jsx("div",{className:"text-sm font-medium text-gray-700 mb-1",children:"消息:"}),s.jsx("div",{className:"text-sm",children:z.message})]})]}),s.jsxs("div",{className:"card bg-yellow-50 border-yellow-200",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-3 flex items-center text-yellow-800",children:[s.jsx("span",{className:"text-xl mr-2",children:"💡"}),"操作提示"]}),s.jsxs("div",{className:"space-y-2 text-sm text-yellow-700",children:[s.jsx("div",{children:"• 请确保AGV已连接后再执行导航操作"}),s.jsx("div",{children:"• 建议在自动模式下执行导航，手动模式仅用于调试"}),s.jsx("div",{children:"• 点位导航直接指定目标点位ID"}),s.jsx("div",{children:"• 机器导航会自动查询机器对应的导航点位"}),s.jsx("div",{children:"• 激光导航初始化用于设置AGV的初始位置，谨慎使用"}),s.jsx("div",{children:"• 可以使用快捷按钮快速选择常用的点位和机器"})]})]})]})}function B0({connected:i,onStartWorkflow:d,onStopWorkflow:h,onRefreshTemplates:x,onRefreshHistory:p}){const[S,k]=v.useState(!1),[A,b]=v.useState(""),[f,g]=v.useState([]),[C,M]=v.useState(null),[J,z]=v.useState([]),[D,j]=v.useState({templateName:"",config:{}}),[te,I]=v.useState(null),[le,ae]=v.useState(!1),[_,V]=v.useState(!1);v.useEffect(()=>{i&&(u(),q(),X())},[i]),v.useEffect(()=>{const o=P=>{M(P),(P.status==="completed"||P.status==="failed"||P.status==="cancelled")&&setTimeout(()=>{X(),q()},1e3)},B=()=>{q()};return he.addWebSocketListener("workflow_update",o),he.addWebSocketListener("workflow_step_update",B),()=>{he.removeWebSocketListener("workflow_update",o),he.removeWebSocketListener("workflow_step_update",B)}},[]);const u=async()=>{try{const o=await he.getWorkflowTemplates();o.success&&o.data&&(g(o.data),!D.templateName&&o.data&&o.data.length>0&&j(B=>({...B,templateName:o.data[0].name,config:{...o.data[0].default_config}})))}catch(o){b(`加载模板失败: ${o.message}`)}},q=async()=>{try{const o=await he.getCurrentWorkflow();o.success&&M(o.data||null)}catch(o){console.error("加载当前工作流程失败:",o)}},X=async()=>{try{const o=await he.getWorkflowHistory(10);o.success&&o.data&&z(o.data)}catch(o){console.error("加载历史记录失败:",o)}},Y=async()=>{if(!D.templateName){b("请选择工作流程模板");return}k(!0);try{const o=await he.startWorkflow({template_name:D.templateName,config:D.config});o.success?(b(`工作流程 "${D.templateName}" 已启动`),d(D.templateName,D.config),setTimeout(()=>{q()},1e3)):b(`启动失败: ${o.error||"未知错误"}`)}catch(o){b(`启动失败: ${o.message}`)}finally{k(!1)}},L=async()=>{k(!0);try{const o=await he.stopWorkflow();o.success?(b("工作流程已停止"),h(),setTimeout(()=>{q(),X()},1e3)):b(`停止失败: ${o.error||"未知错误"}`)}catch(o){b(`停止失败: ${o.message}`)}finally{k(!1)}},K=async()=>{k(!0);try{await Promise.all([u(),q(),X()]),b("数据刷新完成"),x(),p()}catch(o){b(`刷新失败: ${o.message}`)}finally{k(!1)}},ne=o=>{j({templateName:o.name,config:{...o.default_config}}),V(!0),b(`已选择模板: ${o.name}`)},w=(o,B)=>{j(P=>({...P,config:{...P.config,[o]:B}}))},Q=o=>new Date(o).toLocaleTimeString("zh-CN",{hour12:!1}),E=o=>{if(o<1e3)return`${o}ms`;if(o<6e4)return`${(o/1e3).toFixed(1)}s`;const B=Math.floor(o/6e4),P=Math.floor(o%6e4/1e3);return`${B}m${P}s`},F=o=>L0[o]||"#8C8C8C",ie=o=>U0[o]||o,ee=o=>o.status==="completed"?100:(o.status==="failed"||o.status==="cancelled",o.progress_percentage);return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"AGV工作流程测试"}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("div",{className:`px-3 py-1 rounded-full text-sm ${i?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:i?"✅ 已连接":"❌ 未连接"}),s.jsx("button",{onClick:K,disabled:S,className:"btn btn-secondary btn-sm",children:"🔄 刷新"})]})]}),A&&s.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-3",children:s.jsx("p",{className:"text-sm text-blue-800",children:A})}),C&&s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"⚡"}),"当前工作流程"]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"text-lg font-medium",children:C.template_name}),s.jsxs("div",{className:"text-sm text-gray-600",children:["ID: ",C.id," • 开始: ",Q(C.start_time)]})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"px-3 py-1 rounded-full text-sm font-medium",style:{backgroundColor:F(C.status),color:"white"},children:ie(C.status)}),(C.status==="running"||C.status==="pending")&&s.jsx("button",{onClick:L,disabled:S,className:"btn btn-error btn-sm",children:"⏹️ 停止"})]})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex justify-between text-sm",children:[s.jsxs("span",{children:["进度: ",C.current_step_index+1," / ",C.total_steps]}),s.jsxs("span",{children:[ee(C),"%"]})]}),s.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2.5",children:s.jsx("div",{className:"bg-blue-600 h-2.5 rounded-full transition-all duration-300",style:{width:`${ee(C)}%`}})})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx("div",{className:"text-sm font-medium",children:"步骤执行状态:"}),s.jsx("div",{className:"max-h-48 overflow-y-auto",children:C.steps.map((o,B)=>s.jsxs("div",{className:`p-3 rounded border mb-2 ${o.status==="completed"?"bg-green-50 border-green-200":o.status==="running"?"bg-blue-50 border-blue-200":o.status==="failed"?"bg-red-50 border-red-200":"bg-gray-50 border-gray-200"}`,children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsxs("span",{className:"text-sm font-medium",children:["步骤 ",B+1]}),s.jsx("span",{className:"text-sm text-gray-600",children:o.template.name})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"px-2 py-1 rounded text-xs font-medium",style:{backgroundColor:F(o.status),color:"white"},children:ie(o.status)}),o.end_time&&s.jsx("span",{className:"text-xs text-gray-500",children:E(o.duration_ms)})]})]}),s.jsx("div",{className:"text-xs text-gray-600 mt-1",children:o.template.description}),o.error&&s.jsxs("div",{className:"text-xs text-red-600 mt-1",children:["错误: ",o.error]})]},B))})]}),s.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"耗时:"}),s.jsx("span",{className:"ml-1 font-mono",children:E(C.duration_ms)})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"重试次数:"}),s.jsx("span",{className:"ml-1",children:C.steps.reduce((o,B)=>o+B.attempt-1,0)})]}),C.end_time&&s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"结束时间:"}),s.jsx("span",{className:"ml-1 font-mono",children:Q(C.end_time)})]}),C.error&&s.jsxs("div",{className:"col-span-2",children:[s.jsx("span",{className:"text-gray-600",children:"错误:"}),s.jsx("span",{className:"ml-1 text-red-600",children:C.error})]})]})]})]}),s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"🎮"}),"工作流程控制"]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"选择工作流程模板"}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:f.map(o=>s.jsxs("div",{className:`p-3 border rounded-lg cursor-pointer transition-colors ${D.templateName===o.name?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"}`,onClick:()=>ne(o),children:[s.jsx("div",{className:"font-medium",children:o.name}),s.jsx("div",{className:"text-sm text-gray-600 mt-1",children:o.description}),s.jsxs("div",{className:"text-xs text-gray-500 mt-2",children:[o.steps.length," 步骤 • 预计 ",E(o.estimated_duration_ms)]})]},o.name))})]}),_&&D.templateName&&s.jsxs("div",{className:"border-t pt-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-3",children:[s.jsxs("label",{className:"text-sm font-medium text-gray-700",children:["工作流程配置 - ",D.templateName]}),s.jsx("button",{onClick:()=>V(!_),className:"btn btn-sm btn-outline",children:_?"隐藏配置":"显示配置"})]}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:Object.entries(D.config).map(([o,B])=>s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:o.replace(/_/g," ").replace(/\b\w/g,P=>P.toUpperCase())}),typeof B=="boolean"?s.jsxs("select",{value:String(B),onChange:P=>w(o,P.target.value==="true"),className:"select select-bordered select-sm w-full",children:[s.jsx("option",{value:"true",children:"是"}),s.jsx("option",{value:"false",children:"否"})]}):typeof B=="number"?s.jsx("input",{type:"number",value:B,onChange:P=>w(o,parseFloat(P.target.value)||0),className:"input input-bordered input-sm w-full"}):s.jsx("input",{type:"text",value:B,onChange:P=>w(o,P.target.value),className:"input input-bordered input-sm w-full"})]},o))})]}),s.jsxs("div",{className:"flex space-x-4",children:[s.jsx("button",{onClick:Y,disabled:S||!i||!D.templateName||(C==null?void 0:C.status)==="running",className:"btn btn-primary flex-1",children:S?"启动中...":"🚀 启动工作流程"}),s.jsx("button",{onClick:()=>V(!_),disabled:!D.templateName,className:"btn btn-secondary",children:"⚙️ 配置"})]})]})]}),s.jsxs("div",{className:"card",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-4 flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"📋"}),"模板详情"]}),s.jsx("div",{className:"space-y-3",children:f.map(o=>s.jsxs("div",{className:"border rounded-lg",children:[s.jsx("div",{className:"p-4 cursor-pointer hover:bg-gray-50",onClick:()=>I(te===o.name?null:o.name),children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"font-medium",children:o.name}),s.jsx("div",{className:"text-sm text-gray-600",children:o.description})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsxs("span",{className:"text-sm text-gray-500",children:[o.steps.length," 步骤"]}),s.jsx("span",{className:"text-lg",children:te===o.name?"▼":"▶"})]})]})}),te===o.name&&s.jsxs("div",{className:"border-t px-4 pb-4",children:[s.jsx("div",{className:"space-y-3 mt-4",children:o.steps.map((B,P)=>s.jsxs("div",{className:"flex items-center space-x-3 p-2 bg-gray-50 rounded",children:[s.jsx("span",{className:"flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium",children:P+1}),s.jsxs("div",{className:"flex-1",children:[s.jsx("div",{className:"text-sm font-medium",children:B.name}),s.jsx("div",{className:"text-xs text-gray-600",children:B.description})]}),s.jsx("div",{className:"text-xs text-gray-500",children:E(B.timeout_ms)})]},P))}),s.jsxs("div",{className:"mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"预计耗时:"}),s.jsx("span",{className:"ml-1",children:E(o.estimated_duration_ms)})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"最大重试:"}),s.jsx("span",{className:"ml-1",children:o.max_retries})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"允许跳过:"}),s.jsx("span",{className:"ml-1",children:o.allow_skip_on_error?"是":"否"})]})]})]})]},o.name))})]}),s.jsxs("div",{className:"card",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsxs("h3",{className:"text-lg font-semibold flex items-center",children:[s.jsx("span",{className:"text-xl mr-2",children:"📚"}),"执行历史"]}),s.jsx("button",{onClick:()=>ae(!le),className:"btn btn-sm btn-outline",children:le?"隐藏历史":"显示历史"})]}),le&&s.jsx("div",{className:"space-y-3",children:J.length===0?s.jsx("div",{className:"text-center py-8 text-gray-500",children:"暂无执行历史记录"}):J.map(o=>s.jsxs("div",{className:"border rounded-lg p-4",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("div",{className:"font-medium",children:o.template_name}),s.jsxs("div",{className:"text-sm text-gray-600",children:[Q(o.start_time)," - ",o.end_time?Q(o.end_time):"进行中"]})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"px-3 py-1 rounded-full text-sm font-medium",style:{backgroundColor:F(o.status),color:"white"},children:ie(o.status)}),s.jsx("span",{className:"text-sm text-gray-500",children:E(o.duration_ms)})]})]}),o.error&&s.jsxs("div",{className:"mt-2 text-sm text-red-600",children:["错误: ",o.error]}),s.jsxs("div",{className:"mt-3 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"进度:"}),s.jsxs("span",{className:"ml-1",children:[o.current_step_index+1,"/",o.total_steps]})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"完成度:"}),s.jsxs("span",{className:"ml-1",children:[o.progress_percentage,"%"]})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"成功步骤:"}),s.jsx("span",{className:"ml-1",children:o.steps.filter(B=>B.status==="completed").length})]}),s.jsxs("div",{children:[s.jsx("span",{className:"text-gray-600",children:"失败步骤:"}),s.jsx("span",{className:"ml-1",children:o.steps.filter(B=>B.status==="failed").length})]})]})]},o.id))})]}),s.jsxs("div",{className:"card bg-yellow-50 border-yellow-200",children:[s.jsxs("h3",{className:"text-lg font-semibold mb-3 flex items-center text-yellow-800",children:[s.jsx("span",{className:"text-xl mr-2",children:"💡"}),"操作指南"]}),s.jsxs("div",{className:"space-y-2 text-sm text-yellow-700",children:[s.jsx("div",{children:"• 确保AGV已连接后再启动工作流程"}),s.jsx("div",{children:"• 每个工作流程模板包含多个预定义的测试步骤"}),s.jsx("div",{children:"• 可以通过配置选项自定义工作流程参数"}),s.jsx("div",{children:"• 实时监控工作流程执行进度和每个步骤的状态"}),s.jsx("div",{children:"• 出现错误时可以查看详细的错误信息"}),s.jsx("div",{children:"• 历史记录保存了所有执行过的工作流程"})]})]})]})}function Y0({}){const[i,d]=v.useState("monitor"),[h,x]=v.useState(!1),[p,S]=v.useState(!1),[k,A]=v.useState(""),[b,f]=v.useState({totalNavigations:0,successfulNavigations:0,totalWorkflows:0,successfulWorkflows:0,totalMonitoringTime:0,lastActivity:null});v.useEffect(()=>{g()},[]),v.useEffect(()=>{const w=setInterval(()=>{h&&g()},1e4);return()=>clearInterval(w)},[h]);const g=async()=>{try{const w=await he.getStatus();w.success&&w.data?x(w.data.connected):x(!1)}catch{x(!1)}},C=async()=>{S(!0);try{await he.connect(),await he.connectWebSocket(),x(!0),A("AGV连接成功"),J("connect")}catch(w){A(`连接失败: ${w.message}`)}finally{S(!1)}},M=async()=>{S(!0);try{await he.disconnect(),he.disconnectWebSocket(),x(!1),A("AGV连接已断开")}catch(w){A(`断开失败: ${w.message}`)}finally{S(!1)}},J=w=>{switch(f(Q=>({...Q,lastActivity:new Date().toISOString()})),w){case"navigation_success":f(Q=>({...Q,totalNavigations:Q.totalNavigations+1,successfulNavigations:Q.successfulNavigations+1}));break;case"navigation_total":f(Q=>({...Q,totalNavigations:Q.totalNavigations+1}));break;case"workflow_success":f(Q=>({...Q,totalWorkflows:Q.totalWorkflows+1,successfulWorkflows:Q.successfulWorkflows+1}));break;case"workflow_total":f(Q=>({...Q,totalWorkflows:Q.totalWorkflows+1}));break}},z=w=>{J("navigation_total"),A(`开始导航到点位 ${w}`)},D=w=>{J("navigation_total"),A(`开始导航到机器 ${w}`)},j=w=>{A(`查询机器 ${w} 的导航点`)},te=w=>{A(`工作模式已切换到${w?"自动":"手动"}模式`)},I=(w,Q,E)=>{A(`激光导航初始化: (${w}, ${Q}, ${E}°)`)},le=w=>{A(`监控已启动${w?` (间隔: ${w}ms)`:""}`)},ae=()=>{A("监控已停止")},_=()=>{A("监控历史已清空")},V=(w,Q)=>{J("workflow_total"),A(`工作流程 "${w}" 已启动`)},u=()=>{A("工作流程已停止")},q=()=>{A("工作流程模板已刷新")},X=()=>{A("工作流程历史已刷新")},Y=()=>b.totalNavigations===0?0:Math.round(b.successfulNavigations/b.totalNavigations*100),L=()=>b.totalWorkflows===0?0:Math.round(b.successfulWorkflows/b.totalWorkflows*100),K=[{id:"monitor",name:"AGV监控",icon:"📊",component:s.jsx(G0,{connected:h,onConnect:C,onDisconnect:M,onStartMonitoring:le,onStopMonitoring:ae,onClearHistory:_})},{id:"navigation",name:"导航控制",icon:"🗺️",component:s.jsx(V0,{connected:h,onNavigateToPoint:z,onNavigateToMachine:D,onQueryNavigationPoint:j,onSwitchMode:te,onLaserInit:I})},{id:"workflow",name:"工作流程",icon:"⚡",component:s.jsx(B0,{connected:h,onStartWorkflow:V,onStopWorkflow:u,onRefreshTemplates:q,onRefreshHistory:X})}],ne=K.find(w=>w.id===i);return s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx("div",{className:"bg-white shadow-sm border-b",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:s.jsxs("div",{className:"flex items-center justify-between h-16",children:[s.jsx("div",{className:"flex items-center",children:s.jsxs("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[s.jsx("span",{className:"text-3xl mr-2",children:"🤖"}),"AGV测试系统"]})}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:`w-3 h-3 rounded-full ${h?"bg-green-500":"bg-red-500"}`}),s.jsx("span",{className:"text-sm text-gray-600",children:h?"已连接":"未连接"})]}),h?s.jsx("button",{onClick:M,disabled:p,className:"btn btn-error btn-sm",children:p?"断开中...":"断开连接"}):s.jsx("button",{onClick:C,disabled:p,className:"btn btn-primary btn-sm",children:p?"连接中...":"连接AGV"})]})]})})}),s.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6",children:[s.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center",children:s.jsx("span",{className:"text-blue-600 text-sm",children:"🗺️"})})}),s.jsxs("div",{className:"ml-4",children:[s.jsx("p",{className:"text-sm font-medium text-gray-500",children:"导航次数"}),s.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:b.totalNavigations}),s.jsxs("p",{className:"text-xs text-gray-500",children:["成功率: ",Y(),"%"]})]})]})}),s.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx("div",{className:"w-8 h-8 bg-green-100 rounded-md flex items-center justify-center",children:s.jsx("span",{className:"text-green-600 text-sm",children:"⚡"})})}),s.jsxs("div",{className:"ml-4",children:[s.jsx("p",{className:"text-sm font-medium text-gray-500",children:"工作流程"}),s.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:b.totalWorkflows}),s.jsxs("p",{className:"text-xs text-gray-500",children:["成功率: ",L(),"%"]})]})]})}),s.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx("div",{className:"w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center",children:s.jsx("span",{className:"text-purple-600 text-sm",children:"🔗"})})}),s.jsxs("div",{className:"ml-4",children:[s.jsx("p",{className:"text-sm font-medium text-gray-500",children:"连接状态"}),s.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:h?"正常":"断开"}),s.jsxs("p",{className:"text-xs text-gray-500",children:["WebSocket: ",he.isWebSocketConnected()?"连接":"断开"]})]})]})}),s.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx("div",{className:"w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center",children:s.jsx("span",{className:"text-yellow-600 text-sm",children:"⏰"})})}),s.jsxs("div",{className:"ml-4",children:[s.jsx("p",{className:"text-sm font-medium text-gray-500",children:"最近活动"}),s.jsx("p",{className:"text-lg font-semibold text-gray-900",children:b.lastActivity?new Date(b.lastActivity).toLocaleTimeString("zh-CN",{hour12:!1}):"暂无"}),s.jsx("p",{className:"text-xs text-gray-500",children:"上次操作时间"})]})]})})]}),k&&s.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4 mb-6",children:s.jsxs("div",{className:"flex",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx("span",{className:"text-blue-400",children:"ℹ️"})}),s.jsx("div",{className:"ml-3",children:s.jsx("p",{className:"text-sm text-blue-800",children:k})}),s.jsx("div",{className:"ml-auto pl-3",children:s.jsx("div",{className:"-mx-1.5 -my-1.5",children:s.jsxs("button",{onClick:()=>A(""),className:"inline-flex bg-blue-50 rounded-md p-1.5 text-blue-500 hover:bg-blue-100",children:[s.jsx("span",{className:"sr-only",children:"关闭"}),s.jsx("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})]})})})]})}),s.jsxs("div",{className:"bg-white rounded-lg shadow",children:[s.jsx("div",{className:"border-b border-gray-200",children:s.jsx("nav",{className:"-mb-px flex",children:K.map(w=>s.jsxs("button",{onClick:()=>d(w.id),className:`py-4 px-6 border-b-2 font-medium text-sm transition-colors ${i===w.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[s.jsx("span",{className:"mr-2",children:w.icon}),w.name]},w.id))})}),s.jsx("div",{className:"p-6",children:ne==null?void 0:ne.component})]}),s.jsx("div",{className:"mt-8 border-t border-gray-200 pt-6",children:s.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-500",children:[s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("span",{children:"AGV测试系统 v1.0.0"}),s.jsx("span",{children:"•"}),s.jsx("span",{children:"纺织制造AGV导航测试平台"})]}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsxs("span",{children:["当前标签页: ",ne==null?void 0:ne.name]}),s.jsx("span",{children:"•"}),s.jsxs("span",{children:["系统时间: ",new Date().toLocaleString("zh-CN",{hour12:!1,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})]})]})]})})]})]})}function $0({isOpen:i,onClose:d,onLogin:h}){const[x,p]=v.useState(""),[S,k]=v.useState(""),A=f=>{f.preventDefault(),h(x)?(p(""),k(""),d()):(k("密码错误，请重试"),p(""))},b=()=>{p(""),k(""),d()};return i?s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:s.jsxs("div",{className:"bg-white rounded-lg p-6 w-96 shadow-xl",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"管理员登录"}),s.jsx("button",{onClick:b,className:"text-gray-400 hover:text-gray-600",children:"✕"})]}),s.jsxs("form",{onSubmit:A,children:[s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"管理员密码"}),s.jsx("input",{type:"password",value:x,onChange:f=>p(f.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入管理员密码",autoFocus:!0})]}),S&&s.jsx("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg text-sm",children:S}),s.jsxs("div",{className:"flex space-x-3",children:[s.jsx("button",{type:"button",onClick:b,className:"flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors",children:"取消"}),s.jsx("button",{type:"submit",className:"flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors",children:"登录"})]})]})]})}):null}function X0(i){return{0:"待机",1:"手动",2:"半自动",3:"自动",4:"示教",5:"服务",6:"维修"}[i]||"未知"}function Q0(){const[i,d]=v.useState("monitor"),[h,x]=v.useState("agv"),[p,S]=v.useState(()=>localStorage.getItem("isAdmin")==="true"),[k,A]=v.useState(!1),{isConnected:b,agvStatus:f,error:g}=Kn(),C=I=>I==="admin123"?(S(!0),localStorage.setItem("isAdmin","true"),!0):!1,M=()=>{S(!1),localStorage.removeItem("isAdmin")},J=I=>I>.75||I>.5?"🔋":I>.25?"🪫":"🔴",z=[{id:"monitor",label:"单机看车",extra:null},{id:"auto-watch",label:"🤖 自动看车",extra:null},{id:"manual",label:"手动控制",extra:f?s.jsxs("div",{className:"flex items-center space-x-1 ml-2 text-xs",children:[s.jsx("span",{children:J(f.batteryPercent)}),s.jsxs("span",{className:`font-medium ${f.batteryPercent>.2?"text-green-600":"text-red-600"}`,children:[(f.batteryPercent*100).toFixed(0),"%"]})]}):null},{id:"cache",label:"💾 缓存管理",extra:null},{id:"flow-monitor",label:"🔍 流程监控",extra:null},{id:"status",label:"实时状态",extra:null},{id:"agv-test",label:"🤖 AGV测试",extra:null},{id:"plc-test",label:"🔧 PLC测试",extra:null},{id:"logs",label:"📋 日志查看",extra:null},{id:"settings",label:"设置",extra:null}],D=["monitor","auto-watch","manual","cache"],j=p?z:z.filter(I=>D.includes(I.id)),te=[{id:"agv",label:"AGV控制",icon:"🚛"},{id:"plc",label:"PLC控制",icon:"⚙️"}];return s.jsxs("div",{className:"h-screen bg-gray-50 flex flex-col",children:[s.jsxs("div",{className:"titlebar bg-white shadow-sm border-b border-gray-200 px-6 py-3 flex items-center justify-between",children:[s.jsx("div",{className:"flex items-center space-x-4",children:f&&s.jsxs("div",{className:"flex items-center space-x-4 text-sm",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-gray-600",children:"模式:"}),s.jsx("span",{className:`font-medium ${f.workMode===3?"text-green-600":"text-yellow-600"}`,children:X0(f.workMode)})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-gray-600",children:"电池:"}),s.jsxs("span",{className:`font-medium ${f.batteryPercent>.2?"text-green-600":"text-red-600"}`,children:[(f.batteryPercent*100).toFixed(0),"%"]})]})]})}),s.jsxs("div",{className:"flex items-center space-x-4",children:[p?s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("span",{className:"text-sm text-green-600 font-medium",children:"管理员模式"}),s.jsx("button",{onClick:M,className:"px-3 py-1 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 hover:border-gray-400 rounded transition-colors",children:"退出管理"})]}):s.jsx("button",{onClick:()=>A(!0),className:"px-3 py-1 text-sm text-blue-600 hover:text-blue-800 border border-blue-300 hover:border-blue-400 rounded transition-colors",children:"管理员登录"}),s.jsx(Ah,{isConnected:b,error:g})]})]}),s.jsx("div",{className:"bg-white border-b border-gray-200",children:s.jsx("div",{className:"max-w-7xl mx-auto",children:s.jsx("nav",{className:"flex space-x-8 px-6","aria-label":"标签",children:j.map(I=>s.jsx("button",{onClick:()=>d(I.id),className:`py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors ${i===I.id?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:s.jsxs("span",{className:"flex items-center space-x-2",children:[s.jsx("span",{children:I.label}),I.extra&&I.extra]})},I.id))})})}),i==="manual"&&s.jsx("div",{className:"bg-gray-50 border-b border-gray-200",children:s.jsx("div",{className:"max-w-7xl mx-auto",children:s.jsx("nav",{className:"flex space-x-4 px-6","aria-label":"二级菜单",children:te.map(I=>s.jsx("button",{onClick:()=>x(I.id),className:`py-3 px-4 text-sm font-medium rounded-t-lg transition-colors ${h===I.id?"bg-white text-blue-600 border-t border-l border-r border-gray-200":"text-gray-600 hover:text-gray-800"}`,children:s.jsxs("span",{className:"flex items-center space-x-2",children:[s.jsx("span",{children:I.icon}),s.jsx("span",{children:I.label})]})},I.id))})})}),s.jsx("div",{className:"flex-1 overflow-auto",children:i==="logs"?s.jsx(v0,{}):i==="plc-test"?s.jsx(k0,{}):i==="agv-test"?s.jsx(Y0,{}):s.jsxs("div",{className:"max-w-7xl mx-auto p-6",children:[i==="manual"&&h==="agv"&&s.jsx(ph,{}),i==="manual"&&h==="plc"&&s.jsx(jh,{}),i==="monitor"&&s.jsx(Ch,{}),i==="auto-watch"&&s.jsx(j0,{}),i==="flow-monitor"&&s.jsx(Th,{}),i==="status"&&s.jsx(Nh,{agvStatus:f}),i==="cache"&&s.jsx(p0,{}),i==="settings"&&s.jsx(wh,{})]})}),s.jsx($0,{isOpen:k,onClose:()=>A(!1),onLogin:C})]})}fh.createRoot(document.getElementById("root")).render(s.jsx(ch.StrictMode,{children:s.jsx(hh,{children:s.jsx(Q0,{})})}));
