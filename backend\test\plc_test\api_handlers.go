package plc_test

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/gorilla/mux"
	"github.com/gorilla/websocket"
	"github.com/user/agv_nav/pkg/plc"
)

// PLCTestAPI PLC测试API处理器
type PLCTestAPI struct {
	testController *PLCTestController
	upgrader       websocket.Upgrader
}

// NewPLCTestAPI 创建新的PLC测试API处理器
func NewPLCTestAPI(plcController *plc.Controller) *PLCTestAPI {
	return &PLCTestAPI{
		testController: NewPLCTestController(plcController),
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // 允许跨域
			},
		},
	}
}

// RegisterRoutes 注册路由
func (api *PLCTestAPI) RegisterRoutes(router *mux.Router) {
	// PLC测试控制接口
	router.HandleFunc("/plc-test/step/{stepId}", api.executeStep).Methods("POST", "OPTIONS")
	router.HandleFunc("/plc-test/workflow", api.executeFullWorkflow).Methods("POST", "OPTIONS")
	router.HandleFunc("/plc-test/workflow/stop", api.stopWorkflow).Methods("POST", "OPTIONS")
	router.HandleFunc("/plc-test/status", api.getStatus).Methods("GET", "OPTIONS")
	router.HandleFunc("/plc-test/steps", api.getWorkflowSteps).Methods("GET", "OPTIONS")

	// PLC监控接口
	router.HandleFunc("/plc-test/monitor/values", api.getCurrentValues).Methods("GET", "OPTIONS")
	router.HandleFunc("/plc-test/monitor/start", api.startMonitoring).Methods("POST", "OPTIONS")
	router.HandleFunc("/plc-test/monitor/stop", api.stopMonitoring).Methods("POST", "OPTIONS")
	router.HandleFunc("/plc-test/monitor/refresh", api.refreshValues).Methods("POST", "OPTIONS")

	// WebSocket实时监控
	router.HandleFunc("/plc-test/monitor/ws", api.handleWebSocket).Methods("GET")
}

// executeStep 执行单个步骤
func (api *PLCTestAPI) executeStep(w http.ResponseWriter, r *http.Request) {
	// 处理CORS
	api.setCORSHeaders(w)
	if r.Method == "OPTIONS" {
		return
	}

	vars := mux.Vars(r)
	stepID := vars["stepId"]

	log.Printf("收到执行步骤请求: %s", stepID)

	result, err := api.testController.ExecuteStep(stepID)
	if err != nil {
		log.Printf("执行步骤失败: %v", err)
		api.writeErrorResponse(w, http.StatusBadRequest, err.Error())
		return
	}

	log.Printf("步骤 %s 执行完成，状态: %s", stepID, result.Status)

	response := map[string]interface{}{
		"error":  false,
		"result": result,
	}

	api.writeJSONResponse(w, http.StatusOK, response)
}

// executeFullWorkflow 执行完整工作流程
func (api *PLCTestAPI) executeFullWorkflow(w http.ResponseWriter, r *http.Request) {
	// 处理CORS
	api.setCORSHeaders(w)
	if r.Method == "OPTIONS" {
		return
	}

	log.Printf("收到执行完整工作流程请求")

	if api.testController.IsRunning() {
		api.writeErrorResponse(w, http.StatusBadRequest, "工作流程正在执行中，请先停止")
		return
	}

	// 异步执行工作流程
	go func() {
		err := api.testController.ExecuteFullWorkflow()
		if err != nil {
			log.Printf("完整工作流程执行失败: %v", err)
		} else {
			log.Printf("完整工作流程执行成功")
		}
	}()

	response := map[string]interface{}{
		"error":   false,
		"message": "工作流程已启动",
	}

	api.writeJSONResponse(w, http.StatusOK, response)
}

// stopWorkflow 停止工作流程
func (api *PLCTestAPI) stopWorkflow(w http.ResponseWriter, r *http.Request) {
	// 处理CORS
	api.setCORSHeaders(w)
	if r.Method == "OPTIONS" {
		return
	}

	log.Printf("收到停止工作流程请求")

	api.testController.StopWorkflow()

	response := map[string]interface{}{
		"error":   false,
		"message": "工作流程已停止",
	}

	api.writeJSONResponse(w, http.StatusOK, response)
}

// getStatus 获取当前状态
func (api *PLCTestAPI) getStatus(w http.ResponseWriter, r *http.Request) {
	// 处理CORS
	api.setCORSHeaders(w)
	if r.Method == "OPTIONS" {
		return
	}

	status := map[string]interface{}{
		"isRunning": api.testController.IsRunning(),
		"timestamp": time.Now().Format("2006-01-02 15:04:05"),
	}

	response := map[string]interface{}{
		"error":  false,
		"status": status,
	}

	api.writeJSONResponse(w, http.StatusOK, response)
}

// getWorkflowSteps 获取工作流程步骤
func (api *PLCTestAPI) getWorkflowSteps(w http.ResponseWriter, r *http.Request) {
	// 处理CORS
	api.setCORSHeaders(w)
	if r.Method == "OPTIONS" {
		return
	}

	steps := api.testController.GetWorkflowSteps()

	response := map[string]interface{}{
		"error": false,
		"steps": steps,
	}

	api.writeJSONResponse(w, http.StatusOK, response)
}

// getCurrentValues 获取当前监控值
func (api *PLCTestAPI) getCurrentValues(w http.ResponseWriter, r *http.Request) {
	// 处理CORS
	api.setCORSHeaders(w)
	if r.Method == "OPTIONS" {
		return
	}

	values := api.testController.GetCurrentValues()

	response := map[string]interface{}{
		"error":  false,
		"values": values,
	}

	api.writeJSONResponse(w, http.StatusOK, response)
}

// startMonitoring 开始监控
func (api *PLCTestAPI) startMonitoring(w http.ResponseWriter, r *http.Request) {
	// 处理CORS
	api.setCORSHeaders(w)
	if r.Method == "OPTIONS" {
		return
	}

	log.Printf("启动PLC实时监控")

	api.testController.StartMonitoring()

	response := map[string]interface{}{
		"error":   false,
		"message": "PLC监控已启动",
	}

	api.writeJSONResponse(w, http.StatusOK, response)
}

// stopMonitoring 停止监控
func (api *PLCTestAPI) stopMonitoring(w http.ResponseWriter, r *http.Request) {
	// 处理CORS
	api.setCORSHeaders(w)
	if r.Method == "OPTIONS" {
		return
	}

	log.Printf("停止PLC实时监控")

	api.testController.StopMonitoring()

	response := map[string]interface{}{
		"error":   false,
		"message": "PLC监控已停止",
	}

	api.writeJSONResponse(w, http.StatusOK, response)
}

// refreshValues 手动刷新监控值
func (api *PLCTestAPI) refreshValues(w http.ResponseWriter, r *http.Request) {
	// 处理CORS
	api.setCORSHeaders(w)
	if r.Method == "OPTIONS" {
		return
	}

	api.testController.monitor.ManualRefresh()

	response := map[string]interface{}{
		"error":   false,
		"message": "PLC地址值已刷新",
	}

	api.writeJSONResponse(w, http.StatusOK, response)
}

// handleWebSocket 处理WebSocket连接
func (api *PLCTestAPI) handleWebSocket(w http.ResponseWriter, r *http.Request) {
	conn, err := api.upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket升级失败: %v", err)
		return
	}
	defer conn.Close()

	clientID := fmt.Sprintf("ws_%d", time.Now().UnixNano())
	log.Printf("新的WebSocket连接建立: %s", clientID)

	// 创建消息通道
	messageChan := make(chan PLCChangeData, 100)

	// 添加监控订阅者
	api.testController.AddMonitorSubscriber(clientID, func(data PLCChangeData) {
		select {
		case messageChan <- data:
		default:
			// 通道已满，丢弃消息
			log.Printf("WebSocket消息通道已满，丢弃消息")
		}
	})

	// 清理订阅者
	defer func() {
		api.testController.RemoveMonitorSubscriber(clientID)
		close(messageChan)
		log.Printf("WebSocket连接已关闭: %s", clientID)
	}()

	// 设置ping/pong处理
	conn.SetPongHandler(func(string) error {
		conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	// 启动ping定时器
	pingTicker := time.NewTicker(30 * time.Second)
	defer pingTicker.Stop()

	// 处理消息循环
	for {
		select {
		case data, ok := <-messageChan:
			if !ok {
				return
			}

			// 发送PLC变化数据
			if err := conn.WriteJSON(data); err != nil {
				log.Printf("发送WebSocket消息失败: %v", err)
				return
			}

		case <-pingTicker.C:
			// 发送ping消息
			if err := conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				log.Printf("发送ping消息失败: %v", err)
				return
			}
		}
	}
}

// 测试接口相关的请求和响应结构体

// ExecuteStepRequest 执行步骤请求
type ExecuteStepRequest struct {
	StepID string `json:"stepId"`
}

// ExecuteStepResponse 执行步骤响应
type ExecuteStepResponse struct {
	Error   bool        `json:"error"`
	Message string      `json:"message,omitempty"`
	Result  *StepResult `json:"result,omitempty"`
}

// WorkflowStatusResponse 工作流程状态响应
type WorkflowStatusResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message,omitempty"`
	Status  struct {
		IsRunning bool   `json:"isRunning"`
		Timestamp string `json:"timestamp"`
	} `json:"status,omitempty"`
}

// WorkflowStepsResponse 工作流程步骤响应
type WorkflowStepsResponse struct {
	Error bool           `json:"error"`
	Steps []WorkflowStep `json:"steps,omitempty"`
}

// MonitorValuesResponse 监控值响应
type MonitorValuesResponse struct {
	Error  bool                   `json:"error"`
	Values map[string]interface{} `json:"values,omitempty"`
}

// WebSocketMessage WebSocket消息格式
type WebSocketMessage struct {
	Type string      `json:"type"` // "plc_change", "error", "ping"
	Data interface{} `json:"data"`
}

// 额外的测试工具接口

// setPLCAddress 手动设置PLC地址值（测试用）
func (api *PLCTestAPI) setPLCAddress(w http.ResponseWriter, r *http.Request) {
	// 处理CORS
	api.setCORSHeaders(w)
	if r.Method == "OPTIONS" {
		return
	}

	var req struct {
		Address string      `json:"address"`
		Value   interface{} `json:"value"`
		Type    string      `json:"type"` // "coil" or "register"
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		api.writeErrorResponse(w, http.StatusBadRequest, "请求参数错误: "+err.Error())
		return
	}

	// 这里可以添加手动设置PLC地址的逻辑，用于测试
	log.Printf("手动设置PLC地址: %s = %v (%s)", req.Address, req.Value, req.Type)

	response := map[string]interface{}{
		"error":   false,
		"message": fmt.Sprintf("地址 %s 已设置为 %v", req.Address, req.Value),
	}

	api.writeJSONResponse(w, http.StatusOK, response)
}

// getPLCAddress 手动读取PLC地址值（测试用）
func (api *PLCTestAPI) getPLCAddress(w http.ResponseWriter, r *http.Request) {
	// 处理CORS
	api.setCORSHeaders(w)
	if r.Method == "OPTIONS" {
		return
	}

	vars := mux.Vars(r)
	address := vars["address"]
	addressType := r.URL.Query().Get("type") // "coil" or "register"

	if address == "" || addressType == "" {
		api.writeErrorResponse(w, http.StatusBadRequest, "地址和类型参数不能为空")
		return
	}

	// 解析Modbus地址
	modbusAddr, err := strconv.ParseUint(address[1:], 10, 16)
	if err != nil {
		api.writeErrorResponse(w, http.StatusBadRequest, "地址格式错误")
		return
	}

	var value interface{}
	var readErr error

	if addressType == "coil" {
		data, err := api.testController.plcController.ReadCoils(uint16(modbusAddr), 1)
		if err == nil && len(data) > 0 {
			value = data[0] != 0
		} else {
			readErr = err
		}
	} else if addressType == "register" {
		data, err := api.testController.plcController.ReadHoldingRegisters(uint16(modbusAddr), 1)
		if err == nil && len(data) >= 2 {
			value = uint16(data[0])<<8 | uint16(data[1])
		} else {
			readErr = err
		}
	}

	if readErr != nil {
		api.writeErrorResponse(w, http.StatusInternalServerError, "读取PLC地址失败: "+readErr.Error())
		return
	}

	response := map[string]interface{}{
		"error":   false,
		"address": address,
		"value":   value,
		"type":    addressType,
	}

	api.writeJSONResponse(w, http.StatusOK, response)
}

// RegisterTestRoutes 注册额外的测试工具路由
func (api *PLCTestAPI) RegisterTestRoutes(router *mux.Router) {
	router.HandleFunc("/plc-test/tools/set-address", api.setPLCAddress).Methods("POST", "OPTIONS")
	router.HandleFunc("/plc-test/tools/get-address/{address}", api.getPLCAddress).Methods("GET", "OPTIONS")
}

// 辅助方法

// setCORSHeaders 设置CORS头
func (api *PLCTestAPI) setCORSHeaders(w http.ResponseWriter) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
}

// writeJSONResponse 写入JSON响应
func (api *PLCTestAPI) writeJSONResponse(w http.ResponseWriter, statusCode int, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(data)
}

// writeErrorResponse 写入错误响应
func (api *PLCTestAPI) writeErrorResponse(w http.ResponseWriter, statusCode int, message string) {
	response := map[string]interface{}{
		"error":   true,
		"message": message,
	}
	api.writeJSONResponse(w, statusCode, response)
}
