package zmq

import (
	"context"
	"log"

	zmqpkg "github.com/user/agv_nav/pkg/zmq"
)

// ExampleHeartbeatUsage 展示如何使用心跳管理器
func ExampleHeartbeatUsage() {
	// 1. 创建 ZMQ 配置
	config := zmqpkg.DefaultConfig()
	config.RequesterEndpoint = "tcp://localhost:5556" // 连接到调度系统
	config.ResponderEndpoint = "tcp://*:5557"         // 监听调度系统的心跳

	// 2. 创建请求者和响应者
	requester, err := zmqpkg.NewRequester(config)
	if err != nil {
		log.Fatalf("创建请求者失败: %v", err)
	}

	// 注意：现在心跳通过MessageRouter路由，不需要直接创建responder
	// responder, err := zmqpkg.NewResponder(config)
	// if err != nil {
	//	log.Fatalf("创建响应者失败: %v", err)
	// }

	// 3. 加载系统配置
	sysConfig, err := LoadSystemConfig("../data/task_config.json")
	if err != nil {
		log.Printf("加载系统配置失败，使用默认配置: %v", err)
		sysConfig = DefaultSystemConfig()
	}

	// 4. 创建心跳管理器
	heartbeatManager := NewHeartbeatManager(requester, sysConfig)

	// 5. 启动心跳管理器
	ctx := context.Background()
	go func() {
		if err := heartbeatManager.Start(ctx); err != nil {
			log.Fatalf("启动心跳管理器失败: %v", err)
		}
	}()

	log.Println("心跳管理器已启动...")

	// 6. 程序运行中可以查询状态
	// currentState := heartbeatManager.GetCurrentState()
	// lastHeartbeat := heartbeatManager.GetLastHeartbeat()
	// log.Printf("当前状态: %v, 最后心跳: %v", currentState, lastHeartbeat)

	// 7. 程序退出时停止心跳
	// heartbeatManager.Stop()
}

// customLogger 自定义日志实现
type customLogger struct{}

func (l *customLogger) Debug(msg string, fields ...interface{}) {
	log.Printf("[自定义DEBUG] %s %v", msg, fields)
}

func (l *customLogger) Info(msg string, fields ...interface{}) {
	log.Printf("[自定义INFO] %s %v", msg, fields)
}

func (l *customLogger) Warn(msg string, fields ...interface{}) {
	log.Printf("[自定义WARN] %s %v", msg, fields)
}

func (l *customLogger) Error(msg string, fields ...interface{}) {
	log.Printf("[自定义ERROR] %s %v", msg, fields)
}

// ExampleHeartbeatWithCustomLogger 使用自定义日志的示例
func ExampleHeartbeatWithCustomLogger() {

	// 创建配置和连接
	config := zmqpkg.DefaultConfig()
	requester, _ := zmqpkg.NewRequester(config)
	// 注意：现在心跳通过MessageRouter路由，不需要直接创建responder
	// responder, _ := zmqpkg.NewResponder(config)
	sysConfig := DefaultSystemConfig()

	// 使用自定义日志创建心跳管理器
	logger := &customLogger{}
	heartbeatManager := NewHeartbeatManagerWithLogger(requester, sysConfig, logger)

	// 启动
	ctx := context.Background()
	heartbeatManager.Start(ctx)
}

// 心跳状态监控示例
func ExampleHeartbeatMonitoring(manager *HeartbeatManager) {
	go func() {
		for {
			state := manager.GetCurrentState()
			lastHeartbeat := manager.GetLastHeartbeat()

			log.Printf("心跳监控 - 当前状态: %v, 最后心跳: %v", state, lastHeartbeat)

			// 每30秒检查一次
			// time.Sleep(30 * time.Second)
		}
	}()
}
