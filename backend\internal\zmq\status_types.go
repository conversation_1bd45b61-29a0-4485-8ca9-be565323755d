package zmq

// 机器人状态上报相关类型定义

// StatusReportContent 机器人状态上报内容 (指令100)
type StatusReportContent struct {
	RobotNo         string   `json:"robotNo"`             // 机器人编号
	TaskId          string   `json:"taskId"`              // 任务编号（可选）
	RobotTaskState  int      `json:"robotTaskState"`      // 任务状态：10=未开始，20=进行中，30=已完成
	RobotState      int      `json:"robotState"`          // 机器人状态：-99=异常，-1=电量不足，1=正常
	BatteryPower    float64  `json:"batteryPower"`        // 机器人电量
	CurrentPosition string   `json:"currentPosition"`     // 当前位置：0=停车点，1=充电点，2=权限交接点，10=主干道
	Remark          string   `json:"remark"`              // 备注（可选）
	AllowList       []string `json:"allowList"`           // 允许的细纱机面列表
}

// 位置类型常量（CurrentPosition 字段值）
const (
	PositionParkingStr  = "0"  // 停车点
	PositionChargingStr = "1"  // 充电点
	PositionSwitchStr   = "2"  // 权限交接点
	PositionMainRoadStr = "10" // 主干道
)

// 机器人状态常量（RobotState 字段值）
const (
	RobotStateAbnormal   = -99 // 异常
	RobotStateLowBattery = -1  // 电量不足（电量值小于15%，可配置）
	RobotStateNormal     = 1   // 正常
)

// 任务状态常量（RobotTaskState 字段值）
const (
	RobotTaskStateNotStarted = 10 // 未开始
	RobotTaskStateInProgress = 20 // 进行中
	RobotTaskStateCompleted  = 30 // 已完成
)
