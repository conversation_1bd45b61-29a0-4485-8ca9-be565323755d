package protocol

import (
	"encoding/binary"
	"encoding/hex"
	"fmt"
)

// 命令码常量
const (
	CmdWriteVariable     = 0x03
	CmdSwitchMode        = 0x11 // 添加这行 - 直接切换工作模式
	CmdManualPosition    = 0x14
	CmdNavigation        = 0x16
	CmdConfirmReposition = 0x1F
	CmdQueryStatus       = 0xAF
	CmdSubscribe         = 0xB1
	CmdQueryCargoStatus  = 0xB0
	CmdQueryRunStatus    = 0x17 // 激光导航查询机器人运行状态
	CmdQueryNavStatus    = 0x1D // 查询机器人导航状态
)

// HexToBytes 将长度为 32 个十六进制字符的字符串解析成 16 字节
func HexToBytes(authHex string) ([]byte, error) {
	if len(authHex) != 32 {
		return nil, fmt.Errorf("invalid auth hex length: got %d, want 32", len(authHex))
	}
	data, err := hex.DecodeString(authHex)
	if err != nil {
		return nil, fmt.Errorf("hex decode error: %w", err)
	}
	return data, nil
}

// BuildHeader 构造 28 字节报文头部
func BuildHeader(authCode []byte, seqNum uint16, cmd byte, dataLen uint16) ([]byte, error) {
	if len(authCode) != 16 {
		return nil, fmt.Errorf("authCode must be 16 bytes")
	}
	header := make([]byte, 28)

	// 0x00–0x0F: 16 字节授权码
	copy(header[0:16], authCode)

	// 0x10: 协议版本号 = 0x01
	header[16] = 0x01

	// 0x11: 报文类型 = 0x00 (请求)
	header[17] = 0x00

	// 0x12–0x13: 通信序列号 (2 字节, 小端)
	binary.LittleEndian.PutUint16(header[18:20], seqNum)

	// 0x14: 服务码 = 0x10
	header[20] = 0x10

	// 0x15: 命令码
	header[21] = cmd

	// 0x16: 执行码 = 0x00 (请求一律 0)
	header[22] = 0x00

	// 0x17: 预留 = 0x00
	header[23] = 0x00

	// 0x18–0x19: 数据区长度 (2 字节, 小端)
	binary.LittleEndian.PutUint16(header[24:26], dataLen)

	// 0x1A–0x1B: 预留 = 0x0000
	// (make 初始化即为 0，无需写入)

	// 打印调试信息
	fmt.Printf("Debug - Header: ")
	for i, b := range header {
		fmt.Printf("%02X ", b)
		if i == 15 || i == 21 || i == 25 {
			fmt.Printf("| ")
		}
	}
	fmt.Printf("\n")
	fmt.Printf("Debug - Header details: Version=%d, Type=%d, SeqNum=%d, Service=0x%02X, Cmd=0x%02X, DataLen=%d\n",
		header[16], header[17], binary.LittleEndian.Uint16(header[18:20]), header[20], header[21], binary.LittleEndian.Uint16(header[24:26]))

	return header, nil
}
