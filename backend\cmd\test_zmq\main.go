package main

import (
	"encoding/json"
	"log"
	"time"

	"github.com/user/agv_nav/internal/zmq"
	zmqpkg "github.com/user/agv_nav/pkg/zmq"
)

func main() {
	// 连接到本地程序的responder端口
	serverAddr := "tcp://localhost:5557"
	robotNo := "AGV001"
	
	log.Printf("🚀 ZMQ消息路由测试工具")
	log.Printf("📡 连接到: %s (本地程序的responder端口)", serverAddr)
	log.Printf("🤖 机器人: %s", robotNo)
	
	// 创建ZMQ配置
	config := &zmqpkg.Config{
		RequesterEndpoint: serverAddr,
		ConnectTimeout:    5 * time.Second,
		SendTimeout:       5 * time.Second,
		RecvTimeout:       5 * time.Second,
		SendHWM:           1000,
		RecvHWM:           1000,
	}
	
	// 创建请求者（模拟调度系统）
	requester, err := zmqpkg.NewRequester(config)
	if err != nil {
		log.Fatalf("❌ 连接失败: %v", err)
	}
	defer requester.Close()
	
	log.Printf("✅ 连接成功，开始测试消息路由...")
	
	// 测试不同的指令
	tests := []struct {
		name        string
		instruction int
		content     interface{}
	}{
		{
			name:        "心跳(state=true)",
			instruction: 0,
			content: map[string]interface{}{
				"robotNo": robotNo,
				"state":   true,
			},
		},
		{
			name:        "心跳(state=false)",
			instruction: 0,
			content: map[string]interface{}{
				"robotNo": robotNo,
				"state":   false,
			},
		},
		{
			name:        "任务分配",
			instruction: 200,
			content: map[string]interface{}{
				"robotNo": robotNo,
				"taskId":  "test-001",
				"laneNos": []string{"61L-60R"},
				"remark":  "测试任务",
			},
		},
		{
			name:        "无任务",
			instruction: 201,
			content: map[string]interface{}{
				"robotNo": robotNo,
			},
		},
		{
			name:        "等待任务",
			instruction: 202,
			content: map[string]interface{}{
				"robotNo": robotNo,
			},
		},
		{
			name:        "继续任务",
			instruction: 203,
			content: map[string]interface{}{
				"robotNo": robotNo,
			},
		},
	}
	
	// 执行测试
	for i, test := range tests {
		log.Printf("\n🔬 测试 %d/%d: %s (指令%d)", i+1, len(tests), test.name, test.instruction)
		
		// 创建消息
		msg := zmq.NewMessage(test.instruction, true, test.content)
		data, _ := json.Marshal(msg)
		log.Printf("📤 发送: %s", string(data))
		
		// 发送并接收
		reply, err := requester.RequestWithTimeout(data, 5*time.Second)
		if err != nil {
			log.Printf("❌ 失败: %v", err)
		} else {
			log.Printf("📥 回复: %s", string(reply))
			
			// 解析回复验证路由是否正确
			var replyMsg zmq.Message
			if err := json.Unmarshal(reply, &replyMsg); err == nil {
				if replyMsg.Code {
					log.Printf("✅ 路由成功: 指令%d被正确处理", test.instruction)
				} else {
					log.Printf("⚠️ 处理失败: %s", replyMsg.Message)
				}
			}
		}
		
		time.Sleep(2 * time.Second)
	}
	
	log.Printf("\n🏁 测试完成")
}