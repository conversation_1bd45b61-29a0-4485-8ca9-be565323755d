# ZeroMQ 请求-响应通信模块

这是一个用于 Go 项目的 ZeroMQ 通信框架，提供了请求-响应（Request-Response）模式的完整实现。

## 特性

- 🚀 **高性能**：基于 ZeroMQ 的高性能消息传递
- 🔄 **请求/响应模式**：同步的请求-响应通信
- 🛡️ **并发安全**：线程安全的实现
- 🔧 **易于配置**：灵活的配置选项
- 📝 **完善日志**：支持自定义日志记录
- 🔌 **自动连接**：客户端自动连接到服务端
- ⏱️ **超时控制**：可配置的连接和操作超时

## 安装

确保系统已安装 ZeroMQ 库：

```bash
# Ubuntu/Debian
sudo apt-get install libzmq3-dev

# CentOS/RHEL
sudo yum install zeromq-devel

# macOS
brew install zmq

# Windows (使用 vcpkg)
vcpkg install zeromq
```

然后安装 Go 依赖：

```bash
go get github.com/pebbe/zmq4
```

## 快速开始

### 1. 创建服务端（Responder）

```go
package main

import (
    "context"
    "log"
    
    "your-project/pkg/zmq"
)

func main() {
    // 创建配置
    config := zmq.DefaultConfig()
    config.ResponderEndpoint = "tcp://*:5556"
    
    // 创建响应者
    responder, err := zmq.NewResponder(config)
    if err != nil {
        log.Fatal(err)
    }
    defer responder.Close()
    
    // 设置请求处理函数
    responder.SetHandler(func(request []byte) ([]byte, error) {
        log.Printf("Received: %s", string(request))
        // 处理请求并返回响应
        return []byte("Response: " + string(request)), nil
    })
    
    // 启动服务
    ctx := context.Background()
    log.Println("Server started...")
    if err := responder.Start(ctx); err != nil {
        log.Fatal(err)
    }
}
```

### 2. 创建客户端（Requester）

```go
package main

import (
    "log"
    "time"
    
    "your-project/pkg/zmq"
)

func main() {
    // 创建配置
    config := zmq.DefaultConfig()
    config.RequesterEndpoint = "tcp://localhost:5556"
    
    // 创建请求者
    requester, err := zmq.NewRequester(config)
    if err != nil {
        log.Fatal(err)
    }
    defer requester.Close()
    
    // 发送请求
    response, err := requester.RequestString("Hello Server!")
    if err != nil {
        log.Fatal(err)
    }
    
    log.Printf("Response: %s", response)
    
    // 带超时的请求
    data := []byte("Test with timeout")
    resp, err := requester.RequestWithTimeout(data, 2*time.Second)
    if err != nil {
        log.Printf("Request timeout: %v", err)
    } else {
        log.Printf("Response: %s", string(resp))
    }
}
```

## 高级用法

### JSON 消息处理

```go
// 定义消息结构
type Request struct {
    ID      string `json:"id"`
    Command string `json:"command"`
    Data    string `json:"data"`
}

type Response struct {
    ID     string `json:"id"`
    Status string `json:"status"`
    Result string `json:"result"`
}

// 服务端处理
responder.SetHandler(func(request []byte) ([]byte, error) {
    var req Request
    if err := json.Unmarshal(request, &req); err != nil {
        return nil, err
    }
    
    // 处理请求
    resp := Response{
        ID:     req.ID,
        Status: "success",
        Result: processCommand(req.Command, req.Data),
    }
    
    return json.Marshal(resp)
})

// 客户端请求
req := Request{
    ID:      "123",
    Command: "echo",
    Data:    "test data",
}

reqData, _ := json.Marshal(req)
respData, err := requester.Request(reqData)
if err != nil {
    log.Fatal(err)
}

var resp Response
json.Unmarshal(respData, &resp)
```

### 自定义日志

```go
type MyLogger struct {
    // 自定义日志实现
}

func (l *MyLogger) Debug(msg string, fields ...interface{}) {
    // 自定义日志处理
}

// 使用自定义日志
requester, err := zmq.NewRequesterWithLogger(config, &MyLogger{})
responder, err := zmq.NewResponderWithLogger(config, &MyLogger{})
```

### 错误处理

```go
// 服务端错误处理
responder.SetHandler(func(request []byte) ([]byte, error) {
    if len(request) == 0 {
        return nil, fmt.Errorf("empty request")
    }
    
    // 处理请求
    result, err := processRequest(request)
    if err != nil {
        // 返回错误信息
        return []byte(fmt.Sprintf("ERROR: %v", err)), nil
    }
    
    return result, nil
})

// 客户端错误处理
response, err := requester.Request(data)
if err != nil {
    if err.Error() == "resource temporarily unavailable" {
        // 超时错误
        log.Println("Request timeout")
    } else {
        // 其他错误
        log.Printf("Request failed: %v", err)
    }
}
```

## 配置选项

```go
type Config struct {
    // 请求者端点（客户端连接地址）
    RequesterEndpoint string
    
    // 响应者端点（服务端绑定地址）
    ResponderEndpoint string
    
    // 连接超时
    ConnectTimeout time.Duration
    
    // 发送超时
    SendTimeout time.Duration
    
    // 接收超时
    RecvTimeout time.Duration
    
    // 发送队列高水位
    SendHWM int
    
    // 接收队列高水位
    RecvHWM int
}

// 默认配置
config := &zmq.Config{
    RequesterEndpoint: "tcp://localhost:5556",
    ResponderEndpoint: "tcp://*:5556",
    ConnectTimeout:    5 * time.Second,
    SendTimeout:       1 * time.Second,
    RecvTimeout:       100 * time.Millisecond,
    SendHWM:           1000,
    RecvHWM:           1000,
}
```

## 性能优化

1. **调整高水位标记**：根据消息量调整 SendHWM 和 RecvHWM
2. **合理设置超时**：根据网络状况和处理时间调整超时设置
3. **批量处理**：在处理函数中批量处理请求以提高吞吐量
4. **连接池**：对于高并发场景，可以创建多个 Requester 实例

## 注意事项

1. **请求-响应顺序**：必须严格遵循请求-响应的顺序，每个请求必须等待响应
2. **单线程处理**：每个 Responder 在单线程中处理请求，如需并发处理，需要在处理函数中实现
3. **错误恢复**：连接断开后，Requester 会在下次请求时自动重连
4. **资源清理**：使用完毕后务必调用 Close() 方法释放资源

## 示例代码

更多示例请参考：
- `zmq_example_reqrep.go` - 完整的使用示例
- `zmq_reqrep_test.go` - 单元测试示例

## License

MIT License