package zmq

import (
	"fmt"
	"sync"
	"time"

	"github.com/pebbe/zmq4"
)

// ZMQRequester ZeroMQ请求者实现
type ZMQRequester struct {
	config    *Config
	socket    *zmq4.Socket
	ctx       *Context
	logger    Logger
	mu        sync.RWMutex
	connected bool
}

// NewRequester 创建新的请求者
func NewRequester(config *Config) (Requester, error) {
	if err := ValidateConfig(config); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}

	ctx, err := GetGlobalContext()
	if err != nil {
		return nil, fmt.Errorf("get context failed: %w", err)
	}

	return &ZMQRequester{
		config: config,
		ctx:    ctx,
		logger: DefaultLogger,
	}, nil
}

// NewRequesterWithLogger 创建带自定义日志的请求者
func NewRequesterWithLogger(config *Config, logger Logger) (Requester, error) {
	req, err := NewRequester(config)
	if err != nil {
		return nil, err
	}

	if logger != nil {
		req.(*ZMQRequester).logger = logger
	}

	return req, nil
}

// connect 连接到服务端
func (r *ZMQRequester) connect() error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if r.connected {
		return nil
	}

	// 创建REQ套接字
	socket, err := zmq4.NewSocket(zmq4.REQ)
	if err != nil {
		return NewZMQError("create_socket", err)
	}

	// 设置套接字选项
	if err := socket.SetRcvtimeo(r.config.RecvTimeout); err != nil {
		socket.Close()
		return NewZMQError("set_rcvtimeo", err)
	}

	if err := socket.SetSndtimeo(r.config.SendTimeout); err != nil {
		socket.Close()
		return NewZMQError("set_sndtimeo", err)
	}

	if err := socket.SetSndhwm(r.config.SendHWM); err != nil {
		socket.Close()
		return NewZMQError("set_sndhwm", err)
	}

	if err := socket.SetRcvhwm(r.config.RecvHWM); err != nil {
		socket.Close()
		return NewZMQError("set_rcvhwm", err)
	}

	// 连接到服务端
	if err := socket.Connect(r.config.RequesterEndpoint); err != nil {
		socket.Close()
		return NewZMQError("connect", err)
	}

	r.socket = socket
	r.connected = true
	r.logger.Info("Requester connected", "endpoint", r.config.RequesterEndpoint)

	return nil
}

// Request 发送请求并等待响应
func (r *ZMQRequester) Request(data []byte) ([]byte, error) {
	// 确保已连接
	if err := r.connect(); err != nil {
		return nil, err
	}

	r.mu.RLock()
	defer r.mu.RUnlock()

	if !r.connected || r.socket == nil {
		return nil, NewZMQError("request", fmt.Errorf("not connected"))
	}

	// 记录发送的消息
	r.logger.Info("📤 ZMQ Requester sending message",
		"endpoint", r.config.RequesterEndpoint,
		"messageSize", len(data),
		"messageContent", string(data))

	// 发送请求
	if _, err := r.socket.SendBytes(data, 0); err != nil {
		r.logger.Error("❌ ZMQ Requester send failed",
			"endpoint", r.config.RequesterEndpoint,
			"error", err,
			"messageContent", string(data))
		return nil, NewZMQError("send_request", err)
	}

	r.logger.Info("✅ ZMQ Requester message sent successfully",
		"endpoint", r.config.RequesterEndpoint,
		"messageSize", len(data))

	// 接收响应
	reply, err := r.socket.RecvBytes(0)
	if err != nil {
		r.logger.Error("❌ ZMQ Requester receive failed",
			"endpoint", r.config.RequesterEndpoint,
			"error", err)
		return nil, NewZMQError("recv_reply", err)
	}

	// 记录接收的响应
	r.logger.Info("📥 ZMQ Requester received response",
		"endpoint", r.config.RequesterEndpoint,
		"responseSize", len(reply),
		"responseContent", string(reply))

	return reply, nil
}

// RequestString 发送字符串请求并等待响应
func (r *ZMQRequester) RequestString(message string) (string, error) {
	reply, err := r.Request([]byte(message))
	if err != nil {
		return "", err
	}
	return string(reply), nil
}

// RequestWithTimeout 发送请求并在指定时间内等待响应
func (r *ZMQRequester) RequestWithTimeout(data []byte, timeout time.Duration) ([]byte, error) {
	// 记录带超时的请求
	r.logger.Info("📤 ZMQ Requester sending message with timeout",
		"endpoint", r.config.RequesterEndpoint,
		"timeout", timeout,
		"messageSize", len(data),
		"messageContent", string(data))

	// 确保已连接
	if err := r.connect(); err != nil {
		r.logger.Error("❌ ZMQ Requester connect failed for timeout request",
			"endpoint", r.config.RequesterEndpoint,
			"error", err,
			"messageContent", string(data))
		return nil, err
	}

	r.mu.Lock()
	// 临时修改超时时间
	oldTimeout := r.config.RecvTimeout
	r.config.RecvTimeout = timeout

	if r.socket != nil {
		if err := r.socket.SetRcvtimeo(timeout); err != nil {
			r.mu.Unlock()
			r.logger.Error("❌ ZMQ Requester set timeout failed",
				"endpoint", r.config.RequesterEndpoint,
				"timeout", timeout,
				"error", err)
			return nil, NewZMQError("set_timeout", err)
		}
	}
	r.mu.Unlock()

	// 执行请求
	reply, err := r.Request(data)

	// 恢复原超时时间
	r.mu.Lock()
	r.config.RecvTimeout = oldTimeout
	if r.socket != nil {
		r.socket.SetRcvtimeo(oldTimeout)
	}
	r.mu.Unlock()

	if err != nil {
		r.logger.Error("❌ ZMQ Requester timeout request failed",
			"endpoint", r.config.RequesterEndpoint,
			"timeout", timeout,
			"error", err,
			"messageContent", string(data))
	} else {
		r.logger.Info("✅ ZMQ Requester timeout request successful",
			"endpoint", r.config.RequesterEndpoint,
			"timeout", timeout,
			"replySize", len(reply))
	}

	return reply, err
}

// Close 关闭请求者
func (r *ZMQRequester) Close() error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if !r.connected || r.socket == nil {
		return nil
	}

	if err := r.socket.Close(); err != nil {
		return NewZMQError("close_socket", err)
	}

	r.socket = nil
	r.connected = false
	r.logger.Info("Requester closed")

	return nil
}

// IsConnected 检查连接状态
func (r *ZMQRequester) IsConnected() bool {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.connected
}
