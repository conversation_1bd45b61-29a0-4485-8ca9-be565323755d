{"permissions": {"allow": ["Bash(ls:*)", "<PERSON><PERSON>(go:*)", "Bash(find:*)", "Bash(rm:*)", "Bash(npm run build:*)", "Bash(grep:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(sed:*)", "Bash(git add:*)", "Bash(del test_scenario_verification.go)", "<PERSON><PERSON>(curl:*)", "Bash(./agv_nav.exe)", "Bash(npm run dev:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(taskkill:*)", "Bash(timeout 8s go run ./cmd/agv_nav/)", "Bash(./agv_nav_new.exe)", "Bash(sqlite3:*)", "Bash(./agv_nav)"], "deny": []}}