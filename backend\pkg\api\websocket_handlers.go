package api

import (
	"encoding/json"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/user/agv_nav/pkg/logger"
)

// WebSocketMessage WebSocket消息结构
type WebSocketMessage struct {
	Type string      `json:"type"`
	Data interface{} `json:"data,omitempty"`
}

// handleWebSocket 处理WebSocket连接
func (s *Server) handleWebSocket(w http.ResponseWriter, r *http.Request) {
	apiLogger := logger.GetModuleLogger("api")
	clientIP := r.RemoteAddr
	apiLogger.Info("WebSocket连接请求", "clientIP", clientIP, "userAgent", r.UserAgent())

	// 升级HTTP连接为WebSocket
	conn, err := s.upgrader.Upgrade(w, r, nil)
	if err != nil {
		apiLogger.Error("WebSocket升级失败", "clientIP", clientIP, "error", err)
		return
	}

	// 添加客户端到连接列表
	s.clientsMutex.Lock()
	s.clients[conn] = true
	s.clientLocks[conn] = &sync.Mutex{} // 为每个连接创建写入锁
	clientCount := len(s.clients)
	s.clientsMutex.Unlock()

	apiLogger.Info("新WebSocket客户端连接成功", "clientIP", clientIP, "totalClients", clientCount)

	// 发送当前状态给新连接的客户端
	go func() {
		// 发送AGV状态
		if s.agvController.GetStatus() != nil {
			s.BroadcastStatus()
		}
		// 发送任务状态
		s.BroadcastTaskStatus()
		// 发送流程状态
		s.BroadcastFlowStatus()
	}()

	// 处理客户端消息
	defer func() {
		// 连接关闭时清理
		s.clientsMutex.Lock()
		delete(s.clients, conn)
		delete(s.clientLocks, conn) // 清理客户端锁
		remainingClients := len(s.clients)
		s.clientsMutex.Unlock()
		conn.Close()
		apiLogger.Info("WebSocket客户端断开连接", "clientIP", conn.RemoteAddr().String(), "remainingClients", remainingClients)
	}()

	// 读取消息循环
	for {
		messageType, message, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket读取错误: %v", err)
			}
			break
		}

		// 处理客户端发送的消息
		log.Printf("收到WebSocket消息 [%d]: %s", messageType, string(message))

		// 解析消息
		var wsMsg WebSocketMessage
		if err := json.Unmarshal(message, &wsMsg); err != nil {
			log.Printf("解析WebSocket消息失败: %v", err)
			continue
		}

		// 处理不同类型的消息
		switch wsMsg.Type {
		case "request_status":
			// 请求AGV状态
			s.BroadcastStatus()
		case "request_task_status":
			// 请求任务状态
			s.BroadcastTaskStatus()
		case "subscribe_logs":
			// 订阅实时日志
			s.handleLogSubscription(conn)
		case "unsubscribe_logs":
			// 取消日志订阅
			s.handleLogUnsubscription(conn)
		default:
			log.Printf("未知的WebSocket消息类型: %s", wsMsg.Type)
		}
	}
}

// handleLogSubscription 处理日志订阅
func (s *Server) handleLogSubscription(conn *websocket.Conn) {
	// 生成订阅者ID
	subscriberID := conn.RemoteAddr().String() + "_" + time.Now().Format("20060102150405")

	// 订阅实时日志
	subscriber := logger.SubscribeRealTimeLogs(subscriberID)

	// 启动日志推送协程
	go func() {
		defer func() {
			// 清理订阅
			logger.UnsubscribeRealTimeLogs(subscriberID)
		}()

		for {
			select {
			case logEntry, ok := <-subscriber.Channel:
				if !ok {
					// 通道已关闭
					return
				}

				// 发送日志条目到客户端
				msg := WebSocketMessage{
					Type: "log_entry",
					Data: logEntry,
				}

				if err := conn.WriteJSON(msg); err != nil {
					log.Printf("发送日志条目失败: %v", err)
					return
				}
			}
		}
	}()

	// 发送订阅确认消息
	confirmMsg := WebSocketMessage{
		Type: "log_subscription_confirmed",
		Data: map[string]string{
			"subscriberID": subscriberID,
			"message":      "日志订阅已激活",
		},
	}
	conn.WriteJSON(confirmMsg)

	log.Printf("客户端 %s 已订阅实时日志", conn.RemoteAddr())
}

// handleLogUnsubscription 处理取消日志订阅
func (s *Server) handleLogUnsubscription(conn *websocket.Conn) {
	// 根据连接地址取消订阅
	subscriberID := conn.RemoteAddr().String() + "_*" // 模糊匹配
	logger.UnsubscribeRealTimeLogs(subscriberID)

	// 发送取消订阅确认消息
	confirmMsg := WebSocketMessage{
		Type: "log_unsubscription_confirmed",
		Data: map[string]string{
			"message": "日志订阅已取消",
		},
	}
	conn.WriteJSON(confirmMsg)

	log.Printf("客户端 %s 已取消日志订阅", conn.RemoteAddr())
}
