package logger

import (
	"encoding/json"
	"io"
	"regexp"
	"strings"
	"sync"
	"time"
)

// LogSubscriber 日志订阅者
type LogSubscriber struct {
	ID      string
	Channel chan LogEntry
	Active  bool
	mutex   sync.RWMutex
}

// NewLogSubscriber 创建新的日志订阅者
func NewLogSubscriber(id string) *LogSubscriber {
	return &LogSubscriber{
		ID:      id,
		Channel: make(chan LogEntry, 100), // 缓冲100条日志
		Active:  true,
	}
}

// Send 发送日志条目到订阅者
func (ls *LogSubscriber) Send(entry LogEntry) {
	ls.mutex.RLock()
	defer ls.mutex.RUnlock()

	if !ls.Active {
		return
	}

	select {
	case ls.Channel <- entry:
		// 成功发送
	default:
		// 通道已满，丢弃最旧的日志
		select {
		case <-ls.Channel:
			ls.Channel <- entry
		default:
			// 如果仍然无法发送，则放弃
		}
	}
}

// Close 关闭订阅者
func (ls *LogSubscriber) Close() {
	ls.mutex.Lock()
	defer ls.mutex.Unlock()

	if ls.Active {
		ls.Active = false
		close(ls.Channel)
	}
}

// RealtimeLogBroadcaster 实时日志广播器
type RealtimeLogBroadcaster struct {
	subscribers map[string]*LogSubscriber
	mutex       sync.RWMutex
}

// NewRealtimeLogBroadcaster 创建实时日志广播器
func NewRealtimeLogBroadcaster() *RealtimeLogBroadcaster {
	return &RealtimeLogBroadcaster{
		subscribers: make(map[string]*LogSubscriber),
	}
}

// Subscribe 订阅实时日志
func (rlb *RealtimeLogBroadcaster) Subscribe(id string) *LogSubscriber {
	rlb.mutex.Lock()
	defer rlb.mutex.Unlock()

	// 如果已存在订阅者，先关闭旧的
	if oldSubscriber, exists := rlb.subscribers[id]; exists {
		oldSubscriber.Close()
	}

	subscriber := NewLogSubscriber(id)
	rlb.subscribers[id] = subscriber

	return subscriber
}

// Unsubscribe 取消订阅
func (rlb *RealtimeLogBroadcaster) Unsubscribe(id string) {
	rlb.mutex.Lock()
	defer rlb.mutex.Unlock()

	if subscriber, exists := rlb.subscribers[id]; exists {
		subscriber.Close()
		delete(rlb.subscribers, id)
	}
}

// Broadcast 广播日志条目到所有订阅者
func (rlb *RealtimeLogBroadcaster) Broadcast(entry LogEntry) {
	rlb.mutex.RLock()
	defer rlb.mutex.RUnlock()

	for id, subscriber := range rlb.subscribers {
		if !subscriber.Active {
			// 清理失效的订阅者
			delete(rlb.subscribers, id)
			continue
		}

		go subscriber.Send(entry)
	}
}

// GetSubscriberCount 获取订阅者数量
func (rlb *RealtimeLogBroadcaster) GetSubscriberCount() int {
	rlb.mutex.RLock()
	defer rlb.mutex.RUnlock()

	return len(rlb.subscribers)
}

// CleanupInactiveSubscribers 清理失效的订阅者
func (rlb *RealtimeLogBroadcaster) CleanupInactiveSubscribers() {
	rlb.mutex.Lock()
	defer rlb.mutex.Unlock()

	for id, subscriber := range rlb.subscribers {
		if !subscriber.Active {
			delete(rlb.subscribers, id)
		}
	}
}

// 全局实时日志广播器
var globalRealtimeBroadcaster *RealtimeLogBroadcaster

// InitRealtimeLogging 初始化实时日志系统
func InitRealtimeLogging() {
	if globalRealtimeBroadcaster == nil {
		globalRealtimeBroadcaster = NewRealtimeLogBroadcaster()

		// 启动清理协程
		go func() {
			ticker := time.NewTicker(5 * time.Minute)
			defer ticker.Stop()

			for range ticker.C {
				globalRealtimeBroadcaster.CleanupInactiveSubscribers()
			}
		}()
	}
}

// GetRealtimeBroadcaster 获取全局实时日志广播器
func GetRealtimeBroadcaster() *RealtimeLogBroadcaster {
	if globalRealtimeBroadcaster == nil {
		InitRealtimeLogging()
	}
	return globalRealtimeBroadcaster
}

// SubscribeRealTimeLogs 订阅实时日志
func SubscribeRealTimeLogs(subscriberID string) *LogSubscriber {
	broadcaster := GetRealtimeBroadcaster()
	return broadcaster.Subscribe(subscriberID)
}

// UnsubscribeRealTimeLogs 取消实时日志订阅
func UnsubscribeRealTimeLogs(subscriberID string) {
	broadcaster := GetRealtimeBroadcaster()
	broadcaster.Unsubscribe(subscriberID)
}

// BroadcastLogEntry 广播日志条目
func BroadcastLogEntry(entry LogEntry) {
	broadcaster := GetRealtimeBroadcaster()
	broadcaster.Broadcast(entry)
}

// RealtimeLogWriter 实时日志写入器，用于拦截日志输出
type RealtimeLogWriter struct {
	originalWriter io.Writer
	broadcaster    *RealtimeLogBroadcaster
	parser         func([]byte) *LogEntry
}

// NewRealtimeLogWriter 创建实时日志写入器
func NewRealtimeLogWriter(originalWriter io.Writer, parser func([]byte) *LogEntry) *RealtimeLogWriter {
	return &RealtimeLogWriter{
		originalWriter: originalWriter,
		broadcaster:    GetRealtimeBroadcaster(),
		parser:         parser,
	}
}

// Write 实现io.Writer接口
func (rlw *RealtimeLogWriter) Write(data []byte) (n int, err error) {
	// 先写入原始输出
	n, err = rlw.originalWriter.Write(data)

	// 解析并广播日志条目
	if rlw.parser != nil {
		if entry := rlw.parser(data); entry != nil {
			rlw.broadcaster.Broadcast(*entry)
		}
	}

	return n, err
}

// ParseLogLine 解析日志行为LogEntry
func ParseLogLine(data []byte) *LogEntry {
	line := string(data)
	line = strings.TrimSpace(line)

	if line == "" {
		return nil
	}

	// 检测是否为JSON格式
	if strings.HasPrefix(line, "{") && strings.HasSuffix(line, "}") {
		return parseJSONLogLine(line)
	}

	// 解析文本格式
	return parseTextFormatLogLine(line)
}

// parseJSONLogLine 解析JSON格式日志行
func parseJSONLogLine(line string) *LogEntry {
	var jsonLog struct {
		Timestamp string `json:"timestamp"`
		Level     string `json:"level"`
		Module    string `json:"module"`
		Caller    string `json:"caller"`
		Message   string `json:"message"`
	}

	if json.Unmarshal([]byte(line), &jsonLog) != nil {
		return nil
	}

	timestamp, _ := time.Parse("2006-01-02 15:04:05.000", jsonLog.Timestamp)

	return &LogEntry{
		Timestamp: timestamp,
		Level:     jsonLog.Level,
		Module:    jsonLog.Module,
		Caller:    jsonLog.Caller,
		Message:   jsonLog.Message,
		Raw:       line,
	}
}

// parseTextFormatLogLine 解析文本格式日志行
func parseTextFormatLogLine(line string) *LogEntry {
	// 正则表达式匹配格式: [2025-06-25 15:31:44.448] [INFO] [module] caller.go:123 - message
	re := regexp.MustCompile(`^\[([^\]]+)\]\s*\[([^\]]+)\]\s*\[([^\]]+)\]\s*([^\s]+)\s*-\s*(.*)`)
	matches := re.FindStringSubmatch(line)

	if len(matches) != 6 {
		// 如果格式不匹配，返回简单解析
		return &LogEntry{
			Timestamp: time.Now(),
			Level:     "INFO",
			Module:    "unknown",
			Message:   line,
			Raw:       line,
		}
	}

	timestamp, _ := time.Parse("2006-01-02 15:04:05.000", matches[1])

	return &LogEntry{
		Timestamp: timestamp,
		Level:     matches[2],
		Module:    matches[3],
		Caller:    matches[4],
		Message:   matches[5],
		Raw:       line,
	}
}
