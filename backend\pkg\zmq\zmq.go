package zmq

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/pebbe/zmq4"
)

// Config ZeroMQ配置
type Config struct {
	// 请求者配置（客户端）
	RequesterEndpoint string `json:"requester_endpoint"` // 如: "tcp://localhost:5556"

	// 响应者配置（服务端）
	ResponderEndpoint string `json:"responder_endpoint"` // 如: "tcp://*:5556"

	// 连接参数
	ConnectTimeout time.Duration `json:"connect_timeout"` // 连接超时
	SendTimeout    time.Duration `json:"send_timeout"`    // 发送超时
	RecvTimeout    time.Duration `json:"recv_timeout"`    // 接收超时

	// 高水位标记（队列大小限制）
	SendHWM int `json:"send_hwm"` // 发送队列高水位
	RecvHWM int `json:"recv_hwm"` // 接收队列高水位
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		RequesterEndpoint: "tcp://************:5556",
		ResponderEndpoint: "tcp://*:5557",
		ConnectTimeout:    5 * time.Second,
		SendTimeout:       1 * time.Second,
		RecvTimeout:       100 * time.Millisecond,
		SendHWM:           1000,
		RecvHWM:           1000,
	}
}

// Requester 请求者接口（客户端）
type Requester interface {
	// Request 发送请求并等待响应
	Request(data []byte) ([]byte, error)

	// RequestString 发送字符串请求并等待响应
	RequestString(message string) (string, error)

	// RequestWithTimeout 发送请求并在指定时间内等待响应
	RequestWithTimeout(data []byte, timeout time.Duration) ([]byte, error)

	// Close 关闭请求者
	Close() error

	// IsConnected 检查连接状态
	IsConnected() bool
}

// Responder 响应者接口（服务端）
type Responder interface {
	// SetHandler 设置请求处理函数
	SetHandler(handler RequestHandler) error

	// Start 开始监听请求（阻塞）
	Start(ctx context.Context) error

	// Close 关闭响应者
	Close() error

	// IsConnected 检查连接状态
	IsConnected() bool
}

// RequestHandler 请求处理函数类型
type RequestHandler func(request []byte) ([]byte, error)

// ZMQError ZeroMQ错误类型
type ZMQError struct {
	Op  string // 操作名称
	Err error  // 原始错误
}

func (e *ZMQError) Error() string {
	return fmt.Sprintf("zmq %s error: %v", e.Op, e.Err)
}

// NewZMQError 创建ZeroMQ错误
func NewZMQError(op string, err error) *ZMQError {
	return &ZMQError{Op: op, Err: err}
}

// Logger ZeroMQ模块日志接口
type Logger interface {
	Debug(msg string, fields ...interface{})
	Info(msg string, fields ...interface{})
	Warn(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
}

// defaultLogger 默认日志实现
type defaultLogger struct{}

func (l *defaultLogger) Debug(msg string, fields ...interface{}) {
	log.Printf("[DEBUG] ZMQ: %s %v", msg, fields)
}

func (l *defaultLogger) Info(msg string, fields ...interface{}) {
	log.Printf("[INFO] ZMQ: %s %v", msg, fields)
}

func (l *defaultLogger) Warn(msg string, fields ...interface{}) {
	log.Printf("[WARN] ZMQ: %s %v", msg, fields)
}

func (l *defaultLogger) Error(msg string, fields ...interface{}) {
	log.Printf("[ERROR] ZMQ: %s %v", msg, fields)
}

// DefaultLogger 默认日志实例
var DefaultLogger Logger = &defaultLogger{}

// Context ZeroMQ上下文管理
type Context struct {
	ctx    *zmq4.Context
	mu     sync.RWMutex
	closed bool
}

// NewContext 创建新的ZeroMQ上下文
func NewContext() (*Context, error) {
	ctx, err := zmq4.NewContext()
	if err != nil {
		return nil, NewZMQError("create_context", err)
	}

	return &Context{
		ctx: ctx,
	}, nil
}

// GetContext 获取原始ZeroMQ上下文
func (c *Context) GetContext() *zmq4.Context {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.ctx
}

// Close 关闭上下文
func (c *Context) Close() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if c.closed {
		return nil
	}

	err := c.ctx.Term()
	if err != nil {
		return NewZMQError("close_context", err)
	}

	c.closed = true
	return nil
}

// IsClosed 检查上下文是否已关闭
func (c *Context) IsClosed() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.closed
}

// GlobalContext 全局ZeroMQ上下文
var (
	globalContext *Context
	globalOnce    sync.Once
)

// GetGlobalContext 获取全局ZeroMQ上下文
func GetGlobalContext() (*Context, error) {
	var err error
	globalOnce.Do(func() {
		globalContext, err = NewContext()
	})
	return globalContext, err
}

// CloseGlobalContext 关闭全局上下文
func CloseGlobalContext() error {
	if globalContext != nil {
		return globalContext.Close()
	}
	return nil
}

// ValidateConfig 验证配置
func ValidateConfig(config *Config) error {
	if config == nil {
		return fmt.Errorf("config cannot be nil")
	}

	if config.RequesterEndpoint == "" && config.ResponderEndpoint == "" {
		return fmt.Errorf("at least one endpoint must be specified")
	}

	if config.ConnectTimeout <= 0 {
		return fmt.Errorf("connect_timeout must be positive")
	}

	if config.SendTimeout <= 0 {
		return fmt.Errorf("send_timeout must be positive")
	}

	if config.RecvTimeout <= 0 {
		return fmt.Errorf("recv_timeout must be positive")
	}

	return nil
}
