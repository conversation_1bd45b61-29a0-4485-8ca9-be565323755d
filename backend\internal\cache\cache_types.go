package cache

import (
	"time"
)

// MESBreakageDTO 断头数据（复用现有结构）
type MESBreakageDTO struct {
	MachineID string `json:"machineId"`
	Side      int    `json:"side"`
	SpindleNo int    `json:"spindleNo"`
	StartTime int64  `json:"startTime"`
	EndTime   *int64 `json:"endTime"`
}

// CacheEntry 缓存条目
type CacheEntry struct {
	MachineID  string           `json:"machine_id"`  // 细纱机ID
	Data       []MESBreakageDTO `json:"data"`        // 断头数据
	LastUpdate time.Time        `json:"last_update"` // 最后更新时间
	IsValid    bool             `json:"is_valid"`    // 数据是否有效
	ErrorCount int              `json:"error_count"` // 错误次数
	LastError  string           `json:"last_error"`  // 最后错误信息
}

// CacheStatus 缓存状态
type CacheStatus struct {
	IsRunning       bool                   `json:"is_running"`       // 是否运行中
	EnabledMachines []string               `json:"enabled_machines"` // 启用的细纱机
	Interval        time.Duration          `json:"interval"`         // 更新间隔
	MachineStatus   map[string]*CacheEntry `json:"machine_status"`   // 各机器状态
	TotalHits       int                    `json:"total_hits"`       // 缓存命中数
	TotalMisses     int                    `json:"total_misses"`     // 缓存未命中数
}

// CacheRequest 缓存请求
type CacheRequest struct {
	Action   string   `json:"action"`   // start, stop, clear
	Machines []string `json:"machines"` // 要缓存的细纱机
	Interval int      `json:"interval"` // 间隔时间（分钟）
}

// CacheResponse 缓存响应
type CacheResponse struct {
	Success bool        `json:"success"` // 是否成功
	Message string      `json:"message"` // 响应消息
	Data    interface{} `json:"data"`    // 响应数据
}

// IsExpired 检查缓存是否过期（30分钟）
func (entry *CacheEntry) IsExpired() bool {
	return time.Since(entry.LastUpdate) > 30*time.Minute
}

// IsHealthy 检查是否健康（连续错误少于5次）
func (entry *CacheEntry) IsHealthy() bool {
	return entry.ErrorCount < 5
}
