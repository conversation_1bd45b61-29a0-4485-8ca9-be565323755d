2025/07/28 17:24:43 正在初始化AGV测试控制器...
2025/07/28 17:24:43 找到数据库文件: data/agv_data.db
2025/07/28 17:24:43 AGV测试控制器初始化成功
2025/07/28 17:24:43 AGV测试API处理器初始化完成
2025/07/28 17:24:43 AGV测试API初始化成功（复用主程序AGV控制器）
2025/07/28 17:24:43 找到配置文件: data\task_config.json
2025/07/28 17:24:43 加载配置文件: data\task_config.json
2025/07/28 17:24:43 任务配置初始化完成
2025/07/28 17:24:43 找到数据库文件: data/agv_data.db
2025/07/28 17:24:43 找到数据库文件: data/agv_data.db
2025/07/28 17:24:43 加载缓存配置文件: data\task_config.json
2025/07/28 17:24:43 缓存配置初始化完成
2025/07/28 17:24:43 全局缓存管理器初始化成功
[INFO] AutoWatch: Initializing auto watch components []
2025/07/28 17:24:43 找到数据库文件: data/agv_data.db
2025/07/28 17:24:43 找到数据库文件: data/agv_data.db
2025/07/28 17:24:43 找到数据库文件: data/agv_data.db
2025/07/28 17:24:43 找到数据库文件: data/agv_data.db
2025/07/28 17:24:43 找到数据库文件: data/agv_data.db
2025/07/28 17:24:43 找到数据库文件: data/agv_data.db
[INFO] AutoWatch: Auto watch navigation handler validation passed [navigationTimeout 20m0s checkInterval 2s agvConnected false dbAvailable true]
[INFO] AutoWatch: Auto watch PLC handler validation passed [plcConnected false workTimeout 10m0s checkInterval 2s dbAvailable true]
[INFO] AutoWatch: Auto watch work executor validation passed [spindleServiceAvailable true plcHandlerAvailable true navHandlerAvailable true taskManagerAvailable true completionHandlerAvailable false dbAvailable true]
[INFO] AutoWatch: Auto watch lane processor validation passed [workExecutorAvailable true navHandlerAvailable true plcHandlerAvailable true spindleProcAvailable true dbAvailable true]
panic: runtime error: invalid memory address or nil pointer dereference
[signal 0xc0000005 code=0x0 addr=0x0 pc=0x7ff6e022daf2]

goroutine 1 [running]:
github.com/user/agv_nav/internal/auto_watch.(*AutoWatchTaskExecutor).ValidateAutoWatchTaskExecutor(0xc0000923c0?)
	D:/code/try/agv_nav_new/agv_nav_new/backend/internal/auto_watch/exec_auto_task_executor.go:552 +0x12
github.com/user/agv_nav/internal/auto_watch.(*AutoWatchConfigManager).validateComponents(0xc000092200?, 0xc0001ab1f0)
	D:/code/try/agv_nav_new/agv_nav_new/backend/internal/auto_watch/config_auto_config_manager.go:412 +0x1a7
github.com/user/agv_nav/internal/auto_watch.(*AutoWatchConfigManager).InitializeComponents(0xc0001ba228, 0xc0001807c0, 0xc0001c8000)
	D:/code/try/agv_nav_new/agv_nav_new/backend/internal/auto_watch/config_auto_config_manager.go:344 +0x2fd
github.com/user/agv_nav/internal/auto_watch.NewAutoWatchService(0xc0001807c0, 0xc0001c8000)
	D:/code/try/agv_nav_new/agv_nav_new/backend/internal/auto_watch/service.go:265 +0x4bf
github.com/user/agv_nav/pkg/api.NewServer()
	D:/code/try/agv_nav_new/agv_nav_new/backend/pkg/api/server.go:97 +0x6c9
main.main()
	D:/code/try/agv_nav_new/agv_nav_new/backend/cmd/agv_nav/main.go:21 +0x1a5
exit status 2
