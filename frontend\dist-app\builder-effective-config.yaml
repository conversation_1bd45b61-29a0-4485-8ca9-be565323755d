directories:
  output: dist-app
  buildResources: assets
appId: com.agv.controller
productName: AGV Controller
copyright: Copyright © 2024 AGV Navigation System
icon: assets/icon.ico
files:
  - filter:
      - dist/**/*
      - dist-electron/**/*
      - node_modules/**/*
      - assets/**/*
      - '!node_modules/**/*.{o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}'
      - '!node_modules/.bin'
      - '!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}'
      - '!.editorconfig'
      - '!**/._*'
      - '!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}'
      - '!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}'
      - '!**/{appveyor.yml,.travis.yml,circle.yml}'
      - '!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}'
extraResources:
  - from: ../backend/agv_nav.exe
    to: agv-backend.exe
    filter:
      - '**/*'
  - from: ../backend/config
    to: backend/config
    filter:
      - '**/*'
  - from: ../backend/data
    to: backend/data
    filter:
      - '**/*'
win:
  target:
    - target: nsis
      arch:
        - x64
  icon: assets/icon.ico
  verifyUpdateCodeSignature: false
  forceCodeSigning: false
  signAndEditExecutable: false
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  allowElevation: true
  installerIcon: assets/icon.ico
  uninstallerIcon: assets/icon.ico
  installerHeaderIcon: assets/icon.ico
  createDesktopShortcut: always
  createStartMenuShortcut: true
  shortcutName: AGV Controller
  license: LICENSE
  displayLanguageSelector: true
  installerLanguages:
    - zh_CN
    - en_US
  runAfterFinish: true
  menuCategory: Industrial Control
  artifactName: AGV-Controller-Setup-${version}.${ext}
  deleteAppDataOnUninstall: false
compression: normal
npmRebuild: false
forceCodeSigning: false
electronVersion: 36.5.0
