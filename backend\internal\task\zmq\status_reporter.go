package zmq

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"sync"
	"time"

	"github.com/user/agv_nav/internal/database"
	"github.com/user/agv_nav/internal/zmq"
	"github.com/user/agv_nav/pkg/agv"
	zmqpkg "github.com/user/agv_nav/pkg/zmq"
)

// NavigationPoint 导航点坐标信息
type NavigationPoint struct {
	ID    int     `json:"id"`
	X     float32 `json:"x"`
	Y     float32 `json:"y"`
	Angle float32 `json:"angle"`
}

// StatusReporter 单机版状态上报器
type StatusReporter struct {
	// 核心组件
	requester       zmqpkg.Requester        // 复用现有ZMQ连接
	agvController   *agv.Controller         // AGV控制器
	workModeManager WorkModeManager         // 工作模式管理器
	navigationDB    *database.NavigationDB  // 数据库访问
	config          *zmq.SystemConfig       // 系统配置

	// 导航点缓存
	allNavigationPoints []NavigationPoint

	// 任务完成状态跟踪
	taskCompletionTime *time.Time
	mu                 sync.RWMutex

	// 定时器和控制
	ticker  *time.Ticker
	stopCh  chan struct{}
	ctx     context.Context
	cancel  context.CancelFunc

	// 配置参数
	reportInterval     time.Duration // 1分钟
	requestTimeout     time.Duration // 5秒
	distanceThreshold  float32       // 0.5米
	completionDuration time.Duration // 3分钟
}

// NewStatusReporter 创建状态上报器
func NewStatusReporter(
	requester zmqpkg.Requester,
	agvController *agv.Controller,
	workModeManager WorkModeManager,
	navigationDB *database.NavigationDB,
	config *zmq.SystemConfig,
) *StatusReporter {
	ctx, cancel := context.WithCancel(context.Background())

	return &StatusReporter{
		requester:          requester,
		agvController:      agvController,
		workModeManager:    workModeManager,
		navigationDB:       navigationDB,
		config:             config,
		allNavigationPoints: make([]NavigationPoint, 0),
		taskCompletionTime: nil,
		stopCh:             make(chan struct{}),
		ctx:                ctx,
		cancel:             cancel,
		reportInterval:     1 * time.Minute,  // 每分钟上报一次
		requestTimeout:     5 * time.Second,  // 5秒超时
		distanceThreshold:  0.5,              // 0.5米距离阈值
		completionDuration: 3 * time.Minute,  // 任务完成后3分钟窗口
	}
}

// Start 启动状态上报器
func (r *StatusReporter) Start() error {
	log.Printf("🚀 启动状态上报器，上报间隔: %v", r.reportInterval)

	// 加载并缓存所有导航点坐标
	if err := r.loadAllNavigationPoints(); err != nil {
		log.Printf("⚠️  加载导航点失败，但继续启动: %v", err)
	}

	// 立即发送一次状态报告
	if err := r.sendStatusReport(); err != nil {
		log.Printf("⚠️  首次状态上报失败: %v", err)
	}

	// 启动定时上报
	r.ticker = time.NewTicker(r.reportInterval)
	go r.reportLoop()

	return nil
}

// Stop 停止状态上报器
func (r *StatusReporter) Stop() {
	log.Printf("🛑 停止状态上报器")
	
	r.cancel()
	close(r.stopCh)
	
	if r.ticker != nil {
		r.ticker.Stop()
	}
}

// OnTaskCompleted 任务完成时调用（发送130指令时）
func (r *StatusReporter) OnTaskCompleted() {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	now := time.Now()
	r.taskCompletionTime = &now
	log.Printf("📝 任务完成状态设置，3分钟内视为权限交接点状态")
}

// OnNewTaskStarted 开始新任务时调用
func (r *StatusReporter) OnNewTaskStarted() {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	r.taskCompletionTime = nil
	log.Printf("🆕 新任务开始，清除任务完成状态")
}

// reportLoop 状态上报循环
func (r *StatusReporter) reportLoop() {
	log.Printf("📊 状态上报循环开始")

	for {
		select {
		case <-r.ctx.Done():
			log.Printf("📊 状态上报循环结束 (context)")
			return
		case <-r.stopCh:
			log.Printf("📊 状态上报循环结束 (stop)")
			return
		case <-r.ticker.C:
			if err := r.sendStatusReport(); err != nil {
				log.Printf("❌ 状态上报失败: %v", err)
			}
		}
	}
}

// sendStatusReport 发送状态报告
func (r *StatusReporter) sendStatusReport() error {
	// 收集状态信息
	statusContent, message, err := r.collectStatusInfo()
	if err != nil {
		return fmt.Errorf("收集状态信息失败: %w", err)
	}

	log.Printf("📤 发送状态上报 - 机器人: %s, 任务: %s, 任务状态: %d, 机器人状态: %d, 电量: %.1f%%, 位置: %s",
		statusContent.RobotNo,
		statusContent.TaskId,
		statusContent.RobotTaskState,
		statusContent.RobotState,
		statusContent.BatteryPower,
		statusContent.CurrentPosition)

	// 创建状态上报消息
	msg := &zmq.Message{
		Instruction: zmq.InstructionStatusReport, // 指令100
		TimeStamp:   time.Now(),
		Code:        true,
		Message:     message,
		Content:     statusContent,
	}

	// 序列化消息
	data, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("序列化状态上报消息失败: %w", err)
	}

	// 发送并等待回复
	replyData, err := r.requester.RequestWithTimeout(data, r.requestTimeout)
	if err != nil {
		return fmt.Errorf("发送状态上报失败: %w", err)
	}

	// 解析回复
	var reply zmq.Message
	if err := json.Unmarshal(replyData, &reply); err != nil {
		return fmt.Errorf("解析回复失败: %w", err)
	}

	// 验证回复
	if reply.Instruction != zmq.InstructionReply {
		return fmt.Errorf("回复指令错误: 期望 %d, 收到 %d", zmq.InstructionReply, reply.Instruction)
	}

	if !reply.Code {
		return fmt.Errorf("回复代码为false，表示错误: %s", reply.Message)
	}

	log.Printf("✅ 状态上报成功")
	return nil
}

// collectStatusInfo 收集状态信息
func (r *StatusReporter) collectStatusInfo() (*zmq.StatusReportContent, string, error) {
	// 获取AGV状态
	agvStatus := r.agvController.GetStatus()
	if agvStatus == nil {
		return nil, "", fmt.Errorf("无法获取AGV状态")
	}

	// 获取TaskId
	taskId := r.getCurrentTaskId()

	// 确定任务状态
	robotTaskState := r.determineRobotTaskState(taskId)

	// 确定机器人状态
	robotState := r.determineRobotState(agvStatus)

	// 获取电量
	batteryPower := r.getBatteryPower(agvStatus)

	// 确定当前位置
	currentPosition := r.determineCurrentPosition()

	// 生成状态消息
	message := r.generateStatusMessage(robotState, robotTaskState, currentPosition)

	return &zmq.StatusReportContent{
		RobotNo:         r.config.RobotNo,
		TaskId:          taskId,
		RobotTaskState:  robotTaskState,
		RobotState:      robotState,
		BatteryPower:    batteryPower,
		CurrentPosition: currentPosition,
		Remark:          message,
		AllowList:       []string{}, // 暂时为空
	}, message, nil
}

// getCurrentTaskId 获取当前任务ID
func (r *StatusReporter) getCurrentTaskId() string {
	if r.workModeManager == nil {
		return ""
	}
	return r.workModeManager.GetCurrentTaskId()
}

// determineRobotTaskState 确定机器人任务状态
func (r *StatusReporter) determineRobotTaskState(taskId string) int {
	if taskId != "" {
		return zmq.RobotTaskStateInProgress // 20 进行中
	}
	return zmq.RobotTaskStateNotStarted // 10 未开始
}

// determineRobotState 确定机器人状态
func (r *StatusReporter) determineRobotState(agvStatus *agv.Status) int {
	// 检查是否有故障状态
	if agvStatus.AGVState == 0x06 || agvStatus.AGVState == 0x03 { // 导航失败或未初始化
		return zmq.RobotStateAbnormal // -99
	}

	// 检查电量是否低于15%
	if agvStatus.BatteryPercent < 0.15 {
		return zmq.RobotStateLowBattery // -1
	}

	return zmq.RobotStateNormal // 1
}

// getBatteryPower 获取电量百分比
func (r *StatusReporter) getBatteryPower(agvStatus *agv.Status) float64 {
	batteryPower := float64(agvStatus.BatteryPercent * 100)

	// 确保电量值在0-100范围内
	if batteryPower < 0 {
		batteryPower = 0
	} else if batteryPower > 100 {
		batteryPower = 100
	}

	return batteryPower
}

// determineCurrentPosition 确定当前位置
func (r *StatusReporter) determineCurrentPosition() string {
	agvStatus := r.agvController.GetStatus()
	if agvStatus == nil {
		return zmq.PositionParkingStr // 默认停车点
	}

	navStatus := r.agvController.GetNavigationStatus()
	if navStatus == nil {
		return zmq.PositionParkingStr // 默认停车点
	}

	// 1. 主干道：自动模式 + 正在导航
	if agvStatus.WorkMode == 0x03 && navStatus.IsNavigating() {
		return zmq.PositionMainRoadStr // "10"
	}

	// 2. 停车点：自动模式 + LastPointID == 80 + 不在导航
	if agvStatus.WorkMode == 0x03 &&
		agvStatus.LastPointID == 80 &&
		!navStatus.IsNavigating() {
		return zmq.PositionParkingStr // "0"
	}

	// 3. 权限交接点：在任意导航点0.5米范围内 + 任务完成后3分钟内
	if r.isNearAnyNavigationPoint(agvStatus.PosX, agvStatus.PosY) &&
		r.isWithinCompletionWindow() {
		return zmq.PositionSwitchStr // "2"
	}

	// 4. 默认停车点
	return zmq.PositionParkingStr // "0"
}

// isNearAnyNavigationPoint 检查是否在任意导航点附近
func (r *StatusReporter) isNearAnyNavigationPoint(agvX, agvY float32) bool {
	for _, point := range r.allNavigationPoints {
		dx := agvX - point.X
		dy := agvY - point.Y
		distance := math.Sqrt(float64(dx*dx + dy*dy))

		if distance <= float64(r.distanceThreshold) {
			return true
		}
	}
	return false
}

// isWithinCompletionWindow 检查是否在任务完成后的3分钟内
func (r *StatusReporter) isWithinCompletionWindow() bool {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if r.taskCompletionTime == nil {
		return false
	}

	elapsed := time.Since(*r.taskCompletionTime)
	return elapsed <= r.completionDuration // 3分钟
}

// loadAllNavigationPoints 加载并缓存所有导航点坐标
func (r *StatusReporter) loadAllNavigationPoints() error {
	// 获取所有导航信息
	allNavInfo, err := r.navigationDB.GetAllNavigationInfo()
	if err != nil {
		return fmt.Errorf("获取导航信息失败: %w", err)
	}

	r.allNavigationPoints = make([]NavigationPoint, 0, len(allNavInfo))

	// 为每个导航点获取坐标
	for _, info := range allNavInfo {
		x, y, angle, err := r.navigationDB.GetNavigationPosition(info.NavigationID)
		if err != nil {
			log.Printf("⚠️  跳过导航点 %d: %v", info.NavigationID, err)
			continue // 跳过没有坐标的点
		}

		r.allNavigationPoints = append(r.allNavigationPoints, NavigationPoint{
			ID:    info.NavigationID,
			X:     x,
			Y:     y,
			Angle: angle,
		})
	}

	log.Printf("📍 成功缓存 %d 个导航点坐标", len(r.allNavigationPoints))
	return nil
}

// generateStatusMessage 生成状态描述信息
func (r *StatusReporter) generateStatusMessage(robotState, robotTaskState int, currentPosition string) string {
	switch robotState {
	case zmq.RobotStateAbnormal:
		return "机器人异常"
	case zmq.RobotStateLowBattery:
		return "电量不足，需要充电"
	case zmq.RobotStateNormal:
		switch robotTaskState {
		case zmq.RobotTaskStateNotStarted:
			switch currentPosition {
			case zmq.PositionParkingStr:
				return "在停车点等待任务"
			case zmq.PositionSwitchStr:
				return "在权限交接点等待指令"
			case zmq.PositionMainRoadStr:
				return "在主干道移动中"
			default:
				return "等待任务分配"
			}
		case zmq.RobotTaskStateInProgress:
			switch currentPosition {
			case zmq.PositionMainRoadStr:
				return "正在执行任务-导航中"
			case zmq.PositionSwitchStr:
				return "正在执行任务-工作中"
			default:
				return "正在执行任务"
			}
		default:
			return "状态正常"
		}
	default:
		return "状态未知"
	}
}