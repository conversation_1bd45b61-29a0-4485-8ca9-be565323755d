package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"github.com/user/agv_nav/pkg/logger"
)

// AGV连接请求
type AGVConnectRequest struct {
	AuthCode string `json:"authCode"`
	IP       string `json:"ip"`
	Port     string `json:"port"`
}

// AGV导航请求
type AGVNavigateRequest struct {
	PointID uint16 `json:"pointId"`
}

// AGV工作模式请求
type AGVWorkModeRequest struct {
	IsAuto bool `json:"isAuto"`
}

// AGV位置设置请求
type AGVPositionRequest struct {
	X     float64 `json:"x"`
	Y     float64 `json:"y"`
	Angle float64 `json:"angle"`
}

// handleAGVConnect 处理AGV连接
func (s *Server) handleAGVConnect(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	apiLogger := logger.GetModuleLogger("api")
	startTime := time.Now()
	clientIP := r.RemoteAddr
	apiLogger.Info("AGV连接API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	var req AGVConnectRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		apiLogger.Error("AGV连接请求数据解析失败", "clientIP", clientIP, "error", err)
		s.respondError(w, "请求数据格式错误", http.StatusBadRequest)
		return
	}

	apiLogger.Info("处理AGV连接请求", "ip", req.IP, "port", req.Port, "clientIP", clientIP)
	err := s.agvController.Connect(req.AuthCode, req.IP, req.Port)
	if err != nil {
		apiLogger.Error("AGV连接失败", "ip", req.IP, "port", req.Port, "clientIP", clientIP, "error", err, "duration", time.Since(startTime))
		s.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	apiLogger.Info("AGV连接成功", "ip", req.IP, "port", req.Port, "clientIP", clientIP, "duration", time.Since(startTime))
	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"message": "AGV连接成功",
	})
}

// handleAGVDisconnect 处理AGV断开连接
func (s *Server) handleAGVDisconnect(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	apiLogger := logger.GetModuleLogger("api")
	clientIP := r.RemoteAddr
	apiLogger.Info("AGV断开连接API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	s.agvController.Disconnect()
	apiLogger.Info("AGV断开连接成功", "clientIP", clientIP)
	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"message": "AGV已断开连接",
	})
}

// handleAGVStatus 获取AGV状态
func (s *Server) handleAGVStatus(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	apiLogger := logger.GetModuleLogger("api")
	clientIP := r.RemoteAddr
	apiLogger.Debug("AGV状态查询API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	connected := s.agvController.IsConnected()
	var status interface{}

	if connected {
		// 如果已连接，主动查询最新状态
		apiLogger.Debug("AGV已连接，正在查询最新状态", "clientIP", clientIP)
		err := s.agvController.QueryStatus()
		if err != nil {
			apiLogger.Error("AGV状态查询失败", "clientIP", clientIP, "error", err)
			s.respondError(w, fmt.Sprintf("查询AGV状态失败: %v", err), http.StatusInternalServerError)
			return
		}
		status = s.agvController.GetStatus()
		apiLogger.Debug("AGV状态查询成功", "clientIP", clientIP, "connected", connected)

		// 广播最新状态给所有WebSocket客户端
		s.BroadcastStatus()
	} else {
		// 如果未连接，返回null状态
		apiLogger.Debug("AGV未连接，返回空状态", "clientIP", clientIP)
		status = nil
	}

	s.respondJSON(w, map[string]interface{}{
		"connected": connected,
		"status":    status,
	})
}

// handleAGVNavigate 处理AGV导航
func (s *Server) handleAGVNavigate(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	apiLogger := logger.GetModuleLogger("api")
	startTime := time.Now()
	clientIP := r.RemoteAddr
	apiLogger.Info("AGV导航API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	var req AGVNavigateRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		apiLogger.Error("AGV导航请求数据解析失败", "clientIP", clientIP, "error", err)
		s.respondError(w, "请求数据格式错误", http.StatusBadRequest)
		return
	}

	apiLogger.Info("处理AGV导航请求", "pointID", req.PointID, "clientIP", clientIP)
	err := s.agvController.SendNavToPointID(req.PointID)
	if err != nil {
		apiLogger.Error("AGV导航命令发送失败", "pointID", req.PointID, "clientIP", clientIP, "error", err, "duration", time.Since(startTime))
		s.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	apiLogger.Info("AGV导航命令发送成功", "pointID", req.PointID, "clientIP", clientIP, "duration", time.Since(startTime))
	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"message": "导航命令已发送",
		"pointId": req.PointID,
	})
}

// handleAGVSubscribe 处理AGV订阅
func (s *Server) handleAGVSubscribe(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	apiLogger := logger.GetModuleLogger("api")
	startTime := time.Now()
	clientIP := r.RemoteAddr
	apiLogger.Info("AGV订阅API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	err := s.agvController.Subscribe()
	if err != nil {
		apiLogger.Error("AGV状态订阅失败", "clientIP", clientIP, "error", err, "duration", time.Since(startTime))
		s.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	apiLogger.Info("AGV状态订阅成功", "clientIP", clientIP, "duration", time.Since(startTime))
	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"message": "AGV状态订阅成功",
	})
}

// handleAGVWorkMode 处理AGV工作模式切换
func (s *Server) handleAGVWorkMode(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	apiLogger := logger.GetModuleLogger("api")
	startTime := time.Now()
	clientIP := r.RemoteAddr
	apiLogger.Info("AGV工作模式切换API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	var req AGVWorkModeRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		apiLogger.Error("AGV工作模式请求数据解析失败", "clientIP", clientIP, "error", err)
		s.respondError(w, "请求数据格式错误", http.StatusBadRequest)
		return
	}

	mode := "手动"
	if req.IsAuto {
		mode = "自动"
	}
	apiLogger.Info("处理AGV工作模式切换请求", "targetMode", mode, "isAuto", req.IsAuto, "clientIP", clientIP)

	err := s.agvController.SwitchWorkMode(req.IsAuto)
	if err != nil {
		apiLogger.Error("AGV工作模式切换失败", "targetMode", mode, "clientIP", clientIP, "error", err, "duration", time.Since(startTime))
		s.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	apiLogger.Info("AGV工作模式切换成功", "targetMode", mode, "isAuto", req.IsAuto, "clientIP", clientIP, "duration", time.Since(startTime))
	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"message": "工作模式已切换到" + mode,
		"isAuto":  req.IsAuto,
	})
}

// handleAGVPosition 处理AGV手动定位
func (s *Server) handleAGVPosition(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	apiLogger := logger.GetModuleLogger("api")
	startTime := time.Now()
	clientIP := r.RemoteAddr
	apiLogger.Info("AGV手动定位API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	var req AGVPositionRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		apiLogger.Error("AGV手动定位请求数据解析失败", "clientIP", clientIP, "error", err)
		s.respondError(w, "请求数据格式错误", http.StatusBadRequest)
		return
	}

	apiLogger.Info("处理AGV手动定位请求", "x", req.X, "y", req.Y, "angle", req.Angle, "clientIP", clientIP)
	err := s.agvController.ManualPosition(req.X, req.Y, req.Angle)
	if err != nil {
		apiLogger.Error("AGV手动定位命令发送失败", "x", req.X, "y", req.Y, "angle", req.Angle, "clientIP", clientIP, "error", err, "duration", time.Since(startTime))
		s.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	apiLogger.Info("AGV手动定位命令发送成功", "x", req.X, "y", req.Y, "angle", req.Angle, "clientIP", clientIP, "duration", time.Since(startTime))
	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"message": "手动定位命令已发送",
		"x":       req.X,
		"y":       req.Y,
		"angle":   req.Angle,
	})
}

// handleAGVConfirmPosition 处理AGV确认重定位
func (s *Server) handleAGVConfirmPosition(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	err := s.agvController.ConfirmReposition()
	if err != nil {
		s.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"message": "重定位确认命令已发送",
	})
}

// handleAGVLaserInit 处理AGV激光导航初始化
func (s *Server) handleAGVLaserInit(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	var req AGVPositionRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		s.respondError(w, "请求数据格式错误", http.StatusBadRequest)
		return
	}

	err := s.agvController.InitializeLaserNavigation(req.X, req.Y, req.Angle)
	if err != nil {
		s.respondError(w, err.Error(), http.StatusInternalServerError)
		return
	}

	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"message": "激光导航初始化命令已发送",
		"x":       req.X,
		"y":       req.Y,
		"angle":   req.Angle,
	})
}

// handleAGVNavigationStatus 处理AGV导航状态查询
func (s *Server) handleAGVNavigationStatus(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	apiLogger := logger.GetModuleLogger("api")
	startTime := time.Now()
	clientIP := r.RemoteAddr
	apiLogger.Info("AGV导航状态查询API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	// 检查AGV连接状态
	if !s.agvController.IsConnected() {
		apiLogger.Warn("AGV导航状态查询失败：AGV未连接", "clientIP", clientIP)
		s.respondError(w, "AGV未连接", http.StatusServiceUnavailable)
		return
	}

	// 查询导航状态
	err := s.agvController.QueryNavigationStatus()
	if err != nil {
		apiLogger.Error("AGV导航状态查询失败", "error", err, "clientIP", clientIP)
		s.respondError(w, fmt.Sprintf("查询导航状态失败: %v", err), http.StatusInternalServerError)
		return
	}

	// 获取导航状态
	navStatus := s.agvController.GetNavigationStatus()
	if navStatus == nil {
		apiLogger.Error("AGV导航状态获取失败：状态为空", "clientIP", clientIP)
		s.respondError(w, "获取导航状态失败", http.StatusInternalServerError)
		return
	}

	// 计算路径点统计
	passedCount := len(navStatus.GetValidPassedPoints())
	remainingCount := len(navStatus.GetValidRemainingPoints())

	duration := time.Since(startTime)
	apiLogger.Info("AGV导航状态查询成功",
		"status", navStatus.GetStatusString(),
		"targetPointID", navStatus.TargetPointID,
		"passedCount", passedCount,
		"remainingCount", remainingCount,
		"duration", duration,
		"clientIP", clientIP)

	s.respondJSON(w, map[string]interface{}{
		"success":         true,
		"message":         "导航状态查询成功",
		"status":          navStatus.GetStatusString(),
		"statusCode":      navStatus.Status,
		"targetPointID":   navStatus.TargetPointID,
		"passedCount":     passedCount,
		"remainingCount":  remainingCount,
		"isNavigating":    navStatus.IsNavigating(),
		"isCompleted":     navStatus.IsCompleted(),
		"isFailed":        navStatus.IsFailed(),
		"passedPoints":    navStatus.GetValidPassedPoints(),
		"remainingPoints": navStatus.GetValidRemainingPoints(),
	})
}

// handleOneClickOnline 处理一键上线功能
func (s *Server) handleOneClickOnline(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	apiLogger := logger.GetModuleLogger("api")
	startTime := time.Now()
	clientIP := r.RemoteAddr
	apiLogger.Info("一键上线API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	// 检查AGV连接状态
	if !s.agvController.IsConnected() {
		apiLogger.Warn("一键上线失败：AGV未连接", "clientIP", clientIP)
		s.respondError(w, "AGV未连接，请先连接AGV", http.StatusServiceUnavailable)
		return
	}

	// 读取配置文件获取停车点参数
	configPath := filepath.Join("backend", "data", "task_config.json")
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		// 尝试当前目录
		configPath = filepath.Join("data", "task_config.json")
	}

	configData, err := os.ReadFile(configPath)
	if err != nil {
		apiLogger.Error("读取配置文件失败", "path", configPath, "error", err, "clientIP", clientIP)
		s.respondError(w, "读取配置文件失败", http.StatusInternalServerError)
		return
	}

	var config struct {
		ParkingPoint struct {
			ID    int     `json:"id"`
			X     float64 `json:"x"`
			Y     float64 `json:"y"`
			Angle float64 `json:"angle"`
		} `json:"parkingPoint"`
	}

	if err := json.Unmarshal(configData, &config); err != nil {
		apiLogger.Error("解析配置文件失败", "error", err, "clientIP", clientIP)
		s.respondError(w, "配置文件格式错误", http.StatusInternalServerError)
		return
	}

	parkingPoint := config.ParkingPoint
	apiLogger.Info("从配置文件读取停车点参数",
		"id", parkingPoint.ID,
		"x", parkingPoint.X,
		"y", parkingPoint.Y,
		"angle", parkingPoint.Angle,
		"clientIP", clientIP)

	// 调用激光导航初始化
	err = s.agvController.InitializeLaserNavigation(parkingPoint.X, parkingPoint.Y, parkingPoint.Angle)
	if err != nil {
		apiLogger.Error("一键上线激光导航初始化失败",
			"x", parkingPoint.X,
			"y", parkingPoint.Y,
			"angle", parkingPoint.Angle,
			"clientIP", clientIP,
			"error", err,
			"duration", time.Since(startTime))
		s.respondError(w, fmt.Sprintf("激光导航初始化失败: %v", err), http.StatusInternalServerError)
		return
	}

	apiLogger.Info("一键上线成功",
		"parkingPointID", parkingPoint.ID,
		"x", parkingPoint.X,
		"y", parkingPoint.Y,
		"angle", parkingPoint.Angle,
		"clientIP", clientIP,
		"duration", time.Since(startTime))

	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"message": "一键上线成功",
		"parkingPoint": map[string]interface{}{
			"id":    parkingPoint.ID,
			"x":     parkingPoint.X,
			"y":     parkingPoint.Y,
			"angle": parkingPoint.Angle,
		},
	})
}
