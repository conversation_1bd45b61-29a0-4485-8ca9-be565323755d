package main

import (
	"flag"
	"log"

	"github.com/user/agv_nav/pkg/api"
	"github.com/user/agv_nav/pkg/logger"
)

func main() {
	// 解析命令行参数
	port := flag.String("port", "8080", "API服务器端口")
	flag.Parse()

	// 初始化日志系统
	logger.InitRealtimeLogging()
	logger.InitializeModuleLoggers()

	// 创建API服务器
	server := api.NewServer()

	// 启动服务器
	log.Printf("🚀 AGV控制API服务启动...")
	if err := server.Start(*port); err != nil {
		log.Fatalf("❌ 服务器启动失败: %v", err)
	}
}
