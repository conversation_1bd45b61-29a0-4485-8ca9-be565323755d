package task

import (
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	"math"

	"github.com/user/agv_nav/internal/database"
	"github.com/user/agv_nav/internal/task/zmq"
	internalzmq "github.com/user/agv_nav/internal/zmq"
	"github.com/user/agv_nav/pkg/agv"
	"github.com/user/agv_nav/pkg/logger"
	"github.com/user/agv_nav/pkg/plc"
)

// 全局工作模式管理器
var globalWorkModeManager *WorkModeManager

// InitTaskManager 初始化全局任务管理器
func InitTaskManager(agvController *agv.Controller, plcController *plc.Controller) {
	// 初始化配置
	if err := InitConfig(); err != nil {
		log.Printf("警告：初始化配置失败: %v，使用默认配置", err)
	}

	// 初始化数据库服务
	dbService, err := database.NewNavigationDB()
	if err != nil {
		log.Printf("警告：初始化数据库连接失败: %v", err)
		dbService = nil
	}

	globalTaskManager = &WatchTaskManager{
		status:         StatusIdle,
		stopChan:       make(chan struct{}),
		pauseChan:      make(chan struct{}),
		resumeChan:     make(chan struct{}),
		agvController:  agvController,
		plcController:  plcController,
		dbService:      dbService,                       // 添加数据库服务初始化
		spindleService: NewSpindleService(),             // 初始化锭号服务
		plcDataHelper:  NewPLCDataHelper(plcController), // 初始化PLC数据辅助
		maxRetries:     GetConfig().MaxRetries,          // 从配置获取最大重试次数
		failedMachines: make([]string, 0),               // 初始化失败机器列表
		completedTimes: make([]time.Time, 0),            // 初始化完成时间列表
	}

	// 初始化工作模式管理器
	globalWorkModeManager = NewWorkModeManager()
	
	// 设置导航函数回调，让WorkModeManager可以调用任务管理器的导航功能
	globalWorkModeManager.SetNavigateFunc(func(pointID int) error {
		if globalTaskManager != nil {
			log.Printf("🧭 WorkModeManager调用导航到点ID: %d", pointID)
			return globalTaskManager.navigateToTarget(pointID)
		}
		return fmt.Errorf("任务管理器未初始化")
	})
	
	// 同步初始化ZMQ管理器，确保它在需要时已经准备好
	log.Printf("🚀 开始初始化ZMQ管理器...")
	if err := initZMQManager(); err != nil {
		log.Printf("⚠️  ZMQ管理器初始化失败: %v", err)
		log.Printf("📝 系统将以手动模式运行")
	} else {
		log.Printf("✅ ZMQ管理器初始化成功")
	}
	
	// 设置模式变更回调，同步任务管理器状态
	globalWorkModeManager.SetModeChangeCallback(func(mode string) {
		log.Printf("工作模式已变更为: %s", mode)
	})

	log.Printf("任务管理器和工作模式管理器初始化完成")
}

// GetWorkModeManager 获取全局工作模式管理器
func GetWorkModeManager() *WorkModeManager {
	return globalWorkModeManager
}

// initZMQManager 初始化ZMQ管理器
func initZMQManager() error {
	config := GetConfig()
	
	// 检查ZMQ是否启用
	if !config.ZMQConfig.Enabled {
		log.Printf("ZMQ功能未启用，跳过ZMQ管理器初始化")
		return nil
	}

	// 创建系统配置 
	// 使用现有的配置路径查找机制
	configPath := getConfigPath()
	systemConfig, sysErr := internalzmq.LoadSystemConfig(configPath)
	if sysErr != nil {
		log.Printf("从配置文件加载系统配置失败，使用默认配置: %v", sysErr)
		systemConfig = internalzmq.DefaultSystemConfig()
	}
	
	log.Printf("使用机器人编号: %s", systemConfig.RobotNo)

	// 转换配置格式
	zmqConfig := &zmq.ZMQConfig{
		Enabled: config.ZMQConfig.Enabled,
		Scheduler: zmq.ZMQSchedulerConfig{
			RequesterEndpoint:        config.ZMQConfig.Scheduler.RequesterEndpoint,
			ResponderEndpoint:        config.ZMQConfig.Scheduler.ResponderEndpoint,
			HeartbeatIntervalMinutes: config.ZMQConfig.Scheduler.HeartbeatIntervalMinutes,
			ReplyTimeoutSeconds:      config.ZMQConfig.Scheduler.ReplyTimeoutSeconds,
			MaxRetryAttempts:         config.ZMQConfig.Scheduler.MaxRetryAttempts,
			RetryIntervalSeconds:     config.ZMQConfig.Scheduler.RetryIntervalSeconds,
		},
	}

	// 创建带完整组件的ZMQ管理器
	zmqManager, err := zmq.NewZMQManagerWithComponents(
		zmqConfig,
		systemConfig,
		globalTaskManager.agvController,
		globalWorkModeManager,
		globalTaskManager.dbService,
		StartTask,
	)
	if err != nil {
		return fmt.Errorf("创建ZMQ管理器失败: %v", err)
	}

	// 注入到工作模式管理器
	globalWorkModeManager.SetZMQManager(zmqManager)

	// 心跳管理器已集成在ZMQManager中，无需单独初始化
	// HeartbeatManager会在zmqManager.Start()时自动启动

	// 初始化任务处理器
	if err := initTaskHandlers(zmqManager, systemConfig); err != nil {
		log.Printf("警告：任务处理器初始化失败: %v", err)
	}
	
	// 如果配置为调度模式，自动启动ZMQ服务
	log.Printf("🔍 检查工作模式配置: %s (期望: %s)", config.WorkMode, WorkModeScheduled)
	if config.WorkMode == WorkModeScheduled {
		log.Printf("🚀 检测到调度模式配置，自动启动ZMQ服务...")
		if err := globalWorkModeManager.SwitchToScheduledMode(); err != nil {
			log.Printf("⚠️  自动启动ZMQ服务失败: %v", err)
		} else {
			log.Printf("✅ ZMQ调度服务已自动启动")
		}
	} else {
		log.Printf("📝 当前为手动模式，不自动启动ZMQ服务")
	}
	
	log.Printf("ZMQ管理器初始化成功")
	return nil
}

// initHeartbeat 函数已移除，心跳功能现在由HeartbeatManager在ZMQManager中统一管理

// initTaskHandlers 初始化任务处理器
func initTaskHandlers(zmqManager *zmq.ZMQManager, systemConfig *internalzmq.SystemConfig) error {
	log.Printf("🚀 初始化任务处理器")

	// 创建任务分配处理器
	taskAssignmentHandler := zmq.NewTaskAssignmentHandler(systemConfig, StartTask)

	// 设置WorkModeManager引用，用于保存TaskId
	if globalWorkModeManager != nil {
		taskAssignmentHandler.SetWorkModeManager(globalWorkModeManager)
		log.Printf("🔗 TaskAssignmentHandler已关联WorkModeManager")
	}

	// 创建任务生命周期通知器
	taskLifecycleNotifier := zmq.NewTaskLifecycleNotifier(zmqManager, systemConfig)

	// 设置任务生命周期通知器到WorkModeManager
	if globalWorkModeManager != nil {
		globalWorkModeManager.SetTaskLifecycleNotifier(taskLifecycleNotifier)
		log.Printf("🔗 WorkModeManager已关联TaskLifecycleNotifier")
	}

	// 注册到路由器（处理指令200）
	zmqManager.RegisterHandler(taskAssignmentHandler)
	log.Printf("✅ 任务分配处理器已注册到路由器（指令200）")

	log.Printf("✅ 任务处理器初始化完成")
	return nil
}

// StartAutoWatching 开始自动看车（调度模式）
func StartAutoWatching() error {
	// 切换到调度模式
	if globalWorkModeManager != nil {
		if err := globalWorkModeManager.SwitchToScheduledMode(); err != nil {
			return fmt.Errorf("切换到调度模式失败: %v", err)
		}
		log.Printf("已切换到调度模式，等待调度系统分配任务")
		return nil
	}
	return fmt.Errorf("工作模式管理器未初始化")
}

// StopAutoWatching 停止自动看车（从调度模式切换到手动模式）
func StopAutoWatching() error {
	// 检查是否有任务正在运行
	if globalTaskManager != nil && globalTaskManager.isWorking {
		return fmt.Errorf("当前有任务正在运行，请先停止任务")
	}

	// 切换到手动模式
	if globalWorkModeManager != nil {
		if err := globalWorkModeManager.SwitchToManualMode(); err != nil {
			return fmt.Errorf("切换到手动模式失败: %v", err)
		}
		log.Printf("已停止自动看车，切换到手动模式")
		return nil
	}
	return fmt.Errorf("工作模式管理器未初始化")
}

// StartTask 开始看车任务
func StartTask(spinningMachines []string) error {
	globalTaskManager.mu.Lock()
	defer globalTaskManager.mu.Unlock()

	// 🔥 保持当前模式不变，不强制切换
	if globalWorkModeManager != nil {
		currentMode := globalWorkModeManager.GetCurrentMode()
		log.Printf("🎯 %s模式下执行任务: %v", currentMode, spinningMachines)
	}

	// 前置检查：机器人运行条件检查
	err := globalTaskManager.checkRobotRunCondition()
	if err != nil {
		return fmt.Errorf("机器人运行条件检查失败: %v", err)
	}

	// 检查是否已有任务在运行
	if globalTaskManager.isWorking {
		return fmt.Errorf("已有看车任务在运行中")
	}

	// 验证输入
	if len(spinningMachines) == 0 {
		return fmt.Errorf("细纱机范围不能为空")
	}

	// 初始化任务
	now := time.Now()
	globalTaskManager.queue = make([]string, len(spinningMachines))
	copy(globalTaskManager.queue, spinningMachines)
	globalTaskManager.currentMachine = ""
	globalTaskManager.status = StatusRunning
	globalTaskManager.progress = 0
	globalTaskManager.total = len(spinningMachines)
	globalTaskManager.isWorking = true
	globalTaskManager.failedMachines = make([]string, 0)    // 清空失败机器列表
	globalTaskManager.lastError = ""                        // 清空错误信息
	globalTaskManager.startTime = &now                      // 记录开始时间
	globalTaskManager.currentStep = "准备开始"                  // 初始化步骤
	globalTaskManager.stepProgress = 0                      // 初始化步骤进度
	globalTaskManager.completedTimes = make([]time.Time, 0) // 清空完成时间列表

	// 更新工作模式管理器的任务状态
	if globalWorkModeManager != nil {
		globalWorkModeManager.SetTaskRunning(true)
	}

	log.Printf("看车任务已启动，细纱机范围: %v", spinningMachines)

	// 启动后台工作协程
	log.Printf("正在启动后台工作协程...")
	go func() {
		log.Printf("后台工作协程已启动，开始执行workRoutine")
		globalTaskManager.workRoutine()
	}()

	log.Printf("StartTask函数执行完成，协程已启动")

	// 在释放锁之后再广播任务状态变化（避免死锁）
	go func() {
		time.Sleep(100 * time.Millisecond) // 短暂延迟确保锁已释放
		broadcastTaskStatus()
	}()

	return nil
}

// StopWatchTask 停止看车任务
func StopWatchTask() error {
	globalTaskManager.mu.Lock()
	defer globalTaskManager.mu.Unlock()

	if !globalTaskManager.isWorking {
		return fmt.Errorf("没有正在运行的看车任务")
	}

	log.Printf("发送停止信号并清空任务队列")

	// 发送停止信号
	select {
	case <-globalTaskManager.stopChan:
	default:
	}

	// 清空所有任务相关状态，使用户可以开始新任务
	globalTaskManager.status = StatusStopped
	globalTaskManager.isWorking = false
	globalTaskManager.queue = nil          // 清空任务队列
	globalTaskManager.currentMachine = ""  // 清空当前机器
	globalTaskManager.progress = 0         // 重置进度
	globalTaskManager.total = 0            // 重置总数
	globalTaskManager.failedMachines = nil // 清空失败机器列表
	globalTaskManager.lastError = ""       // 清空最后错误
	globalTaskManager.startTime = nil      // 清空开始时间
	globalTaskManager.currentStep = ""     // 清空当前步骤
	globalTaskManager.stepProgress = 0     // 重置步骤进度
	globalTaskManager.completedTimes = nil // 清空完成时间列表

	// 更新工作模式管理器的任务状态
	if globalWorkModeManager != nil {
		globalWorkModeManager.SetTaskRunning(false)
	}

	log.Printf("任务状态已清空，用户可以开始新的看车任务")

	// 在释放锁之后再广播任务状态变化（避免死锁）
	go func() {
		time.Sleep(10 * time.Millisecond)
		broadcastTaskStatus()
	}()

	return nil
}

// PauseTask 暂停任务
func PauseTask() error {
	globalTaskManager.mu.Lock()
	defer globalTaskManager.mu.Unlock()

	if !globalTaskManager.isWorking || globalTaskManager.status != StatusRunning {
		return fmt.Errorf("没有可暂停的任务")
	}

	log.Printf("发送暂停信号")

	select {
	case <-globalTaskManager.pauseChan:
	default:
	}

	globalTaskManager.status = StatusPaused

	// 在释放锁之后再广播任务状态变化（避免死锁）
	go func() {
		time.Sleep(10 * time.Millisecond)
		broadcastTaskStatus()
	}()

	return nil
}

// ResumeTask 恢复任务
func ResumeTask() error {
	globalTaskManager.mu.Lock()
	defer globalTaskManager.mu.Unlock()

	if !globalTaskManager.isWorking || globalTaskManager.status != StatusPaused {
		return fmt.Errorf("没有可恢复的任务")
	}

	log.Printf("发送恢复信号")

	select {
	case <-globalTaskManager.resumeChan:
	default:
	}

	globalTaskManager.status = StatusRunning

	// 在释放锁之后再广播任务状态变化（避免死锁）
	go func() {
		time.Sleep(10 * time.Millisecond)
		broadcastTaskStatus()
	}()

	return nil
}

// GetTaskStatus 获取任务状态
func GetTaskStatus() map[string]interface{} {
	globalTaskManager.mu.RLock()
	defer globalTaskManager.mu.RUnlock()

	// 计算处理速度
	var speed float64
	if len(globalTaskManager.completedTimes) > 1 && globalTaskManager.startTime != nil {
		elapsed := time.Since(*globalTaskManager.startTime).Minutes()
		if elapsed > 0 {
			speed = float64(len(globalTaskManager.completedTimes)) / elapsed
		}
	}

	// 预估剩余时间
	var estimatedTime *time.Time
	remaining := len(globalTaskManager.queue)
	if speed > 0 && remaining > 0 {
		minutesLeft := float64(remaining) / speed
		estimated := time.Now().Add(time.Duration(minutesLeft) * time.Minute)
		estimatedTime = &estimated
	}

	// 计算总体进度百分比
	progressPercent := 0
	if globalTaskManager.total > 0 {
		progressPercent = (globalTaskManager.progress * 100) / globalTaskManager.total
	}

	return map[string]interface{}{
		"status":           string(globalTaskManager.status),
		"current_machine":  globalTaskManager.currentMachine,
		"progress":         globalTaskManager.progress,
		"total":            globalTaskManager.total,
		"remaining":        globalTaskManager.queue,
		"failed_machines":  globalTaskManager.failedMachines,
		"last_error":       globalTaskManager.lastError,
		"progress_percent": progressPercent,
		"current_step":     globalTaskManager.currentStep,
		"step_progress":    globalTaskManager.stepProgress,
		"processing_speed": speed,
		"estimated_time":   estimatedTime,
		"start_time":       globalTaskManager.startTime,
		"elapsed_time":     calculateElapsedTime(globalTaskManager.startTime),
	}
}

// workRoutine 后台工作协程
// 修改说明：重构为使用任务组处理，支持巷道双侧连续作业
func (tm *WatchTaskManager) workRoutine() {
	defer func() {
		tm.mu.Lock()
		tm.isWorking = false
		tm.status = StatusIdle
		tm.currentMachine = ""
		tm.queue = nil
		tm.mu.Unlock()

		// TaskId在任务完成通知发送成功后已清理，这里不再重复清理

		// 广播任务完成状态
		broadcastTaskStatus()

		log.Printf("看车任务协程结束")
	}()

	log.Printf("看车任务协程启动，开始预处理任务")

	// 新增：预处理任务，生成任务组
	log.Printf("开始调用PreprocessTasks，队列长度: %d, 队列内容: %v", len(tm.queue), tm.queue)
	taskGroups, err := PreprocessTasks(tm.queue)
	if err != nil {
		log.Printf("预处理任务失败: %v", err)
		tm.mu.Lock()
		tm.lastError = fmt.Sprintf("任务预处理失败: %v", err)
		tm.mu.Unlock()
		return
	}
	log.Printf("PreprocessTasks调用成功，返回%d个任务组", len(taskGroups))

	// 打印任务组摘要
	log.Printf("任务预处理完成:\n%s", GetTaskGroupSummary(taskGroups))

	// 按任务组处理
	for groupIndex, group := range taskGroups {
		// 检查控制信号
		select {
		case <-tm.stopChan:
			log.Printf("收到停止信号，退出任务")
			return
		case <-tm.pauseChan:
			log.Printf("收到暂停信号，等待恢复")
			<-tm.resumeChan // 等待恢复信号
			log.Printf("收到恢复信号，继续执行")
		default:
			// 继续处理
		}

		log.Printf("开始处理任务组 %d/%d: %s", groupIndex+1, len(taskGroups), group.GroupID)

		// 更新当前任务组信息
		tm.mu.Lock()
		tm.currentStep = fmt.Sprintf("处理任务组 %s", group.GroupID)
		tm.stepProgress = 0
		tm.mu.Unlock()
		broadcastTaskStatus()

		// 处理任务组
		err := tm.processTaskGroup(group)

		// 更新进度（任务组中的所有细纱机）
		tm.mu.Lock()
		tm.progress += len(group.Machines)

		if err != nil {
			log.Printf("处理任务组 %s 失败: %v", group.GroupID, err)
			// 记录失败信息
			tm.failedMachines = append(tm.failedMachines, group.Machines...)
			tm.lastError = fmt.Sprintf("任务组 %s: %v", group.GroupID, err)
			tm.currentStep = fmt.Sprintf("任务组 %s 处理失败", group.GroupID)
		} else {
			log.Printf("任务组 %s 处理完成", group.GroupID)
			for range group.Machines {
				tm.completedTimes = append(tm.completedTimes, time.Now())
			}
			tm.currentStep = fmt.Sprintf("任务组 %s 处理完成", group.GroupID)
		}
		tm.stepProgress = 100
		tm.mu.Unlock()

		// 根据错误类型决定是否继续
		if err != nil && tm.shouldStopOnError(err) {
			log.Printf("严重错误，停止任务执行")
			return
		}

		// 广播进度更新
		broadcastTaskStatus()
	}

	log.Printf("所有任务组处理完成")

	// 🔥 调度模式下不自动导航到停车点，由等待调度指令的流程来决定
	if globalWorkModeManager != nil && globalWorkModeManager.IsScheduledMode() {
		log.Printf("调度模式：任务完成，等待调度指令决定下一步")
		// 不执行导航，让调度指令（201）来处理
	} else {
		// 手动模式：所有任务完成后，导航到停车点
		log.Printf("手动模式：开始导航到停车点")
		err = tm.navigateToParking()
		if err != nil {
			log.Printf("导航到停车点失败: %v", err)
			tm.mu.Lock()
			tm.lastError = fmt.Sprintf("导航到停车点失败: %v", err)
			tm.mu.Unlock()
		} else {
			log.Printf("已成功导航到停车点")
		}
	}
}

// processSingleMachine 处理单个细纱机
func (tm *WatchTaskManager) processSingleMachine(machine string) error {
	log.Printf("开始处理细纱机: %s", machine)

	// 更新流程状态：开始处理机器
	updateMachine(machine)
	updateStep(fmt.Sprintf("开始处理细纱机 %s", machine))

	// 步骤1: 查询导航点
	tm.updateStepProgress(fmt.Sprintf("查询细纱机 %s 导航点", machine), 10)
	navPoint, err := tm.queryNavigationPoint(machine)
	if err != nil {
		return fmt.Errorf("查询导航点失败: %v", err)
	}
	log.Printf("细纱机 %s 的导航点: %d", machine, navPoint)

	// 步骤2: AGV导航到目标点
	tm.updateStepProgress(fmt.Sprintf("AGV导航到细纱机 %s", machine), 25)
	updateStep("AGV导航中...")
	updateNavigation("laser")
	err = tm.navigateToTarget(navPoint)
	if err != nil {
		return fmt.Errorf("AGV导航失败: %v", err)
	}
	log.Printf("AGV已到达细纱机 %s 的位置", machine)

	// 🔥 步骤2.5: 发送任务开始通知（仅调度模式）
	if globalWorkModeManager != nil {
		err = globalWorkModeManager.NotifyTaskStart(machine)
		if err != nil {
			log.Printf("❌ 发送任务开始通知失败: %v", err) // 不阻断流程
		} else {
			log.Printf("✅ 任务开始通知已发送: %s", machine)
		}
	}

	// 步骤3: 切换到PLC控制（包含码值写入和任务启动）
	tm.updateStepProgress("切换到PLC控制模式", 40)
	updateStep("切换到PLC控制")
	updateNavigation("plc")
	err = tm.switchToPLCControl(machine)
	if err != nil {
		return fmt.Errorf("切换PLC控制失败: %v", err)
	}
	log.Printf("已切换到PLC控制并启动任务")

	// 步骤5: 执行看车工作
	tm.updateStepProgress(fmt.Sprintf("执行细纱机 %s 看车工作", machine), 60)
	err = tm.executeWatchWork(machine, true) // 单侧任务调用NotifyMachineComplete
	if err != nil {
		return fmt.Errorf("看车工作失败: %v", err)
	}
	log.Printf("看车工作完成")

	// 步骤6: PLC释放控制权
	tm.updateStepProgress("释放PLC控制权", 90)
	err = tm.releasePLCControl()
	if err != nil {
		return fmt.Errorf("PLC释放控制权失败: %v", err)
	}
	log.Printf("PLC控制权已释放")

	return nil
}

// processTaskGroup 处理任务组
// 新增方法：根据任务组类型选择相应的处理策略
func (tm *WatchTaskManager) processTaskGroup(group TaskGroup) error {
	log.Printf("处理任务组: Type=%s, Machines=%v", group.Type, group.Machines)

	switch group.Type {
	case "single":
		// 单侧任务处理
		return tm.processSingleSide(group.Machines[0])
	case "lane":
		// 巷道任务处理
		if group.LaneInfo == nil {
			return fmt.Errorf("巷道任务组缺少LaneInfo")
		}
		return tm.processLane(group.LaneInfo.LeftMachine, group.LaneInfo.RightMachine)
	default:
		return fmt.Errorf("未知的任务组类型: %s", group.Type)
	}
}

// processSingleSide 处理单侧任务
// 新增方法：处理中央通道等单侧任务
func (tm *WatchTaskManager) processSingleSide(machine string) error {
	log.Printf("处理单侧任务: %s", machine)

	// 使用原有的单机处理逻辑（带重试）
	return tm.processSingleMachineWithRetry(machine)
}

// processLane 处理巷道任务
// 新增方法：处理巷道双侧连续作业（L侧→调头→R侧）
func (tm *WatchTaskManager) processLane(leftMachine, rightMachine string) error {
	log.Printf("处理巷道任务: %s <-> %s", leftMachine, rightMachine)

	// 步骤1: 导航到巷道入口（L侧）
	log.Printf("步骤1: 导航到巷道入口 %s", leftMachine)
	navPoint, err := tm.queryNavigationPoint(leftMachine)
	if err != nil {
		return fmt.Errorf("查询巷道入口导航点失败: %v", err)
	}

	err = tm.navigateToTarget(navPoint)
	if err != nil {
		return fmt.Errorf("导航到巷道入口失败: %v", err)
	}

	// 🔥 步骤1.5: 发送任务开始通知（仅调度模式）
	if globalWorkModeManager != nil {
		err = globalWorkModeManager.NotifyTaskStart(leftMachine)
		if err != nil {
			log.Printf("❌ 发送巷道任务开始通知失败: %v", err) // 不阻断流程
		} else {
			log.Printf("✅ 巷道任务开始通知已发送: %s", leftMachine)
		}
	}

	// 步骤2: 切换到PLC控制，进入巷道（使用L侧机器码值）
	log.Printf("步骤2: 切换PLC控制，进入巷道")
	err = tm.switchToPLCControl(leftMachine)
	if err != nil {
		return fmt.Errorf("切换PLC控制失败: %v", err)
	}

	// 步骤3: 处理L侧
	log.Printf("步骤3: 处理左侧细纱机 %s", leftMachine)
	tm.updateStepProgress(fmt.Sprintf("处理巷道左侧 %s", leftMachine), 30)

	// 更新当前处理的细纱机
	tm.mu.Lock()
	tm.currentMachine = leftMachine
	tm.mu.Unlock()
	broadcastTaskStatus()

	err = tm.executeWatchWork(leftMachine, false) // 双侧任务L侧不调用NotifyMachineComplete
	if err != nil {
		return fmt.Errorf("处理左侧细纱机失败: %v", err)
	}

	// 步骤4: L侧完成，发送调头信号并等待调头完成
	log.Printf("步骤4: L侧完成，发送调头信号")
	tm.updateStepProgress("L侧完成，等待调头", 60)

	// L侧完成：发送 M606=true, 写入安全调头区间, M608=true, M610=false
	err = tm.plcDataHelper.NotifyLaneLSideComplete(leftMachine, tm.dbService)
	if err != nil {
		return fmt.Errorf("通知PLC L侧完成失败: %v", err)
	}

	// 等待调头完成
	err = tm.plcDataHelper.WaitForTurnComplete()
	if err != nil {
		return fmt.Errorf("等待调头完成失败: %v", err)
	}

	// 步骤5: 处理R侧
	log.Printf("步骤5: 处理右侧细纱机 %s", rightMachine)
	tm.updateStepProgress(fmt.Sprintf("处理巷道右侧 %s", rightMachine), 80)

	// 更新当前处理的细纱机
	tm.mu.Lock()
	tm.currentMachine = rightMachine
	tm.mu.Unlock()
	broadcastTaskStatus()

	err = tm.executeWatchWork(rightMachine, false) // 双侧任务R侧不调用NotifyMachineComplete
	if err != nil {
		return fmt.Errorf("处理右侧细纱机失败: %v", err)
	}

	// R侧完成：执行完整的5步流程（包含切换回激光控制）
	log.Printf("R侧完成，执行完整退出流程")
	err = tm.executeRSideCompleteFlow(rightMachine)
	if err != nil {
		return fmt.Errorf("r侧完成流程失败: %v", err)
	}

	log.Printf("巷道任务完成: %s <-> %s，已切换回激光控制", leftMachine, rightMachine)
	return nil
}

// processSingleMachineWithRetry 带重试机制的单机处理
func (tm *WatchTaskManager) processSingleMachineWithRetry(machine string) error {
	var lastErr error

	for attempt := 1; attempt <= tm.maxRetries; attempt++ {
		log.Printf("处理细纱机 %s - 第 %d/%d 次尝试", machine, attempt, tm.maxRetries)

		err := tm.processSingleMachine(machine)
		if err == nil {
			if attempt > 1 {
				log.Printf("细纱机 %s 在第 %d 次尝试后成功处理", machine, attempt)
			}
			return nil
		}

		lastErr = err
		log.Printf("细纱机 %s 第 %d 次尝试失败: %v", machine, attempt, err)

		// 如果是严重错误，不进行重试
		if tm.shouldStopOnError(err) {
			log.Printf("严重错误，不进行重试: %v", err)
			break
		}

		// 如果不是最后一次尝试，等待后重试
		if attempt < tm.maxRetries {
			config := GetConfig()
			retryDelay := time.Duration(attempt) * config.RetryDelayBase // 递增延迟
			log.Printf("等待 %v 后进行第 %d 次重试", retryDelay, attempt+1)
			time.Sleep(retryDelay)
		}
	}

	return fmt.Errorf("重试 %d 次后仍然失败: %v", tm.maxRetries, lastErr)
}

// shouldStopOnError 判断是否应该因为错误而停止整个任务
func (tm *WatchTaskManager) shouldStopOnError(err error) bool {
	errorMsg := err.Error()

	// 严重错误类型，需要停止任务
	criticalErrors := []string{
		"机器人运行条件检查失败",
		"AGV未连接",
		"PLC未连接",
		"数据库服务未初始化",
		"切换AGV到自动模式失败",
	}

	for _, criticalError := range criticalErrors {
		if strings.Contains(errorMsg, criticalError) {
			return true
		}
	}

	// 其他错误可以跳过继续处理
	return false
}

// queryNavigationPoint 查询导航点
func (tm *WatchTaskManager) queryNavigationPoint(machine string) (int, error) {

	db, err := database.NewNavigationDB()
	if err != nil {
		return 0, err
	}
	defer db.Close()

	navInfo, err := db.GetNavigationByRobotID(machine)
	if err != nil {
		return 0, err
	}

	return navInfo.NavigationID, nil
}

// navigateToTarget AGV导航到目标点 - 复用AGV控制器逻辑
func (tm *WatchTaskManager) navigateToTarget(navPoint int) error {
	log.Printf("开始AGV导航到导航点: %d", navPoint)

	// 检查AGV连接状态
	if !tm.agvController.IsConnected() {
		return fmt.Errorf("AGV未连接，无法执行导航")
	}

	// 检查AGV是否在自动模式
	status := tm.agvController.GetStatus()
	if status != nil && status.WorkMode != 0x03 { // 0x03 = 自动模式
		log.Printf("AGV当前工作模式: %s，尝试切换到自动模式", status.WorkModeString())

		// 自动切换到自动模式
		err := tm.agvController.SwitchWorkMode(true)
		if err != nil {
			return fmt.Errorf("切换AGV到自动模式失败: %v", err)
		}

		// 等待模式切换完成
		time.Sleep(2 * time.Second)

		// 验证模式切换
		err = tm.agvController.QueryStatus()
		if err != nil {
			return fmt.Errorf("查询AGV状态失败: %v", err)
		}

		status = tm.agvController.GetStatus()
		if status == nil || status.WorkMode != 0x03 {
			return fmt.Errorf("AGV模式切换失败，当前模式: %s", status.WorkModeString())
		}
	}

	// 发送导航命令 - 复用已有的SendNavToPointID方法
	err := tm.agvController.SendNavToPointID(uint16(navPoint))
	if err != nil {
		return fmt.Errorf("发送导航命令失败: %v", err)
	}

	log.Printf("导航命令已发送，目标点: %d", navPoint)

	// 等待导航完成
	err = tm.waitForNavigationComplete(navPoint)
	if err != nil {
		return fmt.Errorf("导航执行失败: %v", err)
	}

	log.Printf("AGV已成功到达导航点: %d", navPoint)
	return nil
}

// isPhysicallyArrived 检查AGV是否物理上到达目标位置
func (tm *WatchTaskManager) isPhysicallyArrived(currentX, currentY, currentAngle, targetX, targetY, targetAngle float32) bool {
	// X、Y坐标误差检查（±5）
	deltaX := abs(currentX - targetX)
	deltaY := abs(currentY - targetY)
	if deltaX > 5.0 || deltaY > 5.0 {
		return false
	}

	// 角度误差检查（±60度）
	// 将弧度转换为度数
	currentAngleDeg := currentAngle * 180 / 3.14159
	targetAngleDeg := targetAngle

	// 处理角度差值（考虑±180度的循环）
	angleDiff := abs(currentAngleDeg - targetAngleDeg)
	if angleDiff > 180 {
		angleDiff = 360 - angleDiff
	}

	return angleDiff <= 60.0
}

// waitForNavigationComplete 等待导航完成
func (tm *WatchTaskManager) waitForNavigationComplete(targetPoint int) error {
	config := GetConfig()
	maxWaitTime := config.Timeouts.NavigationTimeout         // 从配置获取导航超时时间
	checkInterval := config.Timeouts.NavigationCheckInterval // 从配置获取检查间隔

	// 尝试获取目标点的物理坐标
	var targetX, targetY, targetAngle float32
	var hasCoordinates bool = false

	db, err := database.NewNavigationDB()
	if err != nil {
		log.Printf("警告：无法连接数据库，将仅使用状态空闲判断: %v", err)
	} else {
		defer db.Close()

		targetX, targetY, targetAngle, err = db.GetNavigationPosition(targetPoint)
		if err != nil {
			log.Printf("警告：无法获取目标点%d的坐标信息，将仅使用状态空闲判断: %v", targetPoint, err)
		} else {
			hasCoordinates = true
			log.Printf("导航目标点%d坐标信息: X=%.2f, Y=%.2f, 角度=%.1f°", targetPoint, targetX, targetY, targetAngle)
		}
	}

	// 加入初始延时，确保AGV开始执行导航命令
	log.Printf("等待AGV开始执行导航命令，延时30秒...")
	time.Sleep(30 * time.Second)
	log.Printf("延时结束，开始检查AGV导航状态...")

	timeout := time.After(maxWaitTime)
	ticker := time.NewTicker(checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			return fmt.Errorf("导航超时，超过%v未完成", maxWaitTime)

		case <-ticker.C:
			// 查询AGV当前状态
			err := tm.agvController.QueryStatus()
			if err != nil {
				log.Printf("查询AGV状态失败: %v", err)
				continue
			}

			status := tm.agvController.GetStatus()
			if status == nil {
				log.Printf("获取AGV状态失败")
				continue
			}

			// 输出状态信息（包含LastPointID用于调试，但不作为判断条件）
			log.Printf("AGV状态 - 位置:(%.2f,%.2f,%.1f°) 速度:(%.2f,%.2f,%.2f) 状态:%s 最后点ID:%d",
				status.PosX, status.PosY, status.Angle*180/3.14159,
				status.VelX, status.VelY, status.AngVel,
				status.AGVStateString(), status.LastPointID)

			// 检查速度是否为0（必要条件）
			if abs(status.VelX) < 0.1 && abs(status.VelY) < 0.1 && abs(status.AngVel) < 0.1 {

				// 路径1：状态空闲（始终可用）
				if status.AGVState == 0x00 { // 0x00 = 空闲
					log.Printf("AGV已到达目标点%d并停止（状态空闲）", targetPoint)
					return nil
				}

				// 路径2：物理位置准确（仅在有坐标信息时可用）
				if hasCoordinates && tm.isPhysicallyArrived(status.PosX, status.PosY, status.Angle, targetX, targetY, targetAngle) {
					log.Printf("AGV已到达目标点%d并停止（物理位置准确）", targetPoint)
					log.Printf("目标位置:(%.2f,%.2f,%.1f°) 当前位置:(%.2f,%.2f,%.1f°)",
						targetX, targetY, targetAngle,
						status.PosX, status.PosY, status.Angle*180/3.14159)
					return nil
				}

				// 如果速度为0但未满足到达条件，继续等待
				if hasCoordinates {
					log.Printf("AGV速度为0但未满足到达条件（状态非空闲且物理位置不准确），继续等待...")
				} else {
					log.Printf("AGV速度为0但状态非空闲，继续等待...")
				}
			}

			// 检查是否有导航失败的状态
			if status.AGVState == 0x06 { // 0x06 = 导航失败
				return fmt.Errorf("AGV报告导航失败状态")
			}

			// 检查是否有严重异常
			if status.EventLevel >= 0x03 { // 严重错误级别
				return fmt.Errorf("AGV出现严重异常: 代码0x%04X", status.EventCode)
			}
		}
	}
}

// switchToPLCControl 切换到PLC控制 - 使用正确的地址配置
func (tm *WatchTaskManager) switchToPLCControl(machine string) error {
	log.Printf("开始切换到PLC控制，细纱机: %s", machine)

	// 检查PLC连接状态
	if !tm.plcController.IsConnected() {
		return fmt.Errorf("PLC未连接，无法切换控制权")
	}

	// 步骤1: 查询并写入切换点码值到D604（BADC格式）
	log.Printf("步骤1: 查询细纱机 %s 的切换点码值", machine)
	codeValue, err := tm.getOriginCodeValueByMachine(machine)
	if err != nil {
		return fmt.Errorf("查询切换点码值失败: %v", err)
	}

	log.Printf("步骤1: 写入切换点码值到D604: %f", codeValue)
	err = tm.plcDataHelper.WriteOriginCodeValue(codeValue)
	if err != nil {
		return fmt.Errorf("写入切换点码值到D604失败: %v", err)
	}

	// PLC控制权地址配置（从配置获取）
	config := GetConfig()
	controlRequestAddress := config.PLCAddresses.ControlRequestAddress // M601 - 控制权请求地址
	controlConfirmAddress := config.PLCAddresses.ControlConfirmAddress // M501 - 控制权确认地址

	// 步骤2: 向PLC M601写入控制权请求信号
	log.Printf("步骤2: 向PLC地址 M%d 写入控制权请求信号 = true", controlRequestAddress)
	_, err = tm.plcController.WriteSingleCoil(controlRequestAddress, true)
	if err != nil {
		return fmt.Errorf("写入PLC控制权请求失败: %v", err)
	}

	// 步骤3: 持续等待M501为true（每隔1s读一次，一共读10次）
	log.Printf("步骤3: 等待PLC控制权确认 M%d = true（10次尝试，间隔1s）", controlConfirmAddress)

	for i := 1; i <= 10; i++ {
		data, err := tm.plcController.ReadCoils(controlConfirmAddress, 1)
		if err != nil {
			log.Printf("第%d次读取M%d失败: %v", i, controlConfirmAddress, err)
			if i < 15 {
				time.Sleep(3 * time.Second)
				continue
			}
			return fmt.Errorf("读取PLC控制权确认失败: %v", err)
		}

		if len(data) > 0 && data[0] != 0 {
			log.Printf("步骤3: 第%d次读取成功，M%d = true，控制权切换成功", i, controlConfirmAddress)
			break
		}

		log.Printf("第%d次读取M%d = false，继续等待...", i, controlConfirmAddress)

		if i == 10 {
			return fmt.Errorf("等待控制权确认超时，M%d 在10次尝试后仍为false", controlConfirmAddress)
		}

		time.Sleep(2 * time.Second)
	}

	// 步骤4: 读到M501为true后，往M602写true
	taskStartAddress := config.PLCAddresses.TaskStartAddress // M602
	log.Printf("步骤4: 向PLC地址 M%d 写入任务开始信号 = true", taskStartAddress)
	_, err = tm.plcController.WriteSingleCoil(taskStartAddress, true)
	if err != nil {
		return fmt.Errorf("写入PLC任务开始信号失败: %v", err)
	}

	log.Printf("PLC控制权切换和任务启动完成")
	
	// 通知状态上报器：进入PLC控制（巷道内）
	if globalWorkModeManager != nil {
		if statusReporter := globalWorkModeManager.GetStatusReporter(); statusReporter != nil {
			statusReporter.SetPLCControlStatus(true)
			log.Printf("✅ 已通知状态上报器：进入PLC控制模式（巷道内）")
		}
	}
	
	return nil
}

// releasePLCControl  PLC释放控制权
func (tm *WatchTaskManager) releasePLCControl() error {
	log.Printf("PLC控制工作完成，无需额外释放操作")
	// 根据您的设计，PLC接收到true信号后会自动处理，我们无需额外操作
	
	// 通知状态上报器：退出PLC控制（返回激光导航）
	if globalWorkModeManager != nil {
		if statusReporter := globalWorkModeManager.GetStatusReporter(); statusReporter != nil {
			statusReporter.SetPLCControlStatus(false)
			log.Printf("✅ 已通知状态上报器：退出PLC控制模式（返回激光导航）")
		}
	}
	
	return nil
}

// executeWatchWork 执行看车工作 - for循环模式
// callMachineComplete: 是否在完成时调用NotifyMachineComplete（单侧任务=true，双侧任务=false）
func (tm *WatchTaskManager) executeWatchWork(machine string, callMachineComplete bool) error {
	log.Printf("开始执行细纱机 %s 的看车工作（for循环模式）", machine)

	// 获取排序方向
	order, err := tm.dbService.GetOrderByMachine(machine)
	if err != nil {
		log.Printf("警告：查询机器 %s 排序信息失败: %v，使用默认升序", machine, err)
		order = 1
	}
	isAscending := order == 1
	log.Printf("机器 %s 排序方向: %s", machine, map[bool]string{true: "升序", false: "降序"}[isAscending])

	var watermark int
	isWatermarkSet := false
	processedCount := 0

	for {
		// 步骤A：POST获取最新断头数据
		spindleList, err := tm.getSpindleList(machine)
		if err != nil {
			return fmt.Errorf("步骤A失败 - 获取锭号列表失败: %v", err)
		}
		log.Printf("步骤A: 获取到 %d 个锭号 %v", len(spindleList), spindleList)

		if len(spindleList) == 0 {
			log.Printf("没有锭号数据，看车工作完成，共处理 %d 个锭号", processedCount)

			// 更新进度：准备通知PLC完成
			tm.updateStepProgress(fmt.Sprintf("细纱机 %s 看车工作完成", machine), 95)

			// 只有单侧任务才调用NotifyMachineComplete
			if callMachineComplete {
				// 单侧任务现在调用和R侧相同的完整退出流程
				err := tm.executeRSideCompleteFlow(machine)
				if err != nil {
					return fmt.Errorf("执行完整退出流程失败: %v", err)
				}
				log.Printf("单侧任务 %s 已完成完整退出流程", machine)
			} else {
				log.Printf("双侧任务中的细纱机 %s 看车工作完成，不调用NotifyMachineComplete", machine)
			}

			break
		}

		// 步骤B：根据数据库Order字段排序
		sortedSpindles := tm.sortSpindlesByOrder(spindleList, machine)
		log.Printf("步骤B: 排序后锭号 %v", sortedSpindles)

		// 步骤C：基于水位线过滤锭号
		var filteredSpindles []int
		for _, spindle := range sortedSpindles {
			if !isWatermarkSet {
				// 第一次处理，全部保留
				filteredSpindles = append(filteredSpindles, spindle)
			} else {
				// 根据排序方向和水位线过滤
				if isAscending {
					if spindle > watermark {
						filteredSpindles = append(filteredSpindles, spindle)
					}
				} else {
					if spindle < watermark {
						filteredSpindles = append(filteredSpindles, spindle)
					}
				}
			}
		}
		log.Printf("步骤C: 水位线过滤后剩余 %d 个锭号 %v", len(filteredSpindles), filteredSpindles)

		if len(filteredSpindles) == 0 {
			log.Printf("没有新的锭号需要处理，看车工作完成")

			// 只有单侧任务才调用完整退出流程
			if callMachineComplete {
				// 单侧任务调用和R侧相同的完整退出流程
				err := tm.executeRSideCompleteFlow(machine)
				if err != nil {
					return fmt.Errorf("执行完整退出流程失败: %v", err)
				}
				log.Printf("单侧任务 %s 已完成完整退出流程", machine)
			} else {
				log.Printf("双侧任务中的细纱机 %s 看车工作完成，不调用完整退出流程", machine)
			}

			return nil // 直接返回，避免继续执行到函数结尾
		}

		// 步骤D：取第一个锭号
		currentSpindle := filteredSpindles[0]
		log.Printf("步骤D: 选择锭号 %d 进行处理", currentSpindle)
		updateSpindle(&currentSpindle)
		updateStep(fmt.Sprintf("处理锭号 %d", currentSpindle))

		// 步骤E：查询数据库获取距离码值（float）
		distance, err := tm.getSpindleCodeValue(machine, currentSpindle)
		if err != nil {
			return fmt.Errorf("步骤E失败 - 查询锭号 %d 码值失败: %v", currentSpindle, err)
		}
		taskLogger := logger.GetModuleLogger("task")
		log.Printf("步骤E: 锭号 %d 距离码值 %f", currentSpindle, distance)
		taskLogger.Info("数据库读取值", "spindle", currentSpindle, "distance", distance)

		// 步骤F：查询数据库获取Direction字段（用于PLC方向写入）
		direction, err := tm.getDirectionByMachine(machine)
		if err != nil {
			return fmt.Errorf("步骤F失败 - 查询细纱机 %s Direction字段失败: %v", machine, err)
		}
		log.Printf("步骤F: 细纱机 %s Direction %d (用于PLC方向)", machine, direction)

		// 步骤G：计算皮辊方向（根据锭号范围和奇偶性）
		isLeft, err := tm.plcDataHelper.CalculateRollerDirection(currentSpindle)
		if err != nil {
			return fmt.Errorf("步骤G失败 - 计算锭号 %d 皮辊方向失败: %v", currentSpindle, err)
		}
		log.Printf("步骤G: 锭号 %d 皮辊方向 %s", currentSpindle, map[bool]string{true: "左", false: "右"}[isLeft])

		// 步骤H：PLC数据传输
		spindleData := SpindleData{
			SpindleNo: currentSpindle,
			Distance:  distance,
			IsLeft:    isLeft,
			Direction: direction,
		}
		err = tm.plcDataHelper.SendSpindleDataToPLC(spindleData)
		if err != nil {
			return fmt.Errorf("步骤H失败 - 向PLC发送锭号 %d 数据时出错: %v", currentSpindle, err)
		}
		log.Printf("步骤H: 锭号 %d 数据已发送到PLC", currentSpindle)
		updatePLC("write", "锭号数据", fmt.Sprintf("锭号%d:方向%d,距离%.2f", currentSpindle, direction, distance))

		// 步骤H2：验证PLC数据写入是否正确
		err = tm.plcDataHelper.VerifyPLCData(spindleData)
		if err != nil {
			return fmt.Errorf("步骤H2失败 - 从PLC读取验证锭号 %d 数据时发现不匹配: %v", currentSpindle, err)
		}
		log.Printf("步骤H2: 锭号 %d PLC数据验证成功", currentSpindle)
		taskLogger.Info("数据流转完成", "spindle", currentSpindle, "databaseValue", distance, "writeStatus", "成功", "verifyStatus", "通过")

		// 步骤H3：通知PLC数据验证完成
		err = tm.plcDataHelper.NotifyVerificationComplete()
		if err != nil {
			return fmt.Errorf("步骤H3失败 - 向PLC发送锭号 %d 验证完成信号时出错: %v", currentSpindle, err)
		}
		log.Printf("步骤H3: 锭号 %d 验证完成信号已发送到PLC", currentSpindle)

		// 步骤I：等待PLC完成工作
		err = tm.waitForPLCWorkComplete()
		if err != nil {
			return fmt.Errorf("步骤I失败 - 等待锭号 %d PLC工作完成失败: %v", currentSpindle, err)
		}
		log.Printf("步骤I: 锭号 %d PLC工作完成", currentSpindle)

		// 步骤J：更新水位线
		watermark = currentSpindle
		isWatermarkSet = true
		processedCount++
		log.Printf("步骤J: 锭号 %d 已处理，水位线更新为 %d，总计处理 %d 个", currentSpindle, watermark, processedCount)

		// 步骤K：继续循环
	}

	log.Printf("细纱机 %s 看车工作完成，总共处理了 %d 个锭号", machine, processedCount)

	// 只有单侧任务才调用完整退出流程
	if callMachineComplete {
		// 单侧任务调用和R侧相同的完整退出流程
		err := tm.executeRSideCompleteFlow(machine)
		if err != nil {
			return fmt.Errorf("执行完整退出流程失败: %v", err)
		}
		log.Printf("单侧任务 %s 已完成完整退出流程", machine)
	} else {
		log.Printf("双侧任务中的细纱机 %s 看车工作完成，不调用完整退出流程", machine)
	}

	return nil
}

// getSpindleList 通过POST请求获取细纱机的锭号列表
func (tm *WatchTaskManager) getSpindleList(machine string) ([]int, error) {
	return tm.spindleService.GetSpindleList(machine)
}

// sortSpindlesByOrder 根据Order字段排序锭号
func (tm *WatchTaskManager) sortSpindlesByOrder(spindleList []int, machine string) []int {
	return tm.spindleService.SortSpindlesByOrder(spindleList, machine)
}

// getSpindleCodeValue 从数据库查询单个锭号对应的码值
func (tm *WatchTaskManager) getSpindleCodeValue(machine string, spindleNo int) (float32, error) {
	// 构造查询key：细纱机号+锭号
	spindleKey := machine + strconv.Itoa(spindleNo)
	log.Printf("查询锭号码值，key: %s", spindleKey)

	// 检查数据库服务是否可用
	if tm.dbService == nil {
		return 0, fmt.Errorf("数据库服务未初始化")
	}

	// 通过数据库服务查询SpindleDistance表
	value, err := tm.dbService.GetSpindleDistance(spindleKey)
	if err != nil {
		return 0, err
	}

	log.Printf("锭号 %s 码值: %f", spindleKey, value)
	return value, nil
}

// getDirectionByMachine 从数据库查询细纱机的Direction字段
func (tm *WatchTaskManager) getDirectionByMachine(machine string) (int, error) {
	log.Printf("查询细纱机 %s 的Direction字段", machine)

	// 检查数据库服务是否可用
	if tm.dbService == nil {
		return 0, fmt.Errorf("数据库服务未初始化")
	}

	direction, err := tm.dbService.GetDirectionByMachine(machine)
	if err != nil {
		return 0, err
	}

	log.Printf("细纱机 %s Direction: %d", machine, direction)
	return direction, nil
}

// waitForPLCWorkComplete 等待PLC完成看车工作
func (tm *WatchTaskManager) waitForPLCWorkComplete() error {
	log.Printf("等待PLC完成看车工作")

	// 检查PLC连接状态
	if !tm.plcController.IsConnected() {
		return fmt.Errorf("PLC未连接，无法等待工作完成")
	}

	config := GetConfig()
	workStatusAddress := config.PLCAddresses.WorkStatusAddress // D500地址
	maxWaitTime := config.Timeouts.PLCWorkTimeout              // 从配置获取等待时间
	checkInterval := config.Timeouts.PLCWorkCheckInterval      // 从配置获取检查间隔

	log.Printf("开始轮询PLC D%d地址等待工作完成信号", workStatusAddress)

	timeout := time.After(maxWaitTime)
	ticker := time.NewTicker(checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			return fmt.Errorf("等待PLC工作完成超时，超过%v", maxWaitTime)

		case <-ticker.C:
			// 读取D500寄存器的值
			data, err := tm.plcController.ReadHoldingRegisters(workStatusAddress, 1)
			if err != nil {
				log.Printf("读取PLC D%d失败: %v", workStatusAddress, err)
				continue
			}

			if len(data) < 2 {
				log.Printf("读取PLC D%d返回数据不足", workStatusAddress)
				continue
			}

			// 转换为uint16值
			statusValue := uint16(data[0])<<8 | uint16(data[1])
			log.Printf("读取PLC D%d值: %d", workStatusAddress, statusValue)

			// 判断工作状态
			switch statusValue {
			case 1:
				log.Printf("PLC工作完成 - 处理成功 (D%d = 1)", workStatusAddress)
				return nil
			case 2:
				log.Printf("PLC工作完成 - 处理失败 (D%d = 2)", workStatusAddress)
				return nil
			case 0:
				// 继续等待
				log.Printf("PLC仍在工作中 (D%d = 0)", workStatusAddress)
				continue
			default:
				log.Printf("PLC返回未知状态值: %d，继续等待", statusValue)
				continue
			}
		}
	}
}

// checkRobotRunCondition 检查机器人运行条件
func (tm *WatchTaskManager) checkRobotRunCondition() error {
	log.Printf("发送机器人启动信号")

	// 检查PLC连接状态
	if !tm.plcController.IsConnected() {
		log.Printf("⚠️ PLC未连接，将跳过启动信号发送（开发模式）")
		return nil // 开发模式下允许继续
	}

	// 向PLC机器人启动信号地址写入true
	config := GetConfig()
	robotStartSignalAddress := config.PLCAddresses.RobotStartSignalAddress
	log.Printf("向PLC地址 M%d 写入机器人启动信号 = true", robotStartSignalAddress)

	_, err := tm.plcController.WriteSingleCoil(robotStartSignalAddress, true)
	if err != nil {
		return fmt.Errorf("写入PLC M%d 启动信号失败: %v", robotStartSignalAddress, err)
	}

	log.Printf("机器人启动信号发送成功，M%d = true", robotStartSignalAddress)
	return nil
}

// SetTaskStatusBroadcastCallback 设置任务状态广播回调函数
func SetTaskStatusBroadcastCallback(callback TaskStatusBroadcastFunc) {
	taskStatusBroadcastCallback = callback
}

// SetMaxRetries 设置最大重试次数
func SetMaxRetries(maxRetries int) {
	if globalTaskManager != nil {
		globalTaskManager.mu.Lock()
		globalTaskManager.maxRetries = maxRetries
		globalTaskManager.mu.Unlock()
		log.Printf("最大重试次数已设置为: %d", maxRetries)
	}
}

// GetMaxRetries 获取当前最大重试次数
func GetMaxRetries() int {
	if globalTaskManager != nil {
		globalTaskManager.mu.RLock()
		defer globalTaskManager.mu.RUnlock()
		return globalTaskManager.maxRetries
	}
	return GetConfig().MaxRetries // 从配置获取默认值
}

// GetFailedMachines 获取失败的机器列表（只读副本）
func GetFailedMachines() []string {
	if globalTaskManager != nil {
		globalTaskManager.mu.RLock()
		defer globalTaskManager.mu.RUnlock()

		// 返回副本，避免外部修改
		failed := make([]string, len(globalTaskManager.failedMachines))
		copy(failed, globalTaskManager.failedMachines)
		return failed
	}
	return nil
}

// ClearFailedMachines 清空失败机器列表
func ClearFailedMachines() {
	if globalTaskManager != nil {
		globalTaskManager.mu.Lock()
		defer globalTaskManager.mu.Unlock()
		globalTaskManager.failedMachines = make([]string, 0)
		globalTaskManager.lastError = ""
		log.Printf("失败机器列表已清空")
	}
}

// GetTaskConfig 获取任务配置
func GetTaskConfig() TaskConfig {
	return GetConfig()
}

// UpdateTaskConfig 更新任务配置
func UpdateTaskConfig(config TaskConfig) error {
	// 更新全局配置
	if err := UpdateConfig(config); err != nil {
		return err
	}

	// 如果任务管理器已初始化，更新其中的配置相关字段
	if globalTaskManager != nil {
		globalTaskManager.mu.Lock()
		globalTaskManager.maxRetries = config.MaxRetries
		globalTaskManager.mu.Unlock()
		log.Printf("任务管理器配置已同步更新")
	}

	return nil
}

// updateStepProgress 更新步骤进度
func (tm *WatchTaskManager) updateStepProgress(step string, progress int) {
	tm.mu.Lock()
	tm.currentStep = step
	tm.stepProgress = progress
	tm.mu.Unlock()
	broadcastTaskStatus()
}

// setOriginInfo 设置原点信息（方向和码值）
func (tm *WatchTaskManager) setOriginInfo(machine string) error {
	log.Printf("开始设置细纱机 %s 的原点信息", machine)

	// 步骤1: 查询Direction（复用现有函数）
	direction, err := tm.getDirectionByMachine(machine)
	if err != nil {
		return fmt.Errorf("查询细纱机 %s Direction失败: %v", machine, err)
	}
	log.Printf("细纱机 %s 原点方向: %d", machine, direction)

	// 步骤2: 查询CodeValue（新增函数）
	codeValue, err := tm.getOriginCodeValueByMachine(machine)
	if err != nil {
		return fmt.Errorf("查询细纱机 %s 原点码值失败: %v", machine, err)
	}
	log.Printf("细纱机 %s 原点码值: %f", machine, codeValue)

	// 步骤3: 写入原点方向到M611
	err = tm.plcDataHelper.WriteOriginDirection(direction)
	if err != nil {
		return fmt.Errorf("写入原点方向失败: %v", err)
	}

	// 步骤4: 写入原点码值到D604
	err = tm.plcDataHelper.WriteOriginCodeValue(codeValue)
	if err != nil {
		return fmt.Errorf("写入原点码值失败: %v", err)
	}

	log.Printf("细纱机 %s 原点信息设置完成", machine)

	// 步骤5: 等待AGV到达原点位置
	log.Printf("等待AGV到达原点位置...")
	err = tm.plcDataHelper.waitForOriginArrival()
	if err != nil {
		return fmt.Errorf("等待AGV到达原点位置失败: %v", err)
	}
	log.Printf("AGV已到达原点位置")

	// 步骤6: 请求控制权交接
	log.Printf("请求控制权交接...")
	err = tm.plcDataHelper.requestControlHandover()
	if err != nil {
		return fmt.Errorf("请求控制权交接失败: %v", err)
	}
	log.Printf("控制权交接请求已发送")

	// 步骤7: 等待控制权交接完成
	log.Printf("等待控制权交接完成...")
	err = tm.plcDataHelper.waitForControlHandover()
	if err != nil {
		return fmt.Errorf("等待控制权交接完成失败: %v", err)
	}
	log.Printf("控制权交接完成，AGV控制权已移交给程序")

	// 步骤8: 执行激光重定位
	log.Printf("开始执行激光重定位...")
	err = tm.performLaserRepositioning(machine)
	if err != nil {
		return fmt.Errorf("激光重定位失败: %v", err)
	}
	log.Printf("激光重定位完成")

	log.Printf("细纱机 %s 原点信息设置和控制权交接完成", machine)

	// 步骤9: 完成清理，准备返回主流程
	err = tm.finalizeSingleTask(machine)
	if err != nil {
		return fmt.Errorf("完成单侧任务清理失败: %v", err)
	}

	return nil
}

// performLaserRepositioning 执行激光重定位（带重试机制）
func (tm *WatchTaskManager) performLaserRepositioning(machine string) error {
	log.Printf("开始为机台 %s 执行激光重定位", machine)

	// 步骤1: 查询机台对应的导航点ID
	navInfo, err := tm.dbService.GetNavigationByRobotID(machine)
	if err != nil {
		return fmt.Errorf("查询机台 %s 的导航信息失败: %v", machine, err)
	}

	// 步骤2: 根据导航点ID获取坐标和角度
	x, y, angle, err := tm.dbService.GetNavigationPosition(navInfo.NavigationID)
	if err != nil {
		return fmt.Errorf("查询导航点 %d 的坐标信息失败: %v", navInfo.NavigationID, err)
	}

	log.Printf("机台 %s 的重定位信息 - 导航点ID: %d, 坐标: (%.3f, %.3f), 角度: %.3f°",
		machine, navInfo.NavigationID, x, y, angle)

	// 步骤3: 将角度转换为弧度（AGV控制器需要弧度）
	angleRad := float64(angle) * math.Pi / 180.0

	log.Printf("激光重定位参数 - X: %.3f, Y: %.3f, 角度: %.3f° (%.6f rad)",
		float64(x), float64(y), angle, angleRad)

	// 步骤4: 执行激光重定位，带重试机制
	const maxRetries = 10
	const retryInterval = 4 * time.Second

	for attempt := 1; attempt <= maxRetries; attempt++ {
		log.Printf("机台 %s 激光重定位尝试 %d/%d", machine, attempt, maxRetries)

		err = tm.agvController.InitializeLaserNavigation(float64(x), float64(y), angleRad)
		if err == nil {
			log.Printf("机台 %s 的激光重定位在第 %d 次尝试中成功", machine, attempt)
			return nil
		}

		log.Printf("机台 %s 激光重定位第 %d 次尝试失败: %v", machine, attempt, err)

		// 如果不是最后一次尝试，等待后重试
		if attempt < maxRetries {
			log.Printf("等待 %v 后进行第 %d 次重试...", retryInterval, attempt+1)
			time.Sleep(retryInterval)
		}
	}

	// 所有重试都失败了
	return fmt.Errorf("AGV激光重定位失败，已重试 %d 次，最后错误: %v", maxRetries, err)
}

// getOriginCodeValueByMachine 从数据库查询细纱机的原点码值
func (tm *WatchTaskManager) getOriginCodeValueByMachine(machine string) (float32, error) {
	log.Printf("查询细纱机 %s 的原点码值", machine)

	if tm.dbService == nil {
		return 0, fmt.Errorf("数据库服务未初始化")
	}

	codeValue, err := tm.dbService.GetOriginCodeValueByMachine(machine)
	if err != nil {
		return 0, err
	}

	log.Printf("细纱机 %s 原点码值: %f", machine, codeValue)
	return codeValue, nil
}

// writeRSideOriginInfo 查询并写入R侧码带信息：M611(方向), D604(位置)
func (tm *WatchTaskManager) writeRSideOriginInfo(machine string) error {
	log.Printf("查询并写入细纱机 %s 的R侧码带信息", machine)

	// 查询码带方向
	direction, err := tm.getDirectionByMachine(machine)
	if err != nil {
		return fmt.Errorf("查询码带方向失败: %v", err)
	}

	// 查询码带位置（原点码值）
	codeValue, err := tm.getOriginCodeValueByMachine(machine)
	if err != nil {
		return fmt.Errorf("查询原点码值失败: %v", err)
	}

	// 写入码带方向到M611
	err = tm.writeRSideDirection(direction)
	if err != nil {
		return fmt.Errorf("写入码带方向失败: %v", err)
	}

	// 写入码带位置到D604
	err = tm.writeRSideCodeValue(codeValue)
	if err != nil {
		return fmt.Errorf("写入码带位置失败: %v", err)
	}

	log.Printf("R侧码带信息写入成功: Direction=%d, CodeValue=%f", direction, codeValue)
	return nil
}

// writeRSideDirection 写入R侧码带方向到M611
func (tm *WatchTaskManager) writeRSideDirection(direction int) error {
	config := GetConfig()
	directionAddress := config.PLCAddresses.OriginDirectionAddress // M611
	directionValue := direction == 1                               // 0→false, 1→true

	log.Printf("写入R侧码带方向: M%d = %t (Direction=%d)", directionAddress, directionValue, direction)

	_, err := tm.plcController.WriteSingleCoil(directionAddress, directionValue)
	if err != nil {
		return fmt.Errorf("写入码带方向到M%d失败: %v", directionAddress, err)
	}

	return nil
}

// writeRSideCodeValue 写入R侧码带位置到D604
func (tm *WatchTaskManager) writeRSideCodeValue(codeValue float32) error {
	config := GetConfig()
	codeValueAddress := config.PLCAddresses.OriginCodeValueAddress // D604

	log.Printf("写入R侧码带位置: D%d = %f", codeValueAddress, codeValue)

	// 使用BADC格式转换
	codeValueBytes := tm.plcDataHelper.ConvertFloatToBADC(codeValue)

	// 写入到D604 (占用2个连续寄存器)
	_, err := tm.plcController.WriteMultipleRegisters(codeValueAddress, 2, codeValueBytes)
	if err != nil {
		return fmt.Errorf("写入码带位置到D%d失败: %v", codeValueAddress, err)
	}

	return nil
}

// executeRSideCompleteFlow 执行R侧完成的完整流程
func (tm *WatchTaskManager) executeRSideCompleteFlow(machine string) error {
	log.Printf("开始执行R侧完成流程: %s", machine)

	// 步骤1: 发送R侧完成信号 M606=true, M608=false, M610=false
	log.Printf("步骤1: 发送R侧完成信号")
	err := tm.plcDataHelper.sendRSideCompleteSignals()
	if err != nil {
		return fmt.Errorf("步骤1失败 - 发送R侧完成信号: %v", err)
	}

	// 步骤2: 查询并写入码带信息 M611(方向), D604(位置)
	log.Printf("步骤2: 查询并写入码带信息")
	err = tm.writeRSideOriginInfo(machine)
	if err != nil {
		return fmt.Errorf("步骤2失败 - 写入码带信息: %v", err)
	}

	// 步骤3: 等待AGV到达原点位置 M510=true
	log.Printf("步骤3: 等待AGV到达原点位置")
	err = tm.plcDataHelper.waitForOriginArrival()
	if err != nil {
		return fmt.Errorf("步骤3失败 - 等待AGV到达原点: %v", err)
	}

	// 步骤4: 切换回激光控制 M607=true, 等待M501=false
	log.Printf("步骤4: 切换回激光控制")
	err = tm.plcDataHelper.switchBackToLaserControl()
	if err != nil {
		return fmt.Errorf("步骤4失败 - 切换回激光控制: %v", err)
	}

	// 步骤5: 执行激光重定位
	log.Printf("步骤5: 开始执行激光重定位...")
	err = tm.performLaserRepositioning(machine)
	if err != nil {
		return fmt.Errorf("步骤5失败 - 激光重定位: %v", err)
	}
	log.Printf("步骤5: 激光重定位完成")

	// 🔥 步骤5.5: 发送任务完成通知（仅调度模式）
	if globalWorkModeManager != nil {
		isScheduledMode := globalWorkModeManager.IsScheduledMode()
		log.Printf("🔍 检查工作模式: IsScheduledMode = %v", isScheduledMode)
		
		err = globalWorkModeManager.NotifyTaskComplete(machine)
		log.Printf("🔍 任务完成通知结果: err = %v", err)
		
		if err != nil && isScheduledMode {
			// 调度模式下通知失败阻断流程
			return fmt.Errorf("步骤5.5失败 - 发送任务完成通知: %v", err)
		}
		if err == nil && isScheduledMode {
			log.Printf("✅ 任务完成通知已发送: %s", machine)
			globalWorkModeManager.ClearCurrentTaskId()
			log.Printf("🗑️ 任务完成，已清理TaskId")
			
			// 🔥 等待调度系统的下一步指令
			log.Printf("⏳ 开始等待调度系统下一步指令...")
			waitErr := globalWorkModeManager.WaitForSchedulerInstruction()
			if waitErr != nil {
				log.Printf("❌ 等待调度指令失败: %v", waitErr)
				// 等待失败不阻断流程，返回主流程
			} else {
				log.Printf("✅ 等待调度指令成功完成")
			}
		} else {
			log.Printf("🔍 不满足等待调度指令的条件: err = %v, isScheduledMode = %v", err, isScheduledMode)
		}
	} else {
		log.Printf("⚠️ globalWorkModeManager为nil，跳过任务完成通知")
	}

	// 步骤6: 返回主流程（日志记录）
	log.Printf("步骤6: R侧完成流程执行成功，细纱机 %s 已切换回激光控制并完成重定位，准备返回主流程", machine)
	return nil
}

// finalizeSingleTask 完成单侧任务的清理工作
func (tm *WatchTaskManager) finalizeSingleTask(machine string) error {
	log.Printf("单侧任务 %s 完成，AGV控制权已切换，准备返回主流程", machine)

	// 步骤1: 确认AGV状态正常
	if tm.agvController.IsConnected() {
		err := tm.agvController.QueryStatus()
		if err != nil {
			log.Printf("警告：查询AGV状态失败: %v", err)
		} else {
			status := tm.agvController.GetStatus()
			if status != nil {
				log.Printf("AGV当前状态 - 位置:(%.2f,%.2f) 工作模式:%s",
					status.PosX, status.PosY, status.WorkModeString())
			}
		}
	}

	// 步骤2: 简单状态验证
	log.Printf("细纱机 %s 看车工作已完成，AGV控制权已交接给程序", machine)
	log.Print("即将返回主流程，开始处理下一个任务或导航到下一个位置")

	log.Printf("单侧任务 %s 处理完毕，清理完成", machine)
	return nil
}

// navigateToParking 导航到停车点（从配置读取）
func (tm *WatchTaskManager) navigateToParking() error {
	config := GetConfig()
	parkingPointID := config.ParkingPoint.ID
	if parkingPointID <= 0 {
		parkingPointID = 1 // 默认值
		log.Printf("警告：停车点ID配置无效，使用默认值: %d", parkingPointID)
	}
	log.Printf("开始导航到停车点，停车点ID: %d", parkingPointID)

	// 更新流程状态
	updateStep("所有任务完成，导航到停车点")
	updateNavigation("laser")

	// 检查AGV连接状态
	if !tm.agvController.IsConnected() {
		return fmt.Errorf("AGV未连接，无法执行导航")
	}

	// 检查AGV是否在自动模式
	status := tm.agvController.GetStatus()
	if status != nil && status.WorkMode != 0x03 { // 0x03 = 自动模式
		log.Printf("AGV当前工作模式: %s，尝试切换到自动模式", status.WorkModeString())

		// 自动切换到自动模式
		err := tm.agvController.SwitchWorkMode(true)
		if err != nil {
			return fmt.Errorf("切换AGV到自动模式失败: %v", err)
		}

		// 等待模式切换完成
		time.Sleep(2 * time.Second)

		// 验证模式切换
		err = tm.agvController.QueryStatus()
		if err != nil {
			return fmt.Errorf("查询AGV状态失败: %v", err)
		}

		status = tm.agvController.GetStatus()
		if status == nil || status.WorkMode != 0x03 {
			return fmt.Errorf("AGV模式切换失败，当前模式: %s", status.WorkModeString())
		}
	}

	// 发送导航命令到停车点
	err := tm.agvController.SendNavToPointID(uint16(parkingPointID))
	if err != nil {
		return fmt.Errorf("发送导航到停车点命令失败: %v", err)
	}

	log.Printf("导航命令已发送，目标停车点: %d", parkingPointID)

	// 等待导航完成
	err = tm.waitForNavigationComplete(parkingPointID)
	if err != nil {
		return fmt.Errorf("等待导航到停车点完成失败: %v", err)
	}

	// 更新流程状态
	updateStep("已到达停车点，任务全部完成")
	log.Printf("AGV已成功到达停车点 ID: %d", parkingPointID)

	return nil
}
