package agv_test

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"
)

// WorkflowManager 工作流程管理器
type WorkflowManager struct {
	mu             sync.RWMutex
	testController *AGVTestController
	navigator      *AGVNavigator
	monitor        *AGVMonitor

	// 当前工作流程
	currentWorkflow *WorkflowExecution
	isRunning       bool

	// 工作流程历史
	workflowHistory []WorkflowExecution
	maxHistorySize  int

	// 回调函数
	onWorkflowUpdate func(execution *WorkflowExecution)
	onStepUpdate     func(step *WorkflowStepExecution)
}

// WorkflowExecution 工作流程执行
type WorkflowExecution struct {
	ID          string                  `json:"id"`
	Name        string                  `json:"name"`
	Description string                  `json:"description"`
	Status      WorkflowStatus          `json:"status"`
	StartTime   time.Time               `json:"start_time"`
	EndTime     *time.Time              `json:"end_time,omitempty"`
	Duration    int64                   `json:"duration_ms"`
	Steps       []WorkflowStepExecution `json:"steps"`
	Result      *WorkflowResult         `json:"result,omitempty"`
	Error       string                  `json:"error,omitempty"`
	Config      map[string]interface{}  `json:"config,omitempty"`

	// 内部字段
	ctx         context.Context    `json:"-"`
	cancel      context.CancelFunc `json:"-"`
	currentStep int                `json:"-"`
}

// WorkflowStepExecution 工作流程步骤执行
type WorkflowStepExecution struct {
	ID          int                    `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Status      WorkflowStatus         `json:"status"`
	StartTime   time.Time              `json:"start_time"`
	EndTime     *time.Time             `json:"end_time,omitempty"`
	Duration    int64                  `json:"duration_ms"`
	Result      map[string]interface{} `json:"result,omitempty"`
	Error       string                 `json:"error,omitempty"`
	Retries     int                    `json:"retries"`
	MaxRetries  int                    `json:"max_retries"`
}

// WorkflowStatus 工作流程状态
type WorkflowStatus string

const (
	WorkflowStatusPending   WorkflowStatus = "pending"
	WorkflowStatusRunning   WorkflowStatus = "running"
	WorkflowStatusCompleted WorkflowStatus = "completed"
	WorkflowStatusFailed    WorkflowStatus = "failed"
	WorkflowStatusCancelled WorkflowStatus = "cancelled"
	WorkflowStatusPaused    WorkflowStatus = "paused"
)

// WorkflowTemplate 工作流程模板
type WorkflowTemplate struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Steps       []WorkflowStepTemplate `json:"steps"`
	Config      map[string]interface{} `json:"config,omitempty"`
}

// WorkflowStepTemplate 工作流程步骤模板
type WorkflowStepTemplate struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Type        string                 `json:"type"`
	Config      map[string]interface{} `json:"config,omitempty"`
	MaxRetries  int                    `json:"max_retries"`
}

// NewWorkflowManager 创建工作流程管理器实例
func NewWorkflowManager(controller *AGVTestController, navigator *AGVNavigator, monitor *AGVMonitor) *WorkflowManager {
	return &WorkflowManager{
		testController:  controller,
		navigator:       navigator,
		monitor:         monitor,
		isRunning:       false,
		workflowHistory: make([]WorkflowExecution, 0),
		maxHistorySize:  100,
	}
}

// GetPredefinedWorkflows 获取预定义工作流程
func (wm *WorkflowManager) GetPredefinedWorkflows() []WorkflowTemplate {
	return []WorkflowTemplate{
		{
			Name:        "单点导航测试",
			Description: "测试AGV导航到指定导航点",
			Steps: []WorkflowStepTemplate{
				{
					Name:        "连接检查",
					Description: "检查AGV连接状态",
					Type:        "connection_check",
					MaxRetries:  2,
				},
				{
					Name:        "模式切换",
					Description: "切换到自动模式",
					Type:        "mode_switch",
					Config:      map[string]interface{}{"mode": "auto"},
					MaxRetries:  3,
				},
				{
					Name:        "导航执行",
					Description: "导航到目标点",
					Type:        "navigate_to_point",
					Config:      map[string]interface{}{"point_id": 0}, // 需要在执行时设置
					MaxRetries:  2,
				},
				{
					Name:        "状态验证",
					Description: "验证导航结果",
					Type:        "status_verification",
					MaxRetries:  1,
				},
			},
			Config: map[string]interface{}{
				"timeout_seconds": 300,
			},
		},
		{
			Name:        "机器导航测试",
			Description: "测试AGV导航到指定细纱机位置",
			Steps: []WorkflowStepTemplate{
				{
					Name:        "连接检查",
					Description: "检查AGV连接状态",
					Type:        "connection_check",
					MaxRetries:  2,
				},
				{
					Name:        "数据库查询",
					Description: "查询机器导航点",
					Type:        "query_navigation_point",
					Config:      map[string]interface{}{"machine": ""}, // 需要在执行时设置
					MaxRetries:  2,
				},
				{
					Name:        "模式切换",
					Description: "切换到自动模式",
					Type:        "mode_switch",
					Config:      map[string]interface{}{"mode": "auto"},
					MaxRetries:  3,
				},
				{
					Name:        "导航执行",
					Description: "导航到机器位置",
					Type:        "navigate_to_machine",
					Config:      map[string]interface{}{"machine": ""}, // 需要在执行时设置
					MaxRetries:  2,
				},
				{
					Name:        "状态验证",
					Description: "验证导航结果",
					Type:        "status_verification",
					MaxRetries:  1,
				},
			},
			Config: map[string]interface{}{
				"timeout_seconds": 300,
			},
		},
		{
			Name:        "巷道完整流程测试",
			Description: "测试完整的巷道L侧→调头→R侧流程",
			Steps: []WorkflowStepTemplate{
				{
					Name:        "连接检查",
					Description: "检查AGV连接状态",
					Type:        "connection_check",
					MaxRetries:  2,
				},
				{
					Name:        "导航到L侧",
					Description: "导航到巷道左侧入口",
					Type:        "navigate_to_machine",
					Config:      map[string]interface{}{"machine": ""}, // 左侧机器
					MaxRetries:  2,
				},
				{
					Name:        "L侧处理模拟",
					Description: "模拟左侧处理过程",
					Type:        "simulation_delay",
					Config:      map[string]interface{}{"delay_seconds": 5},
					MaxRetries:  1,
				},
				{
					Name:        "调头模拟",
					Description: "模拟巷道内调头过程",
					Type:        "simulation_delay",
					Config:      map[string]interface{}{"delay_seconds": 3},
					MaxRetries:  1,
				},
				{
					Name:        "导航到R侧",
					Description: "导航到巷道右侧位置",
					Type:        "navigate_to_machine",
					Config:      map[string]interface{}{"machine": ""}, // 右侧机器
					MaxRetries:  2,
				},
				{
					Name:        "R侧处理模拟",
					Description: "模拟右侧处理过程",
					Type:        "simulation_delay",
					Config:      map[string]interface{}{"delay_seconds": 5},
					MaxRetries:  1,
				},
				{
					Name:        "状态验证",
					Description: "验证完整流程结果",
					Type:        "status_verification",
					MaxRetries:  1,
				},
			},
			Config: map[string]interface{}{
				"timeout_seconds": 600,
			},
		},
		{
			Name:        "连续导航测试",
			Description: "测试连续多点导航",
			Steps: []WorkflowStepTemplate{
				{
					Name:        "连接检查",
					Description: "检查AGV连接状态",
					Type:        "connection_check",
					MaxRetries:  2,
				},
				{
					Name:        "多点导航",
					Description: "按序导航到多个点",
					Type:        "multi_point_navigation",
					Config:      map[string]interface{}{"points": []int{}}, // 需要在执行时设置
					MaxRetries:  1,
				},
				{
					Name:        "状态验证",
					Description: "验证所有导航结果",
					Type:        "status_verification",
					MaxRetries:  1,
				},
			},
			Config: map[string]interface{}{
				"timeout_seconds": 900,
			},
		},
		{
			Name:        "模式切换测试",
			Description: "测试AGV工作模式切换功能",
			Steps: []WorkflowStepTemplate{
				{
					Name:        "连接检查",
					Description: "检查AGV连接状态",
					Type:        "connection_check",
					MaxRetries:  2,
				},
				{
					Name:        "切换到手动模式",
					Description: "切换AGV到手动模式",
					Type:        "mode_switch",
					Config:      map[string]interface{}{"mode": "manual"},
					MaxRetries:  3,
				},
				{
					Name:        "验证手动模式",
					Description: "验证模式切换结果",
					Type:        "mode_verification",
					Config:      map[string]interface{}{"expected_mode": "manual"},
					MaxRetries:  1,
				},
				{
					Name:        "切换到自动模式",
					Description: "切换AGV到自动模式",
					Type:        "mode_switch",
					Config:      map[string]interface{}{"mode": "auto"},
					MaxRetries:  3,
				},
				{
					Name:        "验证自动模式",
					Description: "验证模式切换结果",
					Type:        "mode_verification",
					Config:      map[string]interface{}{"expected_mode": "auto"},
					MaxRetries:  1,
				},
			},
			Config: map[string]interface{}{
				"timeout_seconds": 120,
			},
		},
		{
			Name:        "激光导航初始化测试",
			Description: "测试AGV激光导航初始化功能",
			Steps: []WorkflowStepTemplate{
				{
					Name:        "连接检查",
					Description: "检查AGV连接状态",
					Type:        "connection_check",
					MaxRetries:  2,
				},
				{
					Name:        "激光初始化",
					Description: "执行激光导航初始化",
					Type:        "laser_init",
					Config: map[string]interface{}{
						"x":     0.0,
						"y":     0.0,
						"angle": 0.0,
					}, // 需要在执行时设置
					MaxRetries: 2,
				},
				{
					Name:        "状态验证",
					Description: "验证初始化结果",
					Type:        "status_verification",
					MaxRetries:  1,
				},
			},
			Config: map[string]interface{}{
				"timeout_seconds": 180,
			},
		},
	}
}

// StartWorkflow 启动工作流程
func (wm *WorkflowManager) StartWorkflow(template WorkflowTemplate, config map[string]interface{}) (string, error) {
	wm.mu.Lock()
	defer wm.mu.Unlock()

	if wm.isRunning {
		return "", fmt.Errorf("已有工作流程正在运行")
	}

	// 创建工作流程执行实例
	workflowID := fmt.Sprintf("workflow_%d", time.Now().Unix())
	ctx, cancel := context.WithCancel(context.Background())

	execution := &WorkflowExecution{
		ID:          workflowID,
		Name:        template.Name,
		Description: template.Description,
		Status:      WorkflowStatusPending,
		StartTime:   time.Now(),
		Steps:       make([]WorkflowStepExecution, len(template.Steps)),
		Config:      mergeConfig(template.Config, config),
		ctx:         ctx,
		cancel:      cancel,
		currentStep: 0,
	}

	// 初始化步骤
	for i, stepTemplate := range template.Steps {
		execution.Steps[i] = WorkflowStepExecution{
			ID:          i + 1,
			Name:        stepTemplate.Name,
			Description: stepTemplate.Description,
			Status:      WorkflowStatusPending,
			MaxRetries:  stepTemplate.MaxRetries,
		}
	}

	wm.currentWorkflow = execution
	wm.isRunning = true

	// 启动执行协程
	go wm.executeWorkflow(template, execution)

	return workflowID, nil
}

// StopWorkflow 停止当前工作流程
func (wm *WorkflowManager) StopWorkflow() error {
	wm.mu.Lock()
	defer wm.mu.Unlock()

	if !wm.isRunning || wm.currentWorkflow == nil {
		return fmt.Errorf("当前没有运行中的工作流程")
	}

	log.Printf("停止工作流程: %s", wm.currentWorkflow.Name)

	if wm.currentWorkflow.cancel != nil {
		wm.currentWorkflow.cancel()
	}

	wm.currentWorkflow.Status = WorkflowStatusCancelled
	wm.isRunning = false

	return nil
}

// GetCurrentWorkflow 获取当前工作流程
func (wm *WorkflowManager) GetCurrentWorkflow() *WorkflowExecution {
	wm.mu.RLock()
	defer wm.mu.RUnlock()

	if wm.currentWorkflow == nil {
		return nil
	}

	// 返回副本
	return wm.copyWorkflowExecution(wm.currentWorkflow)
}

// GetWorkflowHistory 获取工作流程历史
func (wm *WorkflowManager) GetWorkflowHistory(limit int) []WorkflowExecution {
	wm.mu.RLock()
	defer wm.mu.RUnlock()

	if limit <= 0 || limit > len(wm.workflowHistory) {
		limit = len(wm.workflowHistory)
	}

	// 返回最新的limit条记录
	start := len(wm.workflowHistory) - limit
	if start < 0 {
		start = 0
	}

	result := make([]WorkflowExecution, limit)
	for i, execution := range wm.workflowHistory[start:] {
		result[i] = *wm.copyWorkflowExecution(&execution)
	}

	return result
}

// IsRunning 检查是否有工作流程正在运行
func (wm *WorkflowManager) IsRunning() bool {
	wm.mu.RLock()
	defer wm.mu.RUnlock()
	return wm.isRunning
}

// SetWorkflowUpdateCallback 设置工作流程更新回调
func (wm *WorkflowManager) SetWorkflowUpdateCallback(callback func(execution *WorkflowExecution)) {
	wm.mu.Lock()
	defer wm.mu.Unlock()
	wm.onWorkflowUpdate = callback
}

// SetStepUpdateCallback 设置步骤更新回调
func (wm *WorkflowManager) SetStepUpdateCallback(callback func(step *WorkflowStepExecution)) {
	wm.mu.Lock()
	defer wm.mu.Unlock()
	wm.onStepUpdate = callback
}

// executeWorkflow 执行工作流程
func (wm *WorkflowManager) executeWorkflow(template WorkflowTemplate, execution *WorkflowExecution) {
	defer func() {
		wm.mu.Lock()
		// 工作流程完成，添加到历史记录
		wm.addToHistory(*execution)
		wm.currentWorkflow = nil
		wm.isRunning = false
		wm.mu.Unlock()

		// 触发回调
		if wm.onWorkflowUpdate != nil {
			wm.onWorkflowUpdate(execution)
		}
	}()

	log.Printf("开始执行工作流程: %s", execution.Name)
	execution.Status = WorkflowStatusRunning

	// 触发工作流程开始回调
	if wm.onWorkflowUpdate != nil {
		wm.onWorkflowUpdate(execution)
	}

	// 依次执行每个步骤
	for i, stepTemplate := range template.Steps {
		select {
		case <-execution.ctx.Done():
			log.Printf("工作流程被取消: %s", execution.Name)
			execution.Status = WorkflowStatusCancelled
			return
		default:
		}

		wm.mu.Lock()
		execution.currentStep = i
		wm.mu.Unlock()

		err := wm.executeStep(stepTemplate, &execution.Steps[i], execution)
		if err != nil {
			log.Printf("工作流程步骤失败: %s - %s: %v", execution.Name, stepTemplate.Name, err)
			execution.Status = WorkflowStatusFailed
			execution.Error = err.Error()

			endTime := time.Now()
			execution.EndTime = &endTime
			execution.Duration = endTime.Sub(execution.StartTime).Milliseconds()
			return
		}
	}

	// 工作流程成功完成
	log.Printf("工作流程执行完成: %s", execution.Name)
	execution.Status = WorkflowStatusCompleted

	endTime := time.Now()
	execution.EndTime = &endTime
	execution.Duration = endTime.Sub(execution.StartTime).Milliseconds()
}

// executeStep 执行工作流程步骤
func (wm *WorkflowManager) executeStep(template WorkflowStepTemplate, step *WorkflowStepExecution, execution *WorkflowExecution) error {
	log.Printf("执行步骤: %s", template.Name)

	step.Status = WorkflowStatusRunning
	step.StartTime = time.Now()

	// 触发步骤开始回调
	if wm.onStepUpdate != nil {
		wm.onStepUpdate(step)
	}

	var lastErr error

	// 重试机制
	for attempt := 0; attempt <= template.MaxRetries; attempt++ {
		if attempt > 0 {
			log.Printf("重试步骤 %s，第 %d/%d 次", template.Name, attempt, template.MaxRetries)
			step.Retries = attempt
		}

		var err error

		// 根据步骤类型执行相应操作
		switch template.Type {
		case "connection_check":
			err = wm.executeConnectionCheck()
		case "mode_switch":
			err = wm.executeModeSwitch(template.Config)
		case "navigate_to_point":
			err = wm.executeNavigateToPoint(template.Config, execution.Config)
		case "navigate_to_machine":
			err = wm.executeNavigateToMachine(template.Config, execution.Config)
		case "query_navigation_point":
			err = wm.executeQueryNavigationPoint(template.Config, execution.Config, step)
		case "status_verification":
			err = wm.executeStatusVerification()
		case "simulation_delay":
			err = wm.executeSimulationDelay(template.Config)
		case "multi_point_navigation":
			err = wm.executeMultiPointNavigation(template.Config, execution.Config)
		case "mode_verification":
			err = wm.executeModeVerification(template.Config)
		case "laser_init":
			err = wm.executeLaserInit(template.Config, execution.Config)
		default:
			err = fmt.Errorf("未知的步骤类型: %s", template.Type)
		}

		if err == nil {
			// 步骤成功
			step.Status = WorkflowStatusCompleted
			endTime := time.Now()
			step.EndTime = &endTime
			step.Duration = endTime.Sub(step.StartTime).Milliseconds()

			log.Printf("步骤执行成功: %s", template.Name)

			// 触发步骤完成回调
			if wm.onStepUpdate != nil {
				wm.onStepUpdate(step)
			}

			return nil
		}

		lastErr = err

		// 如果不是最后一次尝试，等待一段时间再重试
		if attempt < template.MaxRetries {
			retryDelay := time.Duration(attempt+1) * 2 * time.Second
			log.Printf("步骤 %s 失败，%v 后重试: %v", template.Name, retryDelay, err)
			time.Sleep(retryDelay)
		}
	}

	// 所有重试都失败了
	step.Status = WorkflowStatusFailed
	step.Error = lastErr.Error()
	endTime := time.Now()
	step.EndTime = &endTime
	step.Duration = endTime.Sub(step.StartTime).Milliseconds()

	log.Printf("步骤执行失败: %s，所有重试已用完: %v", template.Name, lastErr)

	// 触发步骤失败回调
	if wm.onStepUpdate != nil {
		wm.onStepUpdate(step)
	}

	return lastErr
}

// 各种步骤执行方法的实现
func (wm *WorkflowManager) executeConnectionCheck() error {
	if !wm.testController.IsConnected() {
		return fmt.Errorf("AGV未连接")
	}
	return nil
}

func (wm *WorkflowManager) executeModeSwitch(config map[string]interface{}) error {
	mode, ok := config["mode"].(string)
	if !ok {
		return fmt.Errorf("缺少mode配置")
	}

	isAuto := mode == "auto"
	_, err := wm.navigator.SwitchWorkMode(isAuto)
	return err
}

func (wm *WorkflowManager) executeNavigateToPoint(stepConfig, workflowConfig map[string]interface{}) error {
	pointID, ok := getIntFromConfig(stepConfig, workflowConfig, "point_id")
	if !ok {
		return fmt.Errorf("缺少point_id配置")
	}

	_, err := wm.navigator.NavigateToPoint(pointID)
	return err
}

func (wm *WorkflowManager) executeNavigateToMachine(stepConfig, workflowConfig map[string]interface{}) error {
	machine, ok := getStringFromConfig(stepConfig, workflowConfig, "machine")
	if !ok {
		return fmt.Errorf("缺少machine配置")
	}

	_, err := wm.navigator.NavigateToMachine(machine)
	return err
}

func (wm *WorkflowManager) executeQueryNavigationPoint(stepConfig, workflowConfig map[string]interface{}, step *WorkflowStepExecution) error {
	machine, ok := getStringFromConfig(stepConfig, workflowConfig, "machine")
	if !ok {
		return fmt.Errorf("缺少machine配置")
	}

	navPoint, err := wm.testController.QueryNavigationPoint(machine)
	if err != nil {
		return err
	}

	// 将结果保存到步骤结果中
	if step.Result == nil {
		step.Result = make(map[string]interface{})
	}
	step.Result["navigation_point"] = navPoint
	step.Result["machine"] = machine

	return nil
}

func (wm *WorkflowManager) executeStatusVerification() error {
	status, err := wm.navigator.QueryStatus()
	if err != nil {
		return fmt.Errorf("查询AGV状态失败: %v", err)
	}

	if status == nil {
		return fmt.Errorf("AGV状态为空")
	}

	// 可以添加更多验证逻辑
	return nil
}

func (wm *WorkflowManager) executeSimulationDelay(config map[string]interface{}) error {
	delaySeconds, ok := config["delay_seconds"].(int)
	if !ok {
		delaySeconds = 1 // 默认1秒
	}

	log.Printf("模拟延时 %d 秒", delaySeconds)
	time.Sleep(time.Duration(delaySeconds) * time.Second)
	return nil
}

func (wm *WorkflowManager) executeMultiPointNavigation(stepConfig, workflowConfig map[string]interface{}) error {
	points, ok := stepConfig["points"].([]int)
	if !ok {
		// 尝试从工作流程配置获取
		if pointsInterface, exists := workflowConfig["points"]; exists {
			if pointsSlice, ok := pointsInterface.([]int); ok {
				points = pointsSlice
			} else {
				return fmt.Errorf("points配置格式错误")
			}
		} else {
			return fmt.Errorf("缺少points配置")
		}
	}

	for i, point := range points {
		log.Printf("导航到第 %d/%d 个点: %d", i+1, len(points), point)
		_, err := wm.navigator.NavigateToPoint(point)
		if err != nil {
			return fmt.Errorf("导航到点 %d 失败: %v", point, err)
		}
	}

	return nil
}

func (wm *WorkflowManager) executeModeVerification(config map[string]interface{}) error {
	expectedMode, ok := config["expected_mode"].(string)
	if !ok {
		return fmt.Errorf("缺少expected_mode配置")
	}

	status, err := wm.navigator.QueryStatus()
	if err != nil {
		return fmt.Errorf("查询AGV状态失败: %v", err)
	}

	actualMode := "unknown"
	if status != nil {
		switch status.WorkMode {
		case 0x01:
			actualMode = "manual"
		case 0x03:
			actualMode = "auto"
		}
	}

	if actualMode != expectedMode {
		return fmt.Errorf("模式验证失败: 期望%s，实际%s", expectedMode, actualMode)
	}

	return nil
}

func (wm *WorkflowManager) executeLaserInit(stepConfig, workflowConfig map[string]interface{}) error {
	x, okX := getFloatFromConfig(stepConfig, workflowConfig, "x")
	y, okY := getFloatFromConfig(stepConfig, workflowConfig, "y")
	angle, okA := getFloatFromConfig(stepConfig, workflowConfig, "angle")

	if !okX || !okY || !okA {
		return fmt.Errorf("缺少x, y, angle配置")
	}

	_, err := wm.navigator.LaserNavInit(x, y, angle)
	return err
}

// 辅助函数
func mergeConfig(base, override map[string]interface{}) map[string]interface{} {
	result := make(map[string]interface{})

	// 复制base配置
	for k, v := range base {
		result[k] = v
	}

	// 覆盖配置
	for k, v := range override {
		result[k] = v
	}

	return result
}

func getStringFromConfig(stepConfig, workflowConfig map[string]interface{}, key string) (string, bool) {
	if val, ok := stepConfig[key].(string); ok && val != "" {
		return val, true
	}
	if val, ok := workflowConfig[key].(string); ok && val != "" {
		return val, true
	}
	return "", false
}

func getIntFromConfig(stepConfig, workflowConfig map[string]interface{}, key string) (int, bool) {
	if val, ok := stepConfig[key].(int); ok {
		return val, true
	}
	if val, ok := workflowConfig[key].(int); ok {
		return val, true
	}
	return 0, false
}

func getFloatFromConfig(stepConfig, workflowConfig map[string]interface{}, key string) (float64, bool) {
	if val, ok := stepConfig[key].(float64); ok {
		return val, true
	}
	if val, ok := workflowConfig[key].(float64); ok {
		return val, true
	}
	return 0, false
}

func (wm *WorkflowManager) copyWorkflowExecution(src *WorkflowExecution) *WorkflowExecution {
	dst := *src
	dst.Steps = make([]WorkflowStepExecution, len(src.Steps))
	copy(dst.Steps, src.Steps)

	// 清除内部字段
	dst.ctx = nil
	dst.cancel = nil

	return &dst
}

func (wm *WorkflowManager) addToHistory(execution WorkflowExecution) {
	// 清除内部字段
	execution.ctx = nil
	execution.cancel = nil

	wm.workflowHistory = append(wm.workflowHistory, execution)

	// 限制历史记录大小
	if len(wm.workflowHistory) > wm.maxHistorySize {
		wm.workflowHistory = wm.workflowHistory[1:]
	}
}
