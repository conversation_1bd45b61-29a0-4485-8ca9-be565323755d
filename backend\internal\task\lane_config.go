package task

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"path/filepath"
	"strconv"
	"strings"
)

// SpecialLaneConfig 特殊巷道配置
// 用于配置不符合默认规则的特殊细纱机位置
type SpecialLaneConfig struct {
	Machine   string                 `json:"machine"`   // 细纱机编号，如"61R"
	LaneType  string                 `json:"type"`      // 巷道类型：central_aisle/skip/boundary
	Strategy  string                 `json:"strategy"`  // 处理策略：single_only/skip/custom
	Reason    string                 `json:"reason"`    // 特殊处理原因说明
	ExtraData map[string]interface{} `json:"extraData"` // 扩展数据，预留给未来使用
}

// LaneRules 巷道配对规则配置
type LaneRules struct {
	DefaultPairing string `json:"default_pairing"` // 默认配对规则，如"nL_with_n-1R"
	EntrySide      string `json:"entry_side"`      // 默认进入侧，通常是"L"
}

// LaneConfiguration 完整的巷道配置
type LaneConfiguration struct {
	SpecialLanes []SpecialLaneConfig `json:"special_lanes"` // 特殊巷道配置列表
	LaneRules    LaneRules           `json:"lane_rules"`    // 巷道规则配置
}

// 全局配置变量
var (
	globalLaneConfig *LaneConfiguration
	specialLaneMap   map[string]*SpecialLaneConfig // 用于快速查找特殊配置
)

// LoadLaneConfiguration 加载巷道配置
// 从配置文件中加载特殊巷道配置和规则
func LoadLaneConfiguration() error {
	// 从task_config.json中加载配置
	configPath := filepath.Join("backend", "data", "task_config.json")
	data, err := ioutil.ReadFile(configPath)
	if err != nil {
		// 如果配置文件不存在，使用默认配置
		log.Printf("巷道配置文件不存在，使用默认配置: %v", err)
		globalLaneConfig = getDefaultLaneConfig()
		buildSpecialLaneMap()
		return nil
	}

	// 解析配置
	var config struct {
		LaneConfiguration LaneConfiguration `json:"lane_configuration"`
	}
	if err := json.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("解析巷道配置失败: %v", err)
	}

	globalLaneConfig = &config.LaneConfiguration
	buildSpecialLaneMap()

	log.Printf("巷道配置加载成功，特殊配置数量: %d", len(globalLaneConfig.SpecialLanes))
	return nil
}

// getDefaultLaneConfig 获取默认巷道配置
func getDefaultLaneConfig() *LaneConfiguration {
	return &LaneConfiguration{
		SpecialLanes: []SpecialLaneConfig{
			{
				Machine:  "61R",
				LaneType: "central_aisle",
				Strategy: "single_only",
				Reason:   "中央通道位置，只处理单侧",
			},
			{
				Machine:  "62L",
				LaneType: "skip",
				Strategy: "skip",
				Reason:   "位置原因放弃处理",
			},
		},
		LaneRules: LaneRules{
			DefaultPairing: "nL_with_n-1R",
			EntrySide:      "L",
		},
	}
}

// buildSpecialLaneMap 构建特殊配置的快速查找映射
func buildSpecialLaneMap() {
	specialLaneMap = make(map[string]*SpecialLaneConfig)
	for i := range globalLaneConfig.SpecialLanes {
		config := &globalLaneConfig.SpecialLanes[i]
		specialLaneMap[config.Machine] = config
	}
}

// GetSpecialLaneConfig 获取特殊巷道配置
// 如果不是特殊配置，返回nil
func GetSpecialLaneConfig(machine string) *SpecialLaneConfig {
	if specialLaneMap == nil {
		LoadLaneConfiguration()
	}
	return specialLaneMap[machine]
}

// IsSpecialCase 检查是否是特殊配置的细纱机
func IsSpecialCase(machine string) bool {
	return GetSpecialLaneConfig(machine) != nil
}

// ShouldSkip 检查该细纱机是否应该跳过
func ShouldSkip(machine string) bool {
	config := GetSpecialLaneConfig(machine)
	return config != nil && config.Strategy == "skip"
}

// IsSingleOnly 检查该细纱机是否只能单侧处理
func IsSingleOnly(machine string) bool {
	config := GetSpecialLaneConfig(machine)
	return config != nil && config.Strategy == "single_only"
}

// ParseMachine 解析细纱机编号
// 输入："61L" 或 "61R"
// 输出：机器号(61) 和 侧别("L" 或 "R")
func ParseMachine(machine string) (int, string, error) {
	if len(machine) < 2 {
		return 0, "", fmt.Errorf("无效的细纱机编号: %s", machine)
	}

	// 获取最后一个字符作为侧别
	side := string(machine[len(machine)-1])
	if side != "L" && side != "R" {
		return 0, "", fmt.Errorf("无效的侧别: %s", side)
	}

	// 解析数字部分
	numberStr := machine[:len(machine)-1]
	number, err := strconv.Atoi(numberStr)
	if err != nil {
		return 0, "", fmt.Errorf("解析细纱机编号失败: %s", machine)
	}

	return number, side, nil
}

// FindLanePair 根据规则查找巷道配对
// 返回：左侧机器，右侧机器，是否组成巷道
func FindLanePair(machine string) (leftSide, rightSide string, isLane bool) {
	// 先检查是否是特殊配置
	if IsSingleOnly(machine) || ShouldSkip(machine) {
		return machine, "", false
	}

	// 解析机器编号
	number, side, err := ParseMachine(machine)
	if err != nil {
		log.Printf("解析细纱机编号失败: %v", err)
		return machine, "", false
	}

	// 根据默认规则计算配对
	// 默认规则：nL 与 (n-1)R 组成巷道
	if globalLaneConfig.LaneRules.DefaultPairing == "nL_with_n-1R" {
		if side == "L" {
			leftSide = machine                       // 61L
			rightSide = fmt.Sprintf("%dR", number-1) // 60R
		} else { // side == "R"
			leftSide = fmt.Sprintf("%dL", number+1) // 62L
			rightSide = machine                     // 61R
		}
	} else {
		// 未来可以支持其他配对规则
		log.Printf("未知的配对规则: %s", globalLaneConfig.LaneRules.DefaultPairing)
		return machine, "", false
	}

	// 检查配对的另一侧是否也是特殊配置
	if side == "L" && (IsSingleOnly(rightSide) || ShouldSkip(rightSide)) {
		return machine, "", false
	}
	if side == "R" && (IsSingleOnly(leftSide) || ShouldSkip(leftSide)) {
		return machine, "", false
	}

	return leftSide, rightSide, true
}

// GetLaneEntryMachine 获取巷道的入口侧细纱机
// 根据配置的entry_side决定从哪一侧进入巷道
func GetLaneEntryMachine(leftMachine, rightMachine string) string {
	if globalLaneConfig.LaneRules.EntrySide == "L" {
		return leftMachine
	}
	return rightMachine
}

// FormatMachine 格式化细纱机编号
// 确保编号格式的一致性
func FormatMachine(number int, side string) string {
	return fmt.Sprintf("%d%s", number, strings.ToUpper(side))
}
