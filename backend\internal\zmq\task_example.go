package zmq

import (
	"encoding/json"
	"log"
)

// ExampleTaskMessages 展示任务消息的使用示例
func ExampleTaskMessages() {
	// 加载系统配置
	sysConfig, err := LoadSystemConfig("../../data/task_config.json")
	if err != nil {
		sysConfig = DefaultSystemConfig()
	}

	// 1. 创建任务开始消息
	taskStartMsg := NewTaskStartMessage(sysConfig.RobotNo, "TASK_001", "开始巡检任务")
	data, _ := json.Marshal(taskStartMsg)
	log.Printf("任务开始消息: %s", string(data))

	// 2. 创建任务暂停消息
	taskPauseMsg := NewTaskPauseMessage(sysConfig.RobotNo, "TASK_001", "用户请求暂停")
	data, _ = json.Marshal(taskPauseMsg)
	log.Printf("任务暂停消息: %s", string(data))

	// 3. 创建任务完成消息
	taskCompleteMsg := NewTaskCompleteMessage(sysConfig.RobotNo, "TASK_001", "任务执行完成")
	data, _ = json.Marshal(taskCompleteMsg)
	log.Printf("任务完成消息: %s", string(data))

	// 4. 创建任务强制结束消息
	taskForceStopMsg := NewTaskForceStopMessage(sysConfig.RobotNo, "TASK_001", "紧急情况强制停止")
	data, _ = json.Marshal(taskForceStopMsg)
	log.Printf("任务强制结束消息: %s", string(data))
}

// ExampleParseTaskMessage 展示如何解析任务消息
func ExampleParseTaskMessage() {
	// 模拟接收到的任务消息JSON
	receivedData := `{
		"instruction": 110,
		"timeStamp": "2025-07-22T17:30:00Z",
		"code": true,
		"message": "",
		"content": {
			"robotNo": "AGV001",
			"taskId": "TASK_002",
			"remark": "执行维护任务"
		}
	}`

	// 解析消息
	var msg Message
	if err := json.Unmarshal([]byte(receivedData), &msg); err != nil {
		log.Printf("解析消息失败: %v", err)
		return
	}

	// 根据指令类型处理
	switch msg.Instruction {
	case InstructionTaskStart:
		handleTaskStart(msg)
	case InstructionTaskPause:
		handleTaskPause(msg)
	case InstructionTaskComplete:
		handleTaskComplete(msg)
	case InstructionTaskForceStop:
		handleTaskForceStop(msg)
	default:
		log.Printf("未知指令类型: %d", msg.Instruction)
	}
}

// handleTaskStart 处理任务开始
func handleTaskStart(msg Message) {
	content, ok := msg.Content.(map[string]interface{})
	if !ok {
		log.Printf("任务开始消息内容格式错误")
		return
	}

	robotNo := content["robotNo"].(string)
	taskId := content["taskId"].(string)
	remark := content["remark"].(string)

	log.Printf("收到任务开始指令 - 机器人: %s, 任务ID: %s, 备注: %s", robotNo, taskId, remark)

	// 在这里添加任务开始的业务逻辑
	// 例如：启动相关的AGV控制流程
}

// handleTaskPause 处理任务暂停
func handleTaskPause(msg Message) {
	content, ok := msg.Content.(map[string]interface{})
	if !ok {
		log.Printf("任务暂停消息内容格式错误")
		return
	}

	robotNo := content["robotNo"].(string)
	taskId := content["taskId"].(string)
	remark := content["remark"].(string)

	log.Printf("收到任务暂停指令 - 机器人: %s, 任务ID: %s, 备注: %s", robotNo, taskId, remark)

	// 在这里添加任务暂停的业务逻辑
}

// handleTaskComplete 处理任务完成
func handleTaskComplete(msg Message) {
	content, ok := msg.Content.(map[string]interface{})
	if !ok {
		log.Printf("任务完成消息内容格式错误")
		return
	}

	robotNo := content["robotNo"].(string)
	taskId := content["taskId"].(string)
	remark := content["remark"].(string)

	log.Printf("收到任务完成指令 - 机器人: %s, 任务ID: %s, 备注: %s", robotNo, taskId, remark)

	// 在这里添加任务完成的业务逻辑
}

// handleTaskForceStop 处理任务强制结束
func handleTaskForceStop(msg Message) {
	content, ok := msg.Content.(map[string]interface{})
	if !ok {
		log.Printf("任务强制结束消息内容格式错误")
		return
	}

	robotNo := content["robotNo"].(string)
	taskId := content["taskId"].(string)
	remark := content["remark"].(string)

	log.Printf("收到任务强制结束指令 - 机器人: %s, 任务ID: %s, 备注: %s", robotNo, taskId, remark)

	// 在这里添加任务强制结束的业务逻辑
	// 例如：立即停止AGV运动，清理相关状态
}
