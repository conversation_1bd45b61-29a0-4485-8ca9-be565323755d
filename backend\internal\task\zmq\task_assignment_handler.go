package zmq

import (
	"fmt"
	"log"

	"github.com/user/agv_nav/internal/zmq"
)


// WorkModeManager 接口，避免循环导入
type WorkModeManager interface {
	SetCurrentTaskId(taskId string)
	GetCurrentTaskId() string
	IsWaitingForSchedulerInstruction() bool
	IsScheduledMode() bool
}

// TaskAssignmentHandler 任务分配处理器，处理调度系统的任务下发（指令200）
type TaskAssignmentHandler struct {
	*BaseHandler
	systemConfig    *zmq.SystemConfig
	startTaskFunc   func([]string) error // 回调函数，用于调用StartTask
	workModeManager WorkModeManager      // 工作模式管理器，用于保存TaskId
	zmqManager      *ZMQManager          // ZMQ管理器，用于访问状态上报器
}

// NewTaskAssignmentHandler 创建任务分配处理器
func NewTaskAssignmentHandler(systemConfig *zmq.SystemConfig, startTaskFunc func([]string) error) *TaskAssignmentHandler {
	return &TaskAssignmentHandler{
		BaseHandler:     NewBaseHandler(zmq.InstructionTaskAssignment, "任务分配处理器", systemConfig.RobotNo),
		systemConfig:    systemConfig,
		startTaskFunc:   startTaskFunc,
		workModeManager: nil, // 后续通过SetWorkModeManager设置
		zmqManager:      nil, // 后续通过SetZMQManager设置
	}
}

// SetWorkModeManager 设置工作模式管理器
func (h *TaskAssignmentHandler) SetWorkModeManager(manager WorkModeManager) {
	h.workModeManager = manager
	log.Printf("💼 WorkModeManager set for TaskAssignmentHandler")
}

// SetZMQManager 设置ZMQ管理器
func (h *TaskAssignmentHandler) SetZMQManager(zmqManager *ZMQManager) {
	h.zmqManager = zmqManager
	log.Printf("📡 ZMQManager set for TaskAssignmentHandler")
}

// HandleInstruction 处理任务分配指令
func (h *TaskAssignmentHandler) HandleInstruction(message *zmq.Message) (*zmq.Message, error) {
	// 验证消息
	if err := h.ValidateMessage(message); err != nil {
		return h.createErrorResponse(err.Error()), nil
	}

	// 序列化消息以便打印
	messageBytes, _ := zmq.SerializeMessage(message)
	log.Printf("📥 [任务分配] 收到任务下发消息")
	log.Printf("   - 指令码: %d", message.Instruction)
	log.Printf("   - 时间戳: %v", message.TimeStamp)
	log.Printf("   - 完整消息: %s", string(messageBytes))

	h.LogReceived(message)

	// 解析任务内容
	taskInfo, err := h.parseTaskContent(message.Content)
	if err != nil {
		log.Printf("❌ [任务分配] 解析任务内容失败: %v", err)
		return h.createErrorResponse(fmt.Sprintf("解析任务内容失败: %v", err)), nil
	}

	log.Printf("🎯 [任务分配] 任务信息解析成功")
	log.Printf("   - 任务ID: %s", taskInfo.TaskId)
	log.Printf("   - 机器人: %s", taskInfo.RobotNo)
	log.Printf("   - 车道列表: %v", taskInfo.LaneNos)
	log.Printf("   - 备注: %s", taskInfo.Remark)

	// 验证机器人编号
	if taskInfo.RobotNo != h.systemConfig.RobotNo {
		log.Printf("❌ [任务分配] 机器人编号不匹配，期望: %s, 实际: %s",
			h.systemConfig.RobotNo, taskInfo.RobotNo)
		return h.createRobotMismatchResponse(), nil
	}

	// 🔥 检查是否正在等待调度指令，如果是则忽略200指令
	if h.workModeManager != nil && h.workModeManager.IsWaitingForSchedulerInstruction() {
		log.Printf("⏭️ [任务分配] AGV正在等待调度指令，忽略200指令")
		log.Printf("   - 被忽略的任务ID: %s", taskInfo.TaskId)
		log.Printf("   - 被忽略的车道列表: %v", taskInfo.LaneNos)
		
		// 通知状态上报器：忽略的任务不应影响状态
		if h.zmqManager != nil {
			if statusReporter := h.zmqManager.GetStatusReporter(); statusReporter != nil {
				// 不调用OnNewTaskStarted，保持当前状态
				log.Printf("📊 [任务分配] 忽略任务，状态上报器保持当前状态")
			}
		}
		
		return h.createIgnoreResponse("AGV正在等待调度指令，忽略任务下发"), nil
	}

	// 🔥 新增：检查工作模式，手动模式下拒绝调度任务
	if h.workModeManager != nil && !h.workModeManager.IsScheduledMode() {
		log.Printf("⏭️ [任务分配] 当前为手动模式，拒绝调度任务")
		log.Printf("   - 被拒绝的任务ID: %s", taskInfo.TaskId)
		log.Printf("   - 被拒绝的车道列表: %v", taskInfo.LaneNos)
		
		// 通知状态上报器：拒绝的任务不应影响状态
		if h.zmqManager != nil {
			if statusReporter := h.zmqManager.GetStatusReporter(); statusReporter != nil {
				// 不调用OnNewTaskStarted，保持当前状态
				log.Printf("📊 [任务分配] 拒绝任务，状态上报器保持当前状态")
			}
		}
		
		return h.createIgnoreResponse("当前为手动模式，不接受调度任务"), nil
	}

	// 🔥 保存TaskId到工作模式管理器
	if h.workModeManager != nil {
		h.workModeManager.SetCurrentTaskId(taskInfo.TaskId)
		log.Printf("💾 [任务分配] TaskId已保存: %s", taskInfo.TaskId)
	} else {
		log.Printf("⚠️ [任务分配] WorkModeManager未设置，无法保存TaskId")
	}

	// 🔥 通知状态上报器：收到调度指令和新任务开始
	if h.zmqManager != nil {
		if statusReporter := h.zmqManager.GetStatusReporter(); statusReporter != nil {
			statusReporter.OnSchedulerInstructionReceived(200) // 任务下发指令
			statusReporter.OnNewTaskStarted()
			log.Printf("✅ [任务分配] 已通知状态上报器：收到指令200和新任务开始")
		}
	}

	// 异步启动任务，不阻塞回复
	go h.executeTask(taskInfo)

	// 立即返回成功确认
	reply := h.createSuccessResponse("Task assignment accepted")

	// 序列化回复以便打印
	replyBytes, _ := zmq.SerializeMessage(reply)
	log.Printf("📤 [任务分配] 发送确认回复")
	log.Printf("   - 指令码: %d", reply.Instruction)
	log.Printf("   - 状态码: %v", reply.Code)
	log.Printf("   - 消息: %s", reply.Message)
	log.Printf("   - 完整回复: %s", string(replyBytes))

	h.LogCompleted()
	return reply, nil
}

// parseTaskContent 解析任务内容
func (h *TaskAssignmentHandler) parseTaskContent(content interface{}) (*TaskInfo, error) {
	if content == nil {
		return nil, fmt.Errorf("任务内容为空")
	}

	contentMap, ok := content.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("任务内容格式错误")
	}

	taskInfo := &TaskInfo{}

	// 解析robotNo（必需）
	if robotNo, exists := contentMap["robotNo"]; exists {
		if robotNoStr, ok := robotNo.(string); ok {
			taskInfo.RobotNo = robotNoStr
		} else {
			return nil, fmt.Errorf("robotNo字段类型错误")
		}
	} else {
		return nil, fmt.Errorf("缺少robotNo字段")
	}

	// 解析taskId（必需）
	if taskId, exists := contentMap["taskId"]; exists {
		if taskIdStr, ok := taskId.(string); ok {
			taskInfo.TaskId = taskIdStr
		} else {
			return nil, fmt.Errorf("taskId字段类型错误")
		}
	} else {
		return nil, fmt.Errorf("缺少taskId字段")
	}

	// 解析laneNos（必需）
	if laneNos, exists := contentMap["laneNos"]; exists {
		if laneNosArray, ok := laneNos.([]interface{}); ok {
			taskInfo.LaneNos = make([]string, 0, len(laneNosArray))
			for _, lane := range laneNosArray {
				if laneStr, ok := lane.(string); ok {
					taskInfo.LaneNos = append(taskInfo.LaneNos, laneStr)
				}
			}
		} else {
			return nil, fmt.Errorf("laneNos字段类型错误")
		}
	} else {
		return nil, fmt.Errorf("缺少laneNos字段")
	}

	// 解析remark（可选）
	if remark, exists := contentMap["remark"]; exists {
		if remarkStr, ok := remark.(string); ok {
			taskInfo.Remark = remarkStr
		}
	}

	return taskInfo, nil
}

// executeTask 异步执行任务
func (h *TaskAssignmentHandler) executeTask(taskInfo *TaskInfo) {
	log.Printf("🚀 [任务分配] 异步启动StartTask")
	log.Printf("   - 任务ID: %s", taskInfo.TaskId)
	log.Printf("   - 车道列表: %v", taskInfo.LaneNos)

	// 调用现有的StartTask函数，laneNos就像UI界面勾选的机器列表
	err := h.startTaskFunc(taskInfo.LaneNos)
	if err != nil {
		log.Printf("❌ [任务分配] StartTask执行失败: %v", err)
		log.Printf("   - 任务ID: %s", taskInfo.TaskId)
		log.Printf("   - 错误详情: %v", err)
		// 任务失败处理（后续步骤可能会发送指令131）
	} else {
		log.Printf("✅ [任务分配] StartTask执行完成")
		log.Printf("   - 任务ID: %s", taskInfo.TaskId)
		// 任务完成处理（后续步骤可能会发送指令130）
	}
}

// createSuccessResponse 创建成功响应（指令1）
func (h *TaskAssignmentHandler) createSuccessResponse(message string) *zmq.Message {
	reply := zmq.NewMessage(1, true, nil)
	reply.Message = message
	return reply
}

// createErrorResponse 创建错误响应（指令-99）
func (h *TaskAssignmentHandler) createErrorResponse(errorMsg string) *zmq.Message {
	reply := zmq.NewMessage(-99, true, nil)
	reply.Message = errorMsg
	return reply
}

// createRobotMismatchResponse 创建机器人编号不匹配响应（指令-99）
func (h *TaskAssignmentHandler) createRobotMismatchResponse() *zmq.Message {
	reply := zmq.NewMessage(-99, true, nil)
	reply.Message = "非本机任务"
	return reply
}

// createIgnoreResponse 创建忽略响应（指令1）
func (h *TaskAssignmentHandler) createIgnoreResponse(message string) *zmq.Message {
	reply := zmq.NewMessage(1, true, nil)
	reply.Message = message
	return reply
}

// GetStatus 获取任务处理器状态
func (h *TaskAssignmentHandler) GetStatus() map[string]interface{} {
	return map[string]interface{}{
		"robotNo":     h.robotNo,
		"description": h.GetDescription(),
	}
}