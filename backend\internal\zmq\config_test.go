package zmq

import (
	"os"
	"testing"
)

func TestLoadSystemConfig(t *testing.T) {
	// 测试从实际配置文件加载
	configPath := "../../data/task_config.json"

	// 检查文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		t.<PERSON>("Config file not found at %s, skipping test", configPath)
		return
	}

	config, err := LoadSystemConfig(configPath)
	if err != nil {
		t.Fatalf("LoadSystemConfig failed: %v", err)
	}

	// 验证配置值
	if config.RobotNo == "" {
		t.Error("RobotNo should not be empty")
	}

	if config.RobotNo != "AGV001" {
		t.<PERSON>rrorf("Expected RobotNo to be AGV001, got %s", config.RobotNo)
	}

	if config.Description == "" {
		t.<PERSON>r("Description should not be empty")
	}

	t.Logf("Loaded config: RobotNo=%s, Description=%s", config.RobotNo, config.Description)
}

func TestDefaultSystemConfig(t *testing.T) {
	config := DefaultSystemConfig()

	if config.RobotNo != "AGV001" {
		t.Errorf("Expected default RobotNo to be AGV001, got %s", config.RobotNo)
	}

	if config.Description != "AGV Navigation Control System" {
		t.Errorf("Expected default description, got %s", config.Description)
	}
}

func TestLoadSystemConfig_FileNotFound(t *testing.T) {
	_, err := LoadSystemConfig("non_existent_file.json")
	if err == nil {
		t.Error("Expected error for non-existent file")
	}
}

func TestLoadSystemConfig_InvalidJSON(t *testing.T) {
	// 创建临时文件with无效JSON
	tmpfile, err := os.CreateTemp("", "invalid_config*.json")
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove(tmpfile.Name())

	// 写入无效JSON
	if _, err := tmpfile.Write([]byte("invalid json")); err != nil {
		t.Fatal(err)
	}
	tmpfile.Close()

	_, err = LoadSystemConfig(tmpfile.Name())
	if err == nil {
		t.Error("Expected error for invalid JSON")
	}
}
