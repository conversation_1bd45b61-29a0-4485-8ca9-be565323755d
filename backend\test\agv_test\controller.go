package agv_test

import (
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/user/agv_nav/internal/database"
	"github.com/user/agv_nav/pkg/agv"
)

// AGVTestController AGV测试控制器
type AGVTestController struct {
	mu            sync.RWMutex
	agvController *agv.Controller        // AGV控制器实例
	dbService     *database.NavigationDB // 数据库服务（复用主程序）
	isRunning     bool                   // 是否正在运行测试

	// 状态信息
	connectionStatus bool
	lastError        string
	testResults      []TestResult

	// 配置信息
	config *AGVTestConfig
}

// AGVTestConfig AGV测试配置
type AGVTestConfig struct {
	// AGV连接配置
	IP       string `json:"ip"`
	Port     string `json:"port"`
	AuthCode string `json:"auth_code"`

	// 测试配置
	TimeoutSeconds   int  `json:"timeout_seconds"`
	EnableAutoStatus bool `json:"enable_auto_status"`
	StatusInterval   int  `json:"status_interval_ms"`
}

// TestResult 测试结果
type TestResult struct {
	ID          string                 `json:"id"`
	TestType    string                 `json:"test_type"`
	Description string                 `json:"description"`
	Success     bool                   `json:"success"`
	Message     string                 `json:"message"`
	StartTime   time.Time              `json:"start_time"`
	EndTime     time.Time              `json:"end_time"`
	Duration    int64                  `json:"duration_ms"`
	Data        map[string]interface{} `json:"data,omitempty"`
}

// NewAGVTestController 创建AGV测试控制器实例
func NewAGVTestController(agvController *agv.Controller) (*AGVTestController, error) {
	log.Printf("正在初始化AGV测试控制器...")

	if agvController == nil {
		return nil, fmt.Errorf("AGV控制器不能为空")
	}

	// 初始化数据库连接（复用主程序的数据库服务）
	dbService, err := database.NewNavigationDB()
	if err != nil {
		log.Printf("警告：初始化数据库连接失败: %v", err)
		dbService = nil
	}

	// 默认配置
	defaultConfig := &AGVTestConfig{
		IP:               "***************",
		Port:             "17804",
		AuthCode:         "53E3F5B4C8D6E7A2F9B1C4D5E8A7B3F6",
		TimeoutSeconds:   30,
		EnableAutoStatus: true,
		StatusInterval:   1000, // 1秒
	}

	controller := &AGVTestController{
		agvController:    agvController,
		dbService:        dbService,
		isRunning:        false,
		connectionStatus: false,
		testResults:      make([]TestResult, 0),
		config:           defaultConfig,
	}

	log.Printf("AGV测试控制器初始化成功")
	return controller, nil
}

// Connect 连接AGV（复用主程序的AGV控制器连接状态）
func (c *AGVTestController) Connect() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	log.Printf("AGV测试控制器检查连接状态...")

	// 检查主程序AGV控制器是否已连接
	if !c.agvController.IsConnected() {
		c.lastError = "主程序AGV未连接，请先在设置界面连接AGV"
		c.connectionStatus = false
		return fmt.Errorf("AGV测试连接失败: 主程序AGV未连接，请先在设置界面连接AGV")
	}

	c.connectionStatus = true
	c.lastError = ""
	log.Printf("AGV测试控制器连接成功（复用主程序连接）")
	return nil
}

// Disconnect 断开AGV连接（仅更新测试控制器状态，不影响主程序连接）
func (c *AGVTestController) Disconnect() {
	c.mu.Lock()
	defer c.mu.Unlock()

	log.Printf("AGV测试控制器断开连接...")
	// 注意：不调用c.agvController.Disconnect()，以免影响主程序连接
	c.connectionStatus = false
	log.Printf("AGV测试控制器已断开（主程序连接不受影响）")
}

// IsConnected 检查AGV连接状态
func (c *AGVTestController) IsConnected() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.connectionStatus && c.agvController.IsConnected()
}

// GetStatus 获取AGV状态
func (c *AGVTestController) GetStatus() *agv.Status {
	if !c.IsConnected() {
		return nil
	}
	return c.agvController.GetStatus()
}

// QueryNavigationPoint 查询导航点（复用主程序数据库查询）
func (c *AGVTestController) QueryNavigationPoint(machine string) (int, error) {
	if c.dbService == nil {
		return 0, fmt.Errorf("数据库未连接")
	}

	log.Printf("查询机器 %s 的导航点", machine)

	// 这里调用和主程序相同的数据库查询逻辑
	navInfo, err := c.dbService.GetNavigationByRobotID(machine)
	if err != nil {
		return 0, fmt.Errorf("查询导航点失败: %v", err)
	}

	log.Printf("机器 %s 的导航点: %d", machine, navInfo.NavigationID)
	return navInfo.NavigationID, nil
}

// GetConfig 获取配置
func (c *AGVTestController) GetConfig() *AGVTestConfig {
	c.mu.RLock()
	defer c.mu.RUnlock()
	// 返回配置的副本
	configCopy := *c.config
	return &configCopy
}

// UpdateConfig 更新配置
func (c *AGVTestController) UpdateConfig(newConfig *AGVTestConfig) error {
	c.mu.Lock()
	defer c.mu.Unlock()

	if newConfig == nil {
		return fmt.Errorf("配置不能为空")
	}

	// 验证配置
	if newConfig.IP == "" {
		return fmt.Errorf("IP地址不能为空")
	}
	if newConfig.Port == "" {
		return fmt.Errorf("端口不能为空")
	}
	if newConfig.AuthCode == "" {
		return fmt.Errorf("授权码不能为空")
	}

	c.config = newConfig
	log.Printf("AGV测试配置已更新: %s:%s", newConfig.IP, newConfig.Port)
	return nil
}

// GetTestResults 获取测试结果
func (c *AGVTestController) GetTestResults() []TestResult {
	c.mu.RLock()
	defer c.mu.RUnlock()

	// 返回副本
	results := make([]TestResult, len(c.testResults))
	copy(results, c.testResults)
	return results
}

// ClearTestResults 清空测试结果
func (c *AGVTestController) ClearTestResults() {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.testResults = make([]TestResult, 0)
	log.Printf("测试结果已清空")
}

// addTestResult 添加测试结果（内部方法）
func (c *AGVTestController) addTestResult(result TestResult) {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 限制结果数量，保持最新的100条
	if len(c.testResults) >= 100 {
		c.testResults = c.testResults[1:]
	}

	c.testResults = append(c.testResults, result)
}

// GetLastError 获取最后的错误信息
func (c *AGVTestController) GetLastError() string {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.lastError
}

// IsRunning 检查是否正在运行测试
func (c *AGVTestController) IsRunning() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.isRunning
}
