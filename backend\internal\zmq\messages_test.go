package zmq

import (
	"encoding/json"
	"testing"
)

func TestHeartbeatMessage(t *testing.T) {
	// 创建心跳消息
	msg := NewHeartbeatMessage("AGV001", true)

	// 验证基本字段
	if msg.Instruction != 0 {
		t.<PERSON><PERSON><PERSON>("Expected instruction 0, got %d", msg.Instruction)
	}

	if msg.Code != true {
		t.<PERSON>rror("Expected code to be true")
	}

	// 验证内容
	content, ok := msg.Content.(HeartbeatContent)
	if !ok {
		t.Fatal("Content is not HeartbeatContent type")
	}

	if content.RobotNo != "AGV001" {
		t.<PERSON><PERSON><PERSON>("Expected RobotNo AGV001, got %s", content.RobotNo)
	}

	if content.State != true {
		t.<PERSON>rror("Expected State to be true")
	}
}

func TestMessageJSON(t *testing.T) {
	// 创建心跳消息
	msg := NewHeartbeatMessage("AGV001", true)

	// 序列化为JSON
	data, err := json.Marshal(msg)
	if err != nil {
		t.Fatalf("Failed to marshal message: %v", err)
	}

	t.Logf("JSON output: %s", string(data))

	// 反序列化
	var decoded Message
	if err := json.Unmarshal(data, &decoded); err != nil {
		t.Fatalf("Failed to unmarshal message: %v", err)
	}

	// 验证反序列化的结果
	if decoded.Instruction != 0 {
		t.Errorf("Expected instruction 0, got %d", decoded.Instruction)
	}

	// Content会被解析为map[string]interface{}
	contentMap, ok := decoded.Content.(map[string]interface{})
	if !ok {
		t.Fatal("Content is not a map")
	}

	robotNo, ok := contentMap["robotNo"].(string)
	if !ok || robotNo != "AGV001" {
		t.Errorf("Expected robotNo AGV001, got %v", contentMap["robotNo"])
	}

	state, ok := contentMap["state"].(bool)
	if !ok || state != true {
		t.Errorf("Expected state true, got %v", contentMap["state"])
	}
}
