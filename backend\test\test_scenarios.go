package main

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/user/agv_nav/internal/zmq"
)

// TestScenario 测试场景
type TestScenario struct {
	ID          string               `json:"id"`
	Name        string               `json:"name"`
	Description string               `json:"description"`
	Steps       []TestScenarioStep   `json:"steps"`
	Expected    TestScenarioExpected `json:"expected"`
}

// TestScenarioStep 测试场景步骤
type TestScenarioStep struct {
	Order       int                    `json:"order"`
	Action      string                 `json:"action"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
	Delay       time.Duration          `json:"delay"`
}

// TestScenarioExpected 测试场景预期结果
type TestScenarioExpected struct {
	TotalSteps   int                    `json:"total_steps"`
	SuccessSteps int                    `json:"success_steps"`
	FinalState   map[string]interface{} `json:"final_state"`
	Duration     time.Duration          `json:"duration"`
}

// TestScenarioManager 测试场景管理器
type TestScenarioManager struct {
	scenarios []TestScenario
	tester    *ZMQCommTester
}

// NewTestScenarioManager 创建测试场景管理器
func NewTestScenarioManager(tester *ZMQCommTester) *TestScenarioManager {
	manager := &TestScenarioManager{
		tester:    tester,
		scenarios: make([]TestScenario, 0),
	}

	// 初始化预定义场景
	manager.initializePredefinedScenarios()

	return manager
}

// initializePredefinedScenarios 初始化预定义测试场景
func (m *TestScenarioManager) initializePredefinedScenarios() {
	// 场景1: 基础通信流程
	m.scenarios = append(m.scenarios, TestScenario{
		ID:          "basic_communication",
		Name:        "基础通信流程",
		Description: "测试心跳、状态上报、任务通知的基础通信流程",
		Steps: []TestScenarioStep{
			{Order: 1, Action: "heartbeat", Description: "发送心跳", Delay: 100 * time.Millisecond},
			{Order: 2, Action: "status_report", Description: "状态上报", Delay: 100 * time.Millisecond},
			{Order: 3, Action: "task_start", Description: "任务开始通知", Delay: 100 * time.Millisecond},
			{Order: 4, Action: "task_complete", Description: "任务完成通知", Delay: 100 * time.Millisecond},
		},
		Expected: TestScenarioExpected{
			TotalSteps:   4,
			SuccessSteps: 4,
			Duration:     2 * time.Second,
		},
	})

	// 场景2: 完整任务生命周期
	m.scenarios = append(m.scenarios, TestScenario{
		ID:          "full_task_lifecycle",
		Name:        "完整任务生命周期",
		Description: "模拟完整的任务从分配到完成的生命周期",
		Steps: []TestScenarioStep{
			{Order: 1, Action: "receive_task_assignment", Description: "接收任务分配", Parameters: map[string]interface{}{"machines": []string{"61L", "60R"}}, Delay: 200 * time.Millisecond},
			{Order: 2, Action: "task_start", Description: "任务开始通知", Delay: 500 * time.Millisecond},
			{Order: 3, Action: "status_report", Description: "工作中状态上报", Delay: 1000 * time.Millisecond},
			{Order: 4, Action: "task_pause", Description: "任务暂停", Delay: 200 * time.Millisecond},
			{Order: 5, Action: "receive_continue_task", Description: "接收继续任务指令", Delay: 500 * time.Millisecond},
			{Order: 6, Action: "task_complete", Description: "任务完成通知", Delay: 200 * time.Millisecond},
			{Order: 7, Action: "receive_no_task", Description: "接收无任务指令", Delay: 200 * time.Millisecond},
		},
		Expected: TestScenarioExpected{
			TotalSteps:   7,
			SuccessSteps: 7,
			Duration:     5 * time.Second,
		},
	})

	// 场景3: 异常处理流程
	m.scenarios = append(m.scenarios, TestScenario{
		ID:          "error_handling",
		Name:        "异常处理流程",
		Description: "测试各种异常情况的处理",
		Steps: []TestScenarioStep{
			{Order: 1, Action: "set_low_battery", Description: "设置低电量状态", Parameters: map[string]interface{}{"battery": 10.0}, Delay: 100 * time.Millisecond},
			{Order: 2, Action: "status_report", Description: "低电量状态上报", Delay: 200 * time.Millisecond},
			{Order: 3, Action: "set_robot_error", Description: "设置机器人异常状态", Parameters: map[string]interface{}{"state": zmq.RobotStateAbnormal}, Delay: 100 * time.Millisecond},
			{Order: 4, Action: "status_report", Description: "异常状态上报", Delay: 200 * time.Millisecond},
			{Order: 5, Action: "task_force_stop", Description: "任务强制结束", Delay: 200 * time.Millisecond},
			{Order: 6, Action: "reset_to_normal", Description: "恢复正常状态", Delay: 100 * time.Millisecond},
		},
		Expected: TestScenarioExpected{
			TotalSteps:   6,
			SuccessSteps: 6,
			Duration:     3 * time.Second,
		},
	})

	// 场景4: 连续状态上报
	m.scenarios = append(m.scenarios, TestScenario{
		ID:          "continuous_reporting",
		Name:        "连续状态上报",
		Description: "模拟1分钟间隔的连续状态上报",
		Steps: []TestScenarioStep{
			{Order: 1, Action: "status_report", Description: "第1次状态上报", Delay: 500 * time.Millisecond},
			{Order: 2, Action: "simulate_position_change", Description: "模拟位置变化", Delay: 200 * time.Millisecond},
			{Order: 3, Action: "status_report", Description: "第2次状态上报", Delay: 500 * time.Millisecond},
			{Order: 4, Action: "simulate_battery_change", Description: "模拟电量变化", Delay: 200 * time.Millisecond},
			{Order: 5, Action: "status_report", Description: "第3次状态上报", Delay: 500 * time.Millisecond},
		},
		Expected: TestScenarioExpected{
			TotalSteps:   5,
			SuccessSteps: 5,
			Duration:     4 * time.Second,
		},
	})

	// 场景5: 调度器指令响应
	m.scenarios = append(m.scenarios, TestScenario{
		ID:          "scheduler_commands",
		Name:        "调度器指令响应",
		Description: "测试对所有调度器指令的响应",
		Steps: []TestScenarioStep{
			{Order: 1, Action: "receive_task_assignment", Description: "接收任务分配 (200)", Parameters: map[string]interface{}{"machines": []string{"59L"}}, Delay: 300 * time.Millisecond},
			{Order: 2, Action: "receive_wait_task", Description: "接收等待指令 (202)", Delay: 300 * time.Millisecond},
			{Order: 3, Action: "receive_continue_task", Description: "接收继续任务指令 (203)", Parameters: map[string]interface{}{"mode": "resume"}, Delay: 300 * time.Millisecond},
			{Order: 4, Action: "receive_no_task", Description: "接收无任务指令 (201)", Delay: 300 * time.Millisecond},
		},
		Expected: TestScenarioExpected{
			TotalSteps:   4,
			SuccessSteps: 4,
			Duration:     3 * time.Second,
		},
	})

	fmt.Printf("已初始化 %d 个预定义测试场景\n", len(m.scenarios))
}

// GetScenarios 获取所有场景
func (m *TestScenarioManager) GetScenarios() []TestScenario {
	return m.scenarios
}

// GetScenarioByID 根据ID获取场景
func (m *TestScenarioManager) GetScenarioByID(id string) *TestScenario {
	for _, scenario := range m.scenarios {
		if scenario.ID == id {
			return &scenario
		}
	}
	return nil
}

// ListScenarios 列出所有场景
func (m *TestScenarioManager) ListScenarios() {
	fmt.Println("\n=== 可用测试场景 ===")
	for i, scenario := range m.scenarios {
		fmt.Printf("%d. %s (%s)\n", i+1, scenario.Name, scenario.ID)
		fmt.Printf("   描述: %s\n", scenario.Description)
		fmt.Printf("   步骤数: %d\n", len(scenario.Steps))
		fmt.Printf("   预计耗时: %v\n", scenario.Expected.Duration)
		fmt.Println()
	}
}

// ExecuteScenario 执行测试场景
func (m *TestScenarioManager) ExecuteScenario(scenarioID string) error {
	scenario := m.GetScenarioByID(scenarioID)
	if scenario == nil {
		return fmt.Errorf("未找到测试场景: %s", scenarioID)
	}

	fmt.Printf("\n=== 执行测试场景: %s ===\n", scenario.Name)
	fmt.Printf("描述: %s\n", scenario.Description)
	fmt.Printf("总步骤数: %d\n", len(scenario.Steps))

	startTime := time.Now()
	successCount := 0

	// 执行每个步骤
	for _, step := range scenario.Steps {
		fmt.Printf("\n步骤 %d: %s\n", step.Order, step.Description)

		err := m.executeStep(step)
		if err != nil {
			fmt.Printf("❌ 步骤 %d 执行失败: %v\n", step.Order, err)
		} else {
			fmt.Printf("✅ 步骤 %d 执行成功\n", step.Order)
			successCount++
		}

		// 等待延迟
		if step.Delay > 0 {
			time.Sleep(step.Delay)
		}
	}

	// 计算结果
	duration := time.Since(startTime)
	successRate := float64(successCount) / float64(len(scenario.Steps)) * 100

	fmt.Printf("\n=== 场景执行完成 ===\n")
	fmt.Printf("总步骤: %d\n", len(scenario.Steps))
	fmt.Printf("成功步骤: %d\n", successCount)
	fmt.Printf("失败步骤: %d\n", len(scenario.Steps)-successCount)
	fmt.Printf("成功率: %.1f%%\n", successRate)
	fmt.Printf("执行耗时: %v\n", duration)
	fmt.Printf("预期耗时: %v\n", scenario.Expected.Duration)

	return nil
}

// executeStep 执行单个步骤
func (m *TestScenarioManager) executeStep(step TestScenarioStep) error {
	switch step.Action {
	case "heartbeat":
		return m.tester.TestHeartbeat()

	case "status_report":
		return m.tester.TestStatusReport()

	case "task_start":
		return m.tester.TestTaskStart()

	case "task_complete":
		return m.tester.TestTaskComplete()

	case "task_pause":
		return m.tester.TestTaskPause()

	case "task_force_stop":
		return m.tester.TestTaskForceStop()

	case "receive_task_assignment":
		return m.simulateTaskAssignment(step.Parameters)

	case "receive_wait_task":
		return m.simulateWaitTask()

	case "receive_continue_task":
		return m.simulateContinueTask(step.Parameters)

	case "receive_no_task":
		return m.simulateNoTask()

	case "set_low_battery":
		return m.setLowBattery(step.Parameters)

	case "set_robot_error":
		return m.setRobotError(step.Parameters)

	case "reset_to_normal":
		return m.resetToNormal()

	case "simulate_position_change":
		return m.simulatePositionChange()

	case "simulate_battery_change":
		return m.simulateBatteryChange()

	default:
		return fmt.Errorf("未知操作: %s", step.Action)
	}
}

// 各种模拟操作的具体实现

func (m *TestScenarioManager) simulateTaskAssignment(params map[string]interface{}) error {
	fmt.Println("模拟接收任务分配指令...")
	machines := []string{"61L", "60R"}
	if machinesParam, exists := params["machines"]; exists {
		if machinesList, ok := machinesParam.([]string); ok {
			machines = machinesList
		}
	}

	// 创建任务分配消息
	content := map[string]interface{}{
		"LaneNos": machines,
		"remark":  "场景测试任务分配",
	}

	message := &zmq.Message{
		Instruction: zmq.InstructionTaskAssignment,
		TimeStamp:   time.Now(),
		Code:        true,
		Message:     "任务下发",
		Content:     content,
	}

	data, _ := json.Marshal(message)
	fmt.Printf("接收到任务分配: %s\n", string(data))

	m.tester.mockData.StartNewTask()
	return nil
}

func (m *TestScenarioManager) simulateWaitTask() error {
	fmt.Println("模拟接收等待任务指令...")
	m.tester.mockData.PauseCurrentTask()
	return nil
}

func (m *TestScenarioManager) simulateContinueTask(params map[string]interface{}) error {
	fmt.Println("模拟接收继续任务指令...")
	mode := "resume"
	if modeParam, exists := params["mode"]; exists {
		if modeStr, ok := modeParam.(string); ok {
			mode = modeStr
		}
	}

	if mode == "resume" {
		m.tester.mockData.ResumeCurrentTask()
	} else {
		m.tester.mockData.StartNewTask()
	}
	return nil
}

func (m *TestScenarioManager) simulateNoTask() error {
	fmt.Println("模拟接收无任务指令...")
	m.tester.mockData.SetPosition(zmq.PositionMainRoadStr)
	time.Sleep(200 * time.Millisecond) // 模拟导航时间
	m.tester.mockData.SetPosition(zmq.PositionParkingStr)
	return nil
}

func (m *TestScenarioManager) setLowBattery(params map[string]interface{}) error {
	battery := 10.0
	if batteryParam, exists := params["battery"]; exists {
		if batteryFloat, ok := batteryParam.(float64); ok {
			battery = batteryFloat
		}
	}

	fmt.Printf("设置电量为 %.1f%%\n", battery)
	m.tester.mockData.SetBatteryLevel(battery)
	return nil
}

func (m *TestScenarioManager) setRobotError(params map[string]interface{}) error {
	state := zmq.RobotStateAbnormal
	if stateParam, exists := params["state"]; exists {
		if stateInt, ok := stateParam.(int); ok {
			state = stateInt
		}
	}

	fmt.Printf("设置机器人状态为异常 (%d)\n", state)
	m.tester.mockData.SetRobotState(state)
	return nil
}

func (m *TestScenarioManager) resetToNormal() error {
	fmt.Println("重置为正常状态...")
	m.tester.mockData.ResetToDefault()
	return nil
}

func (m *TestScenarioManager) simulatePositionChange() error {
	fmt.Println("模拟位置变化...")
	positions := []string{
		zmq.PositionMainRoadStr,
		zmq.PositionSwitchStr,
		zmq.PositionParkingStr,
	}

	// 随机选择一个位置
	newPos := positions[time.Now().UnixNano()%int64(len(positions))]
	m.tester.mockData.SetPosition(newPos)
	fmt.Printf("位置已变更为: %s\n", newPos)
	return nil
}

func (m *TestScenarioManager) simulateBatteryChange() error {
	fmt.Println("模拟电量变化...")
	m.tester.mockData.SimulateRandomChange()
	return nil
}

// ExecuteAllScenarios 执行所有场景
func (m *TestScenarioManager) ExecuteAllScenarios() {
	fmt.Println("\n=== 执行所有测试场景 ===")

	totalScenarios := len(m.scenarios)
	successScenarios := 0

	for i, scenario := range m.scenarios {
		fmt.Printf("\n[%d/%d] 正在执行场景: %s\n", i+1, totalScenarios, scenario.Name)

		err := m.ExecuteScenario(scenario.ID)
		if err != nil {
			fmt.Printf("❌ 场景 %s 执行失败: %v\n", scenario.Name, err)
		} else {
			fmt.Printf("✅ 场景 %s 执行成功\n", scenario.Name)
			successScenarios++
		}

		// 场景之间的间隔
		if i < totalScenarios-1 {
			fmt.Println("\n等待2秒后执行下一个场景...")
			time.Sleep(2 * time.Second)
		}
	}

	fmt.Printf("\n=== 所有场景执行完成 ===\n")
	fmt.Printf("总场景数: %d\n", totalScenarios)
	fmt.Printf("成功场景: %d\n", successScenarios)
	fmt.Printf("失败场景: %d\n", totalScenarios-successScenarios)
	fmt.Printf("成功率: %.1f%%\n", float64(successScenarios)/float64(totalScenarios)*100)
}

// AddCustomScenario 添加自定义场景
func (m *TestScenarioManager) AddCustomScenario(scenario TestScenario) {
	m.scenarios = append(m.scenarios, scenario)
	fmt.Printf("已添加自定义场景: %s\n", scenario.Name)
}

// RemoveScenario 移除场景
func (m *TestScenarioManager) RemoveScenario(scenarioID string) error {
	for i, scenario := range m.scenarios {
		if scenario.ID == scenarioID {
			m.scenarios = append(m.scenarios[:i], m.scenarios[i+1:]...)
			fmt.Printf("已移除场景: %s\n", scenario.Name)
			return nil
		}
	}
	return fmt.Errorf("未找到场景: %s", scenarioID)
}
