package api

import (
	"encoding/json"
	"log"
	"net/http"

	"github.com/user/agv_nav/internal/task"
)

// WorkModeAPI 工作模式API处理器
type WorkModeAPI struct {
	workModeManager *task.WorkModeManager
}

// NewWorkModeAPI 创建工作模式API处理器
func NewWorkModeAPI(workModeManager *task.WorkModeManager) *WorkModeAPI {
	return &WorkModeAPI{
		workModeManager: workModeManager,
	}
}

// SwitchToManualModeRequest 切换到手动模式请求
type SwitchToManualModeRequest struct {
	Force bool `json:"force,omitempty"` // 是否强制切换
}

// SwitchToScheduledModeRequest 切换到调度模式请求
type SwitchToScheduledModeRequest struct {
	Force bool `json:"force,omitempty"` // 是否强制切换
}

// WorkModeResponse 工作模式响应
type WorkModeResponse struct {
	Success bool                   `json:"success"`
	Message string                 `json:"message"`
	Data    map[string]interface{} `json:"data,omitempty"`
}

// HandleSwitchToManualMode 处理切换到手动模式的请求
func (w *WorkModeAPI) HandleSwitchToManualMode(writer http.ResponseWriter, request *http.Request) {
	if request.Method != http.MethodPost {
		http.Error(writer, "方法不允许", http.StatusMethodNotAllowed)
		return
	}

	var req SwitchToManualModeRequest
	if err := json.NewDecoder(request.Body).Decode(&req); err != nil {
		log.Printf("解析切换到手动模式请求失败: %v", err)
		// 允许空请求体
		req = SwitchToManualModeRequest{Force: false}
	}

	log.Printf("收到切换到手动模式请求，强制切换: %v", req.Force)

	var err error
	if req.Force {
		err = w.workModeManager.ForceSwitch(task.WorkModeManual)
	} else {
		err = w.workModeManager.SwitchToManualMode()
	}

	response := WorkModeResponse{
		Success: err == nil,
		Data:    w.workModeManager.GetModeStatus(),
	}

	if err != nil {
		response.Message = err.Error()
		log.Printf("切换到手动模式失败: %v", err)
	} else {
		response.Message = "已成功切换到手动模式"
		log.Printf("已成功切换到手动模式")
	}

	writer.Header().Set("Content-Type", "application/json")
	json.NewEncoder(writer).Encode(response)
}

// HandleSwitchToScheduledMode 处理切换到调度模式的请求
func (w *WorkModeAPI) HandleSwitchToScheduledMode(writer http.ResponseWriter, request *http.Request) {
	if request.Method != http.MethodPost {
		http.Error(writer, "方法不允许", http.StatusMethodNotAllowed)
		return
	}

	var req SwitchToScheduledModeRequest
	if err := json.NewDecoder(request.Body).Decode(&req); err != nil {
		log.Printf("解析切换到调度模式请求失败: %v", err)
		// 允许空请求体
		req = SwitchToScheduledModeRequest{Force: false}
	}

	log.Printf("收到切换到调度模式请求，强制切换: %v", req.Force)

	var err error
	if req.Force {
		err = w.workModeManager.ForceSwitch(task.WorkModeScheduled)
	} else {
		err = w.workModeManager.SwitchToScheduledMode()
	}

	response := WorkModeResponse{
		Success: err == nil,
		Data:    w.workModeManager.GetModeStatus(),
	}

	if err != nil {
		response.Message = err.Error()
		log.Printf("切换到调度模式失败: %v", err)
	} else {
		response.Message = "已成功切换到调度模式"
		log.Printf("已成功切换到调度模式")
	}

	writer.Header().Set("Content-Type", "application/json")
	json.NewEncoder(writer).Encode(response)
}

// HandleGetModeStatus 处理获取工作模式状态的请求
func (w *WorkModeAPI) HandleGetModeStatus(writer http.ResponseWriter, request *http.Request) {
	if request.Method != http.MethodGet {
		http.Error(writer, "方法不允许", http.StatusMethodNotAllowed)
		return
	}

	response := WorkModeResponse{
		Success: true,
		Message: "获取工作模式状态成功",
		Data:    w.workModeManager.GetModeStatus(),
	}

	writer.Header().Set("Content-Type", "application/json")
	json.NewEncoder(writer).Encode(response)
}

// RegisterWorkModeRoutes 注册工作模式相关路由
func RegisterWorkModeRoutes(mux *http.ServeMux, workModeManager *task.WorkModeManager) {
	workModeAPI := NewWorkModeAPI(workModeManager)

	// 注册路由
	mux.HandleFunc("/api/work-mode/switch-to-manual", workModeAPI.HandleSwitchToManualMode)
	mux.HandleFunc("/api/work-mode/switch-to-scheduled", workModeAPI.HandleSwitchToScheduledMode)
	mux.HandleFunc("/api/work-mode/status", workModeAPI.HandleGetModeStatus)

	log.Printf("工作模式API路由已注册")
}