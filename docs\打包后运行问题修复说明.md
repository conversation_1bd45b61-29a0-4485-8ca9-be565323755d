# 打包后运行问题修复说明

## 问题描述

打包后的程序运行时出现以下问题：

1. **权限问题**：
   ```
   使用备用日志目录: C:\Users\<USER>\AppData\Local\Temp (原路径 logs 不可写)
   创建数据库目录失败: mkdir data: Access is denied.
   ```

2. **数据库连接失败**：
   ```
   警告：初始化数据库连接失败: 连接数据库失败: unable to open database file: The system cannot find the path specified.
   ```

3. **配置文件解析失败**：
   ```
   警告：加载配置文件失败: json: cannot unmarshal string into Go struct field TaskConfig.retryDelayBase of type time.Duration
   ```

## 问题原因

### 1. 权限问题
- 打包后的程序运行在只读的安装目录
- 程序尝试在安装目录创建 `data` 和 `logs` 目录，权限不足

### 2. 路径问题
- 数据库和配置文件路径使用相对路径，在打包环境中无法找到
- 工作目录设置为 `process.resourcesPath`（只读）

### 3. 配置解析问题
- 配置文件中的时间字段是字符串格式（如 "5s"）
- 代码期望 `time.Duration` 类型，导致解析失败

## 修复方案

### 1. 修改 Electron 主进程 (`frontend/electron/main.ts`)

**修改内容：**
- 添加 `getUserDataPath()` 函数获取用户数据目录
- 添加 `getBackendWorkingDirectory()` 函数设置正确的工作目录
- 添加 `copyBackendResourcesIfNeeded()` 函数复制资源文件

**修复效果：**
- 后端程序运行在可写的用户数据目录
- 自动复制配置文件和数据文件到用户数据目录
- 创建必要的目录结构

### 2. 修改数据库路径查找 (`backend/internal/database/navigation.go`)

**修改内容：**
- 在 `getDBPath()` 函数中添加用户数据目录路径
- 添加对 `backend-data/data/agv_data.db` 的查找支持

**修复效果：**
- 能够找到用户数据目录中的数据库文件
- 提供更好的路径查找容错性

### 3. 修改配置文件处理 (`backend/internal/task/config.go`)

**修改内容：**
- 添加 `getConfigPath()` 函数支持多路径查找
- 添加 `TaskConfigJSON` 结构体处理字符串格式的时间配置
- 修改 `loadConfigFromFile()` 函数支持时间字符串解析

**修复效果：**
- 能够正确解析字符串格式的时间配置
- 支持多路径配置文件查找
- 提供配置解析错误的优雅处理

## 目录结构

### 打包前（开发环境）
```
project/
├── backend/
│   ├── data/
│   │   ├── agv_data.db
│   │   └── task_config.json
│   └── config/
└── frontend/
```

### 打包后（生产环境）
```
用户数据目录/
└── backend-data/
    ├── data/
    │   ├── agv_data.db
    │   └── task_config.json
    ├── config/
    └── logs/
```

## 使用说明

### 1. 重新构建

运行构建脚本：
```powershell
.\scripts\package.ps1
```

### 2. 测试

1. 安装新的安装包
2. 运行程序
3. 检查是否还有权限和路径问题

### 3. 验证修复

如果修复成功，应该看到：
- 不再有权限错误
- 数据库连接成功
- 配置文件正确加载
- 日志文件正常写入

## 技术细节

### 用户数据目录位置
- **Windows**: `C:\Users\<USER>\AppData\Roaming\AGV Controller\`
- **macOS**: `~/Library/Application Support/AGV Controller/`
- **Linux**: `~/.config/AGV Controller/`

### 资源文件复制
程序启动时会自动：
1. 检查用户数据目录是否存在
2. 复制配置文件到用户数据目录
3. 复制数据文件到用户数据目录
4. 创建日志目录

### 配置文件格式兼容性
支持两种格式：
- 字符串格式：`"retryDelayBase": "5s"`
- 数值格式：`"retryDelayBase": 5000000000`

## 注意事项

1. **首次运行**：首次运行时会复制资源文件，可能需要几秒钟
2. **数据迁移**：用户数据将保存在用户数据目录中
3. **权限要求**：不再需要管理员权限运行
4. **升级兼容性**：升级时会保留用户数据目录中的文件

## 故障排除

如果仍然遇到问题：

1. **检查用户数据目录**：
   ```
   %APPDATA%\AGV Controller\backend-data\
   ```

2. **手动创建目录**：
   ```
   mkdir "%APPDATA%\AGV Controller\backend-data\data"
   mkdir "%APPDATA%\AGV Controller\backend-data\logs"
   ```

3. **检查日志文件**：
   ```
   %APPDATA%\AGV Controller\backend-data\logs\agv_nav.log
   ```

4. **重新安装**：删除用户数据目录后重新安装程序 