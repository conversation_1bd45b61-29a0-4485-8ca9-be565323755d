package task

import (
	"encoding/binary"
	"fmt"
	"log"
	"math"
	"time"

	"github.com/user/agv_nav/pkg/logger"
	"github.com/user/agv_nav/pkg/plc"
)

// PLC地址配置常量
var (
	// M区皮辊控制地址（可配置）
	PLCRollerCoilAddress uint16 = 3000 // M3000开始

	// D区距离数据地址（可配置）
	PLCDistanceRegAddress uint16 = 4000 // D4000开始
)

// SpindleData 锭号处理数据
type SpindleData struct {
	SpindleNo int     `json:"spindle_no"` // 锭号
	Distance  float32 `json:"distance"`   // 距离码值
	IsLeft    bool    `json:"is_left"`    // 是否左皮辊
	Direction int     `json:"direction"`  // 方向信息 (0或1)
}

// PLCDataHelper PLC数据处理辅助类
type PLCDataHelper struct {
	plcController *plc.Controller
}

// NewPLCDataHelper 创建PLC数据处理辅助实例
func NewPLCDataHelper(plcController *plc.Controller) *PLCDataHelper {
	return &PLCDataHelper{
		plcController: plcController,
	}
}

// CalculateRollerDirection 计算锭号对应的皮辊方向
func (h *PLCDataHelper) CalculateRollerDirection(spindleNo int) (bool, error) {
	taskLogger := logger.GetModuleLogger("task")
	taskLogger.Info("计算锭号皮辊方向", "spindle", spindleNo)

	var isLeft bool

	if spindleNo >= 0 && spindleNo <= 600 {
		// 0-600范围：奇数锭号=左皮辊，偶数锭号=右皮辊
		isLeft = (spindleNo%2 == 1)
		taskLogger.Info("锭号0-600范围", "spindle", spindleNo, "isOdd", spindleNo%2 == 1, "direction", map[bool]string{true: "左", false: "右"}[isLeft])
	} else if spindleNo >= 601 && spindleNo <= 1200 {
		// 601-1200范围：奇数锭号=右皮辊，偶数锭号=左皮辊
		isLeft = (spindleNo%2 == 0)
		taskLogger.Info("锭号601-1200范围", "spindle", spindleNo, "isOdd", spindleNo%2 == 1, "direction", map[bool]string{true: "左", false: "右"}[isLeft])
	} else {
		return false, fmt.Errorf("锭号 %d 超出有效范围 (0-1200)", spindleNo)
	}

	return isLeft, nil
}

// ConvertFloatToBADC 将float32转换为BADC字节序
func (h *PLCDataHelper) ConvertFloatToBADC(value float32) []byte {
	log.Printf("转换浮点值 %f 为BADC格式", value)

	// 转换为32位浮点数字节
	floatBytes := make([]byte, 4)
	binary.LittleEndian.PutUint32(floatBytes, math.Float32bits(value))

	log.Printf("原始字节序: [%02X, %02X, %02X, %02X]",
		floatBytes[0], floatBytes[1], floatBytes[2], floatBytes[3])

	// 应用BADC字节顺序 - 交换每个字内的字节顺序
	temp := floatBytes[0]
	floatBytes[0] = floatBytes[1]
	floatBytes[1] = temp

	temp = floatBytes[2]
	floatBytes[2] = floatBytes[3]
	floatBytes[3] = temp

	log.Printf("BADC字节序: [%02X, %02X, %02X, %02X]",
		floatBytes[0], floatBytes[1], floatBytes[2], floatBytes[3])

	// 将字节转换为两个16位寄存器值
	register1 := uint16(floatBytes[0])<<8 | uint16(floatBytes[1])
	register2 := uint16(floatBytes[2])<<8 | uint16(floatBytes[3])

	// 创建要写入的寄存器值数组
	registers := []byte{
		byte(register1 >> 8), byte(register1),
		byte(register2 >> 8), byte(register2),
	}

	log.Printf("寄存器数据: [%02X, %02X, %02X, %02X]",
		registers[0], registers[1], registers[2], registers[3])

	return registers
}

// SendSpindleDataToPLC 发送锭号数据到PLC - 新的5步协议
func (h *PLCDataHelper) SendSpindleDataToPLC(data SpindleData) error {
	log.Printf("开始向PLC发送锭号 %d 的数据", data.SpindleNo)

	// 检查PLC连接
	if !h.plcController.IsConnected() {
		return fmt.Errorf("PLC未连接，无法发送数据")
	}

	// 步骤1: 等待PLC准备信号 M509=true
	err := h.waitForPLCReady()
	if err != nil {
		return fmt.Errorf("等待PLC准备信号失败: %v", err)
	}
	log.Printf("PLC已准备就绪，开始发送数据")

	// 步骤2: 写入方向到M604 (Direction: 0→false, 1→true)
	err = h.writeDirection(data.Direction)
	if err != nil {
		return fmt.Errorf("写入方向信息失败: %v", err)
	}

	// 步骤3: 写入码值到D602 (float类型)
	err = h.writeDistance(data.Distance)
	if err != nil {
		return fmt.Errorf("写入距离码值失败: %v", err)
	}

	// 步骤4: 写入皮辊到D600 (左=1, 右=2)
	err = h.writeRoller(data.IsLeft)
	if err != nil {
		return fmt.Errorf("写入皮辊信息失败: %v", err)
	}

	// 步骤5: 发送完成信号M603=true
	err = h.notifyDataComplete()
	if err != nil {
		return fmt.Errorf("发送完成信号失败: %v", err)
	}

	log.Printf("成功发送锭号 %d 数据到PLC: Direction=%d, 距离=%f, 皮辊=%s",
		data.SpindleNo, data.Direction, data.Distance,
		map[bool]string{true: "左", false: "右"}[data.IsLeft])

	return nil
}

// SetPLCAddresses 设置PLC地址配置
func SetPLCAddresses(rollerCoilAddr, distanceRegAddr uint16) {
	PLCRollerCoilAddress = rollerCoilAddr
	PLCDistanceRegAddress = distanceRegAddr
	log.Printf("PLC地址配置已更新: 皮辊线圈=M%d, 距离寄存器=D%d",
		PLCRollerCoilAddress, PLCDistanceRegAddress)
}

// GetPLCAddresses 获取当前PLC地址配置
func GetPLCAddresses() (rollerCoilAddr, distanceRegAddr uint16) {
	return PLCRollerCoilAddress, PLCDistanceRegAddress
}

// waitForPLCReady 等待PLC准备信号 M509=true
func (h *PLCDataHelper) waitForPLCReady() error {
	config := GetConfig()
	plcReadyAddress := config.PLCAddresses.ReadyAddress
	maxWaitTime := config.Timeouts.PLCReadyTimeout
	checkInterval := config.Timeouts.PLCReadyCheckInterval

	log.Printf("等待PLC准备信号 M%d = true", plcReadyAddress)

	timeout := time.After(maxWaitTime)
	ticker := time.NewTicker(checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			return fmt.Errorf("等待PLC准备信号超时，超过%v", maxWaitTime)
		case <-ticker.C:
			data, err := h.plcController.ReadCoils(plcReadyAddress, 1)
			if err != nil {
				log.Printf("读取PLC准备信号失败: %v", err)
				continue
			}

			if len(data) > 0 && data[0] != 0 {
				log.Printf("PLC准备信号已就绪，M%d = true", plcReadyAddress)
				return nil
			}
		}
	}
}

// writeDirection 写入方向信息到M604
func (h *PLCDataHelper) writeDirection(direction int) error {
	config := GetConfig()
	directionAddress := config.PLCAddresses.DirectionAddress
	directionValue := direction == 1 // 0→false, 1→true

	log.Printf("写入方向信息: M%d = %t (Direction=%d)", directionAddress, directionValue, direction)

	_, err := h.plcController.WriteSingleCoil(directionAddress, directionValue)
	if err != nil {
		return fmt.Errorf("写入方向信息到M%d失败: %v", directionAddress, err)
	}

	return nil
}

// writeDistance 写入距离码值到D602
func (h *PLCDataHelper) writeDistance(distance float32) error {
	taskLogger := logger.GetModuleLogger("task")
	config := GetConfig()
	distanceAddress := config.PLCAddresses.DistanceAddress

	taskLogger.Info("写入距离码值", "address", fmt.Sprintf("D%d", distanceAddress), "value", distance)

	// 转换为BADC格式
	distanceBytes := h.ConvertFloatToBADC(distance)

	// 写入到D602 (占用2个连续寄存器)
	_, err := h.plcController.WriteMultipleRegisters(distanceAddress, 2, distanceBytes)
	if err != nil {
		taskLogger.Error("写入失败", "address", fmt.Sprintf("D%d", distanceAddress), "value", distance, "error", err)
		return fmt.Errorf("写入距离码值到D%d失败: %v", distanceAddress, err)
	}

	taskLogger.Info("写入成功", "address", fmt.Sprintf("D%d", distanceAddress), "value", distance)
	return nil
}

// writeRoller 写入皮辊信息到D600
func (h *PLCDataHelper) writeRoller(isLeft bool) error {
	config := GetConfig()
	rollerAddress := config.PLCAddresses.RollerAddress
	var rollerValue uint16

	if isLeft {
		rollerValue = 1 // 左皮辊 = 1
	} else {
		rollerValue = 2 // 右皮辊 = 2
	}

	log.Printf("写入皮辊信息: D%d = %d (%s皮辊)", rollerAddress, rollerValue,
		map[bool]string{true: "左", false: "右"}[isLeft])

	// 将uint16转换为字节数组
	rollerBytes := []byte{
		byte(rollerValue >> 8), byte(rollerValue),
	}

	_, err := h.plcController.WriteMultipleRegisters(rollerAddress, 1, rollerBytes)
	if err != nil {
		return fmt.Errorf("写入皮辊信息到D%d失败: %v", rollerAddress, err)
	}

	return nil
}

// notifyDataComplete 发送数据写入完成信号M603=true
func (h *PLCDataHelper) notifyDataComplete() error {
	config := GetConfig()
	completeAddress := config.PLCAddresses.CompleteAddress

	log.Printf("发送数据写入完成信号: M%d = true", completeAddress)

	_, err := h.plcController.WriteSingleCoil(completeAddress, true)
	if err != nil {
		return fmt.Errorf("发送完成信号到M%d失败: %v", completeAddress, err)
	}

	return nil
}

// VerifyPLCData 验证PLC中的数据是否与写入的数据一致
func (h *PLCDataHelper) VerifyPLCData(expectedData SpindleData) error {
	log.Printf("开始验证锭号 %d 的PLC数据: Direction=%d, Distance=%f, IsLeft=%t",
		expectedData.SpindleNo, expectedData.Direction, expectedData.Distance, expectedData.IsLeft)

	// 验证方向 - 读取M505
	err := h.verifyDirection(expectedData.Direction)
	if err != nil {
		return fmt.Errorf("方向验证失败: %v", err)
	}

	// 验证皮辊 - 读取D501
	err = h.verifyRoller(expectedData.IsLeft)
	if err != nil {
		return fmt.Errorf("皮辊验证失败: %v", err)
	}

	// 验证距离码值 - 读取D502
	_, err = h.verifyDistance(expectedData.Distance)
	if err != nil {
		return fmt.Errorf("距离码值验证失败: %v", err)
	}

	log.Printf("锭号 %d 的PLC数据验证成功，所有数据与预期一致", expectedData.SpindleNo)
	return nil
}

// verifyDirection 验证方向数据 M505
func (h *PLCDataHelper) verifyDirection(expectedDirection int) error {
	config := GetConfig()
	verifyDirectionAddress := config.PLCAddresses.VerifyDirectionAddress
	maxAttempts := 5
	interval := 1 * time.Second

	log.Printf("开始验证M%d方向数据，最多尝试%d次，间隔%v", verifyDirectionAddress, maxAttempts, interval)

	for attempt := 1; attempt <= maxAttempts; attempt++ {
		log.Printf("第%d次读取M%d验证方向数据", attempt, verifyDirectionAddress)

		data, err := h.plcController.ReadCoils(verifyDirectionAddress, 1)
		if err != nil {
			log.Printf("第%d次读取M%d失败: %v", attempt, verifyDirectionAddress, err)
			if attempt < maxAttempts {
				log.Printf("第%d次验证失败，%v后重试", attempt, interval)
				time.Sleep(interval)
				continue
			}
			return fmt.Errorf("读取M%d失败，5次尝试后仍失败: %v", verifyDirectionAddress, err)
		}

		if len(data) == 0 {
			log.Printf("第%d次读取M%d返回空数据", attempt, verifyDirectionAddress)
			if attempt < maxAttempts {
				log.Printf("第%d次验证失败，%v后重试", attempt, interval)
				time.Sleep(interval)
				continue
			}
			return fmt.Errorf("读取M%d返回空数据，5次尝试后仍失败", verifyDirectionAddress)
		}

		// 转换读取的值为方向：false=0, true=1
		readDirection := 0
		if data[0] != 0 {
			readDirection = 1
		}

		log.Printf("第%d次方向验证: 我们写入Direction=%d, PLC读取M%d=%t(对应Direction=%d)",
			attempt, expectedDirection, verifyDirectionAddress, data[0] != 0, readDirection)

		if readDirection == expectedDirection {
			log.Printf("第%d次方向验证成功", attempt)
			return nil
		}

		log.Printf("第%d次方向验证失败: 预期Direction=%d, 实际读取=%d", attempt, expectedDirection, readDirection)

		if attempt < maxAttempts {
			log.Printf("第%d次验证失败，%v后重试", attempt, interval)
			time.Sleep(interval)
		}
	}

	return fmt.Errorf("方向数据验证失败，5次尝试后仍不匹配: 预期Direction=%d", expectedDirection)
}

// verifyRoller 验证皮辊数据 D501
func (h *PLCDataHelper) verifyRoller(expectedIsLeft bool) error {
	config := GetConfig()
	verifyRollerAddress := config.PLCAddresses.VerifyRollerAddress
	maxAttempts := 5
	interval := 1 * time.Second

	log.Printf("开始验证D%d皮辊数据，最多尝试%d次，间隔%v", verifyRollerAddress, maxAttempts, interval)

	for attempt := 1; attempt <= maxAttempts; attempt++ {
		log.Printf("第%d次读取D%d验证皮辊数据", attempt, verifyRollerAddress)

		data, err := h.plcController.ReadHoldingRegisters(verifyRollerAddress, 1)
		if err != nil {
			log.Printf("第%d次读取D%d失败: %v", attempt, verifyRollerAddress, err)
			if attempt < maxAttempts {
				log.Printf("第%d次验证失败，%v后重试", attempt, interval)
				time.Sleep(interval)
				continue
			}
			return fmt.Errorf("读取D%d失败，5次尝试后仍失败: %v", verifyRollerAddress, err)
		}

		if len(data) < 2 {
			log.Printf("第%d次读取D%d返回数据不足", attempt, verifyRollerAddress)
			if attempt < maxAttempts {
				log.Printf("第%d次验证失败，%v后重试", attempt, interval)
				time.Sleep(interval)
				continue
			}
			return fmt.Errorf("读取D%d返回数据不足，5次尝试后仍失败", verifyRollerAddress)
		}

		// 转换为uint16值
		rollerValue := uint16(data[0])<<8 | uint16(data[1])

		// 转换为皮辊类型：1=左皮辊, 2=右皮辊
		var readIsLeft bool
		var validValue bool
		switch rollerValue {
		case 1:
			readIsLeft = true // 左皮辊
			validValue = true
		case 2:
			readIsLeft = false // 右皮辊
			validValue = true
		default:
			log.Printf("第%d次读取到无效的皮辊值: %d", attempt, rollerValue)
			validValue = false
		}

		if !validValue {
			if attempt < maxAttempts {
				log.Printf("第%d次验证失败，%v后重试", attempt, interval)
				time.Sleep(interval)
				continue
			}
			return fmt.Errorf("读取到无效的皮辊值，5次尝试后仍无效: %d", rollerValue)
		}

		expectedRollerValue := uint16(2) // 默认右皮辊
		if expectedIsLeft {
			expectedRollerValue = 1
		}

		log.Printf("第%d次皮辊验证: 我们写入%s皮辊(%d), PLC读取D%d=%d(对应%s皮辊)",
			attempt, map[bool]string{true: "左", false: "右"}[expectedIsLeft], expectedRollerValue,
			verifyRollerAddress, rollerValue,
			map[bool]string{true: "左", false: "右"}[readIsLeft])

		if readIsLeft == expectedIsLeft {
			log.Printf("第%d次皮辊验证成功", attempt)
			return nil
		}

		log.Printf("第%d次皮辊验证失败: 预期%s皮辊, 实际读取%s皮辊",
			attempt, map[bool]string{true: "左", false: "右"}[expectedIsLeft],
			map[bool]string{true: "左", false: "右"}[readIsLeft])

		if attempt < maxAttempts {
			log.Printf("第%d次验证失败，%v后重试", attempt, interval)
			time.Sleep(interval)
		}
	}

	return fmt.Errorf("皮辊数据验证失败，5次尝试后仍不匹配: 预期%s皮辊",
		map[bool]string{true: "左", false: "右"}[expectedIsLeft])
}

// verifyDistance 验证距离码值 D502
func (h *PLCDataHelper) verifyDistance(expectedDistance float32) (float32, error) {
	taskLogger := logger.GetModuleLogger("task")
	config := GetConfig()
	verifyDistanceAddress := config.PLCAddresses.VerifyDistanceAddress
	maxAttempts := 5
	interval := 1 * time.Second
	const tolerance = 0.3 // 放大误差范围到0.3

	taskLogger.Info("开始验证距离码值", "address", fmt.Sprintf("D%d", verifyDistanceAddress), "maxAttempts", maxAttempts, "interval", interval, "tolerance", tolerance)

	for attempt := 1; attempt <= maxAttempts; attempt++ {
		taskLogger.Debug("读取验证距离码值", "attempt", attempt, "address", fmt.Sprintf("D%d", verifyDistanceAddress))

		// 读取2个连续寄存器 (float需要32位)
		data, err := h.plcController.ReadHoldingRegisters(verifyDistanceAddress, 2)
		if err != nil {
			taskLogger.Warn("读取失败", "attempt", attempt, "address", fmt.Sprintf("D%d", verifyDistanceAddress), "error", err)
			if attempt < maxAttempts {
				taskLogger.Debug("验证失败重试", "attempt", attempt, "interval", interval)
				time.Sleep(interval)
				continue
			}
			return 0, fmt.Errorf("读取D%d失败，5次尝试后仍失败: %v", verifyDistanceAddress, err)
		}

		if len(data) < 4 {
			taskLogger.Warn("读取数据不足", "attempt", attempt, "address", fmt.Sprintf("D%d", verifyDistanceAddress), "dataLength", len(data))
			if attempt < maxAttempts {
				taskLogger.Debug("验证失败重试", "attempt", attempt, "interval", interval)
				time.Sleep(interval)
				continue
			}
			return 0, fmt.Errorf("读取D%d返回数据不足，5次尝试后仍失败", verifyDistanceAddress)
		}

		// 从PLC读取的数据转换为float32
		readDistance, err := h.ConvertPLCDataToFloat(data)
		if err != nil {
			taskLogger.Warn("转换数据失败", "attempt", attempt, "error", err)
			if attempt < maxAttempts {
				taskLogger.Debug("验证失败重试", "attempt", attempt, "interval", interval)
				time.Sleep(interval)
				continue
			}
			return 0, fmt.Errorf("转换读取的距离数据失败，5次尝试后仍失败: %v", err)
		}

		taskLogger.Info("距离验证对比", "attempt", attempt, "expected", expectedDistance, "actual", readDistance, "address", fmt.Sprintf("D%d", verifyDistanceAddress))

		// 使用放大的误差范围进行比较 (浮点数精度问题)
		errorValue := math.Abs(float64(expectedDistance - readDistance))
		if errorValue <= tolerance {
			taskLogger.Info("距离验证成功", "attempt", attempt, "errorValue", errorValue, "tolerance", tolerance)
			taskLogger.Info("验证读取值", "address", fmt.Sprintf("D%d", verifyDistanceAddress), "value", readDistance)
			return readDistance, nil
		}

		taskLogger.Warn("距离验证失败", "attempt", attempt, "expected", expectedDistance, "actual", readDistance, "error", errorValue, "tolerance", tolerance)

		if attempt < maxAttempts {
			taskLogger.Debug("验证失败重试", "attempt", attempt, "interval", interval)
			time.Sleep(interval)
		}
	}

	return 0, fmt.Errorf("距离码值验证失败，5次尝试后仍不匹配: 预期%f, 误差范围±%f",
		expectedDistance, tolerance)
}

// ConvertPLCDataToFloat 将从PLC读取的数据转换为float32
// 注意：这里需要确认PLC读取时的字节序是否与写入时的BADC一致
func (h *PLCDataHelper) ConvertPLCDataToFloat(data []byte) (float32, error) {
	if len(data) < 4 {
		return 0, fmt.Errorf("数据长度不足，需要4字节，实际%d字节", len(data))
	}

	log.Printf("从PLC读取的原始4字节数据: [%02X, %02X, %02X, %02X]", data[0], data[1], data[2], data[3])

	// 根据我们写入时使用的BADC字节序，读取时也需要按BADC处理
	// 方案1: 假设读取的是BADC格式，需要转换回标准格式进行解析
	standardBytes := make([]byte, 4)

	// BADC → 标准格式: 交换每个16位字内的字节顺序
	standardBytes[0] = data[1] // B → A
	standardBytes[1] = data[0] // A → B
	standardBytes[2] = data[3] // D → C
	standardBytes[3] = data[2] // C → D

	log.Printf("BADC转标准字节序后: [%02X, %02X, %02X, %02X]",
		standardBytes[0], standardBytes[1], standardBytes[2], standardBytes[3])

	// 转换为float32
	floatBits := binary.LittleEndian.Uint32(standardBytes)
	result := math.Float32frombits(floatBits)

	log.Printf("最终转换的float值: %f", result)

	return result, nil
}

// NotifyVerificationComplete 通知PLC数据验证完成 M605=true
func (h *PLCDataHelper) NotifyVerificationComplete() error {
	config := GetConfig()
	verificationCompleteAddress := config.PLCAddresses.VerificationCompleteAddress

	log.Printf("发送数据验证完成信号: M%d = true", verificationCompleteAddress)

	_, err := h.plcController.WriteSingleCoil(verificationCompleteAddress, true)
	if err != nil {
		return fmt.Errorf("发送验证完成信号到M%d失败: %v", verificationCompleteAddress, err)
	}

	return nil
}

// WriteSafeTurnRange 写入安全调头码值区间到PLC
func (h *PLCDataHelper) WriteSafeTurnRange(startCodeValue, endCodeValue float32) error {
	taskLogger := logger.GetModuleLogger("task")
	config := GetConfig()
	startAddress := config.SafeTurnConfig.StartAddress // D606
	endAddress := config.SafeTurnConfig.EndAddress     // D608

	taskLogger.Info("写入安全调头码值区间", "startAddress", fmt.Sprintf("D%d", startAddress), "startValue", startCodeValue, "endAddress", fmt.Sprintf("D%d", endAddress), "endValue", endCodeValue)

	// 写入起始码值到D606-D607 (BADC格式)
	startBytes := h.ConvertFloatToBADC(startCodeValue)
	_, err := h.plcController.WriteMultipleRegisters(startAddress, 2, startBytes)
	if err != nil {
		taskLogger.Error("写入起始码值失败", "address", fmt.Sprintf("D%d", startAddress), "value", startCodeValue, "error", err)
		return fmt.Errorf("写入起始码值到D%d失败: %v", startAddress, err)
	}

	// 写入结束码值到D608-D609 (BADC格式)
	endBytes := h.ConvertFloatToBADC(endCodeValue)
	_, err = h.plcController.WriteMultipleRegisters(endAddress, 2, endBytes)
	if err != nil {
		taskLogger.Error("写入结束码值失败", "address", fmt.Sprintf("D%d", endAddress), "value", endCodeValue, "error", err)
		return fmt.Errorf("写入结束码值到D%d失败: %v", endAddress, err)
	}

	taskLogger.Info("安全调头码值区间写入成功", "startAddress", fmt.Sprintf("D%d", startAddress), "startValue", startCodeValue, "endAddress", fmt.Sprintf("D%d", endAddress), "endValue", endCodeValue)
	return nil
}

// NotifyMachineComplete 通知PLC单个细纱机看车工作完成
// ⚠️ 注意：此函数专门用于单侧任务，双侧任务的L侧和R侧都不调用此函数
// 单侧任务完成信号：M606=true (机台完成), M610=true (回到原点), M608=false (无调头)
func (h *PLCDataHelper) NotifyMachineComplete() error {
	config := GetConfig()
	machineCompleteAddress := config.PLCAddresses.MachineCompleteAddress
	returnToOriginAddress := config.PLCAddresses.ReturnToOriginAddress
	turnAroundAddress := config.PLCAddresses.TurnAroundAddress

	log.Printf("发送细纱机完成信号: M%d = true", machineCompleteAddress)

	// 写入M606=true (细纱机完成信号)
	_, err := h.plcController.WriteSingleCoil(machineCompleteAddress, true)
	if err != nil {
		return fmt.Errorf("发送细纱机完成信号到M%d失败: %v", machineCompleteAddress, err)
	}

	// 写入M610=true (回到码带原点位置)
	log.Printf("发送回到码带原点位置信号: M%d = true", returnToOriginAddress)
	_, err = h.plcController.WriteSingleCoil(returnToOriginAddress, true)
	if err != nil {
		return fmt.Errorf("发送回到码带原点位置信号到M%d失败: %v", returnToOriginAddress, err)
	}

	// 写入M608=false (确保调头信号为false)
	log.Printf("确保调头信号为false: M%d = false", turnAroundAddress)
	_, err = h.plcController.WriteSingleCoil(turnAroundAddress, false)
	if err != nil {
		return fmt.Errorf("写入调头信号到M%d失败: %v", turnAroundAddress, err)
	}

	log.Printf("细纱机完成信号发送成功: M%d=true, M%d=true, M%d=false",
		machineCompleteAddress, returnToOriginAddress, turnAroundAddress)

	return nil
}

// 巷道控制相关方法

// NotifyLaneLSideComplete 通知PLC巷道L侧看车工作完成（需要调头）
// 双侧任务L侧完成信号：M606=true (机台完成), 写入安全调头区间, M608=true (调头信号), M610=false (不回到原点)
func (h *PLCDataHelper) NotifyLaneLSideComplete(machine string, dbService interface{ GetSpindleDistance(string) (float32, error) }) error {
	taskLogger := logger.GetModuleLogger("task")
	config := GetConfig()
	machineCompleteAddress := config.PLCAddresses.MachineCompleteAddress
	turnAroundAddress := config.PLCAddresses.TurnAroundAddress
	returnToOriginAddress := config.PLCAddresses.ReturnToOriginAddress

	taskLogger.Info("巷道L侧完成流程开始", "machine", machine)

	// ① 先发送机台完成信号 M606=true
	taskLogger.Info("发送机台完成信号", "address", fmt.Sprintf("M%d", machineCompleteAddress))
	_, err := h.plcController.WriteSingleCoil(machineCompleteAddress, true)
	if err != nil {
		return fmt.Errorf("发送机台完成信号到M%d失败: %v", machineCompleteAddress, err)
	}

	// ② 写入安全调头码值区间
	startKey := machine + "650" // 例如: "61L650"
	endKey := machine + "1100"  // 例如: "61L1100"

	// 查询安全码值，失败时使用0
	var startCodeValue, endCodeValue float32
	if dbService != nil {
		if value, err := dbService.GetSpindleDistance(startKey); err == nil {
			startCodeValue = value
		}
		if value, err := dbService.GetSpindleDistance(endKey); err == nil {
			endCodeValue = value
		}
	}

	taskLogger.Info("查询安全调头码值", "startKey", startKey, "startValue", startCodeValue, "endKey", endKey, "endValue", endCodeValue)

	err = h.WriteSafeTurnRange(startCodeValue, endCodeValue)
	if err != nil {
		return fmt.Errorf("写入安全调头码值区间失败: %v", err)
	}

	// ③ 然后发送调头信号 M608=true
	taskLogger.Info("发送调头信号", "address", fmt.Sprintf("M%d", turnAroundAddress))
	_, err = h.plcController.WriteSingleCoil(turnAroundAddress, true)
	if err != nil {
		return fmt.Errorf("发送调头信号到M%d失败: %v", turnAroundAddress, err)
	}

	// ④ 最后设置M610=false (不回到原点)
	taskLogger.Info("设置不回到原点", "address", fmt.Sprintf("M%d", returnToOriginAddress))
	_, err = h.plcController.WriteSingleCoil(returnToOriginAddress, false)
	if err != nil {
		return fmt.Errorf("写入回到原点信号到M%d失败: %v", returnToOriginAddress, err)
	}

	taskLogger.Info("巷道L侧完成信号发送成功", "machine", machine, "machineComplete", fmt.Sprintf("M%d=true", machineCompleteAddress), "turnAround", fmt.Sprintf("M%d=true", turnAroundAddress), "returnToOrigin", fmt.Sprintf("M%d=false", returnToOriginAddress))

	return nil
}

// NotifyLaneRSideComplete 通知PLC巷道R侧看车工作完成并执行完整退出流程
// 双侧任务R侧完成的5步流程：
// 1. 发送完成信号：M606=true, M608=false, M610=false
// 2. 查询并写入码带信息：M611(方向), D604(位置) - 由调用方负责
// 3. 等待AGV到达原点：M510=true
// 4. 切换回激光控制：M607=true, 等待M501=false
// 5. 返回主流程
func (h *PLCDataHelper) NotifyLaneRSideComplete() error {
	// 步骤1: 发送R侧完成信号
	log.Printf("步骤1: 巷道R侧完成，发送完成信号")
	err := h.sendRSideCompleteSignals()
	if err != nil {
		return fmt.Errorf("步骤1失败 - 发送R侧完成信号: %v", err)
	}

	// 步骤2: 查询并写入码带信息 M611(方向), D604(位置) - 由调用方(task_service)负责
	log.Printf("步骤2: 码带信息写入由调用方负责")

	// 步骤3: 等待AGV到达原点位置
	log.Printf("步骤3: 等待AGV到达原点位置")
	err = h.waitForOriginArrival()
	if err != nil {
		return fmt.Errorf("步骤3失败 - 等待AGV到达原点: %v", err)
	}

	// 步骤4: 切换回激光控制
	log.Printf("步骤4: 切换回激光控制")
	err = h.switchBackToLaserControl()
	if err != nil {
		return fmt.Errorf("步骤4失败 - 切换回激光控制: %v", err)
	}

	log.Printf("巷道R侧完成流程执行成功，已切换回激光控制，准备返回主流程")
	return nil
}

// sendRSideCompleteSignals 发送R侧完成信号：M606=true, M608=false, M610=false
func (h *PLCDataHelper) sendRSideCompleteSignals() error {
	config := GetConfig()
	machineCompleteAddress := config.PLCAddresses.MachineCompleteAddress
	turnAroundAddress := config.PLCAddresses.TurnAroundAddress
	returnToOriginAddress := config.PLCAddresses.ReturnToOriginAddress

	// 写入M606=true (机台完成信号)
	log.Printf("发送机台完成信号: M%d = true", machineCompleteAddress)
	_, err := h.plcController.WriteSingleCoil(machineCompleteAddress, true)
	if err != nil {
		return fmt.Errorf("发送机台完成信号到M%d失败: %v", machineCompleteAddress, err)
	}

	// 写入M608=false (调头信号为false，因为调头已完成)
	log.Printf("确保调头信号为false: M%d = false", turnAroundAddress)
	_, err = h.plcController.WriteSingleCoil(turnAroundAddress, false)
	if err != nil {
		return fmt.Errorf("写入调头信号到M%d失败: %v", turnAroundAddress, err)
	}

	// 写入M610=true (回到码带原点)
	log.Printf("回到码带原点: M%d = true", returnToOriginAddress)
	_, err = h.plcController.WriteSingleCoil(returnToOriginAddress, true)
	if err != nil {
		return fmt.Errorf("写入回到原点信号到M%d失败: %v", returnToOriginAddress, err)
	}

	log.Printf("R侧完成信号发送成功: M%d=true, M%d=false, M%d=true",
		machineCompleteAddress, turnAroundAddress, returnToOriginAddress)

	return nil
}

// switchBackToLaserControl 切换回激光控制：M607=true, 等待M501=false
func (h *PLCDataHelper) switchBackToLaserControl() error {
	config := GetConfig()
	controlHandoverAddress := config.PLCAddresses.ControlHandoverAddress
	controlConfirmAddress := config.PLCAddresses.ControlConfirmAddress

	// 发送切换回激光控制信号
	log.Printf("发送切换回激光控制信号: M%d = true", controlHandoverAddress)
	_, err := h.plcController.WriteSingleCoil(controlHandoverAddress, true)
	if err != nil {
		return fmt.Errorf("发送切换控制信号到M%d失败: %v", controlHandoverAddress, err)
	}

	// 等待权限切换成功 M501=false
	log.Printf("等待权限切换成功: M%d = false", controlConfirmAddress)
	return h.waitForControlHandover()
}

// NotifyPLCTurnAround 通知PLC进行调头操作 M608=true
func (h *PLCDataHelper) NotifyPLCTurnAround() error {
	config := GetConfig()
	turnAroundAddress := config.PLCAddresses.TurnAroundAddress

	log.Printf("发送调头控制信号: M%d = true", turnAroundAddress)

	_, err := h.plcController.WriteSingleCoil(turnAroundAddress, true)
	if err != nil {
		return fmt.Errorf("发送调头控制信号到M%d失败: %v", turnAroundAddress, err)
	}

	return nil
}

// WaitForTurnComplete 等待PLC调头完成信号 M511=true
func (h *PLCDataHelper) WaitForTurnComplete() error {
	config := GetConfig()
	turnCompleteAddress := config.PLCAddresses.TurnCompleteAddress
	maxWaitTime := config.Timeouts.TurnAroundTimeout
	checkInterval := config.Timeouts.TurnAroundCheckInterval

	log.Printf("等待PLC调头完成信号 M%d = true", turnCompleteAddress)

	timeout := time.After(maxWaitTime)
	ticker := time.NewTicker(checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			return fmt.Errorf("等待调头完成信号超时，超过%v", maxWaitTime)
		case <-ticker.C:
			data, err := h.plcController.ReadCoils(turnCompleteAddress, 1)
			if err != nil {
				log.Printf("读取调头完成信号失败: %v", err)
				continue
			}

			if len(data) > 0 && data[0] != 0 {
				log.Printf("调头完成信号已收到，M%d = true", turnCompleteAddress)
				// 调头完成后，PLC会自己重置M608调头信号，我们不需要写false
				return nil
			}
		}
	}
}

// NotifyPLCExitLane 通知PLC退出巷道 M608=true
func (h *PLCDataHelper) NotifyPLCExitLane() error {
	config := GetConfig()
	exitLaneAddress := config.PLCAddresses.ExitLaneAddress

	log.Printf("发送退出巷道信号: M%d = true", exitLaneAddress)

	_, err := h.plcController.WriteSingleCoil(exitLaneAddress, true)
	if err != nil {
		return fmt.Errorf("发送退出巷道信号到M%d失败: %v", exitLaneAddress, err)
	}

	return nil
}

// WaitForLaneExit 等待PLC巷道退出完成信号 M508=true
func (h *PLCDataHelper) WaitForLaneExit() error {
	config := GetConfig()
	laneExitCompleteAddress := config.PLCAddresses.LaneExitCompleteAddress
	maxWaitTime := config.Timeouts.LaneExitTimeout
	checkInterval := config.Timeouts.LaneExitCheckInterval

	log.Printf("等待PLC巷道退出完成信号 M%d = true", laneExitCompleteAddress)

	timeout := time.After(maxWaitTime)
	ticker := time.NewTicker(checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			return fmt.Errorf("等待巷道退出完成信号超时，超过%v", maxWaitTime)
		case <-ticker.C:
			data, err := h.plcController.ReadCoils(laneExitCompleteAddress, 1)
			if err != nil {
				log.Printf("读取巷道退出完成信号失败: %v", err)
				continue
			}

			if len(data) > 0 && data[0] != 0 {
				log.Printf("巷道退出完成信号已收到，M%d = true", laneExitCompleteAddress)
				// 退出完成后，重置退出控制信号
				h.plcController.WriteSingleCoil(config.PLCAddresses.ExitLaneAddress, false)
				return nil
			}
		}
	}
}

// WriteOriginDirection 写入原点方向到M611
func (h *PLCDataHelper) WriteOriginDirection(direction int) error {
	config := GetConfig()
	originDirectionAddress := config.PLCAddresses.OriginDirectionAddress
	directionValue := direction == 1 // 0→false, 1→true

	log.Printf("写入原点方向信息: M%d = %t (Direction=%d)", originDirectionAddress, directionValue, direction)

	_, err := h.plcController.WriteSingleCoil(originDirectionAddress, directionValue)
	if err != nil {
		return fmt.Errorf("写入原点方向到M%d失败: %v", originDirectionAddress, err)
	}

	return nil
}

// WriteOriginCodeValue 写入原点码值到D604
func (h *PLCDataHelper) WriteOriginCodeValue(codeValue float32) error {
	config := GetConfig()
	originCodeValueAddress := config.PLCAddresses.OriginCodeValueAddress

	log.Printf("写入原点码值: D%d = %f", originCodeValueAddress, codeValue)

	// 使用BADC格式转换
	codeValueBytes := h.ConvertFloatToBADC(codeValue)

	// 写入到D604 (占用2个连续寄存器)
	_, err := h.plcController.WriteMultipleRegisters(originCodeValueAddress, 2, codeValueBytes)
	if err != nil {
		return fmt.Errorf("写入原点码值到D%d失败: %v", originCodeValueAddress, err)
	}

	return nil
}

// waitForOriginArrival 等待AGV到达原点位置 M510=true
func (h *PLCDataHelper) waitForOriginArrival() error {
	config := GetConfig()
	originArrivalAddress := config.PLCAddresses.OriginArrivalAddress
	maxWaitTime := config.Timeouts.OriginArrivalTimeout
	checkInterval := config.Timeouts.OriginArrivalCheckInterval

	log.Printf("等待AGV到达原点位置信号 M%d = true", originArrivalAddress)

	timeout := time.After(maxWaitTime)
	ticker := time.NewTicker(checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			return fmt.Errorf("等待AGV到达原点位置超时，超过%v", maxWaitTime)
		case <-ticker.C:
			data, err := h.plcController.ReadCoils(originArrivalAddress, 1)
			if err != nil {
				log.Printf("读取原点到达信号失败: %v", err)
				continue
			}

			if len(data) > 0 && data[0] != 0 {
				log.Printf("AGV已到达原点位置，M%d = true", originArrivalAddress)
				return nil
			}
		}
	}
}

// requestControlHandover 请求控制权交接 M607=true
func (h *PLCDataHelper) requestControlHandover() error {
	config := GetConfig()
	controlHandoverAddress := config.PLCAddresses.ControlHandoverAddress

	log.Printf("发送控制权交接请求信号: M%d = true", controlHandoverAddress)

	_, err := h.plcController.WriteSingleCoil(controlHandoverAddress, true)
	if err != nil {
		return fmt.Errorf("发送控制权交接请求信号到M%d失败: %v", controlHandoverAddress, err)
	}

	return nil
}

// waitForControlHandover 等待控制权交接完成 M501=false
func (h *PLCDataHelper) waitForControlHandover() error {
	config := GetConfig()
	controlConfirmAddress := config.PLCAddresses.ControlConfirmAddress
	maxWaitTime := config.Timeouts.OriginArrivalTimeout // 复用原点到达超时时间
	checkInterval := config.Timeouts.OriginArrivalCheckInterval

	log.Printf("等待控制权交接完成信号 M%d = false", controlConfirmAddress)

	timeout := time.After(maxWaitTime)
	ticker := time.NewTicker(checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			return fmt.Errorf("等待控制权交接完成超时，超过%v", maxWaitTime)
		case <-ticker.C:
			data, err := h.plcController.ReadCoils(controlConfirmAddress, 1)
			if err != nil {
				log.Printf("读取控制权确认信号失败: %v", err)
				continue
			}

			if len(data) > 0 && data[0] == 0 {
				log.Printf("控制权交接完成，M%d = false", controlConfirmAddress)
				return nil
			}
		}
	}
}
