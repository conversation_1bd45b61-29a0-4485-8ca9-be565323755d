package zmq

// SchedulerInstructionHandler 调度指令处理器接口
type SchedulerInstructionHandler interface {
	WaitForInstruction() (SchedulerInstructionResult, error)
}

// SchedulerInstructionResult 调度指令结果
type SchedulerInstructionResult struct {
	InstructionType int
	TaskInfo        *TaskInfo
	Error           error
}

// TaskInfo 任务信息
type TaskInfo struct {
	RobotNo string   `json:"robotNo"`
	TaskId  string   `json:"taskId"`
	LaneNos []string `json:"laneNos"`
	Remark  string   `json:"remark"`
}