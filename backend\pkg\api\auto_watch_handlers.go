package api

import (
	"encoding/json"
	"net/http"
	"time"

	"github.com/user/agv_nav/internal/task"
	"github.com/user/agv_nav/pkg/logger"
)

// handleStartAutoWatch 启动自动看车（实际启动ZMQ调度系统）
func (s *Server) handleStartAutoWatch(w http.ResponseWriter, r *http.Request) {
	apiLogger := logger.GetModuleLogger("api")
	startTime := time.Now()
	clientIP := r.RemoteAddr
	apiLogger.Info("启动ZMQ调度系统API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	// 获取工作模式管理器
	workModeManager := task.GetWorkModeManager()
	if workModeManager == nil {
		s.enableCORS(w)
		apiLogger.Error("工作模式管理器未初始化")
		http.Error(w, "工作模式管理器未初始化", http.StatusInternalServerError)
		return
	}

	// 切换到调度模式（这会自动启动ZMQ服务）
	if err := workModeManager.SwitchToScheduledMode(); err != nil {
		apiLogger.Error("启动ZMQ调度系统失败", "error", err)
		errorResponse := map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		}
		s.enableCORS(w)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(errorResponse)
		return
	}

	// 返回成功响应
	response := map[string]interface{}{
		"success": true,
		"message": "ZMQ调度系统启动成功",
		"data": map[string]interface{}{
			"mode":      "scheduled",
			"startTime": time.Now().Format("2006-01-02 15:04:05"),
		},
	}

	s.enableCORS(w)
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)

	duration := time.Since(startTime)
	apiLogger.Info("ZMQ调度系统启动完成", "duration", duration, "clientIP", clientIP)
}

// handleStopAutoWatch 停止自动看车（实际停止ZMQ调度系统）
func (s *Server) handleStopAutoWatch(w http.ResponseWriter, r *http.Request) {
	apiLogger := logger.GetModuleLogger("api")
	startTime := time.Now()
	clientIP := r.RemoteAddr
	apiLogger.Info("停止ZMQ调度系统API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	// 获取工作模式管理器
	workModeManager := task.GetWorkModeManager()
	if workModeManager == nil {
		s.enableCORS(w)
		apiLogger.Error("工作模式管理器未初始化")
		http.Error(w, "工作模式管理器未初始化", http.StatusInternalServerError)
		return
	}

	// 切换到手动模式（这会自动停止ZMQ服务）
	if err := workModeManager.SwitchToManualMode(); err != nil {
		apiLogger.Error("停止ZMQ调度系统失败", "error", err)
		errorResponse := map[string]interface{}{
			"success": false,
			"error":   err.Error(),
		}
		s.enableCORS(w)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(errorResponse)
		return
	}

	// 返回成功响应
	response := map[string]interface{}{
		"success": true,
		"message": "ZMQ调度系统停止成功",
		"data": map[string]interface{}{
			"mode":     "manual",
			"stopTime": time.Now().Format("2006-01-02 15:04:05"),
		},
	}

	s.enableCORS(w)
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)

	duration := time.Since(startTime)
	apiLogger.Info("ZMQ调度系统停止完成", "duration", duration, "clientIP", clientIP)
}

// handleGetAutoWatchStatus 获取自动看车状态（实际获取ZMQ调度系统状态）
func (s *Server) handleGetAutoWatchStatus(w http.ResponseWriter, r *http.Request) {
	apiLogger := logger.GetModuleLogger("api")
	startTime := time.Now()
	clientIP := r.RemoteAddr
	apiLogger.Debug("获取ZMQ调度系统状态API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	// 获取工作模式管理器
	workModeManager := task.GetWorkModeManager()
	if workModeManager == nil {
		apiLogger.Error("工作模式管理器未初始化")
		http.Error(w, "工作模式管理器未初始化", http.StatusInternalServerError)
		return
	}

	// 构造状态信息（适配前端期望的数据结构）
	currentMode := workModeManager.GetCurrentMode()
	isRunning := workModeManager.IsTaskRunning()
	isScheduledMode := workModeManager.IsScheduledMode()

	// 确定连接状态
	connectionStatus := "disconnected"
	if isScheduledMode {
		connectionStatus = "connected" // ZMQ调度模式下视为已连接
	}

	// 获取心跳状态信息
	var lastHeartbeat interface{} = nil
	var heartbeatStatus interface{} = nil
	
	if isScheduledMode {
		// 获取WorkModeManager的详细状态，包含ZMQ管理器信息
		modeStatus := workModeManager.GetModeStatus()
		if zmqRunning, ok := modeStatus["zmqRunning"].(bool); ok && zmqRunning {
			// ZMQ服务正在运行，尝试获取心跳状态
			heartbeatStatus = map[string]interface{}{
				"zmqRunning": zmqRunning,
				"status":     "心跳服务正在运行",
			}
			lastHeartbeat = "心跳服务已启动"
		} else {
			heartbeatStatus = map[string]interface{}{
				"zmqRunning": false,
				"status":     "ZMQ服务未运行",
			}
		}
	}

	status := map[string]interface{}{
		"isRunning":        isRunning,
		"isConnected":      isScheduledMode,
		"robotNo":          "AGV001", // 从系统配置获取
		"currentTask":      nil,      // 暂时为null，后续可以添加实际任务信息
		"statistics": map[string]interface{}{
			"totalTasks":     0,
			"completedTasks": 0,
			"failedTasks":    0,
			"totalRunTime":   0,
		},
		"lastHeartbeat":    lastHeartbeat,
		"heartbeatStatus":  heartbeatStatus,
		"connectionStatus": connectionStatus,
		// 保留原有字段供调试使用
		"currentMode":      currentMode,
		"isScheduled":      isScheduledMode,
		"lastUpdateTime":   time.Now().Format("2006-01-02 15:04:05"),
	}

	// 如果是调度模式，添加ZMQ相关信息
	if isScheduledMode {
		status["zmqEnabled"] = true
		status["description"] = "ZMQ调度系统运行中"
	} else {
		status["zmqEnabled"] = false
		status["description"] = "手动模式"
	}

	// 返回状态响应
	response := map[string]interface{}{
		"success": true,
		"data":    status,
	}

	s.enableCORS(w)
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)

	duration := time.Since(startTime)
	apiLogger.Debug("ZMQ调度系统状态获取完成", "duration", duration, "clientIP", clientIP)
}