package logger

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"
)

// LogLevel 日志级别
type LogLevel int

const (
	DEBUG LogLevel = iota
	INFO
	WARN
	ERROR
	FATAL
)

// String 返回日志级别的字符串表示
func (l LogLevel) String() string {
	switch l {
	case DEBUG:
		return "DEBUG"
	case INFO:
		return "INFO"
	case WARN:
		return "WARN"
	case ERROR:
		return "ERROR"
	case FATAL:
		return "FATAL"
	default:
		return "UNKNOWN"
	}
}

// Logger 日志记录器
type Logger struct {
	level      LogLevel
	module     string
	output     io.Writer
	fileWriter *FileWriter
	config     *Config
}

// NewLogger 创建新的日志记录器
func NewLogger(module string) *Logger {
	config := GetDefaultConfig()

	logger := &Logger{
		level:  config.Level,
		module: module,
		config: config,
	}

	// 设置输出目标
	logger.setupOutput()

	return logger
}

// NewLoggerWithConfig 使用指定配置创建日志记录器
func NewLoggerWithConfig(module string, config *Config) *Logger {
	logger := &Logger{
		level:  config.Level,
		module: module,
		config: config,
	}

	logger.setupOutput()
	return logger
}

// setupOutput 设置输出目标
func (l *Logger) setupOutput() {
	var outputs []io.Writer

	// 控制台输出
	if l.config.EnableConsole {
		outputs = append(outputs, os.Stdout)
	}

	// 文件输出
	if l.config.EnableFile {
		if l.fileWriter == nil {
			var err error
			l.fileWriter, err = NewFileWriter(l.config.FilePath, l.config.MaxSize, l.config.MaxBackups)
			if err != nil {
				// 如果文件写入失败，至少保证控制台输出
				fmt.Fprintf(os.Stderr, "创建文件日志失败: %v\n", err)
				outputs = append(outputs, os.Stdout)
			} else {
				outputs = append(outputs, l.fileWriter)
			}
		}
	}

	// 如果没有任何输出，默认使用控制台
	if len(outputs) == 0 {
		outputs = append(outputs, os.Stdout)
	}

	l.output = io.MultiWriter(outputs...)
}

// SetLevel 设置日志级别
func (l *Logger) SetLevel(level LogLevel) {
	l.level = level
}

// GetLevel 获取当前日志级别
func (l *Logger) GetLevel() LogLevel {
	return l.level
}

// log 通用日志记录方法
func (l *Logger) log(level LogLevel, format string, args ...interface{}) {
	if level < l.level {
		return
	}

	// 获取调用者信息
	_, file, line, ok := runtime.Caller(2)
	caller := "unknown"
	if ok {
		caller = fmt.Sprintf("%s:%d", filepath.Base(file), line)
	}

	// 格式化时间
	timestamp := time.Now().Format("2006-01-02 15:04:05.000")

	// 构建日志消息
	message := fmt.Sprintf(format, args...)

	// 构建完整的日志行
	var logLine string
	if l.config.EnableStructured {
		// 结构化日志格式
		logLine = fmt.Sprintf(`{"timestamp":"%s","level":"%s","module":"%s","caller":"%s","message":"%s"}`,
			timestamp, level.String(), l.module, caller, strings.ReplaceAll(message, `"`, `\"`))
	} else {
		// 普通文本格式
		logLine = fmt.Sprintf("[%s] [%s] [%s] %s - %s",
			timestamp, level.String(), l.module, caller, message)
	}

	// 输出日志
	fmt.Fprintln(l.output, logLine)

	// FATAL级别日志后退出程序
	if level == FATAL {
		os.Exit(1)
	}
}

// logWithFields 结构化日志记录方法
func (l *Logger) logWithFields(level LogLevel, message string, fields ...interface{}) {
	if level < l.level {
		return
	}

	// 获取调用者信息
	_, file, line, ok := runtime.Caller(2)
	caller := "unknown"
	if ok {
		caller = fmt.Sprintf("%s:%d", filepath.Base(file), line)
	}

	// 格式化时间
	timestamp := time.Now().Format("2006-01-02 15:04:05.000")

	// 构建完整的日志行
	var logLine string
	if l.config.EnableStructured {
		// 结构化日志格式，添加字段
		fieldsStr := ""
		for i := 0; i < len(fields); i += 2 {
			if i+1 < len(fields) {
				key := fmt.Sprintf("%v", fields[i])
				value := fmt.Sprintf("%v", fields[i+1])
				value = strings.ReplaceAll(value, `"`, `\"`) // 转义引号
				fieldsStr += fmt.Sprintf(`,"%s":"%s"`, key, value)
			}
		}
		logLine = fmt.Sprintf(`{"timestamp":"%s","level":"%s","module":"%s","caller":"%s","message":"%s"%s}`,
			timestamp, level.String(), l.module, caller, strings.ReplaceAll(message, `"`, `\"`), fieldsStr)
	} else {
		// 普通文本格式，添加字段
		fieldsStr := ""
		for i := 0; i < len(fields); i += 2 {
			if i+1 < len(fields) {
				key := fmt.Sprintf("%v", fields[i])
				value := fmt.Sprintf("%v", fields[i+1])
				fieldsStr += fmt.Sprintf(" %s=%v", key, value)
			}
		}
		logLine = fmt.Sprintf("[%s] [%s] [%s] %s - %s%s",
			timestamp, level.String(), l.module, caller, message, fieldsStr)
	}

	// 输出日志
	fmt.Fprintln(l.output, logLine)

	// FATAL级别日志后退出程序
	if level == FATAL {
		os.Exit(1)
	}
}

// Debug 记录调试级别日志
func (l *Logger) Debug(message string, fields ...interface{}) {
	l.logWithFields(DEBUG, message, fields...)
}

// Info 记录信息级别日志
func (l *Logger) Info(message string, fields ...interface{}) {
	l.logWithFields(INFO, message, fields...)
}

// Warn 记录警告级别日志
func (l *Logger) Warn(message string, fields ...interface{}) {
	l.logWithFields(WARN, message, fields...)
}

// Error 记录错误级别日志
func (l *Logger) Error(message string, fields ...interface{}) {
	l.logWithFields(ERROR, message, fields...)
}

// Fatal 记录致命错误级别日志并退出程序
func (l *Logger) Fatal(message string, fields ...interface{}) {
	l.logWithFields(FATAL, message, fields...)
}

// 兼容性方法，支持格式化字符串日志

// Debugf 记录格式化调试级别日志
func (l *Logger) Debugf(format string, args ...interface{}) {
	l.log(DEBUG, format, args...)
}

// Infof 记录格式化信息级别日志
func (l *Logger) Infof(format string, args ...interface{}) {
	l.log(INFO, format, args...)
}

// Warnf 记录格式化警告级别日志
func (l *Logger) Warnf(format string, args ...interface{}) {
	l.log(WARN, format, args...)
}

// Errorf 记录格式化错误级别日志
func (l *Logger) Errorf(format string, args ...interface{}) {
	l.log(ERROR, format, args...)
}

// Fatalf 记录格式化致命错误级别日志并退出程序
func (l *Logger) Fatalf(format string, args ...interface{}) {
	l.log(FATAL, format, args...)
}

// Close 关闭日志记录器
func (l *Logger) Close() error {
	if l.fileWriter != nil {
		return l.fileWriter.Close()
	}
	return nil
}

// 全局默认日志记录器
var defaultLogger *Logger

// init 初始化默认日志记录器
func init() {
	defaultLogger = NewLogger("default")
}

// SetGlobalLogger 设置全局日志记录器
func SetGlobalLogger(logger *Logger) {
	if defaultLogger != nil {
		defaultLogger.Close()
	}
	defaultLogger = logger
}

// GetGlobalLogger 获取全局日志记录器
func GetGlobalLogger() *Logger {
	return defaultLogger
}

// 全局便捷方法
func Debug(format string, args ...interface{}) {
	defaultLogger.Debug(format, args...)
}

func Info(format string, args ...interface{}) {
	defaultLogger.Info(format, args...)
}

func Warn(format string, args ...interface{}) {
	defaultLogger.Warn(format, args...)
}

func Error(format string, args ...interface{}) {
	defaultLogger.Error(format, args...)
}

func Fatal(format string, args ...interface{}) {
	defaultLogger.Fatal(format, args...)
}

// Printf 兼容标准log.Printf的方法
func Printf(format string, args ...interface{}) {
	defaultLogger.Info(format, args...)
}

// Fatalf 兼容标准log.Fatalf的方法
func Fatalf(format string, args ...interface{}) {
	defaultLogger.Fatal(format, args...)
}

// Println 兼容标准log.Println的方法
func Println(args ...interface{}) {
	defaultLogger.Info(fmt.Sprint(args...))
}
