package zmq

import (
	"fmt"
	"log"
	"sync"

	"github.com/user/agv_nav/internal/zmq"
)

// NoTaskHandler 无任务指令处理器，处理201指令
type NoTaskHandler struct {
	*BaseHandler
	systemConfig *zmq.SystemConfig
	resultChan   chan *SchedulerInstructionResult
	mutex        sync.RWMutex
	isWaiting    bool
	zmqManager   *ZMQManager // ZMQ管理器，用于访问状态上报器
}

// NewNoTaskHandler 创建无任务指令处理器
func NewNoTaskHandler(systemConfig *zmq.SystemConfig) *NoTaskHandler {
	return &NoTaskHandler{
		BaseHandler:  NewBaseHandler(zmq.InstructionNoTask, "无任务指令处理器", systemConfig.RobotNo),
		systemConfig: systemConfig,
		resultChan:   make(chan *SchedulerInstructionResult, 1),
	}
}

// SetZMQManager 设置ZMQ管理器
func (h *NoTaskHandler) SetZMQManager(zmqManager *ZMQManager) {
	h.zmqManager = zmqManager
	log.Printf("📡 ZMQManager set for NoTaskHandler")
}

// HandleInstruction 处理无任务指令（201）
func (h *NoTaskHandler) HandleInstruction(message *zmq.Message) (*zmq.Message, error) {
	log.Printf("📥 [无任务] 收到201指令")

	// 验证消息
	if err := h.ValidateMessage(message); err != nil {
		return h.createErrorResponse(err.Error()), nil
	}

	// 验证机器人编号（如果有content）
	if message.Content != nil {
		taskInfo, err := h.parseTaskContent(message.Content)
		if err != nil {
			log.Printf("❌ [无任务] 解析任务内容失败: %v", err)
			return h.createErrorResponse("解析任务内容失败"), nil
		}

		if taskInfo != nil && taskInfo.RobotNo != h.systemConfig.RobotNo {
			log.Printf("❌ [无任务] 机器人编号不匹配，期望: %s, 实际: %s",
				h.systemConfig.RobotNo, taskInfo.RobotNo)
			return h.createRobotMismatchResponse(), nil
		}
	}

	log.Printf("🚫 [无任务] 收到无任务指令，准备返回主流程")

	// 通知状态上报器：收到调度指令
	if h.zmqManager != nil {
		if statusReporter := h.zmqManager.GetStatusReporter(); statusReporter != nil {
			statusReporter.OnSchedulerInstructionReceived(201) // 无任务指令
			log.Printf("✅ [无任务] 已通知状态上报器：收到指令201")
		}
	}

	// 创建结果
	result := &SchedulerInstructionResult{
		InstructionType: 201,
		TaskInfo:        nil,
		Error:           nil,
	}

	// 发送结果到等待通道（非阻塞）
	h.mutex.Lock()
	if h.isWaiting {
		select {
		case h.resultChan <- result:
			log.Printf("📤 [无任务] 结果已发送到等待通道")
		default:
			log.Printf("⚠️ [无任务] 等待通道已满，跳过结果发送")
		}
	}
	h.mutex.Unlock()

	// 立即回复确认
	reply := h.createSuccessResponse("无任务指令已接收")
	log.Printf("📤 [无任务] 发送确认回复")

	return reply, nil
}

// WaitForResult 等待处理结果
func (h *NoTaskHandler) WaitForResult() (*SchedulerInstructionResult, bool) {
	h.mutex.Lock()
	h.isWaiting = true
	h.mutex.Unlock()

	defer func() {
		h.mutex.Lock()
		h.isWaiting = false
		h.mutex.Unlock()
	}()

	select {
	case result := <-h.resultChan:
		return result, true
	default:
		return nil, false
	}
}

// parseTaskContent 解析任务内容
func (h *NoTaskHandler) parseTaskContent(content interface{}) (*TaskInfo, error) {
	if content == nil {
		return nil, nil
	}

	contentMap, ok := content.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("任务内容格式错误")
	}

	taskInfo := &TaskInfo{}

	// 解析robotNo
	if robotNo, exists := contentMap["robotNo"]; exists {
		if robotNoStr, ok := robotNo.(string); ok {
			taskInfo.RobotNo = robotNoStr
		}
	}

	return taskInfo, nil
}

// createSuccessResponse 创建成功响应
func (h *NoTaskHandler) createSuccessResponse(message string) *zmq.Message {
	reply := zmq.NewMessage(zmq.InstructionReply, true, nil)
	reply.Message = message
	return reply
}

// createErrorResponse 创建错误响应
func (h *NoTaskHandler) createErrorResponse(errorMsg string) *zmq.Message {
	reply := zmq.NewMessage(zmq.InstructionReply, false, nil)
	reply.Message = errorMsg
	return reply
}

// createRobotMismatchResponse 创建机器人编号不匹配响应
func (h *NoTaskHandler) createRobotMismatchResponse() *zmq.Message {
	reply := zmq.NewMessage(zmq.InstructionReply, false, nil)
	reply.Message = "非本机任务"
	return reply
}
