package zmq

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	zmqpkg "github.com/user/agv_nav/pkg/zmq"
)

// HeartbeatTestResult 心跳测试结果
type HeartbeatTestResult struct {
	Success      bool          `json:"success"`
	ResponseTime time.Duration `json:"responseTime"`
	Error        string        `json:"error,omitempty"`
	Timestamp    time.Time     `json:"timestamp"`
}

// TaskTestResult 任务下发测试结果
type TaskTestResult struct {
	TaskId       string        `json:"taskId"`
	Instruction  int           `json:"instruction"`
	RobotNo      string        `json:"robotNo"`
	LaneNos      []string      `json:"laneNos"`
	Remark       string        `json:"remark"`
	Success      bool          `json:"success"`
	ResponseTime time.Duration `json:"responseTime"`
	Message      string        `json:"message"`
	Timestamp    time.Time     `json:"timestamp"`
}

// TaskNotificationResult 任务通知发送结果（任务开始/完成）
type TaskNotificationResult struct {
	TaskId       string        `json:"taskId"`
	Instruction  int           `json:"instruction"` // 110=任务开始, 130=任务完成
	RobotNo      string        `json:"robotNo"`
	Remark       string        `json:"remark"`
	Success      bool          `json:"success"`
	ResponseTime time.Duration `json:"responseTime"`
	Message      string        `json:"message"`
	Timestamp    time.Time     `json:"timestamp"`
}

// TaskLifecycleStatus 任务生命周期状态
type TaskLifecycleStatus struct {
	CurrentTaskId         string                   `json:"currentTaskId"`
	TaskState             string                   `json:"taskState"` // "none", "received", "started", "completed"
	TaskStartTime         time.Time                `json:"taskStartTime,omitempty"`
	TaskCompleteTime      time.Time                `json:"taskCompleteTime,omitempty"`
	StartNotifications    []TaskNotificationResult `json:"startNotifications"`
	CompleteNotifications []TaskNotificationResult `json:"completeNotifications"`
}

// HeartbeatTestStatus 心跳测试状态
type HeartbeatTestStatus struct {
	IsRunning    bool                 `json:"isRunning"`
	SuccessCount int                  `json:"successCount"`
	FailCount    int                  `json:"failCount"`
	LastResult   *HeartbeatTestResult `json:"lastResult,omitempty"`
	StartTime    time.Time            `json:"startTime,omitempty"`
	NextTestTime time.Time            `json:"nextTestTime,omitempty"`
}

// HeartbeatTester 心跳测试器
type HeartbeatTester struct {
	requester zmqpkg.Requester // 用于发送心跳
	responder zmqpkg.Responder // 用于接收调度系统的反转心跳
	config    *SystemConfig
	logger    Logger

	// 状态管理
	mu           sync.RWMutex
	isRunning    bool
	successCount int
	failCount    int
	lastResult   *HeartbeatTestResult
	startTime    time.Time
	nextTestTime time.Time
	currentState bool // 当前状态值，用于状态取反

	// 控制通道
	stopCh chan struct{}
	ctx    context.Context
	cancel context.CancelFunc

	// 任务下发测试相关
	taskTestEnabled bool
	receivedTasks   []TaskTestResult
	taskTestMu      sync.RWMutex

	// 任务生命周期管理
	taskLifecycle   TaskLifecycleStatus
	taskLifecycleMu sync.RWMutex

	// 任务定时器
	taskStartTimer    *time.Timer
	taskCompleteTimer *time.Timer
}

// NewHeartbeatTester 创建心跳测试器
func NewHeartbeatTester(requester zmqpkg.Requester, responder zmqpkg.Responder, config *SystemConfig, logger Logger) *HeartbeatTester {
	return &HeartbeatTester{
		requester:       requester,
		responder:       responder,
		config:          config,
		logger:          logger,
		currentState:    true, // 初始状态为 true，和原本心跳管理器一致
		stopCh:          make(chan struct{}),
		taskTestEnabled: true, // 启用任务下发测试
		receivedTasks:   make([]TaskTestResult, 0),
		taskLifecycle: TaskLifecycleStatus{
			TaskState:             "none",
			StartNotifications:    make([]TaskNotificationResult, 0),
			CompleteNotifications: make([]TaskNotificationResult, 0),
		},
	}
}

// IsRunning 检查心跳测试是否正在运行
func (t *HeartbeatTester) IsRunning() bool {
	t.mu.RLock()
	defer t.mu.RUnlock()
	return t.isRunning
}

// Start 启动持续心跳测试
func (t *HeartbeatTester) Start() error {
	t.mu.Lock()
	defer t.mu.Unlock()

	if t.isRunning {
		return fmt.Errorf("heartbeat test is already running")
	}

	t.logger.Info("Starting heartbeat test with 1-minute interval")

	// 重置状态
	t.isRunning = true
	t.successCount = 0
	t.failCount = 0
	t.lastResult = nil
	t.startTime = time.Now()
	t.nextTestTime = time.Now().Add(1 * time.Minute)

	// 创建上下文
	t.ctx, t.cancel = context.WithCancel(context.Background())

	// 创建启动同步通道
	responderReady := make(chan error, 1)
	requesterReady := make(chan error, 1)

	// 启动Responder监听调度系统的反转心跳
	go func() {
		// 设置默认处理器（在测试期间会被临时替换）
		defaultHandler := func(request []byte) ([]byte, error) {
			t.logger.Info("Received message on responder", "data", string(request))
			// 按照原始代码格式返回JSON回复
			response := NewMessage(1, true, nil) // instruction=1, code=true
			response.Message = "Default response"
			responseData, err := json.Marshal(response)
			if err != nil {
				t.logger.Error("Failed to marshal default response", "error", err)
				return nil, err
			}
			return responseData, nil
		}

		if err := t.responder.SetHandler(defaultHandler); err != nil {
			t.logger.Error("Failed to set responder handler", "error", err)
			responderReady <- err
			return
		}

		t.logger.Info("Responder handler set successfully")
		responderReady <- nil

		// 在另一个goroutine中启动responder（这是阻塞调用）
		go func() {
			if err := t.responder.Start(t.ctx); err != nil {
				t.logger.Error("Responder stopped with error", "error", err)
			} else {
				t.logger.Info("Responder stopped normally")
			}
		}()
	}()

	// Requester不需要连接测试，直接标记为就绪
	go func() {
		// 给Requester一些时间来建立连接
		time.Sleep(100 * time.Millisecond)
		t.logger.Info("Requester ready")
		requesterReady <- nil
	}()

	// 等待两个组件都准备就绪
	var responderErr, requesterErr error
	for i := 0; i < 2; i++ {
		select {
		case responderErr = <-responderReady:
			if responderErr != nil {
				t.logger.Error("Responder startup failed", "error", responderErr)
			}
		case requesterErr = <-requesterReady:
			if requesterErr != nil {
				t.logger.Error("Requester startup failed", "error", requesterErr)
			}
		case <-time.After(2 * time.Minute):
			return fmt.Errorf("timeout waiting for ZMQ components to start")
		}
	}

	// 如果Responder启动失败，返回错误
	if responderErr != nil {
		return fmt.Errorf("responder startup failed: %w", responderErr)
	}

	// 延迟启动测试循环，确保所有组件都已就绪
	go func() {
		time.Sleep(200 * time.Millisecond) // 额外延迟确保稳定
		t.testLoop()
	}()

	return nil
}

// Stop 停止心跳测试
func (t *HeartbeatTester) Stop() error {
	t.mu.Lock()
	defer t.mu.Unlock()

	if !t.isRunning {
		return fmt.Errorf("heartbeat test is not running")
	}

	t.logger.Info("Stopping heartbeat test")

	// 停止测试
	t.isRunning = false
	if t.cancel != nil {
		t.cancel()
	}

	// 清理任务定时器
	t.cleanupTaskTimers()

	// 重置任务生命周期状态
	t.taskLifecycleMu.Lock()
	t.taskLifecycle = TaskLifecycleStatus{
		TaskState:             "none",
		StartNotifications:    make([]TaskNotificationResult, 0),
		CompleteNotifications: make([]TaskNotificationResult, 0),
	}
	t.taskLifecycleMu.Unlock()

	// 发送停止信号
	select {
	case t.stopCh <- struct{}{}:
	default:
	}

	// 关闭Responder
	if t.responder != nil {
		if err := t.responder.Close(); err != nil {
			t.logger.Error("Failed to close responder", "error", err)
		}
	}

	return nil
}

// GetStatus 获取测试状态
func (t *HeartbeatTester) GetStatus() *HeartbeatTestStatus {
	t.mu.RLock()
	defer t.mu.RUnlock()

	return &HeartbeatTestStatus{
		IsRunning:    t.isRunning,
		SuccessCount: t.successCount,
		FailCount:    t.failCount,
		LastResult:   t.lastResult,
		StartTime:    t.startTime,
		NextTestTime: t.nextTestTime,
	}
}

// handleTaskMessage 处理任务下发消息
func (t *HeartbeatTester) handleTaskMessage(msg Message, request []byte) ([]byte, error) {
	startTime := time.Now()

	t.logger.Info("🎯 收到任务下发消息", "instruction", msg.Instruction)

	// 解析任务内容
	taskResult := TaskTestResult{
		Instruction: msg.Instruction,
		Timestamp:   time.Now(),
	}

	// 根据指令类型处理
	switch msg.Instruction {
	case 200: // 任务下发
		return t.handleTaskAssignment(msg, &taskResult, startTime)
	case 201: // 无任务
		return t.handleNoTask(msg, &taskResult, startTime)
	case 202: // 等待任务
		return t.handleWaitTask(msg, &taskResult, startTime)
	case 203: // 继续任务
		return t.handleContinueTask(msg, &taskResult, startTime)
	default:
		taskResult.Success = false
		taskResult.Message = fmt.Sprintf("未知任务指令: %d", msg.Instruction)
		taskResult.ResponseTime = time.Since(startTime)
		t.recordTaskResult(taskResult)
		return t.createTaskErrorResponse(taskResult.Message)
	}
}

// handleTaskAssignment 处理任务下发 (指令200)
func (t *HeartbeatTester) handleTaskAssignment(msg Message, taskResult *TaskTestResult, startTime time.Time) ([]byte, error) {
	t.logger.Info("🎯 处理任务下发 (指令200)")

	// 解析任务内容
	if err := t.parseTaskContent(msg.Content, taskResult); err != nil {
		taskResult.Success = false
		taskResult.Message = fmt.Sprintf("解析任务内容失败: %v", err)
		taskResult.ResponseTime = time.Since(startTime)
		t.recordTaskResult(*taskResult)
		return t.createTaskErrorResponse(taskResult.Message)
	}

	// 验证机器人编号
	if taskResult.RobotNo != t.config.RobotNo {
		taskResult.Success = false
		taskResult.Message = fmt.Sprintf("机器人编号不匹配，期望: %s, 实际: %s", t.config.RobotNo, taskResult.RobotNo)
		taskResult.ResponseTime = time.Since(startTime)
		t.recordTaskResult(*taskResult)
		return t.createRobotMismatchResponse()
	}

	// 模拟任务处理
	taskResult.Success = true
	taskResult.Message = "Task assignment accepted"
	taskResult.ResponseTime = time.Since(startTime)
	t.recordTaskResult(*taskResult)

	// 启动任务生命周期管理
	t.startTaskLifecycle(taskResult.TaskId)

	t.logger.Info("✅ 任务下发处理成功",
		"taskId", taskResult.TaskId,
		"robotNo", taskResult.RobotNo,
		"laneNos", taskResult.LaneNos,
		"responseTime", taskResult.ResponseTime)

	return t.createTaskSuccessResponse(taskResult.Message)
}

// handleNoTask 处理无任务 (指令201)
func (t *HeartbeatTester) handleNoTask(msg Message, taskResult *TaskTestResult, startTime time.Time) ([]byte, error) {
	t.logger.Info("🎯 处理无任务 (指令201)")

	taskResult.Success = true
	taskResult.Message = "No task acknowledged, returning to parking"
	taskResult.ResponseTime = time.Since(startTime)
	t.recordTaskResult(*taskResult)

	t.logger.Info("✅ 无任务处理成功", "responseTime", taskResult.ResponseTime)
	return t.createTaskSuccessResponse(taskResult.Message)
}

// handleWaitTask 处理等待任务 (指令202)
func (t *HeartbeatTester) handleWaitTask(msg Message, taskResult *TaskTestResult, startTime time.Time) ([]byte, error) {
	t.logger.Info("🎯 处理等待任务 (指令202)")

	taskResult.Success = true
	taskResult.Message = "Wait task acknowledged, standing by"
	taskResult.ResponseTime = time.Since(startTime)
	t.recordTaskResult(*taskResult)

	t.logger.Info("✅ 等待任务处理成功", "responseTime", taskResult.ResponseTime)
	return t.createTaskSuccessResponse(taskResult.Message)
}

// handleContinueTask 处理继续任务 (指令203)
func (t *HeartbeatTester) handleContinueTask(msg Message, taskResult *TaskTestResult, startTime time.Time) ([]byte, error) {
	t.logger.Info("🎯 处理继续任务 (指令203)")

	taskResult.Success = true
	taskResult.Message = "Continue task acknowledged"
	taskResult.ResponseTime = time.Since(startTime)
	t.recordTaskResult(*taskResult)

	t.logger.Info("✅ 继续任务处理成功", "responseTime", taskResult.ResponseTime)
	return t.createTaskSuccessResponse(taskResult.Message)
}

// parseTaskContent 解析任务内容
func (t *HeartbeatTester) parseTaskContent(content interface{}, taskResult *TaskTestResult) error {
	if content == nil {
		return fmt.Errorf("invalid content format")
	}

	contentMap, ok := content.(map[string]interface{})
	if !ok {
		return fmt.Errorf("invalid content format")
	}

	// 解析 robotNo (必需字段)
	robotNo, exists := contentMap["robotNo"]
	if !exists {
		return fmt.Errorf("missing or invalid robotNo")
	}
	if robotNoStr, ok := robotNo.(string); ok {
		taskResult.RobotNo = robotNoStr
	} else {
		return fmt.Errorf("missing or invalid robotNo")
	}

	// 解析 taskId (必需字段)
	taskId, exists := contentMap["taskId"]
	if !exists {
		return fmt.Errorf("missing or invalid taskId")
	}
	if taskIdStr, ok := taskId.(string); ok {
		taskResult.TaskId = taskIdStr
	} else {
		return fmt.Errorf("missing or invalid taskId")
	}

	// 解析 laneNos
	if laneNos, exists := contentMap["laneNos"]; exists {
		if laneNosArray, ok := laneNos.([]interface{}); ok {
			taskResult.LaneNos = make([]string, 0, len(laneNosArray))
			for _, lane := range laneNosArray {
				if laneStr, ok := lane.(string); ok {
					taskResult.LaneNos = append(taskResult.LaneNos, laneStr)
				}
			}
		}
	}

	// 解析 remark (可选)
	if remark, exists := contentMap["remark"]; exists {
		if remarkStr, ok := remark.(string); ok {
			taskResult.Remark = remarkStr
		}
	}

	return nil
}

// recordTaskResult 记录任务测试结果
func (t *HeartbeatTester) recordTaskResult(result TaskTestResult) {
	t.taskTestMu.Lock()
	defer t.taskTestMu.Unlock()

	t.receivedTasks = append(t.receivedTasks, result)

	// 保持最近100条记录
	if len(t.receivedTasks) > 100 {
		t.receivedTasks = t.receivedTasks[len(t.receivedTasks)-100:]
	}
}

// createTaskSuccessResponse 创建任务成功响应
func (t *HeartbeatTester) createTaskSuccessResponse(message string) ([]byte, error) {
	response := NewMessage(1, true, nil) // instruction=1, code=true
	response.Message = message

	responseData, err := json.Marshal(response)
	if err != nil {
		return nil, fmt.Errorf("marshal success response failed: %w", err)
	}

	t.logger.Info("📤 发送任务成功响应", "response", string(responseData))
	return responseData, nil
}

// createTaskErrorResponse 创建任务错误响应
func (t *HeartbeatTester) createTaskErrorResponse(errorMsg string) ([]byte, error) {
	response := NewMessage(-99, true, nil) // instruction=-99, code=true
	response.Message = errorMsg

	responseData, err := json.Marshal(response)
	if err != nil {
		return nil, fmt.Errorf("marshal error response failed: %w", err)
	}

	t.logger.Info("📤 发送任务错误响应", "response", string(responseData))
	return responseData, nil
}

// createRobotMismatchResponse 创建机器人编号不匹配响应
func (t *HeartbeatTester) createRobotMismatchResponse() ([]byte, error) {
	response := NewMessage(-99, true, nil) // instruction=-99, code=true
	response.Message = "非本机任务"

	responseData, err := json.Marshal(response)
	if err != nil {
		return nil, fmt.Errorf("marshal robot mismatch response failed: %w", err)
	}

	t.logger.Info("📤 发送机器人不匹配响应", "response", string(responseData))
	return responseData, nil
}

// GetTaskTestResults 获取任务测试结果
func (t *HeartbeatTester) GetTaskTestResults() []TaskTestResult {
	t.taskTestMu.RLock()
	defer t.taskTestMu.RUnlock()

	// 返回副本，避免并发问题
	results := make([]TaskTestResult, len(t.receivedTasks))
	copy(results, t.receivedTasks)
	return results
}

// GetTaskTestStatus 获取任务测试状态
func (t *HeartbeatTester) GetTaskTestStatus() map[string]interface{} {
	t.taskTestMu.RLock()
	defer t.taskTestMu.RUnlock()

	successCount := 0
	failCount := 0
	for _, task := range t.receivedTasks {
		if task.Success {
			successCount++
		} else {
			failCount++
		}
	}

	return map[string]interface{}{
		"taskTestEnabled": t.taskTestEnabled,
		"totalTasks":      len(t.receivedTasks),
		"successCount":    successCount,
		"failCount":       failCount,
		"recentTasks":     t.receivedTasks,
	}
}

// testLoop 测试循环
func (t *HeartbeatTester) testLoop() {
	t.logger.Info("Heartbeat test loop started")

	// 立即执行第一次测试
	t.performTest()

	// 创建1分钟定时器
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-t.ctx.Done():
			t.logger.Info("Heartbeat test loop stopped by context")
			return
		case <-t.stopCh:
			t.logger.Info("Heartbeat test loop stopped by stop signal")
			return
		case <-ticker.C:
			t.performTest()
		}
	}
}

// performTest 执行单次心跳测试
func (t *HeartbeatTester) performTest() {
	t.logger.Debug("Performing heartbeat test")

	startTime := time.Now()

	// 第1步：获取当前状态（不在发送前取反）
	t.mu.Lock()
	currentState := t.currentState
	t.mu.Unlock()

	t.logger.Debug("Heartbeat test state", "currentState", currentState)

	// 创建心跳消息（使用当前状态）
	msg := NewHeartbeatMessage(t.config.RobotNo, currentState)

	// 序列化消息
	data, err := json.Marshal(msg)
	if err != nil {
		t.recordResult(false, 0, fmt.Sprintf("marshal message failed: %v", err))
		return
	}

	// 打印发送的消息
	t.logger.Info("📤 第1步：发送心跳消息", "message", string(data))

	// 发送心跳并等待回复，带重试机制
	var replyData []byte
	maxRetries := 3

	for attempt := 1; attempt <= maxRetries; attempt++ {
		t.logger.Info("尝试发送心跳", "attempt", attempt, "maxRetries", maxRetries)

		replyData, err = t.requester.RequestWithTimeout(data, 2*time.Minute)
		if err == nil {
			t.logger.Info("心跳发送成功", "attempt", attempt)
			break // 成功，跳出重试循环
		}

		t.logger.Error("心跳发送失败",
			"attempt", attempt,
			"maxRetries", maxRetries,
			"error", err,
			"errorType", fmt.Sprintf("%T", err))

		if attempt < maxRetries {
			retryDelay := time.Duration(attempt) * time.Second
			t.logger.Info("等待后重试", "retryDelay", retryDelay)
			time.Sleep(retryDelay)
		}
	}

	responseTime := time.Since(startTime)

	if err != nil {
		t.recordResult(false, responseTime, fmt.Sprintf("send heartbeat failed after %d attempts: %v", maxRetries, err))
		return
	}

	// 打印接收的消息
	t.logger.Info("📥 第2步：接收确认回复", "reply", string(replyData))

	// 第2步：解析调度系统的确认回复
	var reply Message
	if err := json.Unmarshal(replyData, &reply); err != nil {
		t.recordResult(false, responseTime, fmt.Sprintf("unmarshal reply failed: %v", err))
		return
	}

	// 验证确认回复：instruction=1, code=true
	if reply.Instruction != 1 || !reply.Code {
		t.recordResult(false, responseTime, fmt.Sprintf("invalid confirmation reply: instruction=%d, code=%v", reply.Instruction, reply.Code))
		return
	}

	t.logger.Info("✅ 第2步：确认回复验证成功", "instruction", reply.Instruction, "code", reply.Code)

	// 第3步：等待调度系统发送反转心跳
	t.logger.Info("⏳ 第3步：等待调度系统发送反转心跳...")

	// 在真实环境中，这里需要启动ZMQ Responder来接收调度系统的反转心跳
	// 但在测试环境中，我们需要创建一个Responder来监听
	reversedHeartbeatData, err := t.waitForReversedHeartbeat(2 * time.Minute)
	if err != nil {
		t.recordResult(false, time.Since(startTime), fmt.Sprintf("wait for reversed heartbeat failed: %v", err))
		return
	}

	// 打印接收的反转心跳
	t.logger.Info("📥 第3步：接收反转心跳", "reversedHeartbeat", string(reversedHeartbeatData))

	// 解析反转心跳
	var reversedHeartbeat Message
	if err := json.Unmarshal(reversedHeartbeatData, &reversedHeartbeat); err != nil {
		t.recordResult(false, time.Since(startTime), fmt.Sprintf("unmarshal reversed heartbeat failed: %v", err))
		return
	}

	// 验证是心跳消息
	if reversedHeartbeat.Instruction != 0 {
		t.recordResult(false, time.Since(startTime), fmt.Sprintf("invalid reversed heartbeat instruction: expected 0, got %d", reversedHeartbeat.Instruction))
		return
	}

	// 解析反转后的状态
	contentMap, ok := reversedHeartbeat.Content.(map[string]interface{})
	if !ok {
		t.recordResult(false, time.Since(startTime), "invalid content format in reversed heartbeat")
		return
	}

	reversedState, ok := contentMap["state"].(bool)
	if !ok {
		t.recordResult(false, time.Since(startTime), "invalid state in reversed heartbeat content")
		return
	}

	t.logger.Info("✅ 第3步：反转心跳验证成功", "reversedState", reversedState)

	// 第4步：回复确认给调度系统
	confirmReply := NewMessage(1, true, nil) // instruction=1, code=true
	confirmReplyData, err := json.Marshal(confirmReply)
	if err != nil {
		t.recordResult(false, time.Since(startTime), fmt.Sprintf("marshal confirmation reply failed: %v", err))
		return
	}

	t.logger.Info("📤 第4步：发送确认回复", "reply", string(confirmReplyData))

	// 第4步：发送确认回复（在真实环境中，这应该通过ZMQ Responder发送）
	err = t.sendConfirmationReply(confirmReplyData)
	if err != nil {
		t.recordResult(false, time.Since(startTime), fmt.Sprintf("send confirmation reply failed: %v", err))
		return
	}

	t.logger.Info("✅ 第4步：确认回复发送成功")

	// 第5步：更新状态并准备下次心跳
	t.mu.Lock()
	oldState := t.currentState
	t.currentState = reversedState // 使用调度系统发送的反转状态
	t.mu.Unlock()

	t.logger.Info("🔄 第5步：状态更新完成", "oldState", oldState, "newState", t.currentState)

	// 记录完整流程成功
	t.recordResult(true, time.Since(startTime), "complete heartbeat cycle successful")
}

// waitForReversedHeartbeat 等待调度系统发送反转心跳
func (t *HeartbeatTester) waitForReversedHeartbeat(timeout time.Duration) ([]byte, error) {
	// 这里需要实现ZMQ Responder来接收调度系统的反转心跳
	// 在完整实现中，应该创建一个Responder socket监听调度系统的连接

	t.logger.Info("⏳ 等待调度系统发送反转心跳", "timeout", timeout)

	// 在真实环境中，调度系统会通过另一个ZMQ连接发送反转心跳
	// 我们的Responder应该已经在监听这些消息
	// 但在测试环境中，我们需要设置一个临时的消息接收器

	// 创建接收通道
	resultCh := make(chan []byte, 1)
	errorCh := make(chan error, 1)

	// 设置消息处理器来接收反转心跳和任务下发
	handler := func(request []byte) ([]byte, error) {
		t.logger.Info("📥 接收到调度系统消息", "data", string(request))

		// 解析消息
		var msg Message
		if err := json.Unmarshal(request, &msg); err != nil {
			// 记录无法解析的消息详细信息
			t.logger.Error("❌ 无法解析的消息",
				"error", err,
				"raw_data_string", string(request),
				"raw_data_hex", fmt.Sprintf("%x", request),
				"data_length", len(request))

			errorCh <- fmt.Errorf("parse message failed: %w", err)
			return nil, err
		}

		// 根据指令类型处理
		switch msg.Instruction {
		case 0: // 心跳消息 - 反转心跳
			t.logger.Info("📥 第3步：接收到反转心跳消息")
			// 这是反转心跳，发送到结果通道
			resultCh <- request

			// 返回确认回复 (instruction=1, code=true)
			reply := NewMessage(1, true, nil)
			replyData, err := json.Marshal(reply)
			if err != nil {
				errorCh <- fmt.Errorf("marshal reply failed: %w", err)
				return nil, err
			}

			t.logger.Info("📤 第4步：发送确认回复", "reply", string(replyData))
			return replyData, nil

		case 200, 201, 202, 203: // 任务下发消息
			if t.taskTestEnabled {
				return t.handleTaskMessage(msg, request)
			} else {
				t.logger.Warn("任务测试未启用，忽略任务消息", "instruction", msg.Instruction)
				return t.createTaskErrorResponse("任务测试未启用")
			}

		default:
			// 其他消息类型
			t.logger.Warn("收到未知指令消息", "instruction", msg.Instruction)
			errorCh <- fmt.Errorf("unexpected message instruction: %d", msg.Instruction)
			return nil, fmt.Errorf("unexpected message instruction: %d", msg.Instruction)
		}
	}

	// 设置处理器
	if err := t.responder.SetHandler(handler); err != nil {
		return nil, fmt.Errorf("set responder handler failed: %w", err)
	}

	// 等待结果或超时（Responder已经在Start()方法中启动）
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	select {
	case data := <-resultCh:
		return data, nil
	case err := <-errorCh:
		return nil, err
	case <-ctx.Done():
		return nil, fmt.Errorf("wait for reversed heartbeat timeout after %v", timeout)
	}
}

// sendConfirmationReply 发送确认回复
func (t *HeartbeatTester) sendConfirmationReply(replyData []byte) error {
	// 注意：在真实的心跳流程中，确认回复是通过Responder的handler返回值发送的
	// 这个方法在当前实现中实际上不会被调用，因为回复已经在handler中处理了

	t.logger.Info("💡 确认回复已通过Responder handler发送")
	return nil
}

// recordResult 记录测试结果
func (t *HeartbeatTester) recordResult(success bool, responseTime time.Duration, message string) {
	t.mu.Lock()
	defer t.mu.Unlock()

	// 更新统计
	if success {
		t.successCount++
		t.logger.Info("Heartbeat test successful", "responseTime", responseTime, "successCount", t.successCount)
	} else {
		t.failCount++
		t.logger.Error("Heartbeat test failed", "error", message, "failCount", t.failCount)
	}

	// 记录结果
	t.lastResult = &HeartbeatTestResult{
		Success:      success,
		ResponseTime: responseTime,
		Error:        message,
		Timestamp:    time.Now(),
	}

	// 更新下次测试时间
	if t.isRunning {
		t.nextTestTime = time.Now().Add(1 * time.Minute)
	}
}

// GetCurrentState 获取当前状态值
func (t *HeartbeatTester) GetCurrentState() bool {
	t.mu.RLock()
	defer t.mu.RUnlock()
	return t.currentState
}

// cleanupTaskTimers 清理任务定时器
func (t *HeartbeatTester) cleanupTaskTimers() {
	if t.taskStartTimer != nil {
		t.taskStartTimer.Stop()
		t.taskStartTimer = nil
	}
	if t.taskCompleteTimer != nil {
		t.taskCompleteTimer.Stop()
		t.taskCompleteTimer = nil
	}
}

// GetTaskLifecycleStatus 获取任务生命周期状态
func (t *HeartbeatTester) GetTaskLifecycleStatus() TaskLifecycleStatus {
	t.taskLifecycleMu.RLock()
	defer t.taskLifecycleMu.RUnlock()

	// 创建副本以避免并发访问问题
	status := t.taskLifecycle

	// 复制切片
	status.StartNotifications = make([]TaskNotificationResult, len(t.taskLifecycle.StartNotifications))
	copy(status.StartNotifications, t.taskLifecycle.StartNotifications)

	status.CompleteNotifications = make([]TaskNotificationResult, len(t.taskLifecycle.CompleteNotifications))
	copy(status.CompleteNotifications, t.taskLifecycle.CompleteNotifications)

	return status
}

// startTaskLifecycle 启动任务生命周期管理
func (t *HeartbeatTester) startTaskLifecycle(taskId string) {
	t.taskLifecycleMu.Lock()
	defer t.taskLifecycleMu.Unlock()

	// 清理之前的定时器
	t.cleanupTaskTimers()

	// 更新任务状态
	t.taskLifecycle.CurrentTaskId = taskId
	t.taskLifecycle.TaskState = "received"
	t.taskLifecycle.TaskStartTime = time.Time{}    // 重置开始时间
	t.taskLifecycle.TaskCompleteTime = time.Time{} // 重置完成时间

	t.logger.Info("🚀 启动任务生命周期管理", "taskId", taskId)

	// 设置45秒后发送任务开始的定时器
	t.taskStartTimer = time.AfterFunc(45*time.Second, func() {
		t.sendTaskStartNotification(taskId)
	})

	t.logger.Info("⏰ 已设置45秒后发送任务开始通知", "taskId", taskId)
}

// sendTaskStartNotification 发送任务开始通知
func (t *HeartbeatTester) sendTaskStartNotification(taskId string) {
	t.logger.Info("📤 开始发送任务开始通知 (指令110)", "taskId", taskId)

	startTime := time.Now()
	remark := "Task started"

	// 创建任务开始消息
	msg := NewTaskStartMessage(t.config.RobotNo, taskId, remark)
	data, err := json.Marshal(msg)
	if err != nil {
		t.recordTaskNotification(taskId, InstructionTaskStart, false, 0, fmt.Sprintf("marshal task start message failed: %v", err))
		return
	}

	t.logger.Info("📤 发送任务开始消息", "message", string(data))

	// 发送消息并等待回复
	replyData, err := t.requester.RequestWithTimeout(data, 2*time.Minute)
	responseTime := time.Since(startTime)

	if err != nil {
		t.recordTaskNotification(taskId, InstructionTaskStart, false, responseTime, fmt.Sprintf("send task start failed: %v", err))
		return
	}

	t.logger.Info("📥 收到任务开始回复", "reply", string(replyData))

	// 解析回复
	var reply Message
	if err := json.Unmarshal(replyData, &reply); err != nil {
		t.recordTaskNotification(taskId, InstructionTaskStart, false, responseTime, fmt.Sprintf("unmarshal task start reply failed: %v", err))
		return
	}

	// 验证回复
	success := reply.Instruction == 1 && reply.Code
	var message string
	if success {
		message = "任务开始通知发送成功"
	} else {
		message = fmt.Sprintf("任务开始通知回复无效: instruction=%d, code=%v", reply.Instruction, reply.Code)
	}

	t.recordTaskNotification(taskId, InstructionTaskStart, success, responseTime, message)

	if success {
		// 更新任务状态为已开始
		t.taskLifecycleMu.Lock()
		t.taskLifecycle.TaskState = "started"
		t.taskLifecycle.TaskStartTime = time.Now()
		t.taskLifecycleMu.Unlock()

		t.logger.Info("✅ 任务开始通知成功", "taskId", taskId, "responseTime", responseTime)

		// 设置45秒后发送任务完成的定时器
		t.taskCompleteTimer = time.AfterFunc(45*time.Second, func() {
			t.sendTaskCompleteNotification(taskId)
		})

		t.logger.Info("⏰ 已设置45秒后发送任务完成通知", "taskId", taskId)
	} else {
		t.logger.Error("❌ 任务开始通知失败", "taskId", taskId, "message", message)
	}
}

// sendTaskCompleteNotification 发送任务完成通知
func (t *HeartbeatTester) sendTaskCompleteNotification(taskId string) {
	t.logger.Info("📤 开始发送任务完成通知 (指令130)", "taskId", taskId)

	startTime := time.Now()
	remark := "Task completed successfully"

	// 创建任务完成消息
	msg := NewTaskCompleteMessage(t.config.RobotNo, taskId, remark)
	data, err := json.Marshal(msg)
	if err != nil {
		t.recordTaskNotification(taskId, InstructionTaskComplete, false, 0, fmt.Sprintf("marshal task complete message failed: %v", err))
		return
	}

	t.logger.Info("📤 发送任务完成消息", "message", string(data))

	// 发送消息并等待回复
	replyData, err := t.requester.RequestWithTimeout(data, 2*time.Minute)
	responseTime := time.Since(startTime)

	if err != nil {
		t.recordTaskNotification(taskId, InstructionTaskComplete, false, responseTime, fmt.Sprintf("send task complete failed: %v", err))
		return
	}

	t.logger.Info("📥 收到任务完成回复", "reply", string(replyData))

	// 解析回复
	var reply Message
	if err := json.Unmarshal(replyData, &reply); err != nil {
		t.recordTaskNotification(taskId, InstructionTaskComplete, false, responseTime, fmt.Sprintf("unmarshal task complete reply failed: %v", err))
		return
	}

	// 验证回复
	success := reply.Instruction == 1 && reply.Code
	var message string
	if success {
		message = "任务完成通知发送成功"
	} else {
		message = fmt.Sprintf("任务完成通知回复无效: instruction=%d, code=%v", reply.Instruction, reply.Code)
	}

	t.recordTaskNotification(taskId, InstructionTaskComplete, success, responseTime, message)

	if success {
		// 更新任务状态为已完成
		t.taskLifecycleMu.Lock()
		t.taskLifecycle.TaskState = "completed"
		t.taskLifecycle.TaskCompleteTime = time.Now()
		t.taskLifecycleMu.Unlock()

		t.logger.Info("✅ 任务完成通知成功", "taskId", taskId, "responseTime", responseTime)
		t.logger.Info("🎉 任务生命周期完成", "taskId", taskId)
	} else {
		t.logger.Error("❌ 任务完成通知失败", "taskId", taskId, "message", message)
	}
}

// recordTaskNotification 记录任务通知结果
func (t *HeartbeatTester) recordTaskNotification(taskId string, instruction int, success bool, responseTime time.Duration, message string) {
	t.taskLifecycleMu.Lock()
	defer t.taskLifecycleMu.Unlock()

	result := TaskNotificationResult{
		TaskId:       taskId,
		Instruction:  instruction,
		RobotNo:      t.config.RobotNo,
		Remark:       message,
		Success:      success,
		ResponseTime: responseTime,
		Message:      message,
		Timestamp:    time.Now(),
	}

	// 根据指令类型添加到相应的列表
	if instruction == InstructionTaskStart {
		t.taskLifecycle.StartNotifications = append(t.taskLifecycle.StartNotifications, result)
		t.logger.Info("📝 记录任务开始通知结果", "success", success, "responseTime", responseTime)
	} else if instruction == InstructionTaskComplete {
		t.taskLifecycle.CompleteNotifications = append(t.taskLifecycle.CompleteNotifications, result)
		t.logger.Info("📝 记录任务完成通知结果", "success", success, "responseTime", responseTime)
	}
}
