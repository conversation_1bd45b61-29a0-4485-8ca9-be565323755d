{"maxRetries": 3, "retryDelayBase": 5000000000, "plcAddresses": {"controlRequestAddress": 601, "controlConfirmAddress": 501, "taskStartAddress": 602, "readyAddress": 509, "directionAddress": 604, "distanceAddress": 602, "rollerAddress": 600, "completeAddress": 603, "verifyDirectionAddress": 505, "verifyRollerAddress": 501, "verifyDistanceAddress": 502, "verificationCompleteAddress": 605, "workStatusAddress": 500, "robotStartSignalAddress": 612, "machineCompleteAddress": 606, "returnToOriginAddress": 610, "originDirectionAddress": 611, "originCodeValueAddress": 604, "originArrivalAddress": 510, "controlHandoverAddress": 607, "turnAroundAddress": 608, "turnCompleteAddress": 511, "exitLaneAddress": 609, "laneExitCompleteAddress": 508}, "timeouts": {"navigationTimeout": 1200000000000, "navigationCheckInterval": 2000000000, "plcReadyTimeout": 300000000000, "plcReadyCheckInterval": 500000000, "plcWorkTimeout": 300000000000, "plcWorkCheckInterval": 3000000000, "turnAroundTimeout": 600000000000, "turnAroundCheckInterval": 3000000000, "laneExitTimeout": 60000000000, "laneExitCheckInterval": 500000000, "originArrivalTimeout": 600000000000, "originArrivalCheckInterval": 3000000000}, "safeTurnConfig": {"minSpindle": 50, "maxSpindle": 1100, "startAddress": 606, "endAddress": 608}, "parkingPoint": {"id": 1, "x": 0, "y": 0, "angle": 0}, "allowedMachines": [], "spindleDataSource": "mysql", "mysqlSpindleConfig": {"host": "*************", "port": "3306", "username": "root", "password": "remote", "database": "schedule", "timeout": 30000000000}, "work_mode": "scheduled", "zmq": {"enabled": true, "scheduler": {"requester_endpoint": "tcp://************:5556", "responder_endpoint": "tcp://*:5557", "heartbeat_interval_minutes": 1, "reply_timeout_seconds": 30, "max_retry_attempts": 4, "retry_interval_seconds": 5}}}