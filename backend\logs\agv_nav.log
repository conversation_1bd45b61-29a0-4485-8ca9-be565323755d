[2025-06-25 15:31:44.448] [INFO] [default] logger.go:221 - 🚀 AGV控制系统启动
[2025-06-25 15:31:44.448] [WARN] [default] logger.go:225 - 这是警告信息
[2025-06-25 15:31:44.448] [ERROR] [default] logger.go:229 - 这是错误信息
[2025-06-25 15:31:44.448] [INFO] [plc] test_logger.go:25 - PLC连接成功
[2025-06-25 15:31:44.448] [WARN] [plc] test_logger.go:26 - PLC通信延迟较高: 500ms
[2025-06-25 15:31:44.448] [INFO] [plc] module_loggers.go:136 - 写入PLC地址 M601 = true
[2025-06-25 15:31:44.448] [INFO] [agv] module_loggers.go:156 - AGV导航到位置 (10.50, 20.30)
[2025-06-25 15:31:44.550] [INFO] [default] logger.go:221 - 日志记录器健康状态: map[agv:true api:true config:true database:true plc:true protocol:true task:true]
[2025-06-25 15:31:44.550] [INFO] [default] logger.go:221 - 日志系统测试完成
[2025-06-25 17:26:24.174] [INFO] [test] test_logger.go:12 - 测试结构化日志 key1=value1 key2=123 key3=true
[2025-06-25 17:26:24.174] [ERROR] [test] test_logger.go:13 - 测试错误日志 error=something went wrong code=500
[2025-06-25 17:26:24.174] [INFO] [test] test_logger.go:16 - 测试格式化日志: number = 42
[2025-06-25 17:26:24.174] [INFO] [test] test_logger.go:18 - 日志系统测试完成
[2025-06-25 22:33:03.654] [INFO] [test] test_logger.go:10 - 测试结构化日志 key1=value1 key2=123 key3=true
[2025-06-25 22:33:03.654] [ERROR] [test] test_logger.go:11 - 测试错误日志 error=something went wrong code=500
[2025-06-25 22:33:03.654] [INFO] [test] test_logger.go:14 - 测试格式化日志: number = 42
[2025-06-25 22:33:03.654] [INFO] [test] test_logger.go:16 - 日志系统测试完成
[2025-07-30 11:26:57.981] [INFO] [zmq-test] heartbeat_tester.go:73 - Starting heartbeat test with 1-minute interval
[2025-07-30 11:26:57.984] [INFO] [zmq-test] heartbeat_tester.go:135 - Heartbeat test loop started
[2025-07-30 11:26:57.985] [INFO] [zmq-test] heartbeat_tester.go:184 - 📤 发送心跳消息 message={"instruction":0,"timeStamp":"2025-07-30T11:26:57.9850951+08:00","code":true,"content":{"robotNo":"AGV001","state":true}}
[2025-07-30 11:27:02.999] [ERROR] [zmq-test] heartbeat_tester.go:226 - Heartbeat test failed error=send heartbeat failed: zmq recv_reply error: An attempt was made to load a program with an incorrect format. failCount=1
[2025-07-30 11:28:03.000] [INFO] [zmq-test] heartbeat_tester.go:184 - 📤 发送心跳消息 message={"instruction":0,"timeStamp":"2025-07-30T11:28:03.0002361+08:00","code":true,"content":{"robotNo":"AGV001","state":false}}
[2025-07-30 11:28:03.001] [ERROR] [zmq-test] heartbeat_tester.go:226 - Heartbeat test failed error=send heartbeat failed: zmq send_request error: Operation cannot be accomplished in current state failCount=2
[2025-07-30 11:29:03.003] [INFO] [zmq-test] heartbeat_tester.go:184 - 📤 发送心跳消息 message={"instruction":0,"timeStamp":"2025-07-30T11:29:03.0037091+08:00","code":true,"content":{"robotNo":"AGV001","state":true}}
[2025-07-30 11:29:03.023] [ERROR] [zmq-test] heartbeat_tester.go:226 - Heartbeat test failed error=send heartbeat failed: zmq send_request error: Operation cannot be accomplished in current state failCount=3
[2025-07-30 11:30:03.001] [INFO] [zmq-test] heartbeat_tester.go:184 - 📤 发送心跳消息 message={"instruction":0,"timeStamp":"2025-07-30T11:30:03.0012865+08:00","code":true,"content":{"robotNo":"AGV001","state":false}}
[2025-07-30 11:30:03.001] [ERROR] [zmq-test] heartbeat_tester.go:226 - Heartbeat test failed error=send heartbeat failed: zmq send_request error: Operation cannot be accomplished in current state failCount=4
[2025-07-30 11:31:03.000] [INFO] [zmq-test] heartbeat_tester.go:184 - 📤 发送心跳消息 message={"instruction":0,"timeStamp":"2025-07-30T11:31:03.0008495+08:00","code":true,"content":{"robotNo":"AGV001","state":true}}
[2025-07-30 11:31:03.004] [ERROR] [zmq-test] heartbeat_tester.go:226 - Heartbeat test failed error=send heartbeat failed: zmq send_request error: Operation cannot be accomplished in current state failCount=5
[2025-07-30 11:32:03.005] [INFO] [zmq-test] heartbeat_tester.go:184 - 📤 发送心跳消息 message={"instruction":0,"timeStamp":"2025-07-30T11:32:03.0040665+08:00","code":true,"content":{"robotNo":"AGV001","state":false}}
[2025-07-30 11:32:03.005] [ERROR] [zmq-test] heartbeat_tester.go:226 - Heartbeat test failed error=send heartbeat failed: zmq send_request error: Operation cannot be accomplished in current state failCount=6
[2025-07-30 11:32:17.610] [INFO] [zmq-test] heartbeat_tester.go:101 - Stopping heartbeat test
[2025-07-30 11:32:17.610] [INFO] [zmq-test] heartbeat_tester.go:147 - Heartbeat test loop stopped by context
[2025-07-30 11:32:23.829] [INFO] [zmq-test] heartbeat_tester.go:73 - Starting heartbeat test with 1-minute interval
[2025-07-30 11:32:23.829] [INFO] [zmq-test] heartbeat_tester.go:135 - Heartbeat test loop started
[2025-07-30 11:32:23.829] [INFO] [zmq-test] heartbeat_tester.go:184 - 📤 发送心跳消息 message={"instruction":0,"timeStamp":"2025-07-30T11:32:23.829977+08:00","code":true,"content":{"robotNo":"AGV001","state":true}}
[2025-07-30 11:32:23.831] [ERROR] [zmq-test] heartbeat_tester.go:226 - Heartbeat test failed error=send heartbeat failed: zmq send_request error: Operation cannot be accomplished in current state failCount=1
[2025-07-30 11:32:34.668] [INFO] [zmq-test] heartbeat_tester.go:101 - Stopping heartbeat test
[2025-07-30 11:32:34.668] [INFO] [zmq-test] heartbeat_tester.go:147 - Heartbeat test loop stopped by context
