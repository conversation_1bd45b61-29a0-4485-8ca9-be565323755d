package cache

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// Manager 简化的缓存管理器
type Manager struct {
	cache           map[string]*CacheEntry
	enabledMachines []string
	interval        time.Duration
	isRunning       bool
	totalHits       int
	totalMisses     int
	stopChan        chan struct{}
	wg              sync.WaitGroup
	mutex           sync.RWMutex
	httpClient      *http.Client
	storageFile     string
}

// MESRequest MES API请求
type MESRequest struct {
	CustomerNo string `json:"customerNo"`
	MachineID  string `json:"machineId"`
}

// MESResponse MES API响应
type MESResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		BreakageDTOList []MESBreakageDTO `json:"mes86099703BreakageDTOList"`
	} `json:"data"`
}

// 全局实例
var globalManager *Manager
var globalMutex sync.RWMutex

// CacheConfig 缓存配置结构
type CacheConfig struct {
	Enable               bool     `json:"enable"`
	DefaultInterval      int      `json:"default_interval"`
	DefaultMachines      []string `json:"default_machines"`
	ExpiryMinutes        int      `json:"expiry_minutes"`
	MaxRetries           int      `json:"max_retries"`
	RetryIntervalSeconds int      `json:"retry_interval_seconds"`
	DataStoragePath      string   `json:"data_storage_path"`
	EnableFileStorage    bool     `json:"enable_file_storage"`
	AutoStartOnBoot      bool     `json:"auto_start_on_boot"`
}

// 全局缓存配置
var globalCacheConfig *CacheConfig

// 默认配置
var defaultCacheConfig = &CacheConfig{
	Enable:               false,
	DefaultInterval:      5,
	DefaultMachines:      []string{"61", "60", "59", "58", "57"},
	ExpiryMinutes:        30,
	MaxRetries:           3,
	RetryIntervalSeconds: 5,
	DataStoragePath:      "backend/data/cache_data.json",
	EnableFileStorage:    true,
	AutoStartOnBoot:      false,
}

// InitCacheConfig 初始化缓存配置
func InitCacheConfig() error {
	// 首先使用默认配置
	globalCacheConfig = defaultCacheConfig

	// 尝试从配置文件加载
	configPath := getCacheConfigPath()
	if _, err := os.Stat(configPath); err == nil {
		log.Printf("加载缓存配置文件: %s", configPath)
		if err := loadCacheConfigFromFile(configPath); err != nil {
			log.Printf("警告：加载缓存配置文件失败: %v，使用默认配置", err)
		}
	} else {
		log.Printf("缓存配置文件不存在，使用默认配置: %s", configPath)
	}

	log.Printf("缓存配置初始化完成")
	return nil
}

// getCacheConfigPath 获取缓存配置文件路径
func getCacheConfigPath() string {
	possiblePaths := []string{
		filepath.Join("backend", "data", "task_config.json"),
		filepath.Join("data", "task_config.json"),
		filepath.Join(".", "data", "task_config.json"),
		filepath.Join("backend-data", "data", "task_config.json"),
		filepath.Join("..", "backend-data", "data", "task_config.json"),
	}

	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			return path
		}
	}

	return filepath.Join("data", "task_config.json")
}

// loadCacheConfigFromFile 从文件加载缓存配置
func loadCacheConfigFromFile(path string) error {
	data, err := ioutil.ReadFile(path)
	if err != nil {
		return err
	}

	var config struct {
		Cache CacheConfig `json:"cache"`
	}

	if err := json.Unmarshal(data, &config); err != nil {
		return err
	}

	globalCacheConfig = &config.Cache
	return nil
}

// GetCacheConfig 获取当前缓存配置
func GetCacheConfig() *CacheConfig {
	if globalCacheConfig == nil {
		InitCacheConfig()
	}
	return globalCacheConfig
}

// NewManager 创建缓存管理器
func NewManager() *Manager {
	config := GetCacheConfig()
	return &Manager{
		cache:       make(map[string]*CacheEntry),
		interval:    time.Duration(config.DefaultInterval) * time.Minute,
		httpClient:  &http.Client{Timeout: 120 * time.Second},
		storageFile: config.DataStoragePath,
	}
}

// InitGlobalManager 初始化全局管理器
func InitGlobalManager() error {
	globalMutex.Lock()
	defer globalMutex.Unlock()

	if globalManager != nil {
		return fmt.Errorf("全局缓存管理器已经初始化")
	}

	// 先初始化配置
	if err := InitCacheConfig(); err != nil {
		log.Printf("警告：初始化缓存配置失败: %v", err)
	}

	globalManager = NewManager()

	// 从文件加载数据
	if err := globalManager.loadFromFile(); err != nil {
		log.Printf("警告：加载缓存数据失败: %v", err)
	}

	// 检查是否需要自动启动
	config := GetCacheConfig()
	if config.AutoStartOnBoot && config.Enable {
		log.Printf("配置了自动启动缓存，启动机器: %v", config.DefaultMachines)
		interval := time.Duration(config.DefaultInterval) * time.Minute
		if err := globalManager.Start(config.DefaultMachines, interval); err != nil {
			log.Printf("自动启动缓存失败: %v", err)
		}
	}

	log.Printf("全局缓存管理器初始化成功")
	return nil
}

// GetGlobalManager 获取全局管理器
func GetGlobalManager() *Manager {
	globalMutex.RLock()
	defer globalMutex.RUnlock()
	return globalManager
}

// Start 启动缓存服务
func (m *Manager) Start(machines []string, interval time.Duration) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.isRunning {
		return fmt.Errorf("缓存服务已经运行")
	}

	// 验证机器列表
	if err := m.validateMachines(machines); err != nil {
		return err
	}

	m.enabledMachines = machines
	m.interval = interval
	m.isRunning = true
	m.stopChan = make(chan struct{})

	// 启动后台工作线程
	m.wg.Add(1)
	go m.worker()

	log.Printf("缓存服务已启动，机器: %v，间隔: %v", machines, interval)
	return nil
}

// Stop 停止缓存服务
func (m *Manager) Stop() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if !m.isRunning {
		return fmt.Errorf("缓存服务未运行")
	}

	m.isRunning = false
	close(m.stopChan)

	// 等待工作线程结束
	m.wg.Wait()

	// 保存数据
	if err := m.saveToFile(); err != nil {
		log.Printf("警告：保存缓存数据失败: %v", err)
	}

	log.Printf("缓存服务已停止")
	return nil
}

// IsRunning 检查是否运行
func (m *Manager) IsRunning() bool {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.isRunning
}

// GetCachedData 获取缓存数据
func (m *Manager) GetCachedData(machineID string) ([]MESBreakageDTO, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	entry, exists := m.cache[machineID]
	if !exists {
		m.totalMisses++
		return nil, false
	}

	if entry.IsExpired() {
		m.totalMisses++
		return nil, false
	}

	m.totalHits++
	return entry.Data, true
}

// GetExpiredData 获取过期数据（降级使用）
func (m *Manager) GetExpiredData(machineID string) ([]MESBreakageDTO, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	entry, exists := m.cache[machineID]
	if !exists {
		return nil, false
	}

	log.Printf("使用机器 %s 的过期缓存数据", machineID)
	return entry.Data, true
}

// GetStatus 获取状态
func (m *Manager) GetStatus() *CacheStatus {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	status := &CacheStatus{
		IsRunning:       m.isRunning,
		EnabledMachines: m.enabledMachines,
		Interval:        m.interval,
		MachineStatus:   make(map[string]*CacheEntry),
		TotalHits:       m.totalHits,
		TotalMisses:     m.totalMisses,
	}

	// 复制机器状态
	for k, v := range m.cache {
		entryCopy := *v
		status.MachineStatus[k] = &entryCopy
	}

	return status
}

// Clear 清空缓存
func (m *Manager) Clear() {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.cache = make(map[string]*CacheEntry)
	m.totalHits = 0
	m.totalMisses = 0

	log.Printf("缓存已清空")
}

// GetAvailableMachines 获取可用机器列表
func (m *Manager) GetAvailableMachines() []string {
	return []string{"61", "60", "59", "58", "57"}
}

// worker 后台工作线程
func (m *Manager) worker() {
	defer m.wg.Done()

	log.Printf("缓存工作线程开始运行")

	for {
		select {
		case <-m.stopChan:
			log.Printf("缓存工作线程收到停止信号")
			return
		default:
			m.updateAllMachines()

			select {
			case <-m.stopChan:
				return
			case <-time.After(m.getInterval()):
				// 继续下一轮
			}
		}
	}
}

// updateAllMachines 更新所有机器
func (m *Manager) updateAllMachines() {
	machines := m.getEnabledMachines()
	if len(machines) == 0 {
		return
	}

	log.Printf("开始更新 %d 台机器的缓存", len(machines))

	var wg sync.WaitGroup
	for _, machineID := range machines {
		wg.Add(1)
		go func(id string) {
			defer wg.Done()
			m.updateMachine(id)
		}(machineID)
	}
	wg.Wait()
}

// updateMachine 更新单台机器
func (m *Manager) updateMachine(machineID string) {
	fullMachineID := fmt.Sprintf("86099703040%s", machineID)

	// 调用API获取数据
	data, err := m.callMESAPI(fullMachineID)
	if err != nil {
		log.Printf("机器 %s 获取数据失败: %v", machineID, err)
		m.setError(machineID, err)
		return
	}

	// 更新缓存
	m.setData(machineID, data)
	log.Printf("机器 %s 缓存更新成功，获得 %d 条数据", machineID, len(data))
}

// callMESAPI 调用MES API
func (m *Manager) callMESAPI(fullMachineID string) ([]MESBreakageDTO, error) {
	reqBody := MESRequest{
		CustomerNo: "86099703",
		MachineID:  fullMachineID,
	}

	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	req, err := http.NewRequest("POST", "http://47.92.194.122:8081/mes/86099703/abnormalData", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "AGV-Cache-Manager/1.0")

	resp, err := m.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP错误: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	var mesResp MESResponse
	if err := json.Unmarshal(body, &mesResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	if mesResp.Code != 0 {
		return nil, fmt.Errorf("业务错误: %s", mesResp.Msg)
	}

	return mesResp.Data.BreakageDTOList, nil
}

// setData 设置缓存数据
func (m *Manager) setData(machineID string, data []MESBreakageDTO) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	entry, exists := m.cache[machineID]
	if !exists {
		entry = &CacheEntry{MachineID: machineID}
		m.cache[machineID] = entry
	}

	entry.Data = data
	entry.LastUpdate = time.Now()
	entry.IsValid = true
	entry.ErrorCount = 0
	entry.LastError = ""
}

// setError 设置错误状态
func (m *Manager) setError(machineID string, err error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	entry, exists := m.cache[machineID]
	if !exists {
		entry = &CacheEntry{MachineID: machineID}
		m.cache[machineID] = entry
	}

	entry.ErrorCount++
	entry.LastError = err.Error()
	entry.IsValid = false
}

// getEnabledMachines 获取启用的机器（线程安全）
func (m *Manager) getEnabledMachines() []string {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	machines := make([]string, len(m.enabledMachines))
	copy(machines, m.enabledMachines)
	return machines
}

// getInterval 获取间隔（线程安全）
func (m *Manager) getInterval() time.Duration {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	return m.interval
}

// validateMachines 验证机器列表
func (m *Manager) validateMachines(machines []string) error {
	allowed := map[string]bool{"61": true, "60": true, "59": true, "58": true, "57": true}

	for _, machine := range machines {
		if !allowed[machine] {
			return fmt.Errorf("不支持的机器: %s", machine)
		}
	}

	return nil
}

// GetCacheData 获取缓存数据
func (m *Manager) GetCacheData() map[string]*CacheEntry {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 创建深拷贝以避免数据竞争
	result := make(map[string]*CacheEntry)
	for machineID, entry := range m.cache {
		if entry != nil {
			result[machineID] = &CacheEntry{
				MachineID:  entry.MachineID,
				Data:       entry.Data,
				LastUpdate: entry.LastUpdate,
				IsValid:    entry.IsValid,
				ErrorCount: entry.ErrorCount,
				LastError:  entry.LastError,
			}
		}
	}
	return result
}

// saveToFile 保存到文件
func (m *Manager) saveToFile() error {
	if err := os.MkdirAll(filepath.Dir(m.storageFile), 0755); err != nil {
		return fmt.Errorf("创建目录失败: %v", err)
	}

	data, err := json.MarshalIndent(m.cache, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化失败: %v", err)
	}

	if err := ioutil.WriteFile(m.storageFile, data, 0644); err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}

	return nil
}

// loadFromFile 从文件加载
func (m *Manager) loadFromFile() error {
	if _, err := os.Stat(m.storageFile); os.IsNotExist(err) {
		return nil // 文件不存在，跳过
	}

	data, err := ioutil.ReadFile(m.storageFile)
	if err != nil {
		return fmt.Errorf("读取文件失败: %v", err)
	}

	if err := json.Unmarshal(data, &m.cache); err != nil {
		return fmt.Errorf("解析文件失败: %v", err)
	}

	return nil
}

// Shutdown 关闭管理器
func (m *Manager) Shutdown() {
	if m.IsRunning() {
		m.Stop()
	}
}

// 便利函数
func StartCache(machines []string, interval time.Duration) error {
	manager := GetGlobalManager()
	if manager == nil {
		return fmt.Errorf("全局管理器未初始化")
	}
	return manager.Start(machines, interval)
}

func StopCache() error {
	manager := GetGlobalManager()
	if manager == nil {
		return fmt.Errorf("全局管理器未初始化")
	}
	return manager.Stop()
}

func GetCachedSpindleData(machineID string) ([]MESBreakageDTO, bool) {
	manager := GetGlobalManager()
	if manager == nil {
		return nil, false
	}
	return manager.GetCachedData(machineID)
}

func GetExpiredSpindleData(machineID string) ([]MESBreakageDTO, bool) {
	manager := GetGlobalManager()
	if manager == nil {
		return nil, false
	}
	return manager.GetExpiredData(machineID)
}

func GetCacheStatus() *CacheStatus {
	manager := GetGlobalManager()
	if manager == nil {
		return &CacheStatus{IsRunning: false}
	}
	return manager.GetStatus()
}

func ClearCache() {
	manager := GetGlobalManager()
	if manager != nil {
		manager.Clear()
	}
}

func ShutdownCache() {
	globalMutex.Lock()
	defer globalMutex.Unlock()

	if globalManager != nil {
		globalManager.Shutdown()
		globalManager = nil
	}
}

// GetCacheData 获取缓存数据
func GetCacheData() (map[string]*CacheEntry, error) {
	globalMutex.RLock()
	defer globalMutex.RUnlock()

	if globalManager == nil {
		return nil, fmt.Errorf("缓存管理器未初始化")
	}

	return globalManager.GetCacheData(), nil
}
