package task

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"
)

// ParkingPointConfig 停车点配置结构体
type ParkingPointConfig struct {
	ID    int     `json:"id"`    // 停车点ID
	X     float64 `json:"x"`     // X坐标
	Y     float64 `json:"y"`     // Y坐标
	Angle float64 `json:"angle"` // 角度（弧度）
}

// TaskConfig 任务配置结构体
type TaskConfig struct {
	// 重试配置
	MaxRetries     int           `json:"maxRetries"`     // 最大重试次数
	RetryDelayBase time.Duration `json:"retryDelayBase"` // 重试基础延迟

	// PLC地址配置
	PLCAddresses PLCAddressConfig `json:"plcAddresses"`

	// 超时配置
	Timeouts TimeoutConfig `json:"timeouts"`

	// 安全调头配置
	SafeTurnConfig SafeTurnConfig `json:"safeTurnConfig"`

	// 停车点配置
	ParkingPoint ParkingPointConfig `json:"parkingPoint"` // 任务完成后的停车点配置

	// 允许的细纱机面配置
	AllowedMachines []string `json:"allowedMachines"` // 允许的细纱机面列表，空表示允许所有
	
	// 断头数据源配置
	SpindleDataSource string             `json:"spindleDataSource"` // 断头数据源：mes 或 mysql
	MySQLSpindleConfig MySQLSpindleConfig `json:"mysqlSpindleConfig"` // MySQL数据源配置

	// 工作模式配置
	WorkMode string `json:"work_mode"` // 工作模式：manual 或 scheduled

	// ZMQ调度系统配置
	ZMQConfig ZMQConfig `json:"zmq"` // ZMQ通信配置
}

// MySQLSpindleConfig MySQL断头数据源配置
type MySQLSpindleConfig struct {
	Host     string        `json:"host"`     // MySQL主机地址
	Port     string        `json:"port"`     // MySQL端口
	Username string        `json:"username"` // 用户名
	Password string        `json:"password"` // 密码
	Database string        `json:"database"` // 数据库名
	Timeout  time.Duration `json:"timeout"`  // 连接超时时间
}

// ZMQConfig ZMQ调度系统配置
type ZMQConfig struct {
	Enabled   bool              `json:"enabled"`   // 是否启用ZMQ功能
	Scheduler ZMQSchedulerConfig `json:"scheduler"` // 调度系统配置
}

// ZMQSchedulerConfig ZMQ调度系统具体配置
type ZMQSchedulerConfig struct {
	RequesterEndpoint        string        `json:"requester_endpoint"`         // 请求端点（AGV发送消息）
	ResponderEndpoint        string        `json:"responder_endpoint"`         // 响应端点（AGV接收消息）
	HeartbeatIntervalMinutes int           `json:"heartbeat_interval_minutes"` // 心跳间隔（分钟）
	ReplyTimeoutSeconds      int           `json:"reply_timeout_seconds"`      // 回复超时（秒）
	MaxRetryAttempts         int           `json:"max_retry_attempts"`         // 最大重试次数
	RetryIntervalSeconds     int           `json:"retry_interval_seconds"`     // 重试间隔（秒）
}

// PLCAddressConfig PLC地址配置
type PLCAddressConfig struct {
	ControlRequestAddress       uint16 `json:"controlRequestAddress"`       // 控制权请求地址 M601
	ControlConfirmAddress       uint16 `json:"controlConfirmAddress"`       // 控制权确认地址 M501
	TaskStartAddress            uint16 `json:"taskStartAddress"`            // 任务开始地址 M602
	ReadyAddress                uint16 `json:"readyAddress"`                // 准备信号地址 M509
	DirectionAddress            uint16 `json:"directionAddress"`            // 方向地址 M604
	DistanceAddress             uint16 `json:"distanceAddress"`             // 距离地址 D602
	RollerAddress               uint16 `json:"rollerAddress"`               // 皮辊地址 D600
	CompleteAddress             uint16 `json:"completeAddress"`             // 完成信号地址 M603
	VerifyDirectionAddress      uint16 `json:"verifyDirectionAddress"`      // 验证方向地址 M505
	VerifyRollerAddress         uint16 `json:"verifyRollerAddress"`         // 验证皮辊地址 D501
	VerifyDistanceAddress       uint16 `json:"verifyDistanceAddress"`       // 验证距离地址 D502
	VerificationCompleteAddress uint16 `json:"verificationCompleteAddress"` // 验证完成地址 M605
	WorkStatusAddress           uint16 `json:"workStatusAddress"`           // 工作状态地址 D500
	RobotStartSignalAddress     uint16 `json:"robotStartSignalAddress"`     // 机器人启动信号地址 M612
	MachineCompleteAddress      uint16 `json:"machineCompleteAddress"`      // 细纱机完成信号地址 M606
	ReturnToOriginAddress       uint16 `json:"returnToOriginAddress"`       // 回到码带原点位置地址 M610
	OriginDirectionAddress      uint16 `json:"originDirectionAddress"`      // 原点方向地址 M611
	OriginCodeValueAddress      uint16 `json:"originCodeValueAddress"`      // 原点码值地址 D604
	OriginArrivalAddress        uint16 `json:"originArrivalAddress"`        // 原点到达信号地址 M510
	ControlHandoverAddress      uint16 `json:"controlHandoverAddress"`      // 控制权交接请求地址 M607
	// 新增：巷道相关地址
	TurnAroundAddress       uint16 `json:"turnAroundAddress"`       // 调头控制地址 M607
	TurnCompleteAddress     uint16 `json:"turnCompleteAddress"`     // 调头完成地址 M507
	ExitLaneAddress         uint16 `json:"exitLaneAddress"`         // 退出巷道地址 M608
	LaneExitCompleteAddress uint16 `json:"laneExitCompleteAddress"` // 巷道退出完成地址 M508
}

// TimeoutConfig 超时配置
type TimeoutConfig struct {
	NavigationTimeout       time.Duration `json:"navigationTimeout"`       // 导航超时时间
	NavigationCheckInterval time.Duration `json:"navigationCheckInterval"` // 导航检查间隔
	PLCReadyTimeout         time.Duration `json:"plcReadyTimeout"`         // PLC准备超时时间
	PLCReadyCheckInterval   time.Duration `json:"plcReadyCheckInterval"`   // PLC准备检查间隔
	PLCWorkTimeout          time.Duration `json:"plcWorkTimeout"`          // PLC工作超时时间
	PLCWorkCheckInterval    time.Duration `json:"plcWorkCheckInterval"`    // PLC工作检查间隔
	// 新增：巷道相关超时配置
	TurnAroundTimeout       time.Duration `json:"turnAroundTimeout"`       // 调头超时时间
	TurnAroundCheckInterval time.Duration `json:"turnAroundCheckInterval"` // 调头检查间隔
	LaneExitTimeout         time.Duration `json:"laneExitTimeout"`         // 巷道退出超时时间
	LaneExitCheckInterval   time.Duration `json:"laneExitCheckInterval"`   // 巷道退出检查间隔
	// 新增：原点到达相关超时配置
	OriginArrivalTimeout       time.Duration `json:"originArrivalTimeout"`       // 原点到达超时时间
	OriginArrivalCheckInterval time.Duration `json:"originArrivalCheckInterval"` // 原点到达检查间隔
}

// SafeTurnConfig 安全调头配置
type SafeTurnConfig struct {
	MinSpindle   int    `json:"minSpindle"`   // 最小安全锭号
	MaxSpindle   int    `json:"maxSpindle"`   // 最大安全锭号
	StartAddress uint16 `json:"startAddress"` // 起始码值地址 D606
	EndAddress   uint16 `json:"endAddress"`   // 结束码值地址 D608
}

// 默认配置
var defaultConfig = TaskConfig{
	MaxRetries:     3,
	RetryDelayBase: 5 * time.Second,
	ParkingPoint: ParkingPointConfig{
		ID:    1,
		X:     0.0,
		Y:     0.0,
		Angle: 0.0,
	}, // 默认停车点配置
	AllowedMachines: []string{}, // 默认允许所有机面
	SpindleDataSource: "mes", // 默认使用MES API数据源
	MySQLSpindleConfig: MySQLSpindleConfig{
		Host:     "localhost",
		Port:     "3306",
		Username: "root",
		Password: "remote",
		Database: "spindle",
		Timeout:  30 * time.Second,
	}, // 默认MySQL配置
	WorkMode: "scheduled", // 默认调度模式
	ZMQConfig: ZMQConfig{
		Enabled: true, // 默认启用ZMQ功能
		Scheduler: ZMQSchedulerConfig{
			RequesterEndpoint:        "tcp://*************:5556",
			ResponderEndpoint:        "tcp://*:5557",
			HeartbeatIntervalMinutes: 1,
			ReplyTimeoutSeconds:      30,
			MaxRetryAttempts:         4,
			RetryIntervalSeconds:     5,
		},
	}, // 默认ZMQ配置
	PLCAddresses: PLCAddressConfig{
		ControlRequestAddress:       601,
		ControlConfirmAddress:       501,
		TaskStartAddress:            602,
		ReadyAddress:                509,
		DirectionAddress:            604,
		DistanceAddress:             602,
		RollerAddress:               600,
		CompleteAddress:             603,
		VerifyDirectionAddress:      505,
		VerifyRollerAddress:         501,
		VerifyDistanceAddress:       502,
		VerificationCompleteAddress: 605,
		WorkStatusAddress:           500,
		RobotStartSignalAddress:     612,
		MachineCompleteAddress:      606,
		ReturnToOriginAddress:       610,
		OriginDirectionAddress:      611,
		OriginCodeValueAddress:      604,
		OriginArrivalAddress:        510,
		ControlHandoverAddress:      607,
		// 巷道相关地址
		TurnAroundAddress:       608,
		TurnCompleteAddress:     511,
		ExitLaneAddress:         609,
		LaneExitCompleteAddress: 508,
	},
	Timeouts: TimeoutConfig{
		NavigationTimeout:       1200 * time.Second, // 20分钟
		NavigationCheckInterval: 2 * time.Second,
		PLCReadyTimeout:         120 * time.Second, // 2分钟
		PLCReadyCheckInterval:   500 * time.Millisecond,
		PLCWorkTimeout:          300 * time.Second, // 5分钟
		PLCWorkCheckInterval:    3 * time.Second,
		// 巷道相关超时
		TurnAroundTimeout:       300 * time.Second, // 5分钟
		TurnAroundCheckInterval: 3 * time.Second,
		LaneExitTimeout:         30 * time.Second, // 30秒
		LaneExitCheckInterval:   500 * time.Millisecond,
		// 原点到达相关超时
		OriginArrivalTimeout:       600 * time.Second, // 10分钟
		OriginArrivalCheckInterval: 3 * time.Second,
	},
	SafeTurnConfig: SafeTurnConfig{
		MinSpindle:   50,
		MaxSpindle:   1100,
		StartAddress: 606, // D606
		EndAddress:   608, // D608
	},
}

// 全局配置实例
var globalConfig TaskConfig

// InitConfig 初始化配置
func InitConfig() error {
	// 首先使用默认配置
	globalConfig = defaultConfig

	// 尝试从配置文件加载
	configPath := getConfigPath()
	if _, err := os.Stat(configPath); err == nil {
		log.Printf("加载配置文件: %s", configPath)
		if err := loadConfigFromFile(configPath); err != nil {
			log.Printf("警告：加载配置文件失败: %v，使用默认配置", err)
		}
	} else {
		log.Printf("配置文件不存在，使用默认配置并创建示例文件: %s", configPath)
		if err := saveConfigToFile(configPath); err != nil {
			log.Printf("警告：创建配置文件失败: %v", err)
		}
	}

	log.Printf("任务配置初始化完成")
	return nil
}

// GetConfig 获取当前配置
func GetConfig() TaskConfig {
	return globalConfig
}

// UpdateConfig 更新配置
func UpdateConfig(config TaskConfig) error {
	globalConfig = config

	// 保存到文件
	configPath := getConfigPath()
	if err := saveConfigToFile(configPath); err != nil {
		return fmt.Errorf("保存配置文件失败: %v", err)
	}

	log.Printf("配置已更新并保存到: %s", configPath)
	return nil
}

// getConfigPath 获取配置文件路径
func getConfigPath() string {
	// 可能的配置文件路径（按优先级排序）
	possiblePaths := []string{
		filepath.Join("backend", "data", "task_config.json"),            // 项目根目录
		filepath.Join("data", "task_config.json"),                       // 当前目录
		filepath.Join(".", "data", "task_config.json"),                  // 显式当前目录
		filepath.Join("backend-data", "data", "task_config.json"),       // 用户数据目录
		filepath.Join("..", "backend-data", "data", "task_config.json"), // 上级用户数据目录
	}

	// 逐个检查路径是否存在
	for _, path := range possiblePaths {
		if _, err := os.Stat(path); err == nil {
			log.Printf("找到配置文件: %s", path)
			return path
		}
	}

	// 如果都不存在，返回默认路径
	defaultPath := filepath.Join("data", "task_config.json")
	log.Printf("使用默认配置文件路径: %s", defaultPath)
	return defaultPath
}

// loadConfigFromFile 从文件加载配置
func loadConfigFromFile(path string) error {
	data, err := os.ReadFile(path)
	if err != nil {
		return err
	}

	// 首先尝试解析为JSON格式
	var jsonConfig TaskConfigJSON
	if err := json.Unmarshal(data, &jsonConfig); err != nil {
		// 如果JSON解析失败，尝试直接解析为内部格式
		var config TaskConfig
		if err := json.Unmarshal(data, &config); err != nil {
			return err
		}
		globalConfig = config
		// 验证配置
		if err := validateConfig(&globalConfig); err != nil {
			return err
		}
		return nil
	}

	// 转换JSON格式为内部格式
	config := TaskConfig{
		MaxRetries:        jsonConfig.MaxRetries,
		PLCAddresses:      jsonConfig.PLCAddresses,
		SafeTurnConfig:    jsonConfig.SafeTurnConfig,
		ParkingPoint:      jsonConfig.ParkingPoint,
		AllowedMachines:   jsonConfig.AllowedMachines,
		SpindleDataSource: jsonConfig.SpindleDataSource,
		WorkMode:          jsonConfig.WorkMode,
		ZMQConfig:         jsonConfig.ZMQConfig,
	}

	// 解析RetryDelayBase
	if jsonConfig.RetryDelayBase != "" {
		if delay, err := time.ParseDuration(jsonConfig.RetryDelayBase); err != nil {
			log.Printf("警告：RetryDelayBase解析失败: %v，使用默认值", err)
			config.RetryDelayBase = 5 * time.Second
		} else {
			config.RetryDelayBase = delay
		}
	} else {
		config.RetryDelayBase = 5 * time.Second
	}

	// 处理SpindleDataSource默认值
	if config.SpindleDataSource == "" {
		config.SpindleDataSource = "mes"
	}

	// 处理WorkMode默认值
	if config.WorkMode == "" {
		config.WorkMode = "scheduled" // 默认调度模式
	}

	// 处理ZMQ配置默认值
	if config.ZMQConfig.Scheduler.RequesterEndpoint == "" {
		config.ZMQConfig = defaultConfig.ZMQConfig
	}

	// 解析MySQLSpindleConfig
	config.MySQLSpindleConfig = MySQLSpindleConfig{
		Host:     jsonConfig.MySQLSpindleConfig.Host,
		Port:     jsonConfig.MySQLSpindleConfig.Port,
		Username: jsonConfig.MySQLSpindleConfig.Username,
		Password: jsonConfig.MySQLSpindleConfig.Password,
		Database: jsonConfig.MySQLSpindleConfig.Database,
	}

	// 解析MySQL超时时间
	if jsonConfig.MySQLSpindleConfig.Timeout != "" {
		if timeout, err := time.ParseDuration(jsonConfig.MySQLSpindleConfig.Timeout); err != nil {
			log.Printf("警告：MySQLSpindleConfig.Timeout解析失败: %v，使用默认值", err)
			config.MySQLSpindleConfig.Timeout = 30 * time.Second
		} else {
			config.MySQLSpindleConfig.Timeout = timeout
		}
	} else {
		config.MySQLSpindleConfig.Timeout = 30 * time.Second
	}

	// 设置MySQL配置默认值
	if config.MySQLSpindleConfig.Host == "" {
		config.MySQLSpindleConfig.Host = "localhost"
	}
	if config.MySQLSpindleConfig.Port == "" {
		config.MySQLSpindleConfig.Port = "3306"
	}
	if config.MySQLSpindleConfig.Username == "" {
		config.MySQLSpindleConfig.Username = "root"
	}
	if config.MySQLSpindleConfig.Password == "" {
		config.MySQLSpindleConfig.Password = "remote"
	}
	if config.MySQLSpindleConfig.Database == "" {
		config.MySQLSpindleConfig.Database = "spindle"
	}

	// 解析Timeouts
	config.Timeouts = TimeoutConfig{}
	if jsonConfig.Timeouts.NavigationTimeout != "" {
		if timeout, err := time.ParseDuration(jsonConfig.Timeouts.NavigationTimeout); err != nil {
			log.Printf("警告：NavigationTimeout解析失败: %v，使用默认值", err)
			config.Timeouts.NavigationTimeout = 1200 * time.Second
		} else {
			config.Timeouts.NavigationTimeout = timeout
		}
	} else {
		config.Timeouts.NavigationTimeout = 1200 * time.Second
	}

	if jsonConfig.Timeouts.NavigationCheckInterval != "" {
		if interval, err := time.ParseDuration(jsonConfig.Timeouts.NavigationCheckInterval); err != nil {
			log.Printf("警告：NavigationCheckInterval解析失败: %v，使用默认值", err)
			config.Timeouts.NavigationCheckInterval = 2 * time.Second
		} else {
			config.Timeouts.NavigationCheckInterval = interval
		}
	} else {
		config.Timeouts.NavigationCheckInterval = 2 * time.Second
	}

	if jsonConfig.Timeouts.PLCReadyTimeout != "" {
		if timeout, err := time.ParseDuration(jsonConfig.Timeouts.PLCReadyTimeout); err != nil {
			log.Printf("警告：PLCReadyTimeout解析失败: %v，使用默认值", err)
			config.Timeouts.PLCReadyTimeout = 120 * time.Second
		} else {
			config.Timeouts.PLCReadyTimeout = timeout
		}
	} else {
		config.Timeouts.PLCReadyTimeout = 120 * time.Second
	}

	if jsonConfig.Timeouts.PLCReadyCheckInterval != "" {
		if interval, err := time.ParseDuration(jsonConfig.Timeouts.PLCReadyCheckInterval); err != nil {
			log.Printf("警告：PLCReadyCheckInterval解析失败: %v，使用默认值", err)
			config.Timeouts.PLCReadyCheckInterval = 500 * time.Millisecond
		} else {
			config.Timeouts.PLCReadyCheckInterval = interval
		}
	} else {
		config.Timeouts.PLCReadyCheckInterval = 500 * time.Millisecond
	}

	if jsonConfig.Timeouts.PLCWorkTimeout != "" {
		if timeout, err := time.ParseDuration(jsonConfig.Timeouts.PLCWorkTimeout); err != nil {
			log.Printf("警告：PLCWorkTimeout解析失败: %v，使用默认值", err)
			config.Timeouts.PLCWorkTimeout = 300 * time.Second
		} else {
			config.Timeouts.PLCWorkTimeout = timeout
		}
	} else {
		config.Timeouts.PLCWorkTimeout = 300 * time.Second
	}

	if jsonConfig.Timeouts.PLCWorkCheckInterval != "" {
		if interval, err := time.ParseDuration(jsonConfig.Timeouts.PLCWorkCheckInterval); err != nil {
			log.Printf("警告：PLCWorkCheckInterval解析失败: %v，使用默认值", err)
			config.Timeouts.PLCWorkCheckInterval = 3 * time.Second
		} else {
			config.Timeouts.PLCWorkCheckInterval = interval
		}
	} else {
		config.Timeouts.PLCWorkCheckInterval = 3 * time.Second
	}

	if jsonConfig.Timeouts.TurnAroundTimeout != "" {
		if timeout, err := time.ParseDuration(jsonConfig.Timeouts.TurnAroundTimeout); err != nil {
			log.Printf("警告：TurnAroundTimeout解析失败: %v，使用默认值", err)
			config.Timeouts.TurnAroundTimeout = 300 * time.Second
		} else {
			config.Timeouts.TurnAroundTimeout = timeout
		}
	} else {
		config.Timeouts.TurnAroundTimeout = 300 * time.Second
	}

	if jsonConfig.Timeouts.TurnAroundCheckInterval != "" {
		if interval, err := time.ParseDuration(jsonConfig.Timeouts.TurnAroundCheckInterval); err != nil {
			log.Printf("警告：TurnAroundCheckInterval解析失败: %v，使用默认值", err)
			config.Timeouts.TurnAroundCheckInterval = 3 * time.Second
		} else {
			config.Timeouts.TurnAroundCheckInterval = interval
		}
	} else {
		config.Timeouts.TurnAroundCheckInterval = 3 * time.Second
	}

	if jsonConfig.Timeouts.LaneExitTimeout != "" {
		if timeout, err := time.ParseDuration(jsonConfig.Timeouts.LaneExitTimeout); err != nil {
			log.Printf("警告：LaneExitTimeout解析失败: %v，使用默认值", err)
			config.Timeouts.LaneExitTimeout = 30 * time.Second
		} else {
			config.Timeouts.LaneExitTimeout = timeout
		}
	} else {
		config.Timeouts.LaneExitTimeout = 30 * time.Second
	}

	if jsonConfig.Timeouts.LaneExitCheckInterval != "" {
		if interval, err := time.ParseDuration(jsonConfig.Timeouts.LaneExitCheckInterval); err != nil {
			log.Printf("警告：LaneExitCheckInterval解析失败: %v，使用默认值", err)
			config.Timeouts.LaneExitCheckInterval = 500 * time.Millisecond
		} else {
			config.Timeouts.LaneExitCheckInterval = interval
		}
	} else {
		config.Timeouts.LaneExitCheckInterval = 500 * time.Millisecond
	}

	if jsonConfig.Timeouts.OriginArrivalTimeout != "" {
		if timeout, err := time.ParseDuration(jsonConfig.Timeouts.OriginArrivalTimeout); err != nil {
			log.Printf("警告：OriginArrivalTimeout解析失败: %v，使用默认值", err)
			config.Timeouts.OriginArrivalTimeout = 600 * time.Second
		} else {
			config.Timeouts.OriginArrivalTimeout = timeout
		}
	} else {
		config.Timeouts.OriginArrivalTimeout = 600 * time.Second
	}

	if jsonConfig.Timeouts.OriginArrivalCheckInterval != "" {
		if interval, err := time.ParseDuration(jsonConfig.Timeouts.OriginArrivalCheckInterval); err != nil {
			log.Printf("警告：OriginArrivalCheckInterval解析失败: %v，使用默认值", err)
			config.Timeouts.OriginArrivalCheckInterval = 3 * time.Second
		} else {
			config.Timeouts.OriginArrivalCheckInterval = interval
		}
	} else {
		config.Timeouts.OriginArrivalCheckInterval = 3 * time.Second
	}

	globalConfig = config

	// 验证配置
	if err := validateConfig(&globalConfig); err != nil {
		return err
	}

	return nil
}

// validateConfig 验证配置的有效性
func validateConfig(config *TaskConfig) error {
	// 验证SafeTurnConfig
	if config.SafeTurnConfig.StartAddress == 0 || config.SafeTurnConfig.EndAddress == 0 {
		log.Printf("警告：SafeTurnConfig地址为零值，使用默认值")
		config.SafeTurnConfig = defaultConfig.SafeTurnConfig
		log.Printf("已设置默认SafeTurnConfig: StartAddress=%d, EndAddress=%d",
			config.SafeTurnConfig.StartAddress, config.SafeTurnConfig.EndAddress)
	}

	// 验证PLCAddresses关键字段
	if config.PLCAddresses.MachineCompleteAddress == 0 {
		log.Printf("警告：MachineCompleteAddress为零值，使用默认值")
		config.PLCAddresses.MachineCompleteAddress = defaultConfig.PLCAddresses.MachineCompleteAddress
	}

	// 验证最大重试次数
	if config.MaxRetries <= 0 {
		log.Printf("警告：MaxRetries为零或负值，使用默认值")
		config.MaxRetries = defaultConfig.MaxRetries
	}

	// 验证停车点配置
	if config.ParkingPoint.ID <= 0 {
		log.Printf("警告：ParkingPoint.ID为零或负值，使用默认值")
		config.ParkingPoint = defaultConfig.ParkingPoint
	}

	return nil
}

// saveConfigToFile 保存配置到文件
func saveConfigToFile(path string) error {
	// 确保目录存在
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return err
	}

	data, err := json.MarshalIndent(globalConfig, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile(path, data, 0644)
}

// TaskConfigJSON 用于JSON序列化的配置结构体
type TaskConfigJSON struct {
	MaxRetries      int                `json:"maxRetries"`
	RetryDelayBase  string             `json:"retryDelayBase"` // 字符串格式，如 "5s"
	PLCAddresses    PLCAddressConfig   `json:"plcAddresses"`
	Timeouts        TimeoutConfigJSON  `json:"timeouts"`
	SafeTurnConfig  SafeTurnConfig     `json:"safeTurnConfig"`
	ParkingPoint       ParkingPointConfig    `json:"parkingPoint"`       // 停车点配置
	AllowedMachines    []string              `json:"allowedMachines"`    // 允许的细纱机面列表
	SpindleDataSource  string                `json:"spindleDataSource"`  // 断头数据源：mes 或 mysql
	MySQLSpindleConfig MySQLSpindleConfigJSON `json:"mysqlSpindleConfig"` // MySQL数据源配置
	WorkMode           string                `json:"work_mode"`          // 工作模式
	ZMQConfig          ZMQConfig             `json:"zmq"`                // ZMQ配置
}

// MySQLSpindleConfigJSON 用于JSON序列化的MySQL配置
type MySQLSpindleConfigJSON struct {
	Host     string `json:"host"`     // MySQL主机地址
	Port     string `json:"port"`     // MySQL端口
	Username string `json:"username"` // 用户名
	Password string `json:"password"` // 密码
	Database string `json:"database"` // 数据库名
	Timeout  string `json:"timeout"`  // 连接超时时间，字符串格式如"30s"
}

// TimeoutConfigJSON 用于JSON序列化的超时配置
type TimeoutConfigJSON struct {
	NavigationTimeout          string `json:"navigationTimeout"`
	NavigationCheckInterval    string `json:"navigationCheckInterval"`
	PLCReadyTimeout            string `json:"plcReadyTimeout"`
	PLCReadyCheckInterval      string `json:"plcReadyCheckInterval"`
	PLCWorkTimeout             string `json:"plcWorkTimeout"`
	PLCWorkCheckInterval       string `json:"plcWorkCheckInterval"`
	TurnAroundTimeout          string `json:"turnAroundTimeout"`
	TurnAroundCheckInterval    string `json:"turnAroundCheckInterval"`
	LaneExitTimeout            string `json:"laneExitTimeout"`
	LaneExitCheckInterval      string `json:"laneExitCheckInterval"`
	OriginArrivalTimeout       string `json:"originArrivalTimeout"`
	OriginArrivalCheckInterval string `json:"originArrivalCheckInterval"`
}
