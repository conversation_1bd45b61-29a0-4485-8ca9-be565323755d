package zmq

import (
	"fmt"
	"log"

	"github.com/user/agv_nav/internal/zmq"
)

// TaskLifecycleNotifier 任务生命周期通知器
type TaskLifecycleNotifier struct {
	zmqManager *ZMQManager
	config     *zmq.SystemConfig
}

// NewTaskLifecycleNotifier 创建任务生命周期通知器
func NewTaskLifecycleNotifier(zmqManager *ZMQManager, config *zmq.SystemConfig) *TaskLifecycleNotifier {
	return &TaskLifecycleNotifier{
		zmqManager: zmqManager,
		config:     config,
	}
}

// NotifyTaskStart 发送任务开始通知（指令110）
func (n *TaskLifecycleNotifier) NotifyTaskStart(machine string, taskId string) error {
	if n.zmqManager == nil || !n.zmqManager.IsRunning() {
		return fmt.Errorf("ZMQ manager not available")
	}

	// 创建消息 - 使用zmq包的标准方法
	msg := zmq.NewTaskStartMessage(
		n.config.RobotNo,
		taskId,
		fmt.Sprintf("Task started for machine: %s", machine),
	)

	log.Printf("📤 Sending task start notification - taskId: %s, machine: %s, robotNo: %s", 
		taskId, machine, n.config.RobotNo)

	// 通过ZMQ发送消息
	_, err := n.zmqManager.SendMessage(msg)
	if err != nil {
		return fmt.Errorf("send task start notification failed: %w", err)
	}

	// 通知状态上报器：新任务开始
	if statusReporter := n.zmqManager.GetStatusReporter(); statusReporter != nil {
		statusReporter.OnNewTaskStarted()
		log.Printf("✅ 已通知状态上报器：新任务开始")
	}

	log.Printf("✅ Task start notification sent successfully - taskId: %s", taskId)
	return nil
}

// NotifyTaskComplete 发送任务完成通知（指令130）
func (n *TaskLifecycleNotifier) NotifyTaskComplete(taskId string, remark string) error {
	if n.zmqManager == nil || !n.zmqManager.IsRunning() {
		return fmt.Errorf("ZMQ manager not available")
	}

	// 创建消息 - 使用zmq包的标准方法
	msg := zmq.NewTaskCompleteMessage(
		n.config.RobotNo,
		taskId,
		remark,
	)

	log.Printf("📤 Sending task complete notification - taskId: %s, robotNo: %s", 
		taskId, n.config.RobotNo)

	// 通过ZMQ发送消息
	_, err := n.zmqManager.SendMessage(msg)
	if err != nil {
		return fmt.Errorf("send task complete notification failed: %w", err)
	}

	// 通知状态上报器：任务完成
	if statusReporter := n.zmqManager.GetStatusReporter(); statusReporter != nil {
		statusReporter.OnTaskCompleted()
		log.Printf("✅ 已通知状态上报器：任务完成")
	}

	log.Printf("✅ Task complete notification sent successfully - taskId: %s", taskId)
	return nil
}