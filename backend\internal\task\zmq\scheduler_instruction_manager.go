package zmq

import (
	"fmt"
	"log"
	"time"

	"github.com/user/agv_nav/internal/zmq"
)

// SchedulerInstructionManager 调度指令管理器，统一管理201/202/203处理器
type SchedulerInstructionManager struct {
	noTaskHandler       *NoTaskHandler       // 201指令处理器
	waitTaskHandler     *WaitTaskHandler     // 202指令处理器
	continueTaskHandler *ContinueTaskHandler // 203指令处理器
}

// NewSchedulerInstructionManager 创建调度指令管理器
func NewSchedulerInstructionManager(systemConfig *zmq.SystemConfig) *SchedulerInstructionManager {
	return &SchedulerInstructionManager{
		noTaskHandler:       NewNoTaskHandler(systemConfig),
		waitTaskHandler:     NewWaitTaskHandler(systemConfig),
		continueTaskHandler: NewContinueTaskHandler(systemConfig),
	}
}

// GetHandlers 获取所有指令处理器（用于注册到消息路由器）
func (m *SchedulerInstructionManager) GetHandlers() []InstructionHandler {
	return []InstructionHandler{
		m.noT<PERSON>Hand<PERSON>,
		m.waitTaskHandler,
		m.continueTaskHandler,
	}
}

// SetZMQManager 为所有处理器设置ZMQ管理器引用
func (m *SchedulerInstructionManager) SetZMQManager(zmqManager *ZMQManager) {
	m.noTaskHandler.SetZMQManager(zmqManager)
	m.continueTaskHandler.SetZMQManager(zmqManager)
	// waitTaskHandler 暂时不需要，因为它不涉及状态上报器通知
	log.Printf("📡 为调度指令处理器设置ZMQ管理器引用完成")
}

// WaitForInstruction 等待调度指令（3分钟超时）
func (m *SchedulerInstructionManager) WaitForInstruction() (SchedulerInstructionResult, error) {
	log.Printf("⏳ [调度指令管理器] 开始等待指令，超时时间: 3分钟")
	
	timeout := time.NewTimer(3 * time.Minute)
	defer timeout.Stop()

	// 使用ticker定期检查各个处理器的结果
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-timeout.C:
			log.Printf("⏰ [调度指令管理器] 等待超时(3分钟)，返回主流程")
			return SchedulerInstructionResult{
				InstructionType: 201, // 超时视为无任务
				TaskInfo:        nil,
				Error:           fmt.Errorf("等待调度指令超时"),
			}, nil

		case <-ticker.C:
			// 检查201指令（无任务）
			if result, hasResult := m.noTaskHandler.WaitForResult(); hasResult {
				log.Printf("📨 [调度指令管理器] 收到201指令结果")
				return *result, result.Error
			}

			// 检查202指令（等待任务）
			if result, hasResult := m.waitTaskHandler.WaitForResult(); hasResult {
				log.Printf("📨 [调度指令管理器] 收到202指令结果")
				return *result, result.Error
			}

			// 检查203指令（继续任务）
			if result, hasResult := m.continueTaskHandler.WaitForResult(); hasResult {
				log.Printf("📨 [调度指令管理器] 收到203指令结果")
				return *result, result.Error
			}

			// 继续等待
		}
	}
}