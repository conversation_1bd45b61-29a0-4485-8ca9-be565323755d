package api

import (
	"encoding/json"
	"net/http"
	"time"

	"github.com/user/agv_nav/pkg/agv"
	"github.com/user/agv_nav/pkg/config"
	"github.com/user/agv_nav/pkg/logger"
	"github.com/user/agv_nav/pkg/plc"
)

// DefaultConfigResponse 默认配置响应
type DefaultConfigResponse struct {
	AGV AGVConfigData `json:"agv"`
	PLC PLCConfigData `json:"plc"`
}

// AGVConfigData AGV配置数据
type AGVConfigData struct {
	AuthCode string `json:"authCode"`
	IP       string `json:"ip"`
	Port     string `json:"port"`
}

// PLCConfigData PLC配置数据
type PLCConfigData struct {
	IP   string `json:"ip"`
	Port string `json:"port"`
}

// handleGetDefaultConfig 获取默认配置
func (s *Server) handleGetDefaultConfig(w http.ResponseWriter, r *http.Request) {
	apiLogger := logger.GetModuleLogger("api")
	startTime := time.Now()
	clientIP := r.RemoteAddr
	apiLogger.Info("获取默认配置API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	// 获取默认AGV配置
	agvConfig := config.NewDefaultConfig()

	// 获取默认PLC配置
	plcConfig := config.NewDefaultPLCConfig()

	// 构建响应
	response := DefaultConfigResponse{
		AGV: AGVConfigData{
			AuthCode: agvConfig.AuthCode,
			IP:       agvConfig.IP,
			Port:     agvConfig.Port,
		},
		PLC: PLCConfigData{
			IP:   plcConfig.IP,
			Port: plcConfig.Port,
		},
	}

	apiLogger.Info("默认配置获取成功",
		"agvIP", agvConfig.IP,
		"agvPort", agvConfig.Port,
		"plcIP", plcConfig.IP,
		"plcPort", plcConfig.Port,
		"clientIP", clientIP,
		"duration", time.Since(startTime))

	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"data":    response,
	})
}

// handleTestAGVConnection 测试AGV连接
func (s *Server) handleTestAGVConnection(w http.ResponseWriter, r *http.Request) {
	apiLogger := logger.GetModuleLogger("api")
	startTime := time.Now()
	clientIP := r.RemoteAddr
	apiLogger.Info("AGV连接测试API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	var req struct {
		AuthCode string `json:"authCode"`
		IP       string `json:"ip"`
		Port     string `json:"port"`
	}

	if err := s.decodeJSONBody(r, &req); err != nil {
		apiLogger.Error("AGV连接测试请求数据解析失败", "clientIP", clientIP, "error", err)
		s.respondError(w, "请求数据格式错误", http.StatusBadRequest)
		return
	}

	// 创建临时AGV控制器进行连接测试
	testController := agv.NewController()

	// 尝试连接
	err := testController.Connect(req.AuthCode, req.IP, req.Port)
	if err != nil {
		apiLogger.Error("AGV连接测试失败", "ip", req.IP, "port", req.Port, "clientIP", clientIP, "error", err, "duration", time.Since(startTime))
		s.respondJSON(w, map[string]interface{}{
			"success":   false,
			"connected": false,
			"message":   err.Error(),
		})
		return
	}

	// 连接成功，立即断开
	testController.Disconnect()

	apiLogger.Info("AGV连接测试成功", "ip", req.IP, "port", req.Port, "clientIP", clientIP, "duration", time.Since(startTime))
	s.respondJSON(w, map[string]interface{}{
		"success":   true,
		"connected": true,
		"message":   "AGV连接测试成功",
	})
}

// handleTestPLCConnection 测试PLC连接
func (s *Server) handleTestPLCConnection(w http.ResponseWriter, r *http.Request) {
	apiLogger := logger.GetModuleLogger("api")
	startTime := time.Now()
	clientIP := r.RemoteAddr
	apiLogger.Info("PLC连接测试API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	var req struct {
		IP   string `json:"ip"`
		Port string `json:"port"`
	}

	if err := s.decodeJSONBody(r, &req); err != nil {
		apiLogger.Error("PLC连接测试请求数据解析失败", "clientIP", clientIP, "error", err)
		s.respondError(w, "请求数据格式错误", http.StatusBadRequest)
		return
	}

	// 创建临时PLC控制器进行连接测试
	testController := plc.NewController()

	// 尝试连接
	err := testController.Connect(req.IP, req.Port)
	if err != nil {
		apiLogger.Error("PLC连接测试失败", "ip", req.IP, "port", req.Port, "clientIP", clientIP, "error", err, "duration", time.Since(startTime))
		s.respondJSON(w, map[string]interface{}{
			"success":   false,
			"connected": false,
			"message":   err.Error(),
		})
		return
	}

	// 连接成功，立即断开
	testController.Disconnect()

	apiLogger.Info("PLC连接测试成功", "ip", req.IP, "port", req.Port, "clientIP", clientIP, "duration", time.Since(startTime))
	s.respondJSON(w, map[string]interface{}{
		"success":   true,
		"connected": true,
		"message":   "PLC连接测试成功",
	})
}

// handleConnectAll 批量连接AGV和PLC
func (s *Server) handleConnectAll(w http.ResponseWriter, r *http.Request) {
	apiLogger := logger.GetModuleLogger("api")
	startTime := time.Now()
	clientIP := r.RemoteAddr
	apiLogger.Info("批量连接设备API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	// 获取默认配置
	agvConfig := config.NewDefaultConfig()
	plcConfig := config.NewDefaultPLCConfig()

	var results []map[string]interface{}
	allSuccess := true

	// 连接AGV
	agvResult := map[string]interface{}{
		"device": "AGV",
		"ip":     agvConfig.IP,
		"port":   agvConfig.Port,
	}

	if !s.agvController.IsConnected() {
		err := s.agvController.Connect(agvConfig.AuthCode, agvConfig.IP, agvConfig.Port)
		if err != nil {
			agvResult["success"] = false
			agvResult["message"] = err.Error()
			allSuccess = false
			apiLogger.Error("AGV批量连接失败", "ip", agvConfig.IP, "port", agvConfig.Port, "error", err)
		} else {
			agvResult["success"] = true
			agvResult["message"] = "AGV连接成功"
			apiLogger.Info("AGV批量连接成功", "ip", agvConfig.IP, "port", agvConfig.Port)
		}
	} else {
		agvResult["success"] = true
		agvResult["message"] = "AGV已连接"
	}
	results = append(results, agvResult)

	// 连接PLC
	plcResult := map[string]interface{}{
		"device": "PLC",
		"ip":     plcConfig.IP,
		"port":   plcConfig.Port,
	}

	if !s.plcController.IsConnected() {
		err := s.plcController.Connect(plcConfig.IP, plcConfig.Port)
		if err != nil {
			plcResult["success"] = false
			plcResult["message"] = err.Error()
			allSuccess = false
			apiLogger.Error("PLC批量连接失败", "ip", plcConfig.IP, "port", plcConfig.Port, "error", err)
		} else {
			plcResult["success"] = true
			plcResult["message"] = "PLC连接成功"
			apiLogger.Info("PLC批量连接成功", "ip", plcConfig.IP, "port", plcConfig.Port)
		}
	} else {
		plcResult["success"] = true
		plcResult["message"] = "PLC已连接"
	}
	results = append(results, plcResult)

	apiLogger.Info("批量连接完成", "allSuccess", allSuccess, "clientIP", clientIP, "duration", time.Since(startTime))

	// 广播连接状态变化
	s.BroadcastConnectionStatus()

	s.respondJSON(w, map[string]interface{}{
		"success": allSuccess,
		"message": func() string {
			if allSuccess {
				return "所有设备连接成功"
			}
			return "部分设备连接失败"
		}(),
		"results": results,
	})
}

// handleDisconnectAll 批量断开AGV和PLC连接
func (s *Server) handleDisconnectAll(w http.ResponseWriter, r *http.Request) {
	apiLogger := logger.GetModuleLogger("api")
	startTime := time.Now()
	clientIP := r.RemoteAddr
	apiLogger.Info("批量断开连接API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	var results []map[string]interface{}
	allSuccess := true

	// 断开AGV
	agvResult := map[string]interface{}{
		"device": "AGV",
	}

	if s.agvController.IsConnected() {
		s.agvController.Disconnect()
		agvResult["success"] = true
		agvResult["message"] = "AGV已断开连接"
		apiLogger.Info("AGV批量断开成功")
	} else {
		agvResult["success"] = true
		agvResult["message"] = "AGV未连接"
	}
	results = append(results, agvResult)

	// 断开PLC
	plcResult := map[string]interface{}{
		"device": "PLC",
	}

	if s.plcController.IsConnected() {
		err := s.plcController.Disconnect()
		if err != nil {
			plcResult["success"] = false
			plcResult["message"] = err.Error()
			allSuccess = false
			apiLogger.Error("PLC批量断开失败", "error", err)
		} else {
			plcResult["success"] = true
			plcResult["message"] = "PLC已断开连接"
			apiLogger.Info("PLC批量断开成功")
		}
	} else {
		plcResult["success"] = true
		plcResult["message"] = "PLC未连接"
	}
	results = append(results, plcResult)

	apiLogger.Info("批量断开完成", "allSuccess", allSuccess, "clientIP", clientIP, "duration", time.Since(startTime))

	// 广播连接状态变化
	s.BroadcastConnectionStatus()

	s.respondJSON(w, map[string]interface{}{
		"success": allSuccess,
		"message": "设备连接已断开",
		"results": results,
	})
}

// handleGetConnectionStatus 获取连接状态
func (s *Server) handleGetConnectionStatus(w http.ResponseWriter, r *http.Request) {
	agvConnected := s.agvController.IsConnected()
	plcConnected := s.plcController.IsConnected()

	// 获取默认配置以显示IP信息
	agvConfig := config.NewDefaultConfig()
	plcConfig := config.NewDefaultPLCConfig()

	response := map[string]interface{}{
		"agv": map[string]interface{}{
			"connected": agvConnected,
			"ip":        agvConfig.IP,
			"port":      agvConfig.Port,
		},
		"plc": map[string]interface{}{
			"connected": plcConnected,
			"ip":        plcConfig.IP,
			"port":      plcConfig.Port,
		},
		"allConnected": agvConnected && plcConnected,
	}

	s.respondJSON(w, map[string]interface{}{
		"success": true,
		"data":    response,
	})
}

// decodeJSONBody 解析JSON请求体
func (s *Server) decodeJSONBody(r *http.Request, dst interface{}) error {
	return json.NewDecoder(r.Body).Decode(dst)
}
