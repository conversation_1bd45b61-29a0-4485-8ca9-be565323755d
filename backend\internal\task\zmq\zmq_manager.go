package zmq

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/user/agv_nav/internal/database"
	internalzmq "github.com/user/agv_nav/internal/zmq"
	"github.com/user/agv_nav/internal/zmq"
	"github.com/user/agv_nav/pkg/agv"
	zmqpkg "github.com/user/agv_nav/pkg/zmq"
)

// ZMQConfig ZMQ配置结构（本地定义，避免循环依赖）
type ZMQConfig struct {
	Enabled   bool              `json:"enabled"`
	Scheduler ZMQSchedulerConfig `json:"scheduler"`
}

type ZMQSchedulerConfig struct {
	RequesterEndpoint        string `json:"requester_endpoint"`
	ResponderEndpoint        string `json:"responder_endpoint"`
	HeartbeatIntervalMinutes int    `json:"heartbeat_interval_minutes"`
	ReplyTimeoutSeconds      int    `json:"reply_timeout_seconds"`
	MaxRetryAttempts         int    `json:"max_retry_attempts"`
	RetryIntervalSeconds     int    `json:"retry_interval_seconds"`
}

// ZMQManager ZMQ通信管理器
type ZMQManager struct {
	// 配置
	config *ZMQConfig

	// 双通道连接
	requester zmqpkg.Requester // 用于发送消息（状态上报、任务通知等）
	responder zmqpkg.Responder // 用于接收消息（任务分配、心跳等）

	// 运行状态
	isRunning bool
	mutex     sync.RWMutex
	
	// 生命周期控制
	ctx    context.Context
	cancel context.CancelFunc

	// 系统配置
	systemConfig *zmq.SystemConfig

	// 消息路由器
	messageRouter *MessageRouter
	
	// 调度指令管理器
	schedulerInstructionManager *SchedulerInstructionManager
	
	// 任务分配处理器
	taskAssignmentHandler *TaskAssignmentHandler
	
	// 心跳管理器
	heartbeatManager *internalzmq.HeartbeatManager
	
	// 状态上报器
	statusReporter *StatusReporter
}

// MessageHandler 消息处理接口
type MessageHandler interface {
	HandleMessage(message *zmq.Message) (*zmq.Message, error)
}

// NewZMQManager 创建ZMQ管理器
func NewZMQManager(config *ZMQConfig, systemConfig *zmq.SystemConfig) (*ZMQManager, error) {
	if config == nil {
		return nil, fmt.Errorf("ZMQ配置不能为空")
	}
	
	if systemConfig == nil {
		return nil, fmt.Errorf("系统配置不能为空")
	}

	if !config.Enabled {
		return nil, fmt.Errorf("ZMQ功能未启用")
	}

	// 验证调度系统配置
	if config.Scheduler.RequesterEndpoint == "" {
		return nil, fmt.Errorf("请求端点不能为空")
	}
	
	if config.Scheduler.ResponderEndpoint == "" {
		return nil, fmt.Errorf("响应端点不能为空")
	}

	// 创建消息路由器
	messageRouter := NewMessageRouter(systemConfig.RobotNo)
	
	// 创建调度指令管理器
	schedulerInstructionManager := NewSchedulerInstructionManager(systemConfig)

	return &ZMQManager{
		config:                      config,
		systemConfig:                systemConfig,
		messageRouter:               messageRouter,
		schedulerInstructionManager: schedulerInstructionManager,
		isRunning:                   false,
	}, nil
}

// NewZMQManagerWithComponents 创建带完整组件的ZMQ管理器
func NewZMQManagerWithComponents(
	config *ZMQConfig,
	systemConfig *zmq.SystemConfig,
	agvController *agv.Controller,
	workModeManager WorkModeManager,
	navigationDB *database.NavigationDB,
	startTaskFunc func([]string) error,
) (*ZMQManager, error) {
	// 先创建基础ZMQ管理器
	zmqManager, err := NewZMQManager(config, systemConfig)
	if err != nil {
		return nil, err
	}

	// 创建任务分配处理器
	if startTaskFunc != nil {
		log.Printf("📋 创建任务分配处理器...")
		taskAssignmentHandler := NewTaskAssignmentHandler(systemConfig, startTaskFunc)
		
		// 设置工作模式管理器和ZMQ管理器引用
		if workModeManager != nil {
			taskAssignmentHandler.SetWorkModeManager(workModeManager)
		}
		taskAssignmentHandler.SetZMQManager(zmqManager)
		
		zmqManager.taskAssignmentHandler = taskAssignmentHandler
		log.Printf("✅ 任务分配处理器创建完成")
	} else {
		log.Printf("⚠️  任务分配处理器未创建：缺少StartTask函数")
	}

	// 创建状态上报器（但不启动）
	if agvController != nil && workModeManager != nil && navigationDB != nil {
		log.Printf("📊 创建状态上报器...")
		statusReporter := NewStatusReporter(
			nil, // requester会在Start时设置
			agvController,
			workModeManager,
			navigationDB,
			systemConfig,
		)
		zmqManager.statusReporter = statusReporter
		log.Printf("✅ 状态上报器创建完成")
	} else {
		log.Printf("⚠️  状态上报器未创建：缺少必要组件")
	}

	// 心跳管理器将在Start方法中创建（需要ZMQ连接）
	log.Printf("💓 心跳管理器将在启动时创建")

	return zmqManager, nil
}

// Start 启动ZMQ管理器
func (z *ZMQManager) Start() error {
	z.mutex.Lock()
	defer z.mutex.Unlock()

	if z.isRunning {
		log.Printf("ZMQ管理器已在运行")
		return nil
	}

	log.Printf("🚀 正在启动ZMQ管理器...")
	log.Printf("   - 机器人编号: %s", z.systemConfig.RobotNo)
	log.Printf("   - Requester端点: %s", z.config.Scheduler.RequesterEndpoint)
	log.Printf("   - Responder端点: %s", z.config.Scheduler.ResponderEndpoint)

	// 创建上下文
	z.ctx, z.cancel = context.WithCancel(context.Background())

	// 步骤1：创建Requester（用于发送消息到调度系统）
	log.Printf("📤 初始化发送通道...")
	if err := z.initRequester(); err != nil {
		z.cancel()
		return fmt.Errorf("初始化Requester失败: %v", err)
	}

	// 步骤2：创建Responder（用于接收调度系统消息）
	log.Printf("📥 初始化接收通道...")
	if err := z.initResponder(); err != nil {
		z.cleanup()
		return fmt.Errorf("初始化Responder失败: %v", err)
	}

	// 步骤3：启动消息接收协程
	log.Printf("🔄 启动消息接收协程...")
	go z.startMessageReceiver()

	// 步骤3.5：注册指令处理器
	log.Printf("📋 注册指令处理器...")
	
	// 注册调度指令处理器（201/202/203）
	for _, handler := range z.schedulerInstructionManager.GetHandlers() {
		z.messageRouter.RegisterHandler(handler)
	}
	log.Printf("✅ 调度指令处理器注册完成（201/202/203）")
	
	// 注册任务分配处理器（200）
	if z.taskAssignmentHandler != nil {
		z.messageRouter.RegisterHandler(z.taskAssignmentHandler)
		log.Printf("✅ 任务分配处理器注册完成（200）")
	}

	// 给一点时间让Responder完全启动
	time.Sleep(100 * time.Millisecond)

	// 步骤4：创建并启动心跳管理器（通过MessageRouter统一处理）
	log.Printf("💓 创建心跳管理器...")
	z.heartbeatManager = internalzmq.NewHeartbeatManager(
		z.requester,    // 使用已创建立的requester连接
		z.systemConfig, // 系统配置
	)
	
	// 创建心跳处理器适配器并注册到MessageRouter中
	heartbeatHandler := NewHeartbeatHandlerAdapter(z.heartbeatManager, z.systemConfig.RobotNo)
	z.messageRouter.RegisterHandler(heartbeatHandler)
	log.Printf("✅ 心跳处理器注册完成（0）")
	
	// 启动心跳管理器（只启动发送心跳，接收通过MessageRouter处理）
	if err := z.heartbeatManager.Start(z.ctx); err != nil {
		log.Printf("⚠️  心跳管理器启动失败: %v", err)
		// 不阻断启动流程，继续启动状态上报器
	} else {
		log.Printf("✅ 心跳管理器启动成功（路由模式）")
	}

	// 步骤5：启动状态上报器
	if z.statusReporter != nil {
		log.Printf("📊 启动状态上报器...")
		// 设置requester连接
		z.statusReporter.requester = z.requester
		if err := z.statusReporter.Start(); err != nil {
			log.Printf("⚠️  状态上报器启动失败: %v", err)
		} else {
			log.Printf("✅ 状态上报器启动成功")
		}
	}

	// 步骤6：更新运行状态
	z.isRunning = true
	
	log.Printf("✅ ZMQ管理器启动成功")
	log.Printf("   - 双通道连接已建立")
	log.Printf("   - 消息接收协程已启动") 
	log.Printf("   - 心跳管理器已启动")
	log.Printf("   - 状态上报器已启动")
	log.Printf("   - 等待调度系统连接...")
	
	return nil
}

// Stop 停止ZMQ管理器
func (z *ZMQManager) Stop() error {
	z.mutex.Lock()
	defer z.mutex.Unlock()

	if !z.isRunning {
		log.Printf("ZMQ管理器已停止")
		return nil
	}

	log.Printf("🛑 正在停止ZMQ管理器...")

	// 步骤1：停止状态上报器
	if z.statusReporter != nil {
		log.Printf("   - 停止状态上报器...")
		z.statusReporter.Stop()
	}
	
	// 步骤1.5：停止心跳管理器
	if z.heartbeatManager != nil {
		log.Printf("   - 停止心跳管理器...")
		z.heartbeatManager.Stop()
	}

	// 步骤2：取消上下文，停止所有协程
	if z.cancel != nil {
		log.Printf("   - 取消上下文，停止协程...")
		z.cancel()
	}

	// 步骤2：给协程一点时间优雅退出
	time.Sleep(50 * time.Millisecond)

	// 步骤3：清理ZMQ连接资源
	log.Printf("   - 清理ZMQ连接资源...")
	z.cleanup()

	// 步骤4：重置状态
	z.isRunning = false
	z.ctx = nil
	z.cancel = nil
	
	log.Printf("✅ ZMQ管理器已停止")
	log.Printf("   - 双通道连接已关闭")
	log.Printf("   - 心跳管理器已停止")
	log.Printf("   - 状态上报器已停止")
	log.Printf("   - 所有协程已停止")
	log.Printf("   - 资源已清理完毕")
	
	return nil
}

// IsRunning 检查是否正在运行
func (z *ZMQManager) IsRunning() bool {
	z.mutex.RLock()
	defer z.mutex.RUnlock()
	return z.isRunning
}

// GetMessageRouter 获取消息路由器
func (z *ZMQManager) GetMessageRouter() *MessageRouter {
	z.mutex.RLock()
	defer z.mutex.RUnlock()
	return z.messageRouter
}

// RegisterHandler 注册指令处理器
func (z *ZMQManager) RegisterHandler(handler InstructionHandler) {
	if z.messageRouter != nil {
		z.messageRouter.RegisterHandler(handler)
	}
}

// UnregisterHandler 注销指令处理器
func (z *ZMQManager) UnregisterHandler(instructionCode int) {
	if z.messageRouter != nil {
		z.messageRouter.UnregisterHandler(instructionCode)
	}
}

// GetSchedulerInstructionHandler 获取调度指令处理器（实现ZMQManager接口）
func (z *ZMQManager) GetSchedulerInstructionHandler() SchedulerInstructionHandler {
	z.mutex.RLock()
	defer z.mutex.RUnlock()
	return z.schedulerInstructionManager
}

// GetStatusReporter 获取状态上报器
func (z *ZMQManager) GetStatusReporter() *StatusReporter {
	z.mutex.RLock()
	defer z.mutex.RUnlock()
	return z.statusReporter
}

// GetHeartbeatManager 获取心跳管理器
func (z *ZMQManager) GetHeartbeatManager() *internalzmq.HeartbeatManager {
	z.mutex.RLock()
	defer z.mutex.RUnlock()
	return z.heartbeatManager
}


// SendMessage 发送消息（通过Requester）
func (z *ZMQManager) SendMessage(message *zmq.Message) (*zmq.Message, error) {
	z.mutex.RLock()
	defer z.mutex.RUnlock()

	if !z.isRunning {
		return nil, fmt.Errorf("ZMQ管理器未运行")
	}

	if z.requester == nil {
		return nil, fmt.Errorf("Requester未初始化")
	}

	// 序列化消息
	messageBytes, err := zmq.SerializeMessage(message)
	if err != nil {
		return nil, fmt.Errorf("序列化消息失败: %v", err)
	}

	log.Printf("📤 发送ZMQ消息: 指令=%d, 内容长度=%d", message.Instruction, len(messageBytes))

	// 发送消息并等待回复
	timeout := time.Duration(z.config.Scheduler.ReplyTimeoutSeconds) * time.Second
	replyBytes, err := z.requester.RequestWithTimeout(messageBytes, timeout)
	if err != nil {
		return nil, fmt.Errorf("发送消息失败: %v", err)
	}

	// 反序列化回复
	reply, err := zmq.DeserializeMessage(replyBytes)
	if err != nil {
		return nil, fmt.Errorf("反序列化回复失败: %v", err)
	}

	log.Printf("📥 收到ZMQ回复: 指令=%d, 代码=%v", reply.Instruction, reply.Code)
	return reply, nil
}

// initRequester 初始化请求者
func (z *ZMQManager) initRequester() error {
	log.Printf("正在初始化Requester连接到: %s", z.config.Scheduler.RequesterEndpoint)

	// 创建ZMQ配置
	zmqConfig := &zmqpkg.Config{
		RequesterEndpoint: z.config.Scheduler.RequesterEndpoint,
		ConnectTimeout:    10 * time.Second, // 添加连接超时
		RecvTimeout:       time.Duration(z.config.Scheduler.ReplyTimeoutSeconds) * time.Second,
		SendTimeout:       30 * time.Second,
		SendHWM:           1000,
		RecvHWM:           1000,
	}

	// 验证配置有效性
	if zmqConfig.RequesterEndpoint == "" {
		return fmt.Errorf("Requester端点配置为空")
	}

	// 创建Requester
	requester, err := zmqpkg.NewRequester(zmqConfig)
	if err != nil {
		return fmt.Errorf("创建Requester失败: %v", err)
	}

	// 验证Requester是否创建成功
	if requester == nil {
		return fmt.Errorf("Requester创建返回nil")
	}

	z.requester = requester
	log.Printf("✅ Requester初始化成功: %s", z.config.Scheduler.RequesterEndpoint)
	log.Printf("   - 接收超时: %v", zmqConfig.RecvTimeout)
	log.Printf("   - 发送超时: %v", zmqConfig.SendTimeout)
	return nil
}

// initResponder 初始化响应者
func (z *ZMQManager) initResponder() error {
	log.Printf("正在初始化Responder绑定到: %s", z.config.Scheduler.ResponderEndpoint)

	// 创建ZMQ配置
	zmqConfig := &zmqpkg.Config{
		ResponderEndpoint: z.config.Scheduler.ResponderEndpoint,
		ConnectTimeout:    10 * time.Second, // 添加连接超时
		RecvTimeout:       5 * time.Second,  // 接收超时5秒
		SendTimeout:       5 * time.Second,  // 发送超时5秒
		SendHWM:           1000,
		RecvHWM:           1000,
	}

	// 验证配置有效性
	if zmqConfig.ResponderEndpoint == "" {
		return fmt.Errorf("Responder端点配置为空")
	}

	// 创建Responder
	responder, err := zmqpkg.NewResponder(zmqConfig)
	if err != nil {
		return fmt.Errorf("创建Responder失败: %v", err)
	}

	// 验证Responder是否创建成功
	if responder == nil {
		return fmt.Errorf("Responder创建返回nil")
	}

	// 设置请求处理函数
	err = responder.SetHandler(z.handleIncomingMessage)
	if err != nil {
		responder.Close()
		return fmt.Errorf("设置消息处理器失败: %v", err)
	}

	z.responder = responder
	log.Printf("✅ Responder初始化成功: %s", z.config.Scheduler.ResponderEndpoint)
	log.Printf("   - 接收超时: %v", zmqConfig.RecvTimeout)
	log.Printf("   - 发送超时: %v", zmqConfig.SendTimeout)
	log.Printf("   - 消息处理器已设置")
	return nil
}

// startMessageReceiver 启动消息接收协程
func (z *ZMQManager) startMessageReceiver() {
	if z.responder == nil {
		log.Printf("❌ 警告：Responder未初始化，无法启动消息接收")
		return
	}

	log.Printf("🔄 ZMQ消息接收协程已启动...")
	
	// 启动Responder监听
	if err := z.responder.Start(z.ctx); err != nil {
		// 检查是否是正常关闭
		if z.ctx.Err() == context.Canceled {
			log.Printf("📥 Responder正常停止")
		} else {
			log.Printf("❌ Responder启动失败: %v", err)
		}
	}
	
	log.Printf("📥 ZMQ消息接收协程已退出")
}

// handleIncomingMessage 处理接收到的消息
func (z *ZMQManager) handleIncomingMessage(request []byte) ([]byte, error) {
	log.Printf("📥 收到ZMQ请求: 大小=%d字节", len(request))

	// 反序列化消息
	message, err := zmq.DeserializeMessage(request)
	if err != nil {
		log.Printf("反序列化消息失败: %v", err)
		return z.createErrorResponse("invalid message format")
	}

	log.Printf("处理ZMQ消息: 指令=%d, 时间=%v", message.Instruction, message.TimeStamp)

	// 使用路由器处理消息
	response, err := z.messageRouter.HandleMessage(message)
	if err != nil {
		log.Printf("消息处理失败: %v", err)
		return z.createErrorResponse(fmt.Sprintf("handler error: %v", err))
	}

	// 序列化响应
	responseBytes, err := zmq.SerializeMessage(response)
	if err != nil {
		log.Printf("序列化响应失败: %v", err)
		return z.createErrorResponse("serialization error")
	}

	log.Printf("📤 发送ZMQ响应: 指令=%d, 大小=%d字节", response.Instruction, len(responseBytes))
	return responseBytes, nil
}

// createErrorResponse 创建错误响应
func (z *ZMQManager) createErrorResponse(errorMsg string) ([]byte, error) {
	errorResponse := zmq.NewMessage(-99, false, errorMsg)
	return zmq.SerializeMessage(errorResponse)
}

// cleanup 清理资源
func (z *ZMQManager) cleanup() {
	// 清理Requester
	if z.requester != nil {
		log.Printf("   - 关闭Requester连接...")
		if err := z.requester.Close(); err != nil {
			log.Printf("     警告：关闭Requester失败: %v", err)
		}
		z.requester = nil
	}

	// 清理Responder
	if z.responder != nil {
		log.Printf("   - 关闭Responder连接...")
		if err := z.responder.Close(); err != nil {
			log.Printf("     警告：关闭Responder失败: %v", err)
		}
		z.responder = nil
	}
}

// GetStatus 获取ZMQ管理器状态信息
func (z *ZMQManager) GetStatus() map[string]interface{} {
	z.mutex.RLock()
	defer z.mutex.RUnlock()

	// 检查连接状态
	requesterConnected := z.requester != nil && z.requester.IsConnected()
	responderConnected := z.responder != nil && z.responder.IsConnected()

	status := map[string]interface{}{
		"isRunning":            z.isRunning,
		"requesterEndpoint":    z.config.Scheduler.RequesterEndpoint,
		"responderEndpoint":    z.config.Scheduler.ResponderEndpoint,
		"robotNo":              z.systemConfig.RobotNo,
		"enabled":              z.config.Enabled,
		"requesterConnected":   requesterConnected,
		"responderConnected":   responderConnected,
		"bothChannelsReady":    requesterConnected && responderConnected,
		"heartbeatInterval":    z.config.Scheduler.HeartbeatIntervalMinutes,
		"replyTimeout":         z.config.Scheduler.ReplyTimeoutSeconds,
		"maxRetryAttempts":     z.config.Scheduler.MaxRetryAttempts,
		"routerStatus":         z.messageRouter.GetStatus(),
	}

	// 添加状态上报器状态信息
	if z.statusReporter != nil {
		status["statusReporter"] = map[string]interface{}{
			"enabled":              true,
			"reportInterval":       z.statusReporter.reportInterval.String(),
			"distanceThreshold":    z.statusReporter.distanceThreshold,
			"navigationPointsCount": len(z.statusReporter.allNavigationPoints),
			"lastTaskCompleted":    z.statusReporter.taskCompletionTime != nil,
		}
	} else {
		status["statusReporter"] = map[string]interface{}{
			"enabled": false,
		}
	}

	// 添加任务分配处理器状态信息
	if z.taskAssignmentHandler != nil {
		status["taskAssignmentHandler"] = map[string]interface{}{
			"enabled":      true,
			"robotNo":      z.taskAssignmentHandler.robotNo,
			"description":  z.taskAssignmentHandler.GetDescription(),
		}
	} else {
		status["taskAssignmentHandler"] = map[string]interface{}{
			"enabled": false,
		}
	}

	// 添加心跳管理器状态信息
	if z.heartbeatManager != nil {
		status["heartbeatManager"] = map[string]interface{}{
			"enabled":        true,
			"currentState":   z.heartbeatManager.GetCurrentState(),
			"lastHeartbeat":  z.heartbeatManager.GetLastHeartbeat().Format("2006-01-02 15:04:05"),
		}
	} else {
		status["heartbeatManager"] = map[string]interface{}{
			"enabled": false,
		}
	}

	return status
}