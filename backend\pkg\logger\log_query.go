package logger

import (
	"bufio"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"sort"
	"strings"
	"time"
)

// LogQueryRequest 日志查询请求
type LogQueryRequest struct {
	Module    string    `json:"module"`    // 模块过滤 (task, plc, agv, api, etc.)
	Level     string    `json:"level"`     // 级别过滤 (DEBUG, INFO, WARN, ERROR, FATAL)
	StartTime time.Time `json:"startTime"` // 开始时间
	EndTime   time.Time `json:"endTime"`   // 结束时间
	Search    string    `json:"search"`    // 搜索关键字
	Limit     int       `json:"limit"`     // 限制数量
	Offset    int       `json:"offset"`    // 偏移量
	Reverse   bool      `json:"reverse"`   // 是否倒序 (最新的在前)
}

// LogQueryResponse 日志查询响应
type LogQueryResponse struct {
	Logs      []LogEntry `json:"logs"`      // 日志条目
	Total     int        `json:"total"`     // 总数量
	HasMore   bool       `json:"hasMore"`   // 是否还有更多
	StartTime time.Time  `json:"startTime"` // 查询开始时间
	EndTime   time.Time  `json:"endTime"`   // 查询结束时间
}

// LogEntry 日志条目
type LogEntry struct {
	Timestamp time.Time `json:"timestamp"` // 时间戳
	Level     string    `json:"level"`     // 日志级别
	Module    string    `json:"module"`    // 模块名
	Caller    string    `json:"caller"`    // 调用位置
	Message   string    `json:"message"`   // 日志消息
	Raw       string    `json:"raw"`       // 原始日志行
}

// LogStats 日志统计信息
type LogStats struct {
	TotalLogs    int            `json:"totalLogs"`    // 总日志数
	LogsByLevel  map[string]int `json:"logsByLevel"`  // 按级别统计
	LogsByModule map[string]int `json:"logsByModule"` // 按模块统计
	LastUpdate   time.Time      `json:"lastUpdate"`   // 最后更新时间
	FileInfos    []LogFileInfo  `json:"fileInfos"`    // 日志文件信息
}

// LogFileInfo 日志文件信息
type LogFileInfo struct {
	Name         string    `json:"name"`         // 文件名
	Path         string    `json:"path"`         // 文件路径
	Size         int64     `json:"size"`         // 文件大小
	ModTime      time.Time `json:"modTime"`      // 修改时间
	LineCount    int       `json:"lineCount"`    // 行数
	IsStructured bool      `json:"isStructured"` // 是否为结构化日志
}

// LogQueryService 日志查询服务
type LogQueryService struct {
	logDirs      []string              // 日志目录列表
	cache        map[string][]LogEntry // 文件缓存
	fileModTimes map[string]time.Time  // 文件修改时间缓存
}

// NewLogQueryService 创建日志查询服务
func NewLogQueryService() *LogQueryService {
	return &LogQueryService{
		logDirs:      []string{"logs", "."}, // 默认查找logs目录和当前目录
		cache:        make(map[string][]LogEntry),
		fileModTimes: make(map[string]time.Time),
	}
}

// SetLogDirectories 设置日志目录
func (lqs *LogQueryService) SetLogDirectories(dirs []string) {
	lqs.logDirs = dirs
}

// QueryLogs 查询日志
func (lqs *LogQueryService) QueryLogs(req *LogQueryRequest) (*LogQueryResponse, error) {
	// 获取所有日志文件
	logFiles, err := lqs.getLogFiles()
	if err != nil {
		return nil, fmt.Errorf("获取日志文件失败: %v", err)
	}

	// 解析所有日志条目
	allLogs, err := lqs.parseLogFiles(logFiles)
	if err != nil {
		return nil, fmt.Errorf("解析日志文件失败: %v", err)
	}

	// 应用过滤器
	filteredLogs := lqs.applyFilters(allLogs, req)

	// 排序
	if req.Reverse {
		sort.Slice(filteredLogs, func(i, j int) bool {
			return filteredLogs[i].Timestamp.After(filteredLogs[j].Timestamp)
		})
	} else {
		sort.Slice(filteredLogs, func(i, j int) bool {
			return filteredLogs[i].Timestamp.Before(filteredLogs[j].Timestamp)
		})
	}

	// 分页
	total := len(filteredLogs)
	start := req.Offset
	end := start + req.Limit

	if start >= total {
		return &LogQueryResponse{
			Logs:    []LogEntry{},
			Total:   total,
			HasMore: false,
		}, nil
	}

	if end > total {
		end = total
	}

	paginatedLogs := filteredLogs[start:end]
	hasMore := end < total

	return &LogQueryResponse{
		Logs:    paginatedLogs,
		Total:   total,
		HasMore: hasMore,
	}, nil
}

// GetLogStats 获取日志统计信息
func (lqs *LogQueryService) GetLogStats() (*LogStats, error) {
	logFiles, err := lqs.getLogFiles()
	if err != nil {
		return nil, fmt.Errorf("获取日志文件失败: %v", err)
	}

	stats := &LogStats{
		LogsByLevel:  make(map[string]int),
		LogsByModule: make(map[string]int),
		LastUpdate:   time.Now(),
		FileInfos:    make([]LogFileInfo, 0),
	}

	// 统计每个文件
	for _, file := range logFiles {
		fileInfo, err := lqs.getFileStats(file)
		if err != nil {
			continue
		}

		stats.FileInfos = append(stats.FileInfos, *fileInfo)
		stats.TotalLogs += fileInfo.LineCount

		// 快速扫描文件获取级别和模块统计
		err = lqs.scanFileForStats(file, stats)
		if err != nil {
			continue
		}
	}

	return stats, nil
}

// getLogFiles 获取所有日志文件
func (lqs *LogQueryService) getLogFiles() ([]string, error) {
	var logFiles []string

	for _, dir := range lqs.logDirs {
		// 检查目录是否存在
		if _, err := os.Stat(dir); os.IsNotExist(err) {
			continue
		}

		// 查找.log文件
		pattern := filepath.Join(dir, "*.log")
		matches, err := filepath.Glob(pattern)
		if err != nil {
			continue
		}

		logFiles = append(logFiles, matches...)

		// 也查找备份文件 (.log.1, .log.2, etc.)
		backupPattern := filepath.Join(dir, "*.log.*")
		backupMatches, err := filepath.Glob(backupPattern)
		if err == nil {
			logFiles = append(logFiles, backupMatches...)
		}
	}

	return logFiles, nil
}

// parseLogFiles 解析日志文件
func (lqs *LogQueryService) parseLogFiles(files []string) ([]LogEntry, error) {
	var allLogs []LogEntry

	for _, file := range files {
		// 检查缓存
		if logs, cached := lqs.getCachedLogs(file); cached {
			allLogs = append(allLogs, logs...)
			continue
		}

		// 解析文件
		logs, err := lqs.parseLogFile(file)
		if err != nil {
			// 记录错误但继续处理其他文件
			fmt.Printf("解析日志文件失败 %s: %v\n", file, err)
			continue
		}

		// 缓存结果
		lqs.cacheLogs(file, logs)
		allLogs = append(allLogs, logs...)
	}

	return allLogs, nil
}

// parseLogFile 解析单个日志文件
func (lqs *LogQueryService) parseLogFile(filePath string) ([]LogEntry, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var logs []LogEntry
	scanner := bufio.NewScanner(file)

	// 检测是否为结构化日志(JSON格式)
	isStructured := lqs.isStructuredLogFile(filePath)

	for scanner.Scan() {
		line := scanner.Text()
		if strings.TrimSpace(line) == "" {
			continue
		}

		var entry LogEntry
		if isStructured {
			entry, err = lqs.parseStructuredLogLine(line)
		} else {
			entry = lqs.parseTextLogLine(line)
		}

		if err != nil {
			// 解析失败时保存原始行
			entry = LogEntry{
				Timestamp: time.Now(),
				Level:     "UNKNOWN",
				Module:    "unknown",
				Message:   line,
				Raw:       line,
			}
		}

		logs = append(logs, entry)
	}

	return logs, scanner.Err()
}

// parseStructuredLogLine 解析结构化日志行(JSON)
func (lqs *LogQueryService) parseStructuredLogLine(line string) (LogEntry, error) {
	var jsonLog struct {
		Timestamp string `json:"timestamp"`
		Level     string `json:"level"`
		Module    string `json:"module"`
		Caller    string `json:"caller"`
		Message   string `json:"message"`
	}

	err := json.Unmarshal([]byte(line), &jsonLog)
	if err != nil {
		return LogEntry{}, err
	}

	timestamp, err := time.Parse("2006-01-02 15:04:05.000", jsonLog.Timestamp)
	if err != nil {
		timestamp = time.Now()
	}

	return LogEntry{
		Timestamp: timestamp,
		Level:     jsonLog.Level,
		Module:    jsonLog.Module,
		Caller:    jsonLog.Caller,
		Message:   jsonLog.Message,
		Raw:       line,
	}, nil
}

// parseTextLogLine 解析文本日志行
func (lqs *LogQueryService) parseTextLogLine(line string) LogEntry {
	// 正则表达式匹配格式: [2025-06-25 15:31:44.448] [INFO] [module] caller.go:123 - message
	re := regexp.MustCompile(`^\[([^\]]+)\]\s*\[([^\]]+)\]\s*\[([^\]]+)\]\s*([^\s]+)\s*-\s*(.*)`)
	matches := re.FindStringSubmatch(line)

	if len(matches) != 6 {
		// 如果格式不匹配，返回默认解析
		return LogEntry{
			Timestamp: time.Now(),
			Level:     "INFO",
			Module:    "unknown",
			Message:   line,
			Raw:       line,
		}
	}

	timestamp, err := time.Parse("2006-01-02 15:04:05.000", matches[1])
	if err != nil {
		timestamp = time.Now()
	}

	return LogEntry{
		Timestamp: timestamp,
		Level:     matches[2],
		Module:    matches[3],
		Caller:    matches[4],
		Message:   matches[5],
		Raw:       line,
	}
}

// applyFilters 应用过滤器
func (lqs *LogQueryService) applyFilters(logs []LogEntry, req *LogQueryRequest) []LogEntry {
	var filtered []LogEntry

	for _, log := range logs {
		// 时间过滤
		if !req.StartTime.IsZero() && log.Timestamp.Before(req.StartTime) {
			continue
		}
		if !req.EndTime.IsZero() && log.Timestamp.After(req.EndTime) {
			continue
		}

		// 级别过滤
		if req.Level != "" && req.Level != "ALL" && !strings.EqualFold(log.Level, req.Level) {
			continue
		}

		// 模块过滤
		if req.Module != "" && req.Module != "ALL" && !strings.EqualFold(log.Module, req.Module) {
			continue
		}

		// 关键字搜索
		if req.Search != "" {
			searchLower := strings.ToLower(req.Search)
			if !strings.Contains(strings.ToLower(log.Message), searchLower) &&
				!strings.Contains(strings.ToLower(log.Raw), searchLower) {
				continue
			}
		}

		filtered = append(filtered, log)
	}

	return filtered
}

// isStructuredLogFile 检测是否为结构化日志文件
func (lqs *LogQueryService) isStructuredLogFile(filePath string) bool {
	// 基于文件名判断 (api.log 通常是结构化的)
	baseName := filepath.Base(filePath)
	structuredFiles := []string{"api.log", "structured.log"}

	for _, structured := range structuredFiles {
		if strings.Contains(baseName, structured) {
			return true
		}
	}

	return false
}

// getCachedLogs 获取缓存的日志
func (lqs *LogQueryService) getCachedLogs(filePath string) ([]LogEntry, bool) {
	// 检查文件修改时间
	info, err := os.Stat(filePath)
	if err != nil {
		return nil, false
	}

	cachedModTime, exists := lqs.fileModTimes[filePath]
	if !exists || !info.ModTime().Equal(cachedModTime) {
		// 文件已修改，清除缓存
		delete(lqs.cache, filePath)
		delete(lqs.fileModTimes, filePath)
		return nil, false
	}

	logs, exists := lqs.cache[filePath]
	return logs, exists
}

// cacheLogs 缓存日志
func (lqs *LogQueryService) cacheLogs(filePath string, logs []LogEntry) {
	info, err := os.Stat(filePath)
	if err != nil {
		return
	}

	lqs.cache[filePath] = logs
	lqs.fileModTimes[filePath] = info.ModTime()
}

// getFileStats 获取文件统计信息
func (lqs *LogQueryService) getFileStats(filePath string) (*LogFileInfo, error) {
	info, err := os.Stat(filePath)
	if err != nil {
		return nil, err
	}

	// 计算行数
	lineCount, err := lqs.countLines(filePath)
	if err != nil {
		lineCount = 0
	}

	return &LogFileInfo{
		Name:         filepath.Base(filePath),
		Path:         filePath,
		Size:         info.Size(),
		ModTime:      info.ModTime(),
		LineCount:    lineCount,
		IsStructured: lqs.isStructuredLogFile(filePath),
	}, nil
}

// countLines 计算文件行数
func (lqs *LogQueryService) countLines(filePath string) (int, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return 0, err
	}
	defer file.Close()

	count := 0
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		if strings.TrimSpace(scanner.Text()) != "" {
			count++
		}
	}

	return count, scanner.Err()
}

// scanFileForStats 扫描文件获取统计信息
func (lqs *LogQueryService) scanFileForStats(filePath string, stats *LogStats) error {
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	isStructured := lqs.isStructuredLogFile(filePath)

	for scanner.Scan() {
		line := scanner.Text()
		if strings.TrimSpace(line) == "" {
			continue
		}

		var level, module string

		if isStructured {
			// 解析JSON获取级别和模块
			var jsonLog map[string]interface{}
			if json.Unmarshal([]byte(line), &jsonLog) == nil {
				if l, ok := jsonLog["level"].(string); ok {
					level = l
				}
				if m, ok := jsonLog["module"].(string); ok {
					module = m
				}
			}
		} else {
			// 解析文本格式
			entry := lqs.parseTextLogLine(line)
			level = entry.Level
			module = entry.Module
		}

		if level != "" {
			stats.LogsByLevel[level]++
		}
		if module != "" {
			stats.LogsByModule[module]++
		}
	}

	return scanner.Err()
}

// ClearCache 清除缓存
func (lqs *LogQueryService) ClearCache() {
	lqs.cache = make(map[string][]LogEntry)
	lqs.fileModTimes = make(map[string]time.Time)
}

// 全局日志查询服务实例
var globalLogQueryService *LogQueryService

// GetLogQueryService 获取全局日志查询服务
func GetLogQueryService() *LogQueryService {
	if globalLogQueryService == nil {
		globalLogQueryService = NewLogQueryService()
	}
	return globalLogQueryService
}
