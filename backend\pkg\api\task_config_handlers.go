package api

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"path/filepath"
	"runtime"
	"time"

	"github.com/user/agv_nav/pkg/logger"
)

// TaskConfigResponse 任务配置响应
type TaskConfigResponse struct {
	Config interface{} `json:"config"`
}

// TaskConfigRequest 任务配置请求
type TaskConfigRequest struct {
	Config interface{} `json:"config"`
}

// getTaskConfigPath 获取任务配置文件路径
func getTaskConfigPath() string {
	// 方案1：如果在backend目录下运行，使用相对路径
	if _, err := os.Stat("data/task_config.json"); err == nil {
		return "data/task_config.json"
	}

	// 方案2：如果在项目根目录下运行，添加backend前缀
	if _, err := os.Stat("backend/data/task_config.json"); err == nil {
		return "backend/data/task_config.json"
	}

	// 方案3：使用可执行文件所在目录
	execPath, err := os.Executable()
	if err == nil {
		execDir := filepath.Dir(execPath)
		configPath := filepath.Join(execDir, "data", "task_config.json")
		if _, err := os.Stat(configPath); err == nil {
			return configPath
		}

		// 尝试在可执行文件的父目录查找
		parentDir := filepath.Dir(execDir)
		configPath = filepath.Join(parentDir, "data", "task_config.json")
		if _, err := os.Stat(configPath); err == nil {
			return configPath
		}
	}

	// 方案4：基于源代码位置（开发环境）
	_, filename, _, ok := runtime.Caller(0)
	if ok {
		dir := filepath.Dir(filename)
		// 从 pkg/api 回到 backend 目录
		backendDir := filepath.Join(dir, "..", "..")
		configPath := filepath.Join(backendDir, "data", "task_config.json")
		if _, err := os.Stat(configPath); err == nil {
			return configPath
		}
	}

	// 默认返回相对路径
	return "data/task_config.json"
}

// handleGetTaskConfig 获取任务配置
func (s *Server) handleGetTaskConfig(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	apiLogger := logger.GetModuleLogger("api")
	startTime := time.Now()
	clientIP := r.RemoteAddr
	apiLogger.Info("获取任务配置API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	// 读取配置文件
	configPath := getTaskConfigPath()
	apiLogger.Debug("使用配置文件路径", "path", configPath)
	data, err := ioutil.ReadFile(configPath)
	if err != nil {
		apiLogger.Error("读取任务配置文件失败", "path", configPath, "error", err)
		http.Error(w, fmt.Sprintf("读取配置文件失败: %v", err), http.StatusInternalServerError)
		return
	}

	// 验证JSON格式
	var config interface{}
	if err := json.Unmarshal(data, &config); err != nil {
		apiLogger.Error("配置文件JSON格式错误", "error", err)
		http.Error(w, fmt.Sprintf("配置文件格式错误: %v", err), http.StatusInternalServerError)
		return
	}

	// 返回配置数据，使用标准API响应格式
	response := map[string]interface{}{
		"success": true,
		"data": TaskConfigResponse{
			Config: config,
		},
	}

	s.enableCORS(w)
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(response); err != nil {
		apiLogger.Error("编码响应失败", "error", err)
		http.Error(w, "编码响应失败", http.StatusInternalServerError)
		return
	}

	duration := time.Since(startTime)
	apiLogger.Info("获取任务配置API完成", "duration", duration, "clientIP", clientIP)
}

// handleSaveTaskConfig 保存任务配置
func (s *Server) handleSaveTaskConfig(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	apiLogger := logger.GetModuleLogger("api")
	startTime := time.Now()
	clientIP := r.RemoteAddr
	apiLogger.Info("保存任务配置API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	// 读取请求体
	body, err := ioutil.ReadAll(r.Body)
	if err != nil {
		apiLogger.Error("读取请求体失败", "error", err)
		http.Error(w, "读取请求体失败", http.StatusBadRequest)
		return
	}

	// 解析请求数据
	var request TaskConfigRequest
	if err := json.Unmarshal(body, &request); err != nil {
		apiLogger.Error("解析请求数据失败", "error", err)
		http.Error(w, "请求数据格式错误", http.StatusBadRequest)
		return
	}

	// 验证配置格式
	configData, err := json.MarshalIndent(request.Config, "", "  ")
	if err != nil {
		apiLogger.Error("配置数据序列化失败", "error", err)
		http.Error(w, "配置数据格式错误", http.StatusBadRequest)
		return
	}

	// 创建配置文件的备份
	if err := s.backupConfigFile(); err != nil {
		apiLogger.Warn("创建配置备份失败", "error", err)
		// 备份失败不阻止保存操作，只记录警告
	}

	// 确保目录存在
	configPath := getTaskConfigPath()
	if err := os.MkdirAll(filepath.Dir(configPath), 0755); err != nil {
		apiLogger.Error("创建配置目录失败", "error", err)
		http.Error(w, "创建配置目录失败", http.StatusInternalServerError)
		return
	}

	// 原子性写入配置文件
	tempPath := configPath + ".tmp"
	if err := ioutil.WriteFile(tempPath, configData, 0644); err != nil {
		apiLogger.Error("写入临时配置文件失败", "error", err)
		http.Error(w, "保存配置失败", http.StatusInternalServerError)
		return
	}

	// 移动临时文件到目标位置
	if err := os.Rename(tempPath, configPath); err != nil {
		apiLogger.Error("替换配置文件失败", "error", err)
		os.Remove(tempPath) // 清理临时文件
		http.Error(w, "保存配置失败", http.StatusInternalServerError)
		return
	}

	// 验证写入的文件
	if err := s.validateConfigFile(); err != nil {
		apiLogger.Error("配置文件验证失败", "error", err)
		// 尝试恢复备份
		s.restoreConfigBackup()
		http.Error(w, "配置文件验证失败，已恢复备份", http.StatusInternalServerError)
		return
	}

	// 返回成功响应
	response := map[string]interface{}{
		"success":   true,
		"message":   "配置保存成功",
		"timestamp": time.Now().Format("2006-01-02 15:04:05"),
	}

	s.enableCORS(w)
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(response); err != nil {
		apiLogger.Error("编码响应失败", "error", err)
		http.Error(w, "编码响应失败", http.StatusInternalServerError)
		return
	}

	duration := time.Since(startTime)
	apiLogger.Info("保存任务配置API完成", "duration", duration, "clientIP", clientIP)
}

// handleValidateTaskConfig 验证任务配置
func (s *Server) handleValidateTaskConfig(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		s.enableCORS(w)
		w.WriteHeader(http.StatusOK)
		return
	}

	apiLogger := logger.GetModuleLogger("api")
	startTime := time.Now()
	clientIP := r.RemoteAddr
	apiLogger.Info("验证任务配置API请求", "method", r.Method, "path", r.URL.Path, "clientIP", clientIP)

	// 读取请求体
	body, err := ioutil.ReadAll(r.Body)
	if err != nil {
		apiLogger.Error("读取请求体失败", "error", err)
		http.Error(w, "读取请求体失败", http.StatusBadRequest)
		return
	}

	// 验证JSON格式
	var config interface{}
	if err := json.Unmarshal(body, &config); err != nil {
		response := map[string]interface{}{
			"valid": false,
			"error": fmt.Sprintf("JSON格式错误: %v", err),
		}
		s.enableCORS(w)
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
		return
	}

	// 可以在这里添加更多的配置验证逻辑
	// 比如检查必填字段、数值范围等

	// 返回验证结果，使用标准API响应格式
	response := map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"valid":   true,
			"message": "配置格式正确",
		},
	}

	s.enableCORS(w)
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(response); err != nil {
		apiLogger.Error("编码响应失败", "error", err)
		http.Error(w, "编码响应失败", http.StatusInternalServerError)
		return
	}

	duration := time.Since(startTime)
	apiLogger.Info("验证任务配置API完成", "duration", duration, "clientIP", clientIP)
}

// backupConfigFile 备份配置文件
func (s *Server) backupConfigFile() error {
	configPath := getTaskConfigPath()
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil // 配置文件不存在，无需备份
	}

	backupPath := configPath + ".backup." + time.Now().Format("20060102_150405")
	data, err := ioutil.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取原配置文件失败: %w", err)
	}

	if err := ioutil.WriteFile(backupPath, data, 0644); err != nil {
		return fmt.Errorf("写入备份文件失败: %w", err)
	}

	return nil
}

// validateConfigFile 验证配置文件
func (s *Server) validateConfigFile() error {
	configPath := getTaskConfigPath()
	data, err := ioutil.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	var config interface{}
	if err := json.Unmarshal(data, &config); err != nil {
		return fmt.Errorf("配置文件JSON格式错误: %w", err)
	}

	return nil
}

// restoreConfigBackup 恢复配置备份
func (s *Server) restoreConfigBackup() error {
	configPath := getTaskConfigPath()
	backupPattern := configPath + ".backup.*"
	matches, err := filepath.Glob(backupPattern)
	if err != nil || len(matches) == 0 {
		return fmt.Errorf("没有找到配置备份文件")
	}

	// 使用最新的备份文件
	latestBackup := matches[len(matches)-1]
	data, err := ioutil.ReadFile(latestBackup)
	if err != nil {
		return fmt.Errorf("读取备份文件失败: %w", err)
	}

	if err := ioutil.WriteFile(configPath, data, 0644); err != nil {
		return fmt.Errorf("恢复配置文件失败: %w", err)
	}

	return nil
}
