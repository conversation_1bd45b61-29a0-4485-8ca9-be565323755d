package task

import (
	"fmt"
	"log"
	//"sort"
)

// TaskGroup 任务组
// 表示一组需要连续处理的细纱机任务
type TaskGroup struct {
	GroupID      string    `json:"group_id"`      // 任务组ID，用于标识
	Type         string    `json:"type"`          // 任务类型：single（单侧）或 lane（巷道）
	Machines     []string  `json:"machines"`      // 包含的细纱机列表
	ProcessOrder []string  `json:"process_order"` // 处理顺序
	LaneInfo     *LaneInfo `json:"lane_info"`     // 巷道信息（仅当Type为lane时有效）
}

// LaneInfo 巷道信息
// 记录巷道的详细信息
type LaneInfo struct {
	LeftMachine    string `json:"left_machine"`     // 左侧细纱机
	RightMachine   string `json:"right_machine"`    // 右侧细纱机
	EntryMachine   string `json:"entry_machine"`    // 入口侧细纱机
	NeedTurnAround bool   `json:"need_turn_around"` // 是否需要调头
}

// PreprocessTasks 预处理任务列表
// 将用户选择的细纱机列表转换为优化的任务组
// 主要功能：
// 1. 识别巷道关系，将相邻的L/R配对
// 2. 处理特殊配置（中央通道、跳过位置等）
// 3. 生成优化的执行顺序
func PreprocessTasks(machines []string) ([]TaskGroup, error) {
	log.Printf("开始预处理任务，输入细纱机数量: %d, 列表: %v", len(machines), machines)

	// 确保巷道配置已加载
	log.Printf("正在加载巷道配置...")
	if err := LoadLaneConfiguration(); err != nil {
		log.Printf("加载巷道配置失败: %v, 使用默认配置", err)
	} else {
		log.Printf("巷道配置加载成功")
	}

	// 用于记录已处理的细纱机
	processed := make(map[string]bool)

	// 任务组列表
	var groups []TaskGroup

	// 先对输入的细纱机列表排序，便于处理
	sortedMachines := make([]string, len(machines))
	copy(sortedMachines, machines)
	//sort.Strings(sortedMachines)

	// 遍历每个细纱机，生成任务组
	for _, machine := range sortedMachines {
		// 跳过已处理的细纱机
		if processed[machine] {
			continue
		}

		// 检查是否应该跳过
		if ShouldSkip(machine) {
			log.Printf("跳过细纱机 %s（配置为skip）", machine)
			processed[machine] = true
			continue
		}

		// 检查是否只能单侧处理
		if IsSingleOnly(machine) {
			log.Printf("细纱机 %s 配置为单侧处理", machine)
			group := createSingleGroup(machine)
			groups = append(groups, group)
			processed[machine] = true
			continue
		}

		// 尝试查找巷道配对
		leftMachine, rightMachine, isLane := FindLanePair(machine)

		if isLane {
			// 检查配对的两侧是否都在任务列表中
			leftInList := contains(machines, leftMachine)
			rightInList := contains(machines, rightMachine)

			if leftInList && rightInList && !processed[leftMachine] && !processed[rightMachine] {
				// 两侧都在任务中且未处理，创建巷道任务组
				log.Printf("创建巷道任务组: %s - %s", leftMachine, rightMachine)
				group := createLaneGroup(leftMachine, rightMachine)
				groups = append(groups, group)
				processed[leftMachine] = true
				processed[rightMachine] = true
			} else {
				// 只有一侧在任务中，作为单侧处理
				log.Printf("细纱机 %s 的配对不完整，作为单侧处理", machine)
				group := createSingleGroup(machine)
				groups = append(groups, group)
				processed[machine] = true
			}
		} else {
			// 无法配对，作为单侧处理
			log.Printf("细纱机 %s 无法配对，作为单侧处理", machine)
			group := createSingleGroup(machine)
			groups = append(groups, group)
			processed[machine] = true
		}
	}

	// 检查是否有遗漏的细纱机
	for _, machine := range machines {
		if !processed[machine] && !ShouldSkip(machine) {
			log.Printf("警告：细纱机 %s 未被处理", machine)
		}
	}

	log.Printf("预处理完成，生成任务组数量: %d", len(groups))
	return groups, nil
}

// createSingleGroup 创建单侧任务组
func createSingleGroup(machine string) TaskGroup {
	groupID := fmt.Sprintf("single_%s", machine)
	return TaskGroup{
		GroupID:      groupID,
		Type:         "single",
		Machines:     []string{machine},
		ProcessOrder: []string{machine},
		LaneInfo:     nil,
	}
}

// createLaneGroup 创建巷道任务组
func createLaneGroup(leftMachine, rightMachine string) TaskGroup {
	groupID := fmt.Sprintf("lane_%s_%s", leftMachine, rightMachine)

	// 确定入口侧
	entryMachine := GetLaneEntryMachine(leftMachine, rightMachine)

	// 确定处理顺序（总是L侧先处理）
	processOrder := []string{leftMachine, rightMachine}

	return TaskGroup{
		GroupID:      groupID,
		Type:         "lane",
		Machines:     []string{leftMachine, rightMachine},
		ProcessOrder: processOrder,
		LaneInfo: &LaneInfo{
			LeftMachine:    leftMachine,
			RightMachine:   rightMachine,
			EntryMachine:   entryMachine,
			NeedTurnAround: true, // 巷道任务都需要调头
		},
	}
}

// contains 检查字符串切片中是否包含指定元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// GetTaskGroupSummary 获取任务组摘要信息
// 用于日志记录和调试
func GetTaskGroupSummary(groups []TaskGroup) string {
	summary := fmt.Sprintf("任务组总数: %d\n", len(groups))

	singleCount := 0
	laneCount := 0
	totalMachines := 0

	for i, group := range groups {
		summary += fmt.Sprintf("  组%d [%s]: ", i+1, group.Type)

		switch group.Type {
		case "single":
			singleCount++
			summary += fmt.Sprintf("%s\n", group.Machines[0])
		case "lane":
			laneCount++
			summary += fmt.Sprintf("%s <-> %s\n",
				group.LaneInfo.LeftMachine,
				group.LaneInfo.RightMachine)
		}

		totalMachines += len(group.Machines)
	}

	summary += fmt.Sprintf("\n统计: 单侧任务=%d, 巷道任务=%d, 总细纱机数=%d",
		singleCount, laneCount, totalMachines)

	return summary
}

// ValidateTaskGroups 验证任务组的有效性
// 确保没有重复处理和遗漏
func ValidateTaskGroups(groups []TaskGroup, originalMachines []string) error {
	machineMap := make(map[string]int)

	// 统计每个细纱机出现的次数
	for _, group := range groups {
		for _, machine := range group.Machines {
			machineMap[machine]++
		}
	}

	// 检查是否有重复
	for machine, count := range machineMap {
		if count > 1 {
			return fmt.Errorf("细纱机 %s 出现在多个任务组中", machine)
		}
	}

	// 检查是否有遗漏（排除应该跳过的）
	for _, machine := range originalMachines {
		if !ShouldSkip(machine) && machineMap[machine] == 0 {
			return fmt.Errorf("细纱机 %s 未包含在任何任务组中", machine)
		}
	}

	return nil
}
