package logger

import (
	"sync"
)

// ModuleLoggers 模块日志记录器管理
type ModuleLoggers struct {
	mu      sync.RWMutex
	loggers map[string]*Logger
}

var (
	moduleLoggers = &ModuleLoggers{
		loggers: make(map[string]*Logger),
	}
)

// GetModuleLogger 获取模块专用的日志记录器
func GetModuleLogger(module string) *Logger {
	moduleLoggers.mu.RLock()
	if logger, exists := moduleLoggers.loggers[module]; exists {
		moduleLoggers.mu.RUnlock()
		return logger
	}
	moduleLoggers.mu.RUnlock()

	// 创建新的模块日志记录器
	moduleLoggers.mu.Lock()
	defer moduleLoggers.mu.Unlock()

	// 双重检查
	if logger, exists := moduleLoggers.loggers[module]; exists {
		return logger
	}

	// 使用模块特定配置
	config := GetModuleConfig(module)
	logger := NewLoggerWithConfig(module, config)
	moduleLoggers.loggers[module] = logger

	return logger
}

// SetModuleLogger 设置模块的日志记录器
func SetModuleLogger(module string, logger *Logger) {
	moduleLoggers.mu.Lock()
	defer moduleLoggers.mu.Unlock()

	// 关闭旧的日志记录器
	if oldLogger, exists := moduleLoggers.loggers[module]; exists {
		oldLogger.Close()
	}

	moduleLoggers.loggers[module] = logger
}

// CloseAllModuleLoggers 关闭所有模块日志记录器
func CloseAllModuleLoggers() {
	moduleLoggers.mu.Lock()
	defer moduleLoggers.mu.Unlock()

	for _, logger := range moduleLoggers.loggers {
		logger.Close()
	}

	moduleLoggers.loggers = make(map[string]*Logger)
}

// 预定义的模块日志记录器

// TaskLogger 任务模块日志记录器
func TaskLogger() *Logger {
	return GetModuleLogger("task")
}

// PLCLogger PLC模块日志记录器
func PLCLogger() *Logger {
	return GetModuleLogger("plc")
}

// AGVLogger AGV控制模块日志记录器
func AGVLogger() *Logger {
	return GetModuleLogger("agv")
}

// APILogger API模块日志记录器
func APILogger() *Logger {
	return GetModuleLogger("api")
}

// DBLogger 数据库模块日志记录器
func DBLogger() *Logger {
	return GetModuleLogger("database")
}

// ConfigLogger 配置模块日志记录器
func ConfigLogger() *Logger {
	return GetModuleLogger("config")
}

// ProtocolLogger 协议模块日志记录器
func ProtocolLogger() *Logger {
	return GetModuleLogger("protocol")
}

// 便捷的模块日志记录方法

// TaskDebug 任务模块调试日志
func TaskDebug(format string, args ...interface{}) {
	TaskLogger().Debug(format, args...)
}

// TaskInfo 任务模块信息日志
func TaskInfo(format string, args ...interface{}) {
	TaskLogger().Info(format, args...)
}

// TaskWarn 任务模块警告日志
func TaskWarn(format string, args ...interface{}) {
	TaskLogger().Warn(format, args...)
}

// TaskError 任务模块错误日志
func TaskError(format string, args ...interface{}) {
	TaskLogger().Error(format, args...)
}

// PLCDebug PLC模块调试日志
func PLCDebug(format string, args ...interface{}) {
	PLCLogger().Debug(format, args...)
}

// PLCInfo PLC模块信息日志
func PLCInfo(format string, args ...interface{}) {
	PLCLogger().Info(format, args...)
}

// PLCWarn PLC模块警告日志
func PLCWarn(format string, args ...interface{}) {
	PLCLogger().Warn(format, args...)
}

// PLCError PLC模块错误日志
func PLCError(format string, args ...interface{}) {
	PLCLogger().Error(format, args...)
}

// AGVDebug AGV模块调试日志
func AGVDebug(format string, args ...interface{}) {
	AGVLogger().Debug(format, args...)
}

// AGVInfo AGV模块信息日志
func AGVInfo(format string, args ...interface{}) {
	AGVLogger().Info(format, args...)
}

// AGVWarn AGV模块警告日志
func AGVWarn(format string, args ...interface{}) {
	AGVLogger().Warn(format, args...)
}

// AGVError AGV模块错误日志
func AGVError(format string, args ...interface{}) {
	AGVLogger().Error(format, args...)
}

// APIDebug API模块调试日志
func APIDebug(format string, args ...interface{}) {
	APILogger().Debug(format, args...)
}

// APIInfo API模块信息日志
func APIInfo(format string, args ...interface{}) {
	APILogger().Info(format, args...)
}

// APIWarn API模块警告日志
func APIWarn(format string, args ...interface{}) {
	APILogger().Warn(format, args...)
}

// APIError API模块错误日志
func APIError(format string, args ...interface{}) {
	APILogger().Error(format, args...)
}

// InitializeModuleLoggers 初始化所有模块日志记录器
func InitializeModuleLoggers() {
	// 预创建常用模块的日志记录器
	modules := []string{"task", "plc", "agv", "api", "database", "config", "protocol"}

	for _, module := range modules {
		GetModuleLogger(module)
	}
}

// LoggerHealthCheck 日志记录器健康检查
func LoggerHealthCheck() map[string]bool {
	moduleLoggers.mu.RLock()
	defer moduleLoggers.mu.RUnlock()

	health := make(map[string]bool)

	for module, logger := range moduleLoggers.loggers {
		// 简单的健康检查：尝试写入一条测试日志
		health[module] = true

		// 检查文件写入器是否正常
		if logger.fileWriter != nil {
			// 可以在这里添加更复杂的健康检查逻辑
			health[module] = !logger.fileWriter.closed
		}
	}

	return health
}
