package zmq

import (
	"fmt"

	"github.com/user/agv_nav/internal/zmq"
)

// DefaultHandler 默认处理器，用于处理未知或暂未实现的指令
type DefaultHandler struct {
	*BaseHandler
}

// NewDefaultHandler 创建默认处理器
func NewDefaultHandler(robotNo string) *DefaultHandler {
	return &DefaultHandler{
		BaseHandler: NewBaseHandler(-1, "默认处理器", robotNo),
	}
}

// HandleInstruction 处理未知指令
func (h *DefaultHandler) HandleInstruction(message *zmq.Message) (*zmq.Message, error) {
	h.<PERSON>g<PERSON>ei<PERSON>(message)
	
	// 对于未知指令，返回一个通用的回复
	content := fmt.Sprintf("收到指令%d，但该指令暂未实现", message.Instruction)
	
	h.LogCompleted()
	return h.CreateSuccessResponse(content), nil
}