package plc_test

import (
	"fmt"
	"math"
	"time"

	"github.com/user/agv_nav/pkg/plc"
)

// StepExecutor 步骤执行器
type StepExecutor struct {
	plcController *plc.Controller
	monitor       *PLCMonitor
}

// NewStepExecutor 创建新的步骤执行器
func NewStepExecutor(plcController *plc.Controller, monitor *PLCMonitor) *StepExecutor {
	return &StepExecutor{
		plcController: plcController,
		monitor:       monitor,
	}
}

// ExecuteConnectTest 执行连接测试
func (se *StepExecutor) ExecuteConnectTest() *StepResult {
	result := &StepResult{
		StepID:     "connect_test",
		StepName:   "连接测试",
		Status:     "running",
		StartTime:  time.Now(),
		Operations: make([]OperationResult, 0),
	}

	// 检查PLC连接状态
	if !se.plcController.IsConnected() {
		result.Status = "failed"
		result.Error = "PLC未连接"
		result.EndTime = time.Now()
		result.Duration = time.Since(result.StartTime)

		failOp := OperationResult{
			Operation: "check",
			Message:   "❌ PLC连接检查失败：设备未连接",
			Success:   false,
			Timestamp: time.Now().Format("15:04:05.000"),
		}
		result.Operations = append(result.Operations, failOp)
		return result
	}

	// 测试心跳读取
	readResult := se.readCoilWithLog("M500", 500, "测试心跳读取")
	result.Operations = append(result.Operations, readResult)

	if !readResult.Success {
		result.Status = "failed"
		result.Error = "心跳读取失败"
		result.EndTime = time.Now()
		result.Duration = time.Since(result.StartTime)
		return result
	}

	// 测试心跳写入
	writeResult := se.writeCoilWithLog("M600", 600, true, "测试心跳写入")
	result.Operations = append(result.Operations, writeResult)

	if !writeResult.Success {
		result.Status = "failed"
		result.Error = "心跳写入失败"
		result.EndTime = time.Now()
		result.Duration = time.Since(result.StartTime)
		return result
	}

	// 连接测试成功
	result.Status = "success"
	result.EndTime = time.Now()
	result.Duration = time.Since(result.StartTime)

	successOp := OperationResult{
		Operation: "summary",
		Message:   "✅ PLC连接测试成功！心跳读写功能正常",
		Success:   true,
		Timestamp: time.Now().Format("15:04:05.000"),
		Duration:  result.Duration.String(),
	}
	result.Operations = append(result.Operations, successOp)

	return result
}

// ExecuteRequestControl 执行请求控制权
func (se *StepExecutor) ExecuteRequestControl() *StepResult {
	result := &StepResult{
		StepID:     "request_control",
		StepName:   "请求控制权",
		Status:     "running",
		StartTime:  time.Now(),
		Operations: make([]OperationResult, 0),
	}

	// 步骤1: 向M601写入控制权请求信号
	writeResult := se.writeCoilWithLog("M601", 601, true, "AGV请求PLC控制权")
	result.Operations = append(result.Operations, writeResult)

	if !writeResult.Success {
		result.Status = "failed"
		result.Error = writeResult.Message
		result.EndTime = time.Now()
		result.Duration = time.Since(result.StartTime)
		return result
	}

	// 步骤2: 等待并读取M501确认控制权切换
	waitOp := OperationResult{
		Operation: "wait",
		Message:   "⏳ 等待PLC确认控制权切换（M501）...",
		Success:   true,
		Timestamp: time.Now().Format("15:04:05.000"),
	}
	result.Operations = append(result.Operations, waitOp)

	maxWaitTime := 5 * time.Second
	waitStart := time.Now()

	for time.Since(waitStart) < maxWaitTime {
		time.Sleep(200 * time.Millisecond) // 200ms检查一次

		readResult := se.readCoilWithLog("M501", 501, "读取PLC控制权确认状态")
		result.Operations = append(result.Operations, readResult)

		if readResult.Success && readResult.Value.(bool) {
			// 控制权获取成功
			result.Status = "success"
			result.EndTime = time.Now()
			result.Duration = time.Since(result.StartTime)

			successOp := OperationResult{
				Operation: "summary",
				Message:   "✅ 控制权获取成功！M601=true → M501=true",
				Success:   true,
				Timestamp: time.Now().Format("15:04:05.000"),
				Duration:  result.Duration.String(),
			}
			result.Operations = append(result.Operations, successOp)
			return result
		}
	}

	// 超时失败
	result.Status = "failed"
	result.Error = "等待M501确认超时（5秒）"
	result.EndTime = time.Now()
	result.Duration = time.Since(result.StartTime)

	failureOp := OperationResult{
		Operation: "summary",
		Message:   "❌ 控制权获取失败：M501在5秒内未变为true",
		Success:   false,
		Timestamp: time.Now().Format("15:04:05.000"),
	}
	result.Operations = append(result.Operations, failureOp)

	return result
}

// ExecuteStartTask 执行开始任务信号
func (se *StepExecutor) ExecuteStartTask() *StepResult {
	result := &StepResult{
		StepID:     "start_task",
		StepName:   "开始任务信号",
		Status:     "running",
		StartTime:  time.Now(),
		Operations: make([]OperationResult, 0),
	}

	// 向M602写入任务开始信号
	writeResult := se.writeCoilWithLog("M602", 602, true, "发送任务开始信号")
	result.Operations = append(result.Operations, writeResult)

	if !writeResult.Success {
		result.Status = "failed"
		result.Error = writeResult.Message
		result.EndTime = time.Now()
		result.Duration = time.Since(result.StartTime)
		return result
	}

	// 任务开始信号发送成功
	result.Status = "success"
	result.EndTime = time.Now()
	result.Duration = time.Since(result.StartTime)

	successOp := OperationResult{
		Operation: "summary",
		Message:   "✅ 任务开始信号发送成功！M602=true",
		Success:   true,
		Timestamp: time.Now().Format("15:04:05.000"),
		Duration:  result.Duration.String(),
	}
	result.Operations = append(result.Operations, successOp)

	return result
}

// ExecuteDataPrepare 执行数据准备检查
func (se *StepExecutor) ExecuteDataPrepare() *StepResult {
	result := &StepResult{
		StepID:     "data_prepare",
		StepName:   "数据准备检查",
		Status:     "running",
		StartTime:  time.Now(),
		Operations: make([]OperationResult, 0),
	}

	// 等待M509变为true，表示PLC准备接收数据
	waitOp := OperationResult{
		Operation: "wait",
		Message:   "⏳ 等待PLC准备接收数据（M509）...",
		Success:   true,
		Timestamp: time.Now().Format("15:04:05.000"),
	}
	result.Operations = append(result.Operations, waitOp)

	maxWaitTime := 10 * time.Second
	waitStart := time.Now()

	for time.Since(waitStart) < maxWaitTime {
		time.Sleep(200 * time.Millisecond)

		readResult := se.readCoilWithLog("M509", 509, "检查PLC数据准备状态")
		result.Operations = append(result.Operations, readResult)

		if readResult.Success && readResult.Value.(bool) {
			// PLC已准备好接收数据
			result.Status = "success"
			result.EndTime = time.Now()
			result.Duration = time.Since(result.StartTime)

			successOp := OperationResult{
				Operation: "summary",
				Message:   "✅ PLC数据准备就绪！M509=true",
				Success:   true,
				Timestamp: time.Now().Format("15:04:05.000"),
				Duration:  result.Duration.String(),
			}
			result.Operations = append(result.Operations, successOp)
			return result
		}
	}

	// 超时失败
	result.Status = "failed"
	result.Error = "等待M509准备信号超时（10秒）"
	result.EndTime = time.Now()
	result.Duration = time.Since(result.StartTime)

	failureOp := OperationResult{
		Operation: "summary",
		Message:   "❌ 数据准备检查失败：M509在10秒内未变为true",
		Success:   false,
		Timestamp: time.Now().Format("15:04:05.000"),
	}
	result.Operations = append(result.Operations, failureOp)

	return result
}

// ExecuteDataWrite 执行数据写入
func (se *StepExecutor) ExecuteDataWrite() *StepResult {
	result := &StepResult{
		StepID:     "data_write",
		StepName:   "数据写入",
		Status:     "running",
		StartTime:  time.Now(),
		Operations: make([]OperationResult, 0),
	}

	// 写入方向数据 (M604: true=右侧, false=左侧)
	directionResult := se.writeCoilWithLog("M604", 604, true, "写入方向数据（右侧）")
	result.Operations = append(result.Operations, directionResult)

	if !directionResult.Success {
		result.Status = "failed"
		result.Error = "方向数据写入失败"
		result.EndTime = time.Now()
		result.Duration = time.Since(result.StartTime)
		return result
	}

	// 写入距离数据 (123.45米)
	distance := float32(123.45)
	distanceResult := se.writeFloatToRegisters("D602", 602, distance, "写入距离数据")
	result.Operations = append(result.Operations, distanceResult)

	if !distanceResult.Success {
		result.Status = "failed"
		result.Error = "距离数据写入失败"
		result.EndTime = time.Now()
		result.Duration = time.Since(result.StartTime)
		return result
	}

	// 写入滚筒方向 (D600: 1=左滚筒, 2=右滚筒)
	drumResult := se.writeRegisterWithLog("D600", 600, 2, "写入滚筒方向（右滚筒）")
	result.Operations = append(result.Operations, drumResult)

	if !drumResult.Success {
		result.Status = "failed"
		result.Error = "滚筒方向写入失败"
		result.EndTime = time.Now()
		result.Duration = time.Since(result.StartTime)
		return result
	}

	// 发送数据写入完成信号
	completeResult := se.writeCoilWithLog("M603", 603, true, "发送数据写入完成信号")
	result.Operations = append(result.Operations, completeResult)

	if !completeResult.Success {
		result.Status = "failed"
		result.Error = "数据写入完成信号发送失败"
		result.EndTime = time.Now()
		result.Duration = time.Since(result.StartTime)
		return result
	}

	// 数据写入成功
	result.Status = "success"
	result.EndTime = time.Now()
	result.Duration = time.Since(result.StartTime)

	successOp := OperationResult{
		Operation: "summary",
		Message:   "✅ 数据写入完成！方向=右侧，距离=123.45米，滚筒=右滚筒",
		Success:   true,
		Timestamp: time.Now().Format("15:04:05.000"),
		Duration:  result.Duration.String(),
	}
	result.Operations = append(result.Operations, successOp)

	return result
}

// ExecuteMachineComplete 执行机台完成
func (se *StepExecutor) ExecuteMachineComplete() *StepResult {
	result := &StepResult{
		StepID:     "machine_complete",
		StepName:   "机台完成",
		Status:     "running",
		StartTime:  time.Now(),
		Operations: make([]OperationResult, 0),
	}

	// 发送机台完成信号
	completeResult := se.writeCoilWithLog("M606", 606, true, "发送机台完成信号")
	result.Operations = append(result.Operations, completeResult)

	if !completeResult.Success {
		result.Status = "failed"
		result.Error = "机台完成信号发送失败"
		result.EndTime = time.Now()
		result.Duration = time.Since(result.StartTime)
		return result
	}

	// 发送调头信号
	turnResult := se.writeCoilWithLog("M608", 608, true, "发送调头信号")
	result.Operations = append(result.Operations, turnResult)

	if !turnResult.Success {
		result.Status = "failed"
		result.Error = "调头信号发送失败"
		result.EndTime = time.Now()
		result.Duration = time.Since(result.StartTime)
		return result
	}

	// 发送回到原点信号
	returnResult := se.writeCoilWithLog("M610", 610, true, "发送回到原点信号")
	result.Operations = append(result.Operations, returnResult)

	if !returnResult.Success {
		result.Status = "failed"
		result.Error = "回到原点信号发送失败"
		result.EndTime = time.Now()
		result.Duration = time.Since(result.StartTime)
		return result
	}

	// 机台完成成功
	result.Status = "success"
	result.EndTime = time.Now()
	result.Duration = time.Since(result.StartTime)

	successOp := OperationResult{
		Operation: "summary",
		Message:   "✅ 机台完成信号发送成功！M606+M608+M610=true",
		Success:   true,
		Timestamp: time.Now().Format("15:04:05.000"),
		Duration:  result.Duration.String(),
	}
	result.Operations = append(result.Operations, successOp)

	return result
}

// ExecuteReleaseControl 执行释放控制权
func (se *StepExecutor) ExecuteReleaseControl() *StepResult {
	result := &StepResult{
		StepID:     "release_control",
		StepName:   "释放控制权",
		Status:     "running",
		StartTime:  time.Now(),
		Operations: make([]OperationResult, 0),
	}

	// 步骤1: 向M607写入控制权释放信号
	writeResult := se.writeCoilWithLog("M607", 607, true, "发送控制权释放信号")
	result.Operations = append(result.Operations, writeResult)

	if !writeResult.Success {
		result.Status = "failed"
		result.Error = writeResult.Message
		result.EndTime = time.Now()
		result.Duration = time.Since(result.StartTime)
		return result
	}

	// 步骤2: 等待并读取M501确认控制权释放
	waitOp := OperationResult{
		Operation: "wait",
		Message:   "⏳ 等待PLC确认控制权释放（M501）...",
		Success:   true,
		Timestamp: time.Now().Format("15:04:05.000"),
	}
	result.Operations = append(result.Operations, waitOp)

	maxWaitTime := 5 * time.Second
	waitStart := time.Now()

	for time.Since(waitStart) < maxWaitTime {
		time.Sleep(200 * time.Millisecond)

		readResult := se.readCoilWithLog("M501", 501, "读取PLC控制权释放确认")
		result.Operations = append(result.Operations, readResult)

		if readResult.Success && !readResult.Value.(bool) {
			// 控制权释放成功
			result.Status = "success"
			result.EndTime = time.Now()
			result.Duration = time.Since(result.StartTime)

			successOp := OperationResult{
				Operation: "summary",
				Message:   "✅ 控制权释放成功！M607=true → M501=false",
				Success:   true,
				Timestamp: time.Now().Format("15:04:05.000"),
				Duration:  result.Duration.String(),
			}
			result.Operations = append(result.Operations, successOp)
			return result
		}
	}

	// 超时失败
	result.Status = "failed"
	result.Error = "等待M501释放确认超时（5秒）"
	result.EndTime = time.Now()
	result.Duration = time.Since(result.StartTime)

	failureOp := OperationResult{
		Operation: "summary",
		Message:   "❌ 控制权释放失败：M501在5秒内未变为false",
		Success:   false,
		Timestamp: time.Now().Format("15:04:05.000"),
	}
	result.Operations = append(result.Operations, failureOp)

	return result
}

// 辅助方法

// writeCoilWithLog 写入线圈并记录日志
func (se *StepExecutor) writeCoilWithLog(name string, address uint16, value bool, description string) OperationResult {
	startTime := time.Now()

	data, err := se.plcController.WriteSingleCoil(address, value)

	duration := time.Since(startTime)

	result := OperationResult{
		Operation: "write",
		Address:   name,
		Value:     value,
		Success:   err == nil,
		Timestamp: time.Now().Format("15:04:05.000"),
		Duration:  duration.String(),
	}

	if err != nil {
		result.Message = fmt.Sprintf("❌ %s失败：向地址%s写入%v时出错: %v", description, name, value, err)
	} else {
		result.Message = fmt.Sprintf("✅ %s成功：向地址%s写入%v", description, name, value)
		if len(data) > 0 {
			result.Message += fmt.Sprintf("，返回数据: %v", data)
		}
	}

	return result
}

// readCoilWithLog 读取线圈并记录日志
func (se *StepExecutor) readCoilWithLog(name string, address uint16, description string) OperationResult {
	startTime := time.Now()

	data, err := se.plcController.ReadCoils(address, 1)

	duration := time.Since(startTime)

	result := OperationResult{
		Operation: "read",
		Address:   name,
		Success:   err == nil,
		Timestamp: time.Now().Format("15:04:05.000"),
		Duration:  duration.String(),
	}

	if err != nil {
		result.Message = fmt.Sprintf("❌ %s失败：读取地址%s时出错: %v", description, name, err)
		result.Value = false
	} else {
		value := len(data) > 0 && data[0] != 0
		result.Value = value
		result.Message = fmt.Sprintf("✅ %s成功：地址%s当前值为%v", description, name, value)
	}

	return result
}

// writeRegisterWithLog 写入寄存器并记录日志
func (se *StepExecutor) writeRegisterWithLog(name string, address uint16, value uint16, description string) OperationResult {
	startTime := time.Now()

	data, err := se.plcController.WriteSingleRegister(address, value)

	duration := time.Since(startTime)

	result := OperationResult{
		Operation: "write",
		Address:   name,
		Value:     value,
		Success:   err == nil,
		Timestamp: time.Now().Format("15:04:05.000"),
		Duration:  duration.String(),
	}

	if err != nil {
		result.Message = fmt.Sprintf("❌ %s失败：向寄存器%s写入%d时出错: %v", description, name, value, err)
	} else {
		result.Message = fmt.Sprintf("✅ %s成功：向寄存器%s写入%d", description, name, value)
		if len(data) > 0 {
			result.Message += fmt.Sprintf("，返回数据: %v", data)
		}
	}

	return result
}

// writeFloatToRegisters 将float32写入两个连续寄存器
func (se *StepExecutor) writeFloatToRegisters(name string, startAddress uint16, value float32, description string) OperationResult {
	startTime := time.Now()

	// 将float32转换为uint32，然后拆分为两个uint16
	floatBits := math.Float32bits(value)
	lowRegister := uint16(floatBits & 0xFFFF)          // 低16位
	highRegister := uint16((floatBits >> 16) & 0xFFFF) // 高16位

	result := OperationResult{
		Operation: "write",
		Address:   name,
		Value:     value,
		Success:   true,
		Timestamp: time.Now().Format("15:04:05.000"),
	}

	// 写入低位寄存器
	_, err1 := se.plcController.WriteSingleRegister(startAddress, lowRegister)
	if err1 != nil {
		result.Success = false
		result.Message = fmt.Sprintf("❌ %s失败：写入低位寄存器%s时出错: %v", description, name, err1)
		result.Duration = time.Since(startTime).String()
		return result
	}

	// 写入高位寄存器
	_, err2 := se.plcController.WriteSingleRegister(startAddress+1, highRegister)
	if err2 != nil {
		result.Success = false
		result.Message = fmt.Sprintf("❌ %s失败：写入高位寄存器%s+1时出错: %v", description, name, err2)
		result.Duration = time.Since(startTime).String()
		return result
	}

	result.Duration = time.Since(startTime).String()
	result.Message = fmt.Sprintf("✅ %s成功：向寄存器%s和%s+1写入浮点数%.2f (低位:%d,高位:%d)",
		description, name, name, value, lowRegister, highRegister)

	return result
}
