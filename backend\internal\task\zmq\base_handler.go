package zmq

import (
	"fmt"
	"log"

	"github.com/user/agv_nav/internal/zmq"
)

// BaseHandler 指令处理器基类，提供通用功能
type BaseHandler struct {
	instructionCode int    // 指令码
	description     string // 描述
	robotNo         string // 机器人编号
}

// NewBaseHandler 创建基础处理器
func NewBaseHandler(instructionCode int, description string, robotNo string) *BaseHandler {
	return &BaseHandler{
		instructionCode: instructionCode,
		description:     description,
		robotNo:         robotNo,
	}
}

// GetInstructionCode 获取指令码
func (h *BaseHandler) GetInstructionCode() int {
	return h.instructionCode
}

// GetDescription 获取描述
func (h *BaseHandler) GetDescription() string {
	return h.description
}

// CreateSuccessResponse 创建成功响应
func (h *BaseHandler) CreateSuccessResponse(content interface{}) *zmq.Message {
	return zmq.NewMessage(zmq.InstructionReply, true, content)
}

// CreateErrorResponse 创建错误响应
func (h *BaseHandler) CreateErrorResponse(err error) *zmq.Message {
	errorMsg := fmt.Sprintf("处理指令%d失败: %v", h.instructionCode, err)
	log.Printf("❌ %s", errorMsg)
	return zmq.NewMessage(zmq.InstructionReply, false, errorMsg)
}

// LogReceived 记录收到消息
func (h *BaseHandler) LogReceived(message *zmq.Message) {
	log.Printf("📥 [%s] 收到消息: 指令=%d, 时间=%v", 
		h.description, message.Instruction, message.TimeStamp)
}

// LogCompleted 记录处理完成
func (h *BaseHandler) LogCompleted() {
	log.Printf("✅ [%s] 处理完成", h.description)
}

// ValidateMessage 验证消息基本信息
func (h *BaseHandler) ValidateMessage(message *zmq.Message) error {
	if message == nil {
		return fmt.Errorf("消息不能为空")
	}
	
	if message.Instruction != h.instructionCode {
		return fmt.Errorf("指令码不匹配: 期望=%d, 实际=%d", 
			h.instructionCode, message.Instruction)
	}
	
	return nil
}