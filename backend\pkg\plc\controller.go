package plc

import (
	"fmt"
	"sync"
	"time"

	"github.com/goburrow/modbus"
	"github.com/user/agv_nav/pkg/logger"
)

// Controller 表示PLC控制器
type Controller struct {
	handler   *modbus.TCPClientHandler
	client    modbus.Client
	connected bool
	ipAddress string
	port      string
	slaveID   byte
	timeout   time.Duration

	// 心跳相关字段
	heartbeatInterval     time.Duration
	heartbeatReadAddress  uint16
	heartbeatWriteAddress uint16
	heartbeatTicker       *time.Ticker
	heartbeatStop         chan bool
	heartbeatMutex        sync.Mutex
}

// NewController 创建一个新的PLC控制器
func NewController() *Controller {
	return &Controller{
		connected:             false,
		slaveID:               1,
		timeout:               10 * time.Second,
		heartbeatInterval:     2 * time.Second, // 2秒心跳间隔
		heartbeatReadAddress:  500,             // 读M500地址
		heartbeatWriteAddress: 600,             // 写M600地址
		heartbeatStop:         make(chan bool),
	}
}

// Connect 连接到PLC
func (c *Controller) Connect(ipAddress, port string) error {
	plcLogger := logger.GetModuleLogger("plc")
	plcLogger.Info("开始连接PLC控制器", "ip", ipAddress, "port", port, "slaveID", c.slaveID)

	// 保存连接参数
	c.ipAddress = ipAddress
	c.port = port

	// 创建Modbus TCP客户端
	address := fmt.Sprintf("%s:%s", ipAddress, port)
	c.handler = modbus.NewTCPClientHandler(address)
	c.handler.Timeout = c.timeout
	c.handler.SlaveId = c.slaveID

	// 尝试连接
	err := c.handler.Connect()
	if err != nil {
		plcLogger.Error("PLC Modbus TCP连接失败", "address", address, "timeout", c.timeout, "error", err)
		return fmt.Errorf("连接PLC失败: %v", err)
	}

	// 创建Modbus客户端
	c.client = modbus.NewClient(c.handler)
	c.connected = true

	// 启动心跳
	c.startHeartbeat()

	plcLogger.Info("PLC连接成功", "address", address, "heartbeatInterval", c.heartbeatInterval)
	return nil
}

// Disconnect 断开与PLC的连接
func (c *Controller) Disconnect() error {
	plcLogger := logger.GetModuleLogger("plc")
	if !c.connected || c.handler == nil {
		plcLogger.Warn("尝试断开PLC连接，但连接不存在")
		return nil
	}

	address := fmt.Sprintf("%s:%s", c.ipAddress, c.port)
	plcLogger.Info("断开PLC连接", "address", address)

	// 停止心跳
	c.stopHeartbeat()

	err := c.handler.Close()
	if err != nil {
		plcLogger.Error("PLC连接断开失败", "address", address, "error", err)
		return fmt.Errorf("断开PLC连接失败: %v", err)
	}

	c.connected = false
	plcLogger.Info("PLC连接已断开", "address", address)

	return nil
}

// IsConnected 返回是否已连接
func (c *Controller) IsConnected() bool {
	return c.connected
}

// ReadHoldingRegisters 读取保持寄存器
func (c *Controller) ReadHoldingRegisters(address, quantity uint16) ([]byte, error) {
	plcLogger := logger.GetModuleLogger("plc")
	if !c.connected {
		plcLogger.Error("PLC读取保持寄存器失败：未连接", "address", address, "quantity", quantity)
		return nil, fmt.Errorf("未连接到PLC")
	}

	plcLogger.Debug("读取PLC保持寄存器", "address", address, "quantity", quantity)
	result, err := c.client.ReadHoldingRegisters(address, quantity)
	if err != nil {
		plcLogger.Error("PLC读取保持寄存器失败", "address", address, "quantity", quantity, "error", err)
		return nil, err
	}
	// 解析并显示读取的值
	values := make([]uint16, quantity)
	for i := uint16(0); i < quantity; i++ {
		if i*2+1 < uint16(len(result)) {
			values[i] = (uint16(result[i*2]) << 8) | uint16(result[i*2+1])
		}
	}
	plcLogger.Info("PLC读取保持寄存器成功", "address", address, "quantity", quantity, "dataLength", len(result), "values", values)
	return result, nil
}

// ReadInputRegisters 读取输入寄存器
func (c *Controller) ReadInputRegisters(address, quantity uint16) ([]byte, error) {
	plcLogger := logger.GetModuleLogger("plc")
	if !c.connected {
		plcLogger.Error("PLC读取输入寄存器失败：未连接", "address", address, "quantity", quantity)
		return nil, fmt.Errorf("未连接到PLC")
	}

	plcLogger.Debug("读取PLC输入寄存器", "address", address, "quantity", quantity)
	result, err := c.client.ReadInputRegisters(address, quantity)
	if err != nil {
		plcLogger.Error("PLC读取输入寄存器失败", "address", address, "quantity", quantity, "error", err)
		return nil, err
	}
	// 解析并显示读取的值
	values := make([]uint16, quantity)
	for i := uint16(0); i < quantity; i++ {
		if i*2+1 < uint16(len(result)) {
			values[i] = (uint16(result[i*2]) << 8) | uint16(result[i*2+1])
		}
	}
	plcLogger.Info("PLC读取输入寄存器成功", "address", address, "quantity", quantity, "dataLength", len(result), "values", values)
	return result, nil
}

// ReadCoils 读取线圈状态
func (c *Controller) ReadCoils(address, quantity uint16) ([]byte, error) {
	plcLogger := logger.GetModuleLogger("plc")
	if !c.connected {
		plcLogger.Error("PLC读取线圈失败：未连接", "address", address, "quantity", quantity)
		return nil, fmt.Errorf("未连接到PLC")
	}

	plcLogger.Debug("读取PLC线圈状态", "address", address, "quantity", quantity)
	result, err := c.client.ReadCoils(address, quantity)
	if err != nil {
		plcLogger.Error("PLC读取线圈失败", "address", address, "quantity", quantity, "error", err)
		return nil, err
	}
	// 解析并显示读取的值
	values := make([]bool, quantity)
	for i := uint16(0); i < quantity; i++ {
		byteIndex := i / 8
		bitIndex := i % 8
		if byteIndex < uint16(len(result)) {
			values[i] = (result[byteIndex]>>bitIndex)&0x01 != 0
		}
	}
	plcLogger.Info("PLC读取线圈成功", "address", address, "quantity", quantity, "dataLength", len(result), "values", values)
	return result, nil
}

// ReadDiscreteInputs 读取离散输入状态
func (c *Controller) ReadDiscreteInputs(address, quantity uint16) ([]byte, error) {
	plcLogger := logger.GetModuleLogger("plc")
	if !c.connected {
		plcLogger.Error("PLC读取离散输入失败：未连接", "address", address, "quantity", quantity)
		return nil, fmt.Errorf("未连接到PLC")
	}

	plcLogger.Debug("读取PLC离散输入", "address", address, "quantity", quantity)
	result, err := c.client.ReadDiscreteInputs(address, quantity)
	if err != nil {
		plcLogger.Error("PLC读取离散输入失败", "address", address, "quantity", quantity, "error", err)
		return nil, err
	}
	// 解析并显示读取的值
	values := make([]bool, quantity)
	for i := uint16(0); i < quantity; i++ {
		byteIndex := i / 8
		bitIndex := i % 8
		if byteIndex < uint16(len(result)) {
			values[i] = (result[byteIndex]>>bitIndex)&0x01 != 0
		}
	}
	plcLogger.Info("PLC读取离散输入成功", "address", address, "quantity", quantity, "dataLength", len(result), "values", values)
	return result, nil
}

// WriteSingleCoil 写入单个线圈
func (c *Controller) WriteSingleCoil(address uint16, value bool) ([]byte, error) {
	plcLogger := logger.GetModuleLogger("plc")
	if !c.connected {
		plcLogger.Error("PLC写入单个线圈失败：未连接", "address", address, "value", value)
		return nil, fmt.Errorf("未连接到PLC")
	}

	// 将bool转换为uint16
	var valueUint16 uint16
	if value {
		valueUint16 = 0xFF00 // Modbus协议中表示ON的值
	} else {
		valueUint16 = 0x0000 // Modbus协议中表示OFF的值
	}

	plcLogger.Info("写入PLC单个线圈", "address", address, "value", value, "modbusValue", fmt.Sprintf("0x%04X", valueUint16))
	result, err := c.client.WriteSingleCoil(address, valueUint16)
	if err != nil {
		plcLogger.Error("PLC写入单个线圈失败", "address", address, "value", value, "error", err)
		return nil, err
	}
	plcLogger.Info("PLC写入单个线圈成功", "address", address, "value", value)
	return result, nil
}

// WriteSingleRegister 写入单个寄存器
func (c *Controller) WriteSingleRegister(address, value uint16) ([]byte, error) {
	plcLogger := logger.GetModuleLogger("plc")
	if !c.connected {
		plcLogger.Error("PLC写入单个寄存器失败：未连接", "address", address, "value", value)
		return nil, fmt.Errorf("未连接到PLC")
	}

	plcLogger.Info("写入PLC单个寄存器", "address", address, "value", value)
	result, err := c.client.WriteSingleRegister(address, value)
	if err != nil {
		plcLogger.Error("PLC写入单个寄存器失败", "address", address, "value", value, "error", err)
		return nil, err
	}
	plcLogger.Info("PLC写入单个寄存器成功", "address", address, "value", value)
	return result, nil
}

// WriteMultipleCoils 写入多个线圈
func (c *Controller) WriteMultipleCoils(address, quantity uint16, value []byte) ([]byte, error) {
	plcLogger := logger.GetModuleLogger("plc")
	if !c.connected {
		plcLogger.Error("PLC写入多个线圈失败：未连接", "address", address, "quantity", quantity)
		return nil, fmt.Errorf("未连接到PLC")
	}

	plcLogger.Info("写入PLC多个线圈", "address", address, "quantity", quantity, "dataLength", len(value))
	result, err := c.client.WriteMultipleCoils(address, quantity, value)
	if err != nil {
		plcLogger.Error("PLC写入多个线圈失败", "address", address, "quantity", quantity, "error", err)
		return nil, err
	}
	plcLogger.Info("PLC写入多个线圈成功", "address", address, "quantity", quantity)
	return result, nil
}

// WriteMultipleRegisters 写入多个寄存器
func (c *Controller) WriteMultipleRegisters(address, quantity uint16, values []byte) ([]byte, error) {
	plcLogger := logger.GetModuleLogger("plc")
	if !c.connected {
		plcLogger.Error("PLC写入多个寄存器失败：未连接", "address", address, "quantity", quantity)
		return nil, fmt.Errorf("未连接到PLC")
	}

	plcLogger.Info("写入PLC多个寄存器", "address", address, "quantity", quantity, "dataLength", len(values))
	result, err := c.client.WriteMultipleRegisters(address, quantity, values)
	if err != nil {
		plcLogger.Error("PLC写入多个寄存器失败", "address", address, "quantity", quantity, "error", err)
		return nil, err
	}
	plcLogger.Info("PLC写入多个寄存器成功", "address", address, "quantity", quantity)
	return result, nil
}

// startHeartbeat 启动心跳
func (c *Controller) startHeartbeat() {
	c.heartbeatMutex.Lock()
	defer c.heartbeatMutex.Unlock()

	// 如果心跳已经在运行，先停止
	if c.heartbeatTicker != nil {
		c.stopHeartbeatInternal()
	}

	// 创建新的ticker和stop channel
	c.heartbeatTicker = time.NewTicker(c.heartbeatInterval)
	c.heartbeatStop = make(chan bool)

	plcLogger := logger.GetModuleLogger("plc")
	plcLogger.Info("PLC心跳启动", "interval", c.heartbeatInterval, "readAddress", fmt.Sprintf("M%d", c.heartbeatReadAddress), "writeAddress", fmt.Sprintf("M%d", c.heartbeatWriteAddress))

	// 启动心跳goroutine
	go func() {
		defer func() {
			if c.heartbeatTicker != nil {
				c.heartbeatTicker.Stop()
			}
		}()

		for {
			select {
			case <-c.heartbeatTicker.C:
				// 执行心跳
				if err := c.performHeartbeat(); err != nil {
					plcLogger.Error("PLC心跳失败", "error", err)
					// 可以在这里添加重连逻辑或标记连接状态
				}
			case <-c.heartbeatStop:
				plcLogger.Info("PLC心跳已停止")
				return
			}
		}
	}()
}

// stopHeartbeat 停止心跳
func (c *Controller) stopHeartbeat() {
	c.heartbeatMutex.Lock()
	defer c.heartbeatMutex.Unlock()
	c.stopHeartbeatInternal()
}

// stopHeartbeatInternal 内部停止心跳方法（不加锁）
func (c *Controller) stopHeartbeatInternal() {
	if c.heartbeatTicker != nil {
		c.heartbeatTicker.Stop()
		c.heartbeatTicker = nil
	}

	if c.heartbeatStop != nil {
		close(c.heartbeatStop)
		c.heartbeatStop = nil
	}
}

// performHeartbeat 执行心跳操作：从M500读取值，取反后写入M600
func (c *Controller) performHeartbeat() error {
	plcLogger := logger.GetModuleLogger("plc")
	if !c.connected {
		plcLogger.Error("PLC心跳失败：PLC未连接")
		return fmt.Errorf("PLC未连接")
	}

	// 1. 读取M500地址的值（1个线圈）
	result, err := c.ReadCoils(c.heartbeatReadAddress, 1)
	if err != nil {
		return fmt.Errorf("读取心跳地址M%d失败: %v", c.heartbeatReadAddress, err)
	}

	// 2. 获取当前值并取反
	currentValue := (result[0] & 0x01) != 0 // 提取第一位，转换为bool
	plcLogger.Info("读取M500心跳值", "address", fmt.Sprintf("M%d", c.heartbeatReadAddress), "rawByte", fmt.Sprintf("0x%02X", result[0]), "boolValue", currentValue)
	newValue := !currentValue // 取反

	// 3. 写入取反后的值到M600
	_, err = c.WriteSingleCoil(c.heartbeatWriteAddress, newValue)
	if err != nil {
		return fmt.Errorf("写入心跳地址M%d失败: %v", c.heartbeatWriteAddress, err)
	}

	plcLogger.Info("PLC心跳成功", "readAddress", fmt.Sprintf("M%d", c.heartbeatReadAddress), "readValue", currentValue, "writeAddress", fmt.Sprintf("M%d", c.heartbeatWriteAddress), "writeValue", newValue)
	return nil
}

// SetHeartbeatConfig 设置心跳配置
func (c *Controller) SetHeartbeatConfig(interval time.Duration, readAddress, writeAddress uint16) {
	c.heartbeatMutex.Lock()
	defer c.heartbeatMutex.Unlock()

	c.heartbeatInterval = interval
	c.heartbeatReadAddress = readAddress
	c.heartbeatWriteAddress = writeAddress

	// 如果心跳正在运行，重启以应用新配置
	if c.heartbeatTicker != nil && c.connected {
		c.stopHeartbeatInternal()
		c.startHeartbeat()
	}
}
