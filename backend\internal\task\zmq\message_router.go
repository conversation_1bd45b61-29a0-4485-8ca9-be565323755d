package zmq

import (
	"fmt"
	"log"
	"sync"

	"github.com/user/agv_nav/internal/zmq"
)

// MessageRouter 消息路由器，根据指令码将消息路由到相应的处理器
type MessageRouter struct {
	handlers map[int]InstructionHandler // 指令处理器映射
	mutex    sync.RWMutex              // 读写锁保护处理器映射
	robotNo  string                    // 机器人编号
}

// InstructionHandler 指令处理器接口
type InstructionHandler interface {
	HandleInstruction(message *zmq.Message) (*zmq.Message, error)
	GetInstructionCode() int
	GetDescription() string
}

// NewMessageRouter 创建消息路由器
func NewMessageRouter(robotNo string) *MessageRouter {
	router := &MessageRouter{
		handlers: make(map[int]InstructionHandler),
		robotNo:  robotNo,
	}
	
	log.Printf("🔀 消息路由器已创建 (机器人: %s)", robotNo)
	return router
}

// RegisterHandler 注册指令处理器
func (r *MessageRouter) RegisterHandler(handler InstructionHandler) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	instructionCode := handler.GetInstructionCode()
	r.handlers[instructionCode] = handler
	
	log.Printf("📝 注册指令处理器: 指令=%d, 描述=%s", 
		instructionCode, handler.GetDescription())
}

// UnregisterHandler 注销指令处理器
func (r *MessageRouter) UnregisterHandler(instructionCode int) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	
	if handler, exists := r.handlers[instructionCode]; exists {
		delete(r.handlers, instructionCode)
		log.Printf("🗑️ 注销指令处理器: 指令=%d, 描述=%s", 
			instructionCode, handler.GetDescription())
	}
}

// HandleMessage 实现MessageHandler接口，处理接收到的消息
func (r *MessageRouter) HandleMessage(message *zmq.Message) (*zmq.Message, error) {
	if message == nil {
		return nil, fmt.Errorf("消息不能为空")
	}

	log.Printf("🔀 路由消息: 指令=%d, 时间=%v", message.Instruction, message.TimeStamp)

	r.mutex.RLock()
	handler, exists := r.handlers[message.Instruction]
	r.mutex.RUnlock()

	if !exists {
		log.Printf("⚠️ 未找到指令%d的处理器，返回默认回复", message.Instruction)
		return r.createUnhandledResponse(message), nil
	}

	// 调用具体的指令处理器
	response, err := handler.HandleInstruction(message)
	if err != nil {
		log.Printf("❌ 指令%d处理失败: %v", message.Instruction, err)
		return r.createErrorResponse(message, err), nil
	}

	log.Printf("✅ 指令%d处理成功", message.Instruction)
	return response, nil
}

// GetRegisteredHandlers 获取已注册的指令处理器列表
func (r *MessageRouter) GetRegisteredHandlers() map[int]string {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	result := make(map[int]string)
	for code, handler := range r.handlers {
		result[code] = handler.GetDescription()
	}
	return result
}

// HasHandler 检查是否存在指定指令的处理器
func (r *MessageRouter) HasHandler(instructionCode int) bool {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	_, exists := r.handlers[instructionCode]
	return exists
}

// GetHandlerCount 获取已注册处理器数量
func (r *MessageRouter) GetHandlerCount() int {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	
	return len(r.handlers)
}

// createUnhandledResponse 创建未处理消息的默认回复
func (r *MessageRouter) createUnhandledResponse(originalMessage *zmq.Message) *zmq.Message {
	return zmq.NewMessage(
		zmq.InstructionReply,
		false, // code = false 表示未处理
		fmt.Sprintf("指令%d暂未实现", originalMessage.Instruction),
	)
}

// createErrorResponse 创建错误响应
func (r *MessageRouter) createErrorResponse(originalMessage *zmq.Message, err error) *zmq.Message {
	return zmq.NewMessage(
		zmq.InstructionReply,
		false, // code = false 表示处理失败
		fmt.Sprintf("指令%d处理错误: %v", originalMessage.Instruction, err),
	)
}

// GetStatus 获取路由器状态信息
func (r *MessageRouter) GetStatus() map[string]interface{} {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	handlerDetails := make(map[string]interface{})
	for code, handler := range r.handlers {
		handlerDetails[fmt.Sprintf("instruction_%d", code)] = map[string]interface{}{
			"code":        code,
			"description": handler.GetDescription(),
		}
	}

	return map[string]interface{}{
		"robotNo":        r.robotNo,
		"handlerCount":   len(r.handlers),
		"handlers":       handlerDetails,
	}
}