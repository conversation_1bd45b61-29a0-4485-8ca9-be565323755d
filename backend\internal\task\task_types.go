package task

import (
	"sync"
	"time"

	"github.com/user/agv_nav/internal/database"
	"github.com/user/agv_nav/pkg/agv"
	"github.com/user/agv_nav/pkg/plc"
)

// TaskStatus 任务状态
type TaskStatus string

const (
	StatusIdle    TaskStatus = "idle"    // 空闲
	StatusRunning TaskStatus = "running" // 运行中
	StatusPaused  TaskStatus = "paused"  // 已暂停
	StatusStopped TaskStatus = "stopped" // 已停止
)

// WatchTaskInfo 看车任务信息
type WatchTaskInfo struct {
	Status         TaskStatus      `json:"status"`         // 任务状态
	MachineList    []string        `json:"machineList"`    // 细纱机列表
	CurrentMachine string          `json:"currentMachine"` // 当前处理的细纱机
	ProcessedCount int             `json:"processedCount"` // 已处理数量
	TotalCount     int             `json:"totalCount"`     // 总数量
	StartTime      *time.Time      `json:"startTime"`      // 开始时间
	LastUpdateTime *time.Time      `json:"lastUpdateTime"` // 最后更新时间
	ErrorMessage   string          `json:"errorMessage"`   // 错误信息
	ProgressDetail *ProgressDetail `json:"progressDetail"` // 详细进度信息
}

// ProgressDetail 详细进度信息
type ProgressDetail struct {
	CurrentStep     string     `json:"currentStep"`     // 当前步骤
	StepProgress    int        `json:"stepProgress"`    // 步骤进度(百分比)
	TotalSteps      int        `json:"totalSteps"`      // 总步骤数
	CompletedSteps  int        `json:"completedSteps"`  // 已完成步骤数
	EstimatedTime   *time.Time `json:"estimatedTime"`   // 预计完成时间
	ProcessingSpeed float64    `json:"processingSpeed"` // 处理速度(台/分钟)
}

// WatchTaskManager 看车任务管理器
type WatchTaskManager struct {
	mu             sync.RWMutex
	queue          []string      // 待处理的细纱机队列
	currentMachine string        // 当前正在处理的细纱机
	status         TaskStatus    // 任务状态
	progress       int           // 已完成数量
	total          int           // 总数量
	stopChan       chan struct{} // 停止信号
	pauseChan      chan struct{} // 暂停信号
	resumeChan     chan struct{} // 恢复信号
	isWorking      bool          // 是否有任务在运行

	// 错误处理配置
	maxRetries     int      // 最大重试次数
	failedMachines []string // 失败的机器列表
	lastError      string   // 最后的错误信息

	// 详细进度跟踪
	startTime      *time.Time  // 任务开始时间
	currentStep    string      // 当前步骤描述
	stepProgress   int         // 当前步骤进度
	completedTimes []time.Time // 每台机器完成时间（用于计算速度）

	// 依赖服务
	dbService      *database.NavigationDB
	agvController  *agv.Controller
	plcController  *plc.Controller
	spindleService *SpindleService // 锭号服务
	plcDataHelper  *PLCDataHelper  // PLC数据处理辅助
}

// TaskStatusBroadcastFunc 任务状态广播回调函数类型
type TaskStatusBroadcastFunc func()

// 全局任务管理器实例
var globalTaskManager *WatchTaskManager

// 全局状态广播回调函数
var taskStatusBroadcastCallback TaskStatusBroadcastFunc
