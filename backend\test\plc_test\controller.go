package plc_test

import (
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/user/agv_nav/pkg/plc"
)

// PLCTestController PLC测试控制器
type PLCTestController struct {
	plcController *plc.Controller
	monitor       *PLCMonitor
	stepExecutor  *StepExecutor
	isRunning     bool
	mutex         sync.Mutex
}

// NewPLCTestController 创建新的PLC测试控制器
func NewPLCTestController(plcController *plc.Controller) *PLCTestController {
	controller := &PLCTestController{
		plcController: plcController,
	}

	controller.monitor = NewPLCMonitor(plcController)
	controller.stepExecutor = NewStepExecutor(plcController, controller.monitor)

	return controller
}

// StepResult 单步执行结果
type StepResult struct {
	StepID     string            `json:"stepId"`
	StepName   string            `json:"stepName"`
	Status     string            `json:"status"` // running, success, failed
	StartTime  time.Time         `json:"startTime"`
	EndTime    time.Time         `json:"endTime"`
	Duration   time.Duration     `json:"duration"`
	Operations []OperationResult `json:"operations"`
	Error      string            `json:"error,omitempty"`
}

// OperationResult 操作结果
type OperationResult struct {
	Operation string      `json:"operation"` // "write", "read", "wait", "summary"
	Address   string      `json:"address"`   // "M601", "D602"
	Value     interface{} `json:"value"`     // true, false, 123
	Success   bool        `json:"success"`
	Message   string      `json:"message"` // "已经往地址M601写true成功"
	Timestamp string      `json:"timestamp"`
	Duration  string      `json:"duration"`
}

// ExecuteStep 执行单个步骤
func (tc *PLCTestController) ExecuteStep(stepID string) (*StepResult, error) {
	tc.mutex.Lock()
	defer tc.mutex.Unlock()

	if !tc.plcController.IsConnected() {
		return nil, fmt.Errorf("PLC未连接，请先连接PLC")
	}

	switch stepID {
	case "connect_test":
		return tc.stepExecutor.ExecuteConnectTest(), nil
	case "request_control":
		return tc.stepExecutor.ExecuteRequestControl(), nil
	case "start_task":
		return tc.stepExecutor.ExecuteStartTask(), nil
	case "data_prepare":
		return tc.stepExecutor.ExecuteDataPrepare(), nil
	case "data_write":
		return tc.stepExecutor.ExecuteDataWrite(), nil
	case "machine_complete":
		return tc.stepExecutor.ExecuteMachineComplete(), nil
	case "release_control":
		return tc.stepExecutor.ExecuteReleaseControl(), nil
	default:
		return nil, fmt.Errorf("未知的测试步骤: %s", stepID)
	}
}

// ExecuteFullWorkflow 执行完整工作流程
func (tc *PLCTestController) ExecuteFullWorkflow() error {
	tc.mutex.Lock()
	defer tc.mutex.Unlock()

	if !tc.plcController.IsConnected() {
		return fmt.Errorf("PLC未连接，请先连接PLC")
	}

	steps := []string{
		"connect_test",
		"request_control",
		"start_task",
		"data_prepare",
		"data_write",
		"machine_complete",
		"release_control",
	}

	tc.isRunning = true
	defer func() { tc.isRunning = false }()

	log.Printf("开始执行PLC完整工作流程，共%d个步骤", len(steps))

	for i, stepID := range steps {
		if !tc.isRunning {
			log.Printf("工作流程被中断")
			return fmt.Errorf("工作流程被中断")
		}

		log.Printf("执行步骤 %d/%d: %s", i+1, len(steps), stepID)

		result, err := tc.ExecuteStep(stepID)
		if err != nil {
			log.Printf("步骤 %s 执行失败: %v", stepID, err)
			return fmt.Errorf("步骤 %s 执行失败: %v", stepID, err)
		}

		if result.Status == "failed" {
			log.Printf("步骤 %s 执行失败: %s", stepID, result.Error)
			return fmt.Errorf("步骤 %s 执行失败: %s", stepID, result.Error)
		}

		log.Printf("步骤 %s 执行成功，耗时: %v", stepID, result.Duration)

		// 步骤间稍作停顿
		if i < len(steps)-1 {
			time.Sleep(500 * time.Millisecond)
		}
	}

	log.Printf("PLC完整工作流程执行完成")
	return nil
}

// StartMonitoring 启动实时监控
func (tc *PLCTestController) StartMonitoring() {
	tc.monitor.StartMonitoring()
}

// StopMonitoring 停止实时监控
func (tc *PLCTestController) StopMonitoring() {
	tc.monitor.StopMonitoring()
}

// AddMonitorSubscriber 添加监控订阅者
func (tc *PLCTestController) AddMonitorSubscriber(clientID string, callback func(PLCChangeData)) {
	tc.monitor.AddSubscriber(clientID, callback)
}

// RemoveMonitorSubscriber 移除监控订阅者
func (tc *PLCTestController) RemoveMonitorSubscriber(clientID string) {
	tc.monitor.RemoveSubscriber(clientID)
}

// GetCurrentValues 获取当前所有监控地址的值
func (tc *PLCTestController) GetCurrentValues() map[string]interface{} {
	return tc.monitor.GetCurrentValues()
}

// StopWorkflow 停止工作流程执行
func (tc *PLCTestController) StopWorkflow() {
	tc.mutex.Lock()
	defer tc.mutex.Unlock()
	tc.isRunning = false
}

// IsRunning 检查是否正在运行工作流程
func (tc *PLCTestController) IsRunning() bool {
	tc.mutex.Lock()
	defer tc.mutex.Unlock()
	return tc.isRunning
}

// GetWorkflowSteps 获取所有工作流程步骤定义
func (tc *PLCTestController) GetWorkflowSteps() []WorkflowStep {
	return []WorkflowStep{
		{
			ID:          "connect_test",
			Name:        "连接测试",
			Description: "测试PLC连接和心跳机制",
			Addresses:   []string{"M500", "M600"},
		},
		{
			ID:          "request_control",
			Name:        "请求控制权",
			Description: "向PLC请求控制权 (M601=1 → M501=1)",
			Addresses:   []string{"M601", "M501"},
		},
		{
			ID:          "start_task",
			Name:        "开始任务信号",
			Description: "发送任务开始信号 (M602=1)",
			Addresses:   []string{"M602"},
		},
		{
			ID:          "data_prepare",
			Name:        "数据准备检查",
			Description: "检查PLC是否准备接收数据 (M509=1)",
			Addresses:   []string{"M509"},
		},
		{
			ID:          "data_write",
			Name:        "数据写入",
			Description: "写入方向和距离数据 (M604, D602-D603, D600)",
			Addresses:   []string{"M604", "D602", "D603", "D600", "M603"},
		},
		{
			ID:          "machine_complete",
			Name:        "机台完成",
			Description: "发送机台完成信号 (M606, M608, M610)",
			Addresses:   []string{"M606", "M608", "M610"},
		},
		{
			ID:          "release_control",
			Name:        "释放控制权",
			Description: "释放PLC控制权 (M607=1 → M501=0)",
			Addresses:   []string{"M607", "M501"},
		},
	}
}

// WorkflowStep 工作流程步骤定义
type WorkflowStep struct {
	ID          string   `json:"id"`
	Name        string   `json:"name"`
	Description string   `json:"description"`
	Addresses   []string `json:"addresses"`
}
