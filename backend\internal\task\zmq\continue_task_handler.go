package zmq

import (
	"fmt"
	"log"
	"sync"

	"github.com/user/agv_nav/internal/zmq"
)

// ContinueTaskHandler 继续任务指令处理器，处理203指令
type ContinueTaskHandler struct {
	*BaseHandler
	systemConfig *zmq.SystemConfig
	resultChan   chan *SchedulerInstructionResult
	mutex        sync.RWMutex
	isWaiting    bool
	zmqManager   *ZMQManager // ZMQ管理器，用于访问状态上报器
}

// NewContinueTaskHandler 创建继续任务指令处理器
func NewContinueTaskHandler(systemConfig *zmq.SystemConfig) *ContinueTaskHandler {
	return &ContinueTaskHandler{
		BaseHandler:  NewBaseHandler(zmq.InstructionContinueTask, "继续任务指令处理器", systemConfig.RobotNo),
		systemConfig: systemConfig,
		resultChan:   make(chan *SchedulerInstructionResult, 1),
	}
}

// SetZMQManager 设置ZMQ管理器
func (h *ContinueTaskHandler) SetZMQManager(zmqManager *ZMQManager) {
	h.zmqManager = zmqManager
	log.Printf("📡 ZMQManager set for ContinueTaskHandler")
}

// HandleInstruction 处理继续任务指令（203）
func (h *ContinueTaskHandler) HandleInstruction(message *zmq.Message) (*zmq.Message, error) {
	log.Printf("📥 [继续任务] 收到203指令")

	// 验证消息
	if err := h.ValidateMessage(message); err != nil {
		return h.createErrorResponse(err.Error()), nil
	}

	// 解析任务内容（203指令必须有任务信息）
	taskInfo, err := h.parseTaskContent(message.Content)
	if err != nil {
		log.Printf("❌ [继续任务] 解析任务内容失败: %v", err)
		return h.createErrorResponse(fmt.Sprintf("解析任务内容失败: %v", err)), nil
	}

	if taskInfo == nil {
		log.Printf("❌ [继续任务] 继续任务指令缺少任务信息")
		return h.createErrorResponse("继续任务指令缺少任务信息"), nil
	}

	// 验证机器人编号
	if taskInfo.RobotNo != h.systemConfig.RobotNo {
		log.Printf("❌ [继续任务] 机器人编号不匹配，期望: %s, 实际: %s",
			h.systemConfig.RobotNo, taskInfo.RobotNo)
		return h.createRobotMismatchResponse(), nil
	}

	log.Printf("🔄 [继续任务] 收到继续任务指令")
	log.Printf("   - 新任务ID: %s", taskInfo.TaskId)
	log.Printf("   - 车道列表: %v", taskInfo.LaneNos)

	// 通知状态上报器：收到调度指令
	if h.zmqManager != nil {
		if statusReporter := h.zmqManager.GetStatusReporter(); statusReporter != nil {
			statusReporter.OnSchedulerInstructionReceived(203) // 继续任务指令
			log.Printf("✅ [继续任务] 已通知状态上报器：收到指令203")
		}
	}

	// 创建结果
	result := &SchedulerInstructionResult{
		InstructionType: 203,
		TaskInfo:        taskInfo,
		Error:           nil,
	}

	// 发送结果到等待通道（非阻塞）
	h.mutex.Lock()
	if h.isWaiting {
		select {
		case h.resultChan <- result:
			log.Printf("📤 [继续任务] 结果已发送到等待通道")
		default:
			log.Printf("⚠️ [继续任务] 等待通道已满，跳过结果发送")
		}
	}
	h.mutex.Unlock()

	// 立即回复确认
	reply := h.createSuccessResponse("继续任务指令已接收")
	log.Printf("📤 [继续任务] 发送确认回复")
	
	return reply, nil
}

// WaitForResult 等待处理结果
func (h *ContinueTaskHandler) WaitForResult() (*SchedulerInstructionResult, bool) {
	h.mutex.Lock()
	h.isWaiting = true
	h.mutex.Unlock()

	defer func() {
		h.mutex.Lock()
		h.isWaiting = false
		h.mutex.Unlock()
	}()

	select {
	case result := <-h.resultChan:
		return result, true
	default:
		return nil, false
	}
}

// parseTaskContent 解析任务内容（203指令需要完整的任务信息）
func (h *ContinueTaskHandler) parseTaskContent(content interface{}) (*TaskInfo, error) {
	if content == nil {
		return nil, fmt.Errorf("缺少任务内容")
	}

	contentMap, ok := content.(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("任务内容格式错误")
	}

	taskInfo := &TaskInfo{}

	// 解析robotNo（必需）
	if robotNo, exists := contentMap["robotNo"]; exists {
		if robotNoStr, ok := robotNo.(string); ok {
			taskInfo.RobotNo = robotNoStr
		} else {
			return nil, fmt.Errorf("robotNo字段类型错误")
		}
	} else {
		return nil, fmt.Errorf("缺少robotNo字段")
	}

	// 解析taskId（必需）
	if taskId, exists := contentMap["taskId"]; exists {
		if taskIdStr, ok := taskId.(string); ok {
			taskInfo.TaskId = taskIdStr
		} else {
			return nil, fmt.Errorf("taskId字段类型错误")
		}
	} else {
		return nil, fmt.Errorf("缺少taskId字段")
	}

	// 解析laneNos（必需）
	if laneNos, exists := contentMap["laneNos"]; exists {
		if laneNosArray, ok := laneNos.([]interface{}); ok {
			taskInfo.LaneNos = make([]string, 0, len(laneNosArray))
			for _, lane := range laneNosArray {
				if laneStr, ok := lane.(string); ok {
					taskInfo.LaneNos = append(taskInfo.LaneNos, laneStr)
				}
			}
		} else {
			return nil, fmt.Errorf("laneNos字段类型错误")
		}
	} else {
		return nil, fmt.Errorf("缺少laneNos字段")
	}

	// 解析remark（可选）
	if remark, exists := contentMap["remark"]; exists {
		if remarkStr, ok := remark.(string); ok {
			taskInfo.Remark = remarkStr
		}
	}

	return taskInfo, nil
}

// createSuccessResponse 创建成功响应
func (h *ContinueTaskHandler) createSuccessResponse(message string) *zmq.Message {
	reply := zmq.NewMessage(zmq.InstructionReply, true, nil)
	reply.Message = message
	return reply
}

// createErrorResponse 创建错误响应
func (h *ContinueTaskHandler) createErrorResponse(errorMsg string) *zmq.Message {
	reply := zmq.NewMessage(zmq.InstructionReply, false, nil)
	reply.Message = errorMsg
	return reply
}

// createRobotMismatchResponse 创建机器人编号不匹配响应
func (h *ContinueTaskHandler) createRobotMismatchResponse() *zmq.Message {
	reply := zmq.NewMessage(zmq.InstructionReply, false, nil)
	reply.Message = "非本机任务"
	return reply
}