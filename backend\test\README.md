# ZMQ通信独立测试框架

这是一个完全独立的ZeroMQ通信测试框架，用于测试AGV机器人与调度系统之间的所有通信协议，无需实际硬件设备。

## 功能特性

✅ **完全独立** - 无硬件依赖，可在任何环境运行  
✅ **协议完整** - 覆盖所有ZMQ通信协议（指令0-203）  
✅ **格式严格** - 严格按照现有代码的消息格式  
✅ **交互友好** - 提供交互式测试界面  
✅ **场景丰富** - 内置多种测试场景  
✅ **数据模拟** - 智能模拟AGV状态和数据变化  

## 支持的通信协议

### 发送给调度系统的指令 (机器人发送)
- **指令0** - 心跳通信：定期发送机器人状态心跳
- **指令100** - 状态上报：每分钟上报机器人详细状态  
- **指令110** - 任务开始：通知调度系统任务已开始
- **指令120** - 任务暂停：通知调度系统任务已暂停
- **指令130** - 任务完成：通知调度系统任务已完成
- **指令131** - 任务强制结束：通知调度系统任务被强制结束

### 从调度系统接收的指令 (调度系统发送)
- **指令1** - 回复确认：调度系统对所有请求的回复确认
- **指令200** - 任务下发：调度系统分配新任务
- **指令201** - 无任务：调度系统指示返回停车点
- **指令202** - 等待任务：调度系统指示在当前位置等待
- **指令203** - 继续任务：调度系统指示继续任务或分配新任务

## 文件结构

```
backend/test/
├── zmq_comm_tester.go        # 主测试框架
├── mock_data_generator.go    # 模拟数据生成器
├── test_scenarios.go         # 测试场景定义
└── README.md                 # 使用说明 (本文件)
```

## 快速开始

### 1. 编译和运行

```bash
# 进入测试目录
cd backend/test

# 编译测试器
go build -o zmq_tester *.go

# 运行测试器
./zmq_tester
```

### 2. 使用交互式界面

运行测试器后，您将看到交互式菜单：

```
=== ZMQ通信测试器 ===
【发送测试】
1. 测试心跳通信 (指令0)
2. 测试状态上报 (指令100)
3. 测试任务开始通知 (指令110)
4. 测试任务完成通知 (指令130)
5. 测试任务暂停通知 (指令120)
6. 测试任务强制结束通知 (指令131)
【接收测试】
7. 测试接收调度器指令 (指令200-203)
【批量测试】
8. 运行全部发送测试
9. 测试连续状态上报
【场景测试】
10. 列出测试场景
11. 执行指定场景
12. 执行所有场景
【结果和配置】
13. 显示测试结果
14. 清除测试结果
15. 显示模拟数据状态
16. 重置模拟数据
17. 设置配置
0. 退出

请选择操作 (0-17):
```

## 测试场景说明

### 1. 基础通信流程
测试心跳、状态上报、任务通知的基础通信流程。

### 2. 完整任务生命周期
模拟完整的任务从分配到完成的生命周期：
- 接收任务分配 → 任务开始 → 状态上报 → 任务暂停 → 继续任务 → 任务完成 → 接收无任务指令

### 3. 异常处理流程
测试各种异常情况的处理：
- 低电量状态 → 机器人异常 → 任务强制结束 → 恢复正常

### 4. 连续状态上报
模拟1分钟间隔的连续状态上报，包含位置和电量变化。

### 5. 调度器指令响应
测试对所有调度器指令(200-203)的响应处理。

## 模拟数据说明

### 机器人状态
- **正常状态** (1)：机器人工作正常
- **电量不足** (-1)：电量低于15%
- **异常状态** (-99)：机器人故障

### 任务状态
- **未开始** (10)：等待任务分配
- **进行中** (20)：正在执行任务
- **已完成** (30)：任务执行完成

### 位置信息
- **停车点** ("0")：机器人停车位置
- **充电点** ("1")：机器人充电位置  
- **权限交接点** ("2")：PLC控制权切换点
- **主干道** ("10")：激光导航行驶过程

### 模拟机器列表
默认机器列表：["61L", "60R", "59L", "58R", "57L", "56R"]

## 配置说明

### 默认配置
- **机器人编号**：ROBOT_01
- **调度器地址**：tcp://localhost:5555
- **心跳间隔**：按需发送
- **状态上报间隔**：按需发送（模拟1分钟）

### 自定义配置
通过菜单选项17可以修改：
- 机器人编号
- 调度器连接地址

## 使用场景

### 1. 开发阶段测试
- 验证消息格式是否正确
- 测试通信协议实现
- 调试ZMQ连接问题

### 2. 集成测试
- 测试与调度系统的完整通信流程
- 验证异常情况处理
- 检查消息序列化/反序列化

### 3. 压力测试
- 连续发送大量消息
- 测试并发通信性能
- 验证系统稳定性

### 4. 回归测试
- 执行预定义测试场景
- 验证功能完整性
- 确保新代码不破坏现有功能

## 注意事项

### 1. 网络连接
- 默认连接地址：tcp://localhost:5555
- 确保调度系统正在监听该地址
- 可以使用简单的ZMQ服务器进行测试

### 2. 消息格式
- 严格按照现有代码的消息结构
- 包含所有必需字段：instruction, timeStamp, code, message, content
- 支持不同类型的content结构

### 3. 测试结果
- 所有测试结果都会记录执行时间和成功状态
- 可以查看详细的请求和响应数据
- 支持测试结果统计和分析

### 4. 模拟数据
- 电量会模拟缓慢下降和充电恢复
- 位置会根据任务状态智能变化
- 支持手动设置各种状态进行特定测试

## 故障排除

### 连接失败
```
连接失败: connection refused
```
**解决方案**：
1. 检查调度系统是否运行
2. 确认连接地址和端口正确
3. 检查防火墙设置

### 消息格式错误
```
序列化消息失败: json marshal error
```
**解决方案**：
1. 检查消息结构是否完整
2. 确认字段类型匹配
3. 验证content内容格式

### 超时错误
```
接收回复失败: timeout
```
**解决方案**：
1. 增加超时时间设置
2. 检查网络连接稳定性
3. 确认调度系统响应正常

## 扩展开发

### 添加新的测试场景
在 `test_scenarios.go` 中添加新的 `TestScenario`：

```go
scenario := TestScenario{
    ID:          "custom_test",
    Name:        "自定义测试",
    Description: "自定义测试场景描述",
    Steps: []TestScenarioStep{
        {Order: 1, Action: "heartbeat", Description: "发送心跳"},
        // 添加更多步骤...
    },
    Expected: TestScenarioExpected{
        TotalSteps:   1,
        SuccessSteps: 1,
        Duration:     1 * time.Second,
    },
}
```

### 添加新的测试操作
在 `TestScenarioManager.executeStep()` 中添加新的 action 处理。

### 自定义模拟数据
修改 `MockDataGenerator` 以支持更复杂的数据模拟逻辑。

## 版本信息

- **版本**：1.0.0
- **Go版本要求**：Go 1.19+
- **依赖库**：
  - github.com/pebbe/zmq4
  - github.com/user/agv_nav/internal/zmq

## 技术支持

如有问题或建议，请：
1. 检查本文档的故障排除部分
2. 查看代码注释和日志输出
3. 参考现有代码实现
4. 联系开发团队

---

**注意**：此测试框架仅用于开发和测试目的，不适用于生产环境。在生产环境中应使用实际的AGV硬件和PLC设备。